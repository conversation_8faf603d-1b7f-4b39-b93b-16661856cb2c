package com.ruoyi.instruction.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 事件基本信息对象 t_instruction_event
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@Data
@ExcelIgnoreUnannotated
@ColumnWidth(16)
@HeadRowHeight(14)
@HeadFontStyle(fontHeightInPoints = 11)
public class InstructionEvent extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 事件标题
     */
    @ExcelProperty(value = "事件名称(必填)")
    private String eventTitle;


    /**
     * 信息类别（预警信息、风险信息、涉稳动态）
     */
    @ExcelProperty(value = "信息类别")
    private String infoCategory;


    /**
     * 事件表主键
     */
    private Long id;

    /**
     * 关联群体id
     */
    private Long groupId;

    /**
     * 群体名称
     */
    @ExcelProperty(value = "关联群体")
    private String groupName;

    /**
     * 事件类型(由类型表中取出)
     */
    private String type;

    /**
     * 类型名称
     */
    @ExcelProperty(value = "事件类型")
    private String typeName;


    /**
     * 信息来源
     */
    @ExcelProperty(value = "信息来源")
    private String infoSource;

    /**
     * 责任单位（第一选择为人员信息中的责属地）
     */
    @ExcelProperty(value = "责任单位")
    private String dutyUnit;

    /**
     * 责任领导
     */
    @ExcelProperty(value = "责任人")
    private String dutyLeader;

    /**
     * 推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    // @Excel(name = "推送时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ExcelProperty(value = "推送时间(格式yyyy-MM-dd)")
    private Date pushTime;

    /**
     * 基本情况
     */
    @ExcelProperty(value = "基本情况")
    private String baseSituation;

    /**
     * 处置情况
     */
    private String disposeSituation;

    /**
     * 反馈情况
     */
    private String feedBack;

    /**
     * 人员信息ids
     */
    private String personIds;

    /**
     * 1:正常 9：删除
     */
    private String status;


    /**
     * 指令id
     */
    private Long instructionId;

    /**
     * 文件ids
     */
    private String fileIds;

    /**
     * 是否发布 1：发布 2：未发布
     */
    private String isRelease;

    /**
     * 事件关联人员
     */
    private List<InstrucationPerson> personList;

    /**
     * 牵头人ids
     */
    private String leadPersonIds;

    /**
     * 指令列表处理时间(指令交办时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    /**
     * 时间关联人员数
     */
    private Integer personCount;

    /**
     * 处理状态 1：已交办  2：未交办
     */
    private Integer dealStatus;

    /**
     * 领导批示
     */
    private String leadIndication;

    /**
     * 动态更进
     */
    private String dynamicAdvance;

    /**
     * 复盘总结
     */
    private String reviewSummary;

    /**
     * 伤亡人数
     */
    private Integer casualties;

    /**
     * 事件类型1：普通事件 2：重大事件
     */
    private String eventType;

    /**
     * 跟踪指挥集合
     */
    private List<MajorEventCommand> majorEventCommandList;

    /**
     * 排序顺序 1：按照伤亡人数降序 2：按照伤亡人数升序 3：按照推送时间降序 创建时间降序 4：按照涉事人数升序  5：按照涉事人数降序  6：推送时间降序排序  7：推送时间升序排序
     */
    private Integer orderBy;

    /**
     * 上访类型：1：到市 2：赴省 3：进京
     */
    private String petitionType;
    /**
     * 上访类型：1：到市 2：赴省 3：进京
     */
    private String petitionTypeName;


    /**
     * 接收单位
     */
    private String[] unit;

    /**
     * 创建者部门id
     */
    private Long createDeptId;

    /**
     * 关联人员姓名
     */
    private String personName;

    /**
     * 是否是重点群体
     */
    private Boolean isHeartGroup;

    /** 预警编号 */
    private String yjbh;

}