package com.ruoyi.modules.harmTrea.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.harmTrea.entity.HarmTrea;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 无害化处理记录表(harm_trea)表数据库访问层
 * <AUTHOR>
 */
@Repository
public interface HarmTreaDao extends BaseDao<HarmTrea> {

    public void updateByEntity(HarmTrea entity);

    HarmTrea getByTakeInId(String id);
}
