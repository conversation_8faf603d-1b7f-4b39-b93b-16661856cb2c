package com.ruoyi.modules.takeIn.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.modules.takeIn.dao.ReclaimRecordDao;
import com.ruoyi.modules.takeIn.dao.TakeInDao;
import com.ruoyi.modules.takeIn.entity.ReclaimRecord;
import com.ruoyi.modules.takeIn.entity.TakeIn;
import com.ruoyi.modules.takeIn.vo.ReclaimRecordVO;
import com.ruoyi.modules.user.dao.SysUserDao;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.entity.SysUser;
import com.ruoyi.modules.user.service.SysUploadFileService;
import com.ruoyi.util.DateUtil;
import com.ruoyi.util.UploadLocalUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 认领、收养记录(reclaim_record)表服务接口
 *
 * <AUTHOR>
 */
@Service
public class ReclaimRecordService extends BaseService<ReclaimRecordDao, ReclaimRecord> {
    @Autowired
    private TakeInDao takeInDao;
    @Autowired
    private SysUserDao sysUserDao;
    @Autowired
    private SysUploadFileService uploadFileService;
    @Autowired
    private TakeInService takeInService;

    public ReclaimRecord getById(String id) {
        ReclaimRecord record = dao.getById(id);
        if (StringUtils.isNotBlank(record.getAuditRecords())) {
            JSONArray array = JSON.parseArray(record.getAuditRecords());
            if (array != null && array.size() > 0) {
                for (int i = 0; i < array.size(); i++) {
                    if (array.getJSONObject(i).getString("userId") != null) {
                        SysUser user = sysUserDao.getById(array.getJSONObject(i).getString("userId"));
                        if (user != null) {
                            array.getJSONObject(i).put("userName", user.getRealName());
                        }
                    }
                }
                array.sort(Comparator.comparing(obj -> ((JSONObject) obj).getDate("time")).reversed());
                record.setRecordsList(array);
            }
        }

        return record;
    }

    public ReclaimRecord getByEntity(ReclaimRecord entity) {
        ReclaimRecord record = dao.getByEntity(entity);
        if (record == null) return null;
        JSONArray array = JSON.parseArray(record.getAuditRecords());
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size(); i++) {
                if (array.getJSONObject(i).getString("userId") != null) {
                    SysUser user = sysUserDao.getById(array.getJSONObject(i).getString("userId"));
                    if (user != null) {
                        array.getJSONObject(i).put("userName", user.getRealName());
                    }
                }
            }
            array.sort(Comparator.comparing(obj -> ((JSONObject) obj).getDate("time")).reversed());
            record.setRecordsList(array);
        }
        return record;
    }

    @Transactional
    public void saveOrUpdate(ReclaimRecord entity) {
        if (entity.getId() != null && !entity.getId().equals("")) {
            // 更新操作
            entity.preUpdate();
            update(entity);
        } else {
            // 新增操作
            insert(entity);
            /*修改状态*/
//            TakeIn take = new TakeIn();
//            take.setId(entity.getTakeInId());
//            take.setStatus("2");
//            takeInDao.updateByEntity(take);
        }
    }
    @Transactional
    public void updateByEntity(ReclaimRecord entity) {
//        修改收养数据状态
        if ("3".equals(entity.getStatus())) {
            TakeIn takeIn = new TakeIn();
            takeIn.setStatus("3");
            takeIn.setId(entity.getTakeInId());
            takeIn.preUpdate();
            takeInDao.updateByEntity(takeIn);
        }
        entity.preUpdate();
        dao.updateByEntity(entity);
    }

    public PageInfo getApplyList(TakeIn entity) {
        PageHelper.startPage(entity.getPageNum(),entity.getPageSize());

        List<TakeIn> takeInList = takeInDao.getReclaimList(entity);

        PageInfo<TakeIn> pageInfo = new PageInfo<TakeIn>(takeInList);

        return pageInfo;
    }

    public void updateApply(TakeIn entity) {
        TakeIn updateTakeIn = new TakeIn();
        updateTakeIn.setId(entity.getId());
        updateTakeIn.setStatus("2");
        takeInDao.updateByEntity(updateTakeIn);
        if ("2".equals(entity.getTakeInType())) {
            throw new RuntimeException("无主收容无法进行认领");
        }

        ReclaimRecord updateRecord = new ReclaimRecord();
        updateRecord.setTakeInId(entity.getId());
        updateRecord.setStatus("2");
        dao.updateByTakeInId(updateRecord);

        uploadFileService.deleteByInstanceIdAndTypeList(entity.getId(), Lists.newArrayList("idupper", "idlower"));
        if (CollectionUtils.isNotEmpty(entity.getUploadFileList())) {
            List<SysUploadFile> list = entity.getUploadFileList();
            for (SysUploadFile s : list) {
                s.setInstanceId(entity.getId());
            }

            uploadFileService.saveAllList(list);
        }
    }

    public void updateRegist(TakeIn entity) {
        TakeIn updateTakeIn = new TakeIn();
        updateTakeIn.setId(entity.getId());
        updateTakeIn.setStatus("3");
        takeInDao.updateByEntity(updateTakeIn);

//        uploadFileService.delByInstanceAndModel(entity.getId(), "");
        if (CollectionUtils.isNotEmpty(entity.getUploadFileList())) {
            List<SysUploadFile> list = entity.getUploadFileList();
            for (SysUploadFile s : list) {
                s.setInstanceId(entity.getId());
            }

            uploadFileService.saveAllList(list);
        }

        takeInService.recordLog(entity.getId(), "认领登记", null);
    }

    public void updateRegistV2(ReclaimRecordVO recordVO) {
        TakeIn updateTakeIn = new TakeIn();
        updateTakeIn.setId(recordVO.getId());
        updateTakeIn.setStatus("3");
        takeInDao.updateByEntity(updateTakeIn);

        // 将字节数组转换为图片并保存
        String today = DateUtil.format(new Date(), "yyyyMMdd");
        String uploadFileName = DateUtil.format(new Date(), "yyyyMMddHHmmss") + (int) ((Math.random() * 9 + 1) * 100000);
        String resultPath = "/" + today + "/" + uploadFileName + "/" + uploadFileName + ".png";
        String fullFilePath = RuoYiConfig.getUploadPath() + resultPath;
        TakeInService.convertBase64StrToImage(recordVO.getImgStr(), fullFilePath);

        List<SysUploadFile> list = Lists.newArrayList();
        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.preInsert();
        sysUploadFile.setInstanceId(recordVO.getId());
        sysUploadFile.setModelType("signature");
        sysUploadFile.setFileUrl(resultPath);
        sysUploadFile.setFileName(IdUtils.fastUUID() + ".png");
        list.add(sysUploadFile);

        uploadFileService.saveAllList(list);
    }

    public List<ReclaimRecord> listByTakeInList(List<String> takeInIdList) {
        return dao.listByTakeInList(takeInIdList);
    }
}
