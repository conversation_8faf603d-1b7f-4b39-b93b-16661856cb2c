package com.ruoyi.common.core.domain.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.ArrayList;
import java.util.List;

/**
 * 督查考核标准对象 supervision_check_standard
 *
 * <AUTHOR>
 * @date 2021-08-06
 */
public class SupervisionCheckStandard extends BaseEntity {

    private static final long serialVersionUID = 7696533450066973388L;
    /**
     * 督查考核标准id
     */
    private Long checkStandardId;

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 父级id
     */
    @Excel(name = "父级id")
    private Long parentId;

    /**
     * 祖级列表
     */
    @Excel(name = "祖级列表")
    private String ancestors;

    /**
     * 内容
     */
    @Excel(name = "内容")
    private String content;

    /**
     * 积分值
     */
    @Excel(name = "积分值")
    private Integer score;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer sort;

    /**
     * 小类代码
     */
    @Excel(name = "小类代码")
    private String code;

    /**
     * 类型 0-加分 1-减分
     */
    @Excel(name = "类型 0-加分 1-减分")
    private String type;

    /**
     * 状态 0-目录 1-标准
     */
    @Excel(name = "状态 0-目录 1-标准")
    private String status;

    /**
     * 删除标志 0-正常 2-删除
     */
    private String delFlag;

    private List<SupervisionCheckStandard> children = new ArrayList<>();

    public void setCheckStandardId(Long checkStandardId) {
        this.checkStandardId = checkStandardId;
    }

    public Long getCheckStandardId() {
        return checkStandardId;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Integer getScore() {
        return score;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getSort() {
        return sort;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public List<SupervisionCheckStandard> getChildren() {
        return children;
    }

    public void setChildren(List<SupervisionCheckStandard> children) {
        this.children = children;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("checkStandardId", getCheckStandardId())
                .append("title", getTitle())
                .append("parentId", getParentId())
                .append("ancestors", getAncestors())
                .append("content", getContent())
                .append("score", getScore())
                .append("sort", getSort())
                .append("code", getCode())
                .append("type", getType())
                .append("status", getStatus())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
