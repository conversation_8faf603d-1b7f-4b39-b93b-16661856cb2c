package com.ruoyi.modules.sysDict.controller;

import com.github.pagehelper.PageInfo;
import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.sysDict.entity.SysDict;
import com.ruoyi.modules.sysDict.service.SysDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * Created by Administrator on 2019/3/25/025.
 */
@RestController
@RequestMapping("/sysDict")
public class SysDictController {

    @Autowired
    private SysDictService sysDictService;

    /**
     * 获取数据字典列表（分页）
     * @param sysDict
     * @return
     */
    @RequestMapping("/getPageList")
    public AjaxResult getList(SysDict sysDict){
        PageInfo<SysDict> pageInfo = sysDictService.getPageList(sysDict);
        return AjaxResult.success(pageInfo);
    }

    /**
     * 获取数据字典详情
     * @param sysDict
     * @return
     */
    @RequestMapping("/getById")
    public AjaxResult getById(SysDict sysDict){
        return AjaxResult.success(sysDictService.getById(sysDict.getId()));
    }

    /**
     * 新增数据字典
     * @param sysDict
     * @return
     */
    @RequestMapping("/insert")
    public AjaxResult insert(SysDict sysDict){
        sysDictService.insert(sysDict);
        return AjaxResult.success();
    }

    /**
     * 更新数据字典
     * @param sysDict
     * @return
     */
    @RequestMapping("/update")
    public AjaxResult update(SysDict sysDict){
         sysDictService.update(sysDict);
        return AjaxResult.success();
    }

    /**
     * 删除数据字典
     * @param sysDict
     * @return
     */
    @RequestMapping("/delete")
    public AjaxResult delete(SysDict sysDict){
        sysDictService.delete(sysDict);
        return AjaxResult.success();
    }



    /**
     * 根据类型获取字典表数据
     * @param sysDict
     * @return
     */
    @RequestMapping("/getAllList")
    public AjaxResult getAllList(SysDict sysDict){
        sysDict.setPageNum(null);
        sysDict.setPageSize(null);
        return AjaxResult.success(sysDictService.getList(sysDict));
    }

    //根据条件查询字典表
    @RequestMapping("/getByEntity")
    public AjaxResult getByEntity(SysDict sysDict){
        return AjaxResult.success(sysDictService.getByEntity(sysDict));
    }

    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUdate(SysDict sysDict){
        sysDictService.saveOrUpdate(sysDict);
        return AjaxResult.success();
    }

    /**
     * 查询宠物类型字段
     * @param sysDict
     * @return
     */
    @RequestMapping("/getPetType")
    public AjaxResult getPetType(SysDict sysDict){
        PageInfo<SysDict> pageInfo = sysDictService.getPetType(sysDict);
        return AjaxResult.success(pageInfo);
    }

}
