<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysAuthOrganizationMapper">

    <resultMap type="SysAuthOrganization" id="SysAuthOrganizationResult">
        <id property="id" column="id"/>
        <result property="appId" column="app_id"/>
        <result property="appSecret" column="app_secret"/>
        <result property="source" column="source"/>
        <result property="sourceCode" column="source_code"/>
        <result property="expiresIn" column="expires_in"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="userName" column="user_name"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectSysAuthOrganizationVo">
        select id,
               app_id,
               app_secret,
               source,
               source_code,
               expires_in,
               start_date,
               end_date,
               user_name,
               remark
        from sys_auth_organization
    </sql>

    <!--只查询有效的应用app相关信息-->
    <select id="selectDataList" parameterType="SysAuthOrganization" resultMap="SysAuthOrganizationResult">
        <include refid="selectSysAuthOrganizationVo"/>
        <where>
            end_date>=now()
        </where>

    </select>


    <!--只查询有效的应用app相关信息-->
    <select id="getObj" resultMap="SysAuthOrganizationResult">
        <include refid="selectSysAuthOrganizationVo"/>
        <where>
            end_date>=now()
            and app_id=#{appId}
        </where>

    </select>

    <select id="selectSysAuthOrganizationList" parameterType="SysAuthOrganization"
            resultMap="SysAuthOrganizationResult">
        <include refid="selectSysAuthOrganizationVo"/>
        <where>
            <if test="appId != null  and appId != ''">and app_id = #{appId}</if>
            <if test="appSecret != null  and appSecret != ''">and app_secret = #{appSecret}</if>
            <if test="source != null  and source != ''">and source = #{source}</if>
            <if test="sourceCode != null  and sourceCode != ''">and source_code = #{sourceCode}</if>
            <if test="expiresIn != null ">and expires_in = #{expiresIn}</if>
            <if test="startDate != null ">and start_date = #{startDate}</if>
            <if test="endDate != null ">and end_date = #{endDate}</if>
            <if test="userName != null  and userName != ''">and user_name like concat('%', #{userName}, '%')</if>
            <if test="remark != null  and remark != ''">and remark like concat('%', #{remark}, '%')</if>
        </where>
    </select>

    <select id="selectSysAuthOrganizationById" parameterType="Long" resultMap="SysAuthOrganizationResult">
        <include refid="selectSysAuthOrganizationVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysAuthOrganization" parameterType="SysAuthOrganization" useGeneratedKeys="true" keyProperty="id">
        insert into sys_auth_organization
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="appSecret != null and appSecret != ''">app_secret,</if>
            <if test="source != null">source,</if>
            <if test="sourceCode != null">source_code,</if>
            <if test="expiresIn != null">expires_in,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="appSecret != null and appSecret != ''">#{appSecret},</if>
            <if test="source != null">#{source},</if>
            <if test="sourceCode != null">#{sourceCode},</if>
            <if test="expiresIn != null">#{expiresIn},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSysAuthOrganization" parameterType="SysAuthOrganization">
        update sys_auth_organization
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="appSecret != null and appSecret != ''">app_secret = #{appSecret},</if>
            <if test="source != null">source = #{source},</if>
            <if test="sourceCode != null">source_code = #{sourceCode},</if>
            <if test="expiresIn != null">expires_in = #{expiresIn},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysAuthOrganizationById" parameterType="Long">
        delete
        from sys_auth_organization
        where id = #{id}
    </delete>

    <delete id="deleteSysAuthOrganizationByIds" parameterType="String">
        delete from sys_auth_organization where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectMaxId" resultType="java.lang.Integer">
        SELECT id from sys_auth_organization ORDER BY id desc  LIMIT 1
    </select>
</mapper> 