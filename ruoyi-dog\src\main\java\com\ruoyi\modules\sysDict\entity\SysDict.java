package com.ruoyi.modules.sysDict.entity;

import com.ruoyi.base.entity.BaseEntity;

/**
 * Created by 29217 on 2019/2/27.
 */
public class SysDict extends BaseEntity {
    private String parentId;  //父级编号
    private String dictType;  //分类名称
    private String dictKey;  //分类标识
    private String name;  //标签名
    private String value1;  //数据值1
    private String value2;  //数据值2
    private String value3;  //数据值3
    private String value4;  //数据值4
    private String description;  //描述
    private String remarks;  //备注信息
    private Integer status;  //状态 1:禁用，0:正常
    private Integer delFlag;  //删除标识 0:正常, 1:删除
    private Integer sortNum;  //排列序号

    public String getDictType() {
        return dictType;
    }

    public void setDictType(String dictType) {
        this.dictType = dictType;
    }

    public String getDictKey() {
        return dictKey;
    }

    public void setDictKey(String dictKey) {
        this.dictKey = dictKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue1() {
        return value1;
    }

    public void setValue1(String value1) {
        this.value1 = value1;
    }

    public String getValue2() {
        return value2;
    }

    public void setValue2(String value2) {
        this.value2 = value2;
    }

    public String getValue3() {
        return value3;
    }

    public void setValue3(String value3) {
        this.value3 = value3;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getValue4() {
        return value4;
    }

    public void setValue4(String value4) {
        this.value4 = value4;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }
}
