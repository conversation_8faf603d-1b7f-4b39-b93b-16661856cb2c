package com.ruoyi.modules.punish.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.punish.entity.Punish;
import com.ruoyi.modules.punish.service.PunishService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * 违法处罚表(punish)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("punish")
public class PunishController {
    /**
     * 服务对象
     */
    @Resource
    private PunishService service;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("/getPageList")
    public AjaxResult getPageList(Punish entity) {
        return AjaxResult.success(service.getPageList(entity));
    }

    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(Punish entity) {
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }

    @RequestMapping("/updateByEntity")
    public AjaxResult updateByEntity(Punish entity) {
        service.updateByEntity(entity);
        return AjaxResult.success();
    }

    @RequestMapping("/getAddress")
    public AjaxResult getAddress(Double lon, Double lat) {
        return AjaxResult.success(service.getAddress(lon, lat));
    }

    @RequestMapping("/delete")
    public AjaxResult delete(Punish entity) {
        entity.setDelFlag(2);
        service.delete(entity);
        return AjaxResult.success();
    }

}
