package com.ruoyi.instruction.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.instruction.domain.JudementInfo;

import java.util.List;
import java.util.Map;

/**
 * 分析研判Service接口
 * 
 * <AUTHOR>
 * @date 2023-05-04
 */
public interface IJudementInfoService 
{
    /**
     * 查询分析研判
     * 
     * @param id 分析研判主键
     * @return 分析研判
     */
    public JudementInfo selectJudementInfoById(Long id);

    /**
     * 查询分析研判列表
     * 
     * @param judementInfo 分析研判
     * @return 分析研判集合
     */
    public List<JudementInfo> selectJudementInfoList(JudementInfo judementInfo);

    /**
     * 新增分析研判
     * 
     * @param judementInfo 分析研判
     * @return 结果
     */
    public int insertJudementInfo(JudementInfo judementInfo);

    /**
     * 修改分析研判
     * 
     * @param judementInfo 分析研判
     * @return 结果
     */
    public int updateJudementInfo(JudementInfo judementInfo);

    /**
     * 批量删除分析研判
     * 
     * @param ids 需要删除的分析研判主键集合
     * @return 结果
     */
    public int deleteJudementInfoByIds(Long[] ids);

    /**
     * 删除分析研判信息
     * 
     * @param id 分析研判主键
     * @return 结果
     */
    public int deleteJudementInfoById(Long id);

    /**
     * 获取分析研判数、待研判、已研判
     * @return
     */
    Map<String, Object> getData();

    /**
     * 根据备注查询分析研判信息
     * @param remark
     * @return
     */
    JudementInfo selectByRemark(String remark);

    /**
     * 研判信息概况
     * @return
     */
    AjaxResult overviewInfo();

}
