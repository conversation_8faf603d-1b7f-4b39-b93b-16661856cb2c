package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.IndicatorType;
import com.ruoyi.instruction.domain.ItemInfo;
import com.ruoyi.instruction.service.IIndicatorTypeService;
import com.ruoyi.instruction.service.IInstrucationPersonService;
import com.ruoyi.instruction.service.IItemInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 事项基本信息Controller（挂牌整治）风险排查
 * 
 * <AUTHOR>
 * @date 2023-04-21
 */
@RestController
@RequestMapping("/item/info")
public class ItemInfoController extends BaseController
{
    @Autowired
    private IItemInfoService itemInfoService;

    @Autowired
    private IInstrucationPersonService personService;

    @Autowired
    private IIndicatorTypeService iIndicatorTypeService;
    /**
     * 查询事项基本信息列表
     */
//    @PreAuthorize("@ss.hasPermi('item:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(ItemInfo itemInfo)
    {
        if (itemInfo.getPageNum()!=null){
            startPage();
        }
        List<ItemInfo> list = itemInfoService.selectItemInfoList(itemInfo);
        if (list != null && list.size() > 0) {
            for (ItemInfo group : list) {
                //所有人员ids
                group.setPersonCount(0);
                if (group.getAllPersonIds().length() > 0) {
                    List<String> collect = Arrays.stream(group.getAllPersonIds().split(",")).collect(Collectors.toList());
                    //查询人员数量
                    int count = personService.findPeronCountById(collect);
                    group.setPersonCount(count);
                }

            }
        }
        return getDataTable(list);
    }

    /**
     * 导出事项基本信息列表
     */
//    @PreAuthorize("@ss.hasPermi('item:info:export')")
    @Log(title = "事项基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ItemInfo itemInfo)
    {
        List<ItemInfo> list = itemInfoService.selectItemInfoList(itemInfo);
        ExcelUtil<ItemInfo> util = new ExcelUtil<ItemInfo>(ItemInfo.class);
        util.exportExcel(response, list, "风险排查数据");
    }

    /**
     * 获取事项基本信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('item:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(itemInfoService.selectItemInfoById(id));
    }

    /**
     * 新增事项基本信息
     */
//    @PreAuthorize("@ss.hasPermi('item:info:add')")
    @Log(title = "事项基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ItemInfo itemInfo)
    {
        String type = itemInfo.getType();
        if (!StringUtils.isEmpty(type)){
            IndicatorType indicatorType = iIndicatorTypeService.selectIndicatorTypeById(Long.valueOf(type));
            if (indicatorType!=null){
                itemInfo.setItemTypeName(indicatorType.getTypeName());
            }
        }
        return toAjax(itemInfoService.insertItemInfo(itemInfo));
    }

    /**
     * 修改事项基本信息
     */
//    @PreAuthorize("@ss.hasPermi('item:info:edit')")
    @Log(title = "事项基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ItemInfo itemInfo)
    {
        return toAjax(itemInfoService.updateItemInfo(itemInfo));
    }

    /**
     * 删除事项基本信息
     */
//    @PreAuthorize("@ss.hasPermi('item:info:remove')")
    @Log(title = "事项基本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(itemInfoService.deleteItemInfoByIds(ids));
    }

    /**
     * 下载风险排查导入模板
     *
     * @param response
     */
    @PostMapping("/importPersonTemplate")
    public void importPersonTemplate(HttpServletResponse response) {
        ExcelUtil<ItemInfo> util = new ExcelUtil<>(ItemInfo.class);
        util.importTemplateExcel(response, "风险排查导入模板");
    }
    /**
     * 导入风险排查数据
     */
    @Log(title = "导入风险排查", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<ItemInfo> util = new ExcelUtil<>(ItemInfo.class);
        List<ItemInfo> list = util.importExcel(file.getInputStream());
        //获取操作用户
        String operName = getUsername();
        //存入数据
        String msg = itemInfoService.importData(list, operName);
        return AjaxResult.success(msg);
    }
}
