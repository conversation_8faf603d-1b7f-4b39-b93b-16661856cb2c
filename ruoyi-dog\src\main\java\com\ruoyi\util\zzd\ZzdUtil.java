package com.ruoyi.util.zzd;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.xxpt.gateway.shared.api.request.OapiGettokenJsonRequest;
import com.alibaba.xxpt.gateway.shared.api.request.OapiMessageWorkNotificationRequest;
import com.alibaba.xxpt.gateway.shared.api.request.OapiMoziEmployeeGetByMobileRequest;
import com.alibaba.xxpt.gateway.shared.api.request.OapiRpcOauth2DingtalkAppUserJsonRequest;
import com.alibaba.xxpt.gateway.shared.api.response.OapiGettokenJsonResponse;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMessageWorkNotificationResponse;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMoziEmployeeGetByMobileResponse;
import com.alibaba.xxpt.gateway.shared.api.response.OapiRpcOauth2DingtalkAppUserJsonResponse;
import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.alibaba.xxpt.gateway.shared.client.http.IntelligentGetClient;
import com.alibaba.xxpt.gateway.shared.client.http.IntelligentPostClient;
import com.alibaba.xxpt.gateway.shared.client.http.api.OapiSpResultContent;

import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/10/31 9:22
 */
public class ZzdUtil {
    private static final  String APPKEY = "quanzhen_fw-6Gnpq5965w3knI7vMv";  //审核通过后换正式参数
    private static final  String APPSECRET = "LRjh4ZP4rV7GpdbVSi4yI5hH5p0rMu34QQ58w9w9";//审核通过后换正式参数
    private static final  String URL = "openplatform-pro.ding.zj.gov.cn";//审核通过后换浙政钉路径
    private static final  Long TENANTID = 196729L;//审核通过后换正式租户Id
    private static ExecutableClient executableClient;

    static {
        executableClient = ExecutableClient.getInstance();
        //DomainName不同环境对应不同域名，示例为sass域名
        executableClient.setDomainName(URL);
        executableClient.setProtocal("https");
        //应用App Key
        executableClient.setAccessKey(APPKEY);
        //应用App Secret
        executableClient.setSecretKey(APPSECRET);
        executableClient.init();
    }


    public static JSONObject getZzdInfoByauthCode(String  authCode) {
        String accessToken = "";
        JSONObject userInfo = new JSONObject();
        IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient("/gettoken.json");
        OapiGettokenJsonRequest oapiGettokenJsonRequest = new OapiGettokenJsonRequest();
        //应用的唯一标识key
        oapiGettokenJsonRequest.setAppkey(APPKEY);
        //应用的密钥
        oapiGettokenJsonRequest.setAppsecret(APPSECRET);
        //获取结果
        OapiGettokenJsonResponse apiResult = intelligentGetClient.get(oapiGettokenJsonRequest);
        if(apiResult != null){
            System.out.println("---------------apiResult code---------------");
            System.out.println(apiResult.getCode());
            System.out.println("---------------apiResult code end ---------------");

            System.out.println("---------------apiResult code---------------");
            System.out.println(apiResult.getMessage());
            System.out.println("---------------apiResult code end ---------------");

            if(apiResult.getContent() != null){
                System.out.println("---------------content code---------------");
                System.out.println(apiResult.getContent().getResponseCode());
                System.out.println("---------------content code end ---------------");

                System.out.println("---------------content responseMessage---------------");
                System.out.println(apiResult.getContent().getResponseMessage());
                System.out.println("---------------content responseMessage end ---------------");
            }
        }
        if (apiResult.getSuccess()) {
            String date = apiResult.getContent().getData();
            JSONObject jsonDate = JSONObject.parseObject(date);
            accessToken = jsonDate.getString("accessToken");
        }
        System.out.println(accessToken);
        IntelligentPostClient intelligentPostClient = executableClient.newIntelligentPostClient("/rpc/oauth2/dingtalk_app_user.json");
        OapiRpcOauth2DingtalkAppUserJsonRequest oapiRpcOauth2DingtalkAppUserJsonRequest = new OapiRpcOauth2DingtalkAppUserJsonRequest();
        //登录access_token
        oapiRpcOauth2DingtalkAppUserJsonRequest.setAccess_token(accessToken);
        //临时授权码
        oapiRpcOauth2DingtalkAppUserJsonRequest.setAuth_code(authCode);
        //获取结果
        OapiRpcOauth2DingtalkAppUserJsonResponse result = intelligentPostClient.post(oapiRpcOauth2DingtalkAppUserJsonRequest);
        //{"success":true,"content":{"data":{"lastName":"施振强","realmId":********,"clientId":"testql_01","openid":"0b37af9466e8fc8bf045d9599c67176e","realmName":"浙江宝歌科技有限公司","nickNameCn":"施振强","tenantUserId":"********$843907","employeeCode":"GE_27b7f171c431477e88c369a26049e441","accountId":843907,"tenantName":"浙江宝歌科技有限公司","referId":"843907","namespace":"local","tenantId":********,"account":"szq-ISV"},"success":true,"responseMessage":"成功","responseCode":"0"}}

        if (result.getSuccess()) {
            JSONObject content = JSONObject.parseObject(result.getContent());
            System.out.print("调用浙政钉认证返回");
            System.out.print(content.toJSONString());
            if (content.getBoolean("success")) {
                JSONObject c = JSONObject.parseObject(content.getString("content"));
                userInfo = JSONObject.parseObject(c.getString("data"));
                System.out.print(c.getString("data"));
                //{"lastName":"施振强","realmId":********,"clientId":"testql_01","openid":"0b37af9466e8fc8bf045d9599c67176e","realmName":"浙江宝歌科技有限公司","nickNameCn":"施振强","tenantUserId":"********$843907","employeeCode":"GE_27b7f171c431477e88c369a26049e441","accountId":843907,"tenantName":"浙江宝歌科技有限公司","referId":"843907","namespace":"local","tenantId":********,"account":"szq-ISV"}
                //根据 employeeCode 与犬类平台用户体系中的政府人员 进行比对 存在则登录 不存在则不允许登陆了
            }
        }
        return userInfo;
    }


    public static JSONObject findUserInfoByMobile(String mobile) {
        IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient("/mozi/employee/get_by_mobile");
        OapiMoziEmployeeGetByMobileRequest oapiMoziEmployeeGetByMobileRequest = new OapiMoziEmployeeGetByMobileRequest();
        //手机区号（没有特别说明，固定填写86）
        oapiMoziEmployeeGetByMobileRequest.setAreaCode("86");
        //租户ID
        oapiMoziEmployeeGetByMobileRequest.setTenantId(TENANTID);
        //账号类型（没有特别说明，固定填写local）
        oapiMoziEmployeeGetByMobileRequest.setNamespace("local");
        //手机号
        oapiMoziEmployeeGetByMobileRequest.setMobile(mobile);
        //获取结果
        OapiMoziEmployeeGetByMobileResponse apiResult = intelligentGetClient.get(oapiMoziEmployeeGetByMobileRequest);
        JSONObject userInfo = new JSONObject();
        if (apiResult.getSuccess()) {
            OapiSpResultContent date = apiResult.getContent();
            System.out.println(mobile + " " + date.getResponseMessage());
            if (date.getSuccess()) {
                userInfo= JSONObject.parseObject(date.getData());
                System.out.print(date.getData());
                //{"accountId":843907,"mobile":"***********","employeeCode":"GE_27b7f171c431477e88c369a26049e441","status":0}
                //employeeCode为浙政钉用户唯一值  与犬类平台用户体系中的政府人员进行绑定
            }
        }
        return userInfo;
    }


    public static void  sendZzdMessage(String accountIds , String title, String content,String auth ) {
        //executableClient保证单例
        IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient("/message/workNotification");
        OapiMessageWorkNotificationRequest oapiMessageWorkNotificationRequest = new OapiMessageWorkNotificationRequest();
        //接收者的部门id列表
        oapiMessageWorkNotificationRequest.setOrganizationCodes(null);
        //接收人用户ID
        oapiMessageWorkNotificationRequest.setReceiverIds(accountIds);
        //租户ID
        oapiMessageWorkNotificationRequest.setTenantId(TENANTID.toString());
        //业务消息id
        oapiMessageWorkNotificationRequest.setBizMsgId(UUID.randomUUID().toString());

        String msg = "{\n" +
                "    \"msgtype\": \"link\",\n" +
                "    \"link\": {\n" +
                "        \"messageUrl\": \"https://mapi.zjzwfw.gov.cn/web/mgop/gov-open/zj/**********/reserved/index.html\",\n" +
                "        \"picUrl\":\"@lALOACZwe2Rk\",\n" +
                "        \"title\": \"您有新的工作通知\",\n" +
                "        \"text\": \"点击链接，到养犬信息平台中心查看\"\n" +
                "    }\n" +
                "}";
        //消息对象
        oapiMessageWorkNotificationRequest.setMsg(msg);
        //获取结果
        OapiMessageWorkNotificationResponse apiResult = intelligentGetClient.get(oapiMessageWorkNotificationRequest);
        apiResult.getSuccess();
    }

}
