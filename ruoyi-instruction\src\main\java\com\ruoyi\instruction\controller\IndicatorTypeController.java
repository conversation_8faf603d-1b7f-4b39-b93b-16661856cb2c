package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.IndicatorType;
import com.ruoyi.instruction.service.IIndicatorTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 指标类型Controller
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@RestController
@RequestMapping("/indicator/type")
public class IndicatorTypeController extends BaseController {
    @Autowired
    private IIndicatorTypeService indicatorTypeService;

    /**
     * 查询指标类型列表
     */
    // @PreAuthorize("@ss.hasPermi('indicator:type:list')")
    @GetMapping("/list")
    public AjaxResult list(IndicatorType indicatorType) {

        List<IndicatorType> list = indicatorTypeService.selectIndicatorTypeList(indicatorType);
        return AjaxResult.success(list);
    }

    /**
     * 导出指标类型列表
     */
    @PreAuthorize("@ss.hasPermi('indicator:type:export')")
    @Log(title = "指标类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IndicatorType indicatorType) {
        List<IndicatorType> list = indicatorTypeService.selectIndicatorTypeList(indicatorType);
        ExcelUtil<IndicatorType> util = new ExcelUtil<IndicatorType>(IndicatorType.class);
        util.exportExcel(response, list, "指标类型数据");
    }

    /**
     * 获取指标类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('indicator:type:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(indicatorTypeService.selectIndicatorTypeById(id));
    }

    /**
     * 新增指标类型
     */
    @PreAuthorize("@ss.hasPermi('indicator:type:add')")
    @Log(title = "指标类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IndicatorType indicatorType) {
        return toAjax(indicatorTypeService.insertIndicatorType(indicatorType));
    }

    /**
     * 修改指标类型
     */
    @PreAuthorize("@ss.hasPermi('indicator:type:edit')")
    @Log(title = "指标类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IndicatorType indicatorType) {
        return toAjax(indicatorTypeService.updateIndicatorType(indicatorType));
    }

    /**
     * 删除指标类型
     */
    @PreAuthorize("@ss.hasPermi('indicator:type:remove')")
    @Log(title = "指标类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(indicatorTypeService.deleteIndicatorTypeByIds(ids));
    }


}
