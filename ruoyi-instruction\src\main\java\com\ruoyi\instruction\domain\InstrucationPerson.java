package com.ruoyi.instruction.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 * 指令关联人员信息对象 t_instruction_person
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@Data
public class InstrucationPerson extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 指令人员id
     */
    private Long id;

    /**
     * 人员姓名
     */
    @Excel(name = "人员姓名")
    private String personName;

    /**
     * 证件号码
     */
    @Excel(name = "证件号码")
    private String idCard;

    /**
     * 户籍所在地
     */
    @Excel(name = "户籍所在地")
    private String housePlace;

    /**
     * 当前居住地
     */
    @Excel(name = "当前居住地")
    private String currentPlace;

    /**
     * 责任所在地
     */
    @Excel(name = "责任所在地", combo = {"婺城区", "金东区", "兰溪市", "东阳市", "义乌市", "永康市", "浦江市", "武义县", "磐安县", "开发区"})
    private String dutyPlace;

    /**
     * 电话号码
     */
    @Excel(name = "电话号码")
    private String personPhone;

    /**
     * 人员状态 1：正常 9：删除
     */
    private String status;

    /**
     * 群体id（用作关联）
     */
    private Long groupId;

    private String ids;

    /**
     * 是否是牵头人 0：非牵头人 1：牵头人
     */
    private Integer isLead;

    /**
     * 管控级别 低、中、高 从字典表中取出
     */
    @Excel(name = "管控级别", type = Excel.Type.EXPORT)
    private String controlLevel;

    private String controlLevelName;

    /**
     * 关联事件数
     */
    private Integer eventNum;

    /**
     * 关联群体数
     */
    private Integer groupNum;

    /**
     * 关联事件详细信息
     */
    private List<InstructionEvent> eventList;

    /** 管控策略1：五包一 */
    @Excel(name = "管控策略1：五包一")
    private String controlStrategy;

    /** 用户性别0：男 1：女 2：未知 */
    // @Excel(name = "用户性别0：男 1：女 2：未知")
    private String sex;


    /**
     * 重点人员管控列表
     */
    private List<InstructionPersonControl> personControlList;

    /**
     * 关联群体名称
     */
    private String groupName;

    /**
     * 人员最后一次类型名称
     */
    private String typeName;

    /**
     * 排序类型 1：根据关联事件降序 2根据关联事件升序
     */
    private Integer sortType;

    /** 关联群体id */
    private String groupIds;

    /**
     * 人员关联群体集合
     */
    private List<InstructionGroup> groupList;



}
