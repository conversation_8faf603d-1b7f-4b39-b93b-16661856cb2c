package com.ruoyi.modules.user.service;

import com.alibaba.fastjson2.JSONArray;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.hospital.dao.QualifiDao;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.hospital.service.QualifiService;
import com.ruoyi.modules.user.dao.SysUserDao;
import com.ruoyi.modules.user.dao.SysUserRoleDao;
import com.ruoyi.modules.user.entity.SysUser;
import com.ruoyi.modules.user.entity.SysUserRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Administrator on 2020/1/3/003.
 * 默认继承 基础增删查改方法 单表操作可直接使用
 */
@Service
public class SysUserService extends BaseService<SysUserDao,SysUser>{

    @Autowired
    private SysUserRoleDao sysUserRoleDao;

    @Resource
    private QualifiDao qualifiDao;

    /**
     * 登录校验用户名密码，登录成功返回用户  不成功返回null
     * @param sysUser
     * @return
     */
    public SysUser checkLoginUser(SysUser sysUser){
        if(sysUser.getUserName() == null || sysUser.getPassword() == null){
            return null;
        }
        // 按照加密后密码查询
        String md5Pwd = DigestUtils.md5DigestAsHex(sysUser.getPassword().getBytes());
        sysUser.setPassword(md5Pwd);
        SysUser user = dao.getByEntity(sysUser);
        if(user != null && user.getUserType().intValue() == 2){
            Qualifi qualifi = new Qualifi();
            qualifi.setAccount(user.getId());
            user.setUserQualifi(qualifiDao.getQualifiByAccount(qualifi));
        }
        return user;
    }

    /**
     * 保存或更新用户基本信息
     * @param sysUser
     */
    @Override
    public void saveOrUpdate(SysUser sysUser){
        if(sysUser.getId() != null && !"".equals(sysUser.getId())){
            // 更新用户
            sysUser.preUpdate();
            dao.update(sysUser);
        }else{
            // 新增用户
            sysUser.preInsert();
            // 新增时默认密码 123456
            sysUser.setPassword(DigestUtils.md5DigestAsHex("123456".getBytes()));
            dao.insert(sysUser);
        }
    }

    /**
     * 根据用户名查询用户
     * @return
     */
    public SysUser getByUserName(String userName){
        return dao.getByUserName(userName);
    }

    public void editPass(SysUser sysUser) {
        sysUser.setPassword(DigestUtils.md5DigestAsHex(sysUser.getNewPass1().getBytes()));
        sysUser.preUpdate();
        dao.editPass(sysUser);
    }

    /**
     * 修改用户角色数据
     * @param user
     */
    @Transactional
    public void changeUserRole(SysUser user){
        List<SysUserRole> list = new ArrayList<>();
        if(user.getUserRoleStr() != null && !user.getUserRoleStr().equals("")){
            List<String> roleIds = JSONArray.parseArray(user.getUserRoleStr(), String.class);
            for(String roleId : roleIds){
                SysUserRole userRole = new SysUserRole();
                userRole.preInsert();
                userRole.setUserId(user.getId());
                userRole.setRoleId(roleId);
                list.add(userRole);
            }
        }
        sysUserRoleDao.deleteByUserId(user.getId());
        if(list.size() > 0){
            sysUserRoleDao.saveList(list);
        }
    }

    public Boolean updatePassWord(SysUser sysUser){
        SysUser oldPassWord = dao.getById(sysUser.getId());
        if(!oldPassWord.getPassword().equals(DigestUtils.md5DigestAsHex(sysUser.getPassword().getBytes()))){
            return false;
        }
        SysUser result = new SysUser();
        result.preUpdate();
        result.setId(sysUser.getId());
        result.setPassword(DigestUtils.md5DigestAsHex(sysUser.getNewPass1().getBytes()));
        dao.updatePassWord(result);
        return true;
    }

    public void resetPwd(SysUser sysUser){
        // 重置密码 123456
        sysUser.setPassword(DigestUtils.md5DigestAsHex("123456".getBytes()));
        dao.updatePassWord(sysUser);
    }

    public List<SysUser> queryUserAddres(){
        return dao.queryUserAddres();
    }

    /**
     * 修改密码
     *
     * @param oldPassword 旧密码
     * @param user        用户（新密码和ID）
     */
    @Transactional
    public String changePassword(String oldPassword, SysUser user) {
        SysUser dbUser = dao.getById(user.getId());
        dbUser.preUpdate();
        String md5OldPwd = DigestUtils.md5DigestAsHex(oldPassword.getBytes());
        if (md5OldPwd.equals(dbUser.getPassword())) {
            // 旧密码正确  可以修改
            String md5NewPwd = DigestUtils.md5DigestAsHex(user.getPassword().getBytes());
            dbUser.setPassword(md5NewPwd);
            dbUser.preUpdate();
            dao.updateUserPassword(dbUser);
        } else {
            // 旧密码输入错误 不能修改
            return "旧密码错误，不能修改！";
        }
        return null;
    }
    public List<HashMap>  getEchatByDept(){
        return dao.getEchatByDept();
    }
}
