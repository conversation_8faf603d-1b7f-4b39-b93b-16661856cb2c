<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.user.dao.SysMenuDao" >

    <sql id="sysMenuSql">
        a.id AS "id",
        a.system_flag AS "systemFlag",
        a.parent_id AS "parentId",
        a.menu_name AS "menuName",
        a.menu_order AS "menuOrder",
        a.show_flag AS "showFlag",
        a.sort AS "sort",
        a.icon AS "icon",
        a.path AS "path",
        a.component AS "component",
        a.create_date AS "createDate",
        a.create_by AS "createBy",
        a.update_date AS "updateDate",
        a.update_by AS "updateBy",
        a.del_flag AS "delFlag"
    </sql>

    <select id="getList" resultType="com.ruoyi.modules.user.entity.SysMenu">
        select
          <include refid="sysMenuSql"/>
        FROM sys_menu a
        WHERE a.del_flag = 1
        <if test='systemFlag != null and systemFlag != ""'>
            AND a.system_flag = #{systemFlag}
        </if>
        <if test='menuOrder != null'>
            AND a.a.menu_order = #{menuOrder}
        </if>
        <if test='menuName != null and menuName != ""'>
            AND a.menu_name like concat('%',#{roleName},'%')
        </if>
        order by a.sort
    </select>

    <select id="getRightList" resultType="com.ruoyi.modules.user.entity.SysMenu">
        select
            distinct <include refid="sysMenuSql"/>
        FROM
            sys_user_role sur,
            sys_role_menu srm,
            sys_menu a
        WHERE sur.role_id = srm.role_id  AND srm.menu_id = a.id AND a.del_flag = 1 AND sur.user_id = #{userId}
        order by a.sort
    </select>

    <insert id="insert">
        INSERT INTO sys_menu(
            id,
            system_flag,
            parent_id,
            menu_name,
            menu_order,
            show_flag,
            sort,
            icon,
            path,
            component,
            create_by,
            create_date,
            update_by,
            update_date,
            del_flag
        ) VALUES (
            #{id},
            #{systemFlag},
            #{parentId},
            #{menuName},
            #{menuOrder},
            #{showFlag},
            #{sort},
            #{icon},
            #{path},
            #{component},
            #{createBy},
            #{createDate},
            #{updateBy},
            #{updateDate},
            #{delFlag}
        )
    </insert>

    <update id="update">
        update sys_menu a set
        a.system_flag = #{systemFlag} ,
        a.parent_id = #{parentId} ,
        a.menu_name = #{menuName} ,
        a.menu_order = #{menuOrder} ,
        a.show_flag = #{showFlag} ,
        a.sort = #{sort} ,
        a.icon = #{icon} ,
        a.path = #{path} ,
        a.component = #{component} ,
        a.update_date = #{updateDate} ,
        a.update_by = #{updateBy}
        where a.id = #{id}
    </update>

    <update id="delete" >
        update sys_menu set
        del_flag = 2
        where id = #{id}
    </update>
</mapper>
