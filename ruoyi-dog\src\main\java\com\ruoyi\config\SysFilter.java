package com.ruoyi.config;


import com.alibaba.fastjson2.JSON;
import com.ruoyi.base.CodeConst;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.sysDict.entity.SysDict;
import com.ruoyi.modules.sysDict.service.SysDictService;
import com.ruoyi.modules.user.entity.SysUser;
import com.ruoyi.util.IPUtils;
import com.ruoyi.util.UserCache;
import com.ruoyi.util.UserMapCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

/**
 * Created by Administrator on 2020-11-16.
 * 系统过滤器：过滤业务操作
 */
@Component
public class SysFilter{
    //
    // @Autowired
    // private SysDictService dictService;
    //
    // @Override
    // public void init(FilterConfig filterConfig) throws ServletException {
    //
    // }
    //
    // @Override
    // public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
    //     // 允许跨域访问
    //     HttpServletRequest request = (HttpServletRequest) servletRequest;
    //     HttpServletResponse response = (HttpServletResponse) servletResponse;
    //     response.setHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
    //     response.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");
    //     response.setHeader("Access-Control-Max-Age", "3600");
    //     response.setHeader("Access-Control-Allow-Headers", "x-requested-with");
    //     response.setHeader("Access-Control-Allow-Credentials", "true");
    //     response.setHeader("Access-Control-Allow-Headers", "Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
    //     String myOrigin = request.getHeader("origin");
    //     response.setHeader("Access-Control-Allow-Origin", myOrigin);
    //
    //     StringBuffer url = request.getRequestURL();
    //     SysUser user = (SysUser) request.getSession().getAttribute("sysUser");
    //     if(!url.toString().contains("sysUser/login") && !url.toString().contains("/sysUploadFile/uploadFile")
    //             && !url.toString().contains("/sysDict/getAllList") && !url.toString().contains("/sysUploadFile/downloadLocalFile")
    //             && !url.toString().contains("/sysUser/sendLoginMessage")
    //             && !url.toString().contains("/sysUser/checkMessageCode")
    //             && !url.toString().contains("/hospital/saveOrUpdate")
    //             && !url.toString().contains("/petCertificates/getQZDJ")
    //             && !url.toString().contains("/petCertificates/getZFSR")
    //             && !url.toString().contains("/sysUser/getEchatByDept")
    //             && !url.toString().contains("/petBrand/totalEchar")
    //             && !url.toString().contains("/petBrand/getQrImg")
    //             && !url.toString().contains("/immuneRegister/getHospital")
    //             && !url.toString().contains("/immuneRegister/getImmune")
    //             && !url.toString().contains("/immuneRegister/getPetNum")
    //             && !url.toString().contains("/sysUploadFile/ocrIDCard")
    //             && !url.toString().contains("/sysUploadFile/analysisQr")
    //             && !url.toString().contains("/dept/getSecondDeptTree")
    //             && !url.toString().contains("/hospital/saveOrUpdate")
    //             && !url.toString().contains("/hospital/getApply")
    //             && !url.toString().contains("/hospital/getQualifiByAccount")&& !url.toString().contains("/zzd/getZzdInfoByauthCode")&& !url.toString().contains("/zzd/getZzdInfoByMobile")
    //             &&!url.toString().contains("/petCertificates/getByPetNum")
    //             &&!url.toString().contains("/sysUser/getList")
    //             &&!url.toString().contains("/immuneRegister/downloadFile")
    //             &&!url.toString().contains("/petCertificates/getScreenMap")
    //             && !url.toString().contains("/ZLB/wxlogin")&& !url.toString().contains("/ZLB/zlblogin")
    //             && !url.toString().contains("/dept/getAllList") && !url.toString().contains("/hospital/getList")
    //             && !url.toString().contains("/immuneRegister/getByRegister") && !url.toString().contains("/vaccine/getAllList")
    //             && !url.toString().contains("/petCertificates/getByPetNum") && !url.toString().contains("/zzd/updateQrCode")
    //             && !url.toString().contains("/sysUploadFile/downDogCodeZip") && !url.toString().contains("/petBrand/getByEntity")&& !url.toString().contains("/zzd/test")){
    //
    //         // 非登录接口校验 用户是否存在
    //         String token = request.getParameter("token");
    //         String tokenTip = null;
    //         if(token == null){
    //             tokenTip = "无令牌！";
    //         }else{
    //             UserCache userCache = UserMapCache.getByToken(token);
    //             if(userCache != null){
    //                 if(userCache.getEndTime() < System.currentTimeMillis()){
    //                     tokenTip = "令牌超期！";
    //                 }else{
    //                     // 未超期 正常访问时  刷新令牌时间
    //                     UserMapCache.flushEndTime(userCache);
    //                 }
    //             }else{
    //                 tokenTip = "用户未登录！";
    //             }
    //         }
    //         if(tokenTip != null){
    //             response.setCharacterEncoding("utf-8");
    //             PrintWriter out = response.getWriter();
    //             // 无用户跳转登录页 重新登录
    //             JsonResult result = new JsonResult();
    //             result.setCode(CodeConst.LOGIN_FAIL);
    //             result.setResult(tokenTip);
    //             out.print(JSON.toJSONString(result));
    //             out.flush();
    //             out.close();
    //             return;
    //         }
    //     }
    //     if(user != null && user.getDeptName()!= null && !"".equals(user.getDeptName())){
    //         // 非登录 且用户存在管辖乡镇 默认追加参数 管辖乡镇
    //         MyHttpServletRequest myHttpServletRequest = new MyHttpServletRequest((HttpServletRequest) servletRequest);
    //         chain.doFilter(myHttpServletRequest, servletResponse);
    //     }else{
    //         // 登录或非乡镇用户
    //         chain.doFilter(request,response);
    //     }
    // }
    //
    // @Override
    // public void destroy() {
    //
    // }
}
