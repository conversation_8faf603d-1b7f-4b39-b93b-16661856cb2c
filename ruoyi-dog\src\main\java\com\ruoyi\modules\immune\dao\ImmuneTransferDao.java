package com.ruoyi.modules.immune.dao;


import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.immune.entity.ImmuneRegister;
import com.ruoyi.modules.immune.entity.ImmuneTransfer;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 10:04
 * @version: 1.0
 **/
@Repository
public interface ImmuneTransferDao extends BaseDao<ImmuneTransfer> {

    public List<PetCertificates> queryPageList(PetCertificates petCertificates);

    public void updateStatus(ImmuneTransfer immuneTransfer);

    public ImmuneTransfer getPetTransfer(@Param("petId")String petId, @Param("transferId") String transferId);

    public String fingPetNum(String idCard);

}
