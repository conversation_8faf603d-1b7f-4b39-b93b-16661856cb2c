package com.ruoyi.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @创建时间 2022/1/10
 * @作用：日期工具类
 */
public class DateUtil {

    /**
     * 返回指定格式格式
     * @param date
     * @return
     */
    public static String format(Date date , String pattern){
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    /**
     * String格式的日期转换为Date
     * @param
     * @return
     */

    public static Date strParseDate(String dateStr , String pattern){
        SimpleDateFormat ft = new SimpleDateFormat(pattern);
        Date d = null;
        try {
            d = ft.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return d;
    }

    /**
     * 获取固定小时后的日期 返回时间戳
     * @param date
     * @param hour
     * @return
     */
    public static Date getTimeAfterHour(Date date,int hour){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.HOUR_OF_DAY,hour);
        return cal.getTime();
    }

    /**
     * 获取固定分钟后的日期 返回时间戳
     * @param date
     * @param minute
     * @return
     */
    public static Date getTimeAfterMinute(Date date,int minute){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MINUTE,minute);
        return cal.getTime();
    }
}
