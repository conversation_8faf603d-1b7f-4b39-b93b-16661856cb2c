<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.certificates.dao.PetCertificatesDao">

    <sql id="certificates">
        a.id as "id",
        a.owner_name as "ownerName",
        a.pet_id_card as "petIdCard",
        a.tel as "tel",
        a.pet_num as "petNum",
        a.pet_name as "petName",
        a.pet_type as "petType",
        a.pet_sex as "petSex",
        a.pet_varieties as "petVarieties",
        a.pet_varieties_one as "petVarietiesOne",
        a.pet_hair as "petHair",
        a.pet_age as "petAge",
        a.raise_date as "raiseDate",
        a.pet_address as "petAddress",
        a.pet_dept as "petDept",
        a.street as "street",
        a.is_agency as "isAgency",
        a.agency_com as "agencyCom",
        a.source as "source",
        a.is_reissue as "isReissue",
        a.year_status as "yearStatus",
        a.status as "status",
        a.create_date as "createDate",
        a.immune_id as immuneId,
        a.transfer_id as transferId,
        a.cancel_id as cancelId,
        a.end_date as endDate,
        a.owner_address as "ownerAddress",
        a.expres_type as "expresType",
        a.expres_address as "expresAddress",
        a.hospital_id as "hospitalId",
        a.about_make as "aboutMake",
        a.about_date as "aboutDate",
        a.pet_img as "petImg",
        a.activation as "activation",
        a.about_status as "aboutStatus",
        a.about_reason as "aboutReason",
        a.about_hospital as "aboutHospital",
        a.expres_region as "expresRegion",
        a.receive_status as "receiveStatus",
        a.tricolor as "tricolor",
        a.apply_status as "applyStatus",
        a.apply_reason as "applyReason",
        a.other_varieties as "otherVarieties"

    </sql>
    <select id="getList" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates"  resultType="com.ruoyi.modules.certificates.entity.PetCertificates">
        select
        <include refid="certificates"/>,
        (SELECT count( 1 ) FROM pet_certificates d WHERE  d.del_flag = 1
            AND (apply_status = '2' OR (d.pet_num  != '' AND d.pet_num IS NOT NULL ))
            AND (d.id != a.id or apply_status = '3' or apply_status = '2')
            AND d.pet_id_card = a.pet_id_card) as petCount,<!--同一个身份证是否可以申请犬牌-->
        (select c.dept_name from sys_dept c where c.dept_id=a.pet_dept) as petDeptName,
        (select pd.qr_code from pet_brand pd where pd.brand_num=a.pet_num LIMIT 1) as qrCode
        <if test='type != null and type != "" and type == "4"'>,#{examineType} as examineType</if>
        from pet_certificates a
        where a.del_flag = 1
          <if test="keyword!=null and keyword!=''">
              and (a.owner_name like concat('%',#{keyword},'%') or
              a.tel like concat('%',#{keyword},'%') or
              a.pet_name like concat('%',#{keyword},'%') or
              a.pet_num like concat('%',#{keyword},'%'))
          </if>
        <if test="petIds != null and petIds.size()>0">
            AND a.id in
            <foreach item="id" collection="petIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="keywordOne!=null and keywordOne!=''">
            and (a.pet_id_card like concat('%',#{keywordOne},'%') or
            a.pet_num like concat('%',#{keywordOne},'%'))
        </if>
        <if test="ownerName != null and ownerName != ''">
            and a.owner_name like concat('%',#{ownerName},'%')
        </if>
        <if test="tel != null and tel != ''">
            and a.tel like concat('%',#{tel},'%')
        </if>
        <if test="petName != null and petName != ''">
            and a.pet_name like concat('%',#{petName},'%')
        </if>
        <if test="petNum != null and petNum != ''">
            and a.pet_num like concat('%',#{petNum},'%')
        </if>
        <if test="petType != null and petType != ''">
            and a.pet_type  = #{petType}
        </if>
        <if test="petAge != null and petAge != ''">
            and a.pet_age = #{petAge}
        </if>
        <if test="petSex != null">
            and a.pet_sex = #{petSex}
        </if>
        <if test="isReissue != null and isReissue != ''">
            and a.is_reissue = #{isReissue}
        </if>
        <if test="searchType != null and searchType != ''">
            and a.is_reissue != 1
        </if>
        <if test="isReissueFlag != null and isReissueFlag != ''">
            and a.is_reissue is not null
        </if>
        <if test="status != null and status != '' and status ==2">
            and to_days(now( )) - to_days( a.end_date) &lt;= 30
            and to_days(now( )) - to_days( a.end_date) &gt;= 0
        </if>
        <if test="status != null and status != '' and status ==3">
            and to_days( a.end_date) - to_days(now( )) &gt;0
        </if>
        <if test="agencyCom != null and agencyCom != ''">
            and a.hospital_id = #{agencyCom}
        </if>
        <if test="petNumRate != null and petNumRate != ''">
            and a.pet_num = #{petNumRate}
        </if>
        <if test="telRate != null and telRate != ''">
            and a.tel  = #{telRate}
        </if>
        <if test="petIdCard != null and petIdCard != ''">
            and pet_id_card = #{petIdCard}
        </if>
        <if test="deptIds != null and deptIds.size > 0">
            AND a.pet_dept IN
            <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by =  #{createBy}
        </if>
        <if test='type != null and type != "" and type == "4"'>
            and a.id in (
            SELECT pet_id from pet_record where create_by = #{dealPersonId} and  node in (2,4,8)
            <if test="examineType != null">
                <if test="examineType ==1">and node = 2</if>
                <if test="examineType ==2">and node = 4</if>
                <if test="examineType ==3">and node = 8</if>
            </if>

            )
        </if>
        order by a.create_date desc
    </select>

    <select id="getById" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates"  resultType="com.ruoyi.modules.certificates.entity.PetCertificates">
        select <include refid="certificates"/>,
               (select b.status from immune_register b where a.immune_id =b.id) as immuneStatus,
               (select c.brand_num from immune_reissue c where c.pet_id=a.id  order by create_date DESC LIMIT 1) AS oldPetNum,
          b.status  as transferStatus
        from pet_certificates a LEFT JOIN immune_transfer b on a.transfer_id=b.id   where a.id = #{id}
    </select>

    <select id="getByPetNum" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates"
            resultType="java.util.HashMap">
        select
            a.id as "id",
            a.owner_name as "ownerName",
            a.pet_id_card as "petIdCard",
            a.tel as "tel",
            b.brand_num as "petNum",
            a.pet_name as "petName",
            a.pet_type as "petType",
            a.pet_sex as "petSex",
            (select name  from sys_dict where dict_type='varieties_type' and dict_key=a.pet_varieties) as "petVarieties",
            a.pet_hair as "petHair",
            a.pet_age as "petAge",
            a.raise_date as "raiseDate",
            a.pet_address as "petAddress",
            a.pet_dept as "petDept",
            a.is_agency as "isAgency",
            a.agency_com as "agencyCom",
            a.source as "source",
            a.is_reissue as "isReissue",
            a.year_status as "yearStatus",
            a.status as "status",
            a.create_date as "createDate",
            a.immune_id as immuneId,
            a.transfer_id as transferId,
            a.cancel_id as cancelId,
            a.end_date as endDate,
            a.tel as tel,
            a.owner_name as "ownerName",
            a.owner_address as "ownerAddress",
            a.expres_type as "expresType",
            a.expres_address as "expresAddress",
            a.hospital_id as "hospitalId",
            a.about_make as "aboutMake",
            a.about_date as "aboutDate",
            a.pet_img as "petImg"
        ,b.is_use as  isUse
        ,a.activation as activation
        from pet_brand b
        left join pet_certificates a on a.pet_num =b.brand_num
        where b.id=#{brandId}
    </select>

    <insert id="insert" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates">
        insert into pet_certificates
        (id, pet_id_card, pet_num, pet_name,
         pet_type, pet_sex, pet_varieties,pet_varieties_one,
         pet_hair, pet_age, raise_date,
         pet_address, pet_dept, is_agency,hukou_number,other_varieties,
         agency_com, source, is_reissue, status,tel, del_flag,
         create_date, create_by,owner_name,owner_address,immune_id,end_date,
         expres_type,expres_address,hospital_id,about_make,about_date,pet_img,
         about_status,about_reason,about_hospital,street,social_credit_code,use_description)
        values (#{id}, #{petIdCard}, #{petNum}, #{petName},
                #{petType}, #{petSex}, #{petVarieties}, #{petVarietiesOne},
                #{petHair}, #{petAge}, #{raiseDate},
                #{petAddress}, #{petDept}, #{isAgency},#{hukouNumber},#{otherVarieties},
                #{agencyCom}, #{source}, #{isReissue}, #{status}, #{tel}, #{delFlag},
                #{createDate}, #{createBy}, #{ownerName}, #{ownerAddress},
                #{immuneId}, #{endDate},#{expresType}, #{expresAddress},#{hospitalId},
                #{aboutMake},#{aboutDate},#{petImg},#{aboutStatus},#{aboutReason},#{aboutHospital},
                #{street},#{socialCreditCode},#{useDescription})
    </insert>

    <update id="update" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates">
        update pet_certificates
        set pet_id_card   = #{petIdCard},
            pet_num       = #{petNum},
            pet_name      = #{petName},
            pet_type      = #{petType},
            pet_sex       = #{petSex},
            pet_varieties = #{petVarieties},
            other_varieties = #{otherVarieties},
            pet_varieties_one = #{petVarietiesOne},
            pet_hair      = #{petHair},
            pet_age       = #{petAge},
            raise_date    = #{raiseDate},
            pet_address   = #{petAddress},
            pet_dept      = #{petDept},
            street        = #{street},
            is_agency     = #{isAgency},
            agency_com    = #{agencyCom},
            source        = #{source},
            status        = #{status},
            tel           = #{tel},
            update_date   = #{updateDate},
            update_by     = #{updateBy},
            owner_name    = #{ownerName},
            owner_address  = #{ownerAddress},
            end_date  = #{endDate},
            expres_type  = #{expresType},
            expres_address  = #{expresAddress},
            hospital_id  = #{hospitalId},
            about_make = #{aboutMake},
            about_date = #{aboutDate},
            about_status = #{aboutStatus},
            about_reason = #{aboutReason},
            pet_img = #{petImg},
            social_credit_code = #{socialCreditCode},
            use_description = #{useDescription}
        where id = #{id}
    </update>

    <delete id="delete" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates">
        update pet_certificates
        set del_flag    = #{delFlag},
            update_date = #{updateDate},
            update_by   = #{updateBy}
        where id = #{id}
    </delete>

    <update id="upStatus" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates">
        update pet_certificates
        set status      = #{status},
            update_date = #{updateDate},
            update_by   = #{updateBy}
        where id = #{id}
    </update>

    <update id="updateIsReissue" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates">
        update pet_certificates
        set is_reissue = #{isReissue},
            <if test="petNum != null and petNum != ''">
                pet_num = #{petNum},
            </if>
            <if test="applyStatus != null and applyStatus != ''">
                apply_status = #{applyStatus},
            </if>
            update_date = #{updateDate},
            update_by   = #{updateBy}
        where id = #{id}
    </update>

    <update id="updateImmune" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates">
        update pet_certificates
        <set>
            <if test="immuneId != null and immuneId != ''">
                immune_id = #{immuneId},
            </if>
            <if test="endDate != null">
                end_date = #{endDate},
            </if>
            <if test="transferId != null and transferId != ''">
                transfer_id = #{transferId},
            </if>
            <if test="cancelId != null and cancelId != ''">
                cancel_id = #{cancelId},
            </if>
            <if test="petIdCard != null and petIdCard != ''">
                pet_id_card = #{petIdCard},
            </if>
            <if test="ownerAddress != null and ownerAddress != ''">
                owner_address = #{ownerAddress},
            </if>
            <if test="ownerName != null and ownerName != ''">
                owner_name = #{ownerName},
            </if>
            <if test="tel != null and tel != ''">
                tel = #{tel},
            </if>
            <if test="yearStatus != null and yearStatus != ''">
                year_status = #{yearStatus},
            </if>
            <if test="cancelStatus != null and cancelStatus != ''">
                status = #{cancelStatus},
            </if>
            <if test="petNum != null">
                pet_num = #{petNum},
            </if>
            <if test="isAgency != null and isAgency != ''">
                is_agency = #{isAgency},
            </if>
            <if test="agencyCom != null and agencyCom != ''">
                agency_com = #{agencyCom},
            </if>
            <if test="expresType != null and expresType != ''">
                expres_type = #{expresType},
            </if>
            <if test="expresAddress != null and expresAddress != ''">
                expres_address = trim(#{expresAddress}),
            </if>
            <if test="expresRegion != null and expresRegion != ''">
                expres_region = trim(#{expresRegion}),
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                hospital_id = #{hospitalId},
            </if>
            <if test="aboutMake != null and aboutMake != ''">
                about_make = #{aboutMake},
            </if>
            <if test="aboutDate != null">
                about_date = #{aboutDate},
            </if>
            <if test="activation != null and activation != ''">
                activation = #{activation},
            </if>
            <if test="aboutStatus != null and aboutStatus != ''">
                about_status = #{aboutStatus},
            </if>
            <if test="aboutHospital != null and aboutHospital != ''">
                about_hospital = #{aboutHospital},
            </if>
            <if test="receiveStatus != null and receiveStatus != ''">
                receive_status = #{receiveStatus},
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag},
            </if>
            <if test="certId != null and certId != ''">
                cert_id = #{certId},
            </if>
            <if test="certDate != null">
                cert_date = #{certDate},
            </if>
            <if test="applyStatus != null and applyStatus != ''">
                apply_status = #{applyStatus},
            </if>
            <if test="applyReason != null and applyReason != ''">
                apply_reason = #{applyReason},
            </if>
            <if test="zfckId != null and zfckId != ''">
                zfck_id = trim(#{zfckId}),
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getPet" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates"
            resultType="com.ruoyi.modules.certificates.entity.PetCertificates">
        select
        <include refid="certificates"/>,
        ua.hospital,
        ua.doctor,
        ua.vaccine_brand,
        ua.vaccine_batch,
        ua.injection_date,
        ua.injection_enddate,
        ua.reason,
        ua.id as immuneId
        from pet_certificates a LEFT JOIN (SELECT t2.* FROM
        ( SELECT MAX( create_date ) AS create_date, pet_id_card FROM immune_register GROUP BY pet_id_card DESC ORDER BY
        create_date DESC ) t1
        JOIN immune_register t2 ON t1.pet_id_card = t2.pet_id_card
        AND t1.create_date = t2.create_date) AS ua ON ua.pet_id_card = a.pet_id_card
        where a.del_flag = 1
        and a.id = #{petId}
    </select>


    <resultMap id="petMap" type="com.ruoyi.modules.certificates.entity.PetCertificates">
        <id property="id" column="id"/>
        <result property="ownerName" column="owner_name"/>
        <result property="petIdCard" column="pet_id_card"/>
        <result property="tel" column="tel"/>
        <result property="ownerAddress" column="owner_address"/>
        <result property="petName" column="pet_name"/>
        <result property="petNum" column="pet_num"/>
        <result property="petType" column="pet_type"/>
        <result property="petSex" column="pet_sex"/>
        <result property="petVarieties" column="pet_varieties"/>
        <result property="petVarietiesOne" column="pet_varieties_one"/>
        <result property="petHair" column="pet_hair"/>
        <result property="petAge" column="pet_age"/>
        <result property="raiseDate" column="raise_date"/>
        <result property="petAddress" column="pet_address"/>
        <result property="petDept" column="pet_dept"/>
        <result property="street" column="street"/>
        <result property="petDeptName" column="petDeptName"/>
        <result property="isAgency" column="is_agency"/>
        <result property="agencyCom" column="agency_com"/>
        <result property="source" column="source"/>
        <result property="status" column="status"/>
        <result property="immuneId" column="immune_id"/>
        <result property="transferId" column="transfer_id"/>
        <result property="cancelId" column="cancel_id"/>
        <result property="endDate" column="end_date"/>
        <result property="aboutMake" column="about_make"/>
        <result property="aboutDate" column="about_date"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="petCount" column="petCount"/>
        <result property="petImg" column="pet_img"/>
        <result property="hospital" column="hospital"/>
        <result property="activation" column="activation"/>
        <result property="aboutStatus" column="about_status"/>
        <result property="aboutReason" column="about_reason"/>
        <result property="applyStatus" column="apply_status"/>
        <result property="applyReason" column="apply_reason"/>
        <result property="expresType" column="expres_type"/>
        <result property="createDate" column="create_date"/>
        <collection property="immuneRegister" ofType="com.ruoyi.modules.immune.entity.ImmuneRegister">
            <id property="id" column="immuneId"/>
            <result property="hospital" column="hospital"/>
            <result property="doctor" column="doctor"/>
            <result property="vaccineBrand" column="vaccine_brand"/>
            <result property="vaccineBatch" column="vaccine_batch"/>
            <result property="injectionDate" column="injection_date"/>
            <result property="injectionEnddate" column="injection_enddate"/>
            <result property="createBy" column="create_by"/>
            <result property="status" column="immuneStatus"/>
            <result property="reason" column="reason"/>
            <result property="petId" column="pet_id"/>
            <result property="immuneCard" column="immune_card"/>
        </collection>
    </resultMap>

    <select id="queryPageList" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates"  resultMap="petMap">
        select
            a.id, a.owner_name, a.pet_id_card, a.tel, a.owner_address, a.pet_name, 
            a.pet_num, a.pet_type, a.pet_sex, a.pet_varieties, a.pet_varieties_one,
            a.pet_hair, a.pet_age, a.raise_date, a.pet_address, a.pet_dept, a.street,
            a.is_agency, a.agency_com, a.source, a.is_reissue, a.year_status, 
            a.status, a.del_flag, a.create_date, a.create_by, a.update_date, 
            a.update_by, a.immune_id, a.transfer_id, a.cancel_id, a.end_date, 
            a.expres_type, a.expres_address, a.hospital_id, a.about_make, 
            a.about_date, a.pet_img, a.activation, a.about_status, a.about_reason,
            a.about_hospital, a.expres_region, a.receive_status, a.tricolor, 
            a.apply_status, a.apply_reason, a.other_varieties,
            sd.dept_name as petDeptName,
            (SELECT COUNT(1) FROM pet_certificates 
             WHERE del_flag = 1 
             AND pet_id_card = a.pet_id_card 
             AND ((apply_status = '2') OR (pet_num != '' AND pet_num IS NOT NULL))
             AND id != a.id) as petCount,
            ua.hospital,
            ua.doctor, 
            ua.vaccine_brand,
            ua.vaccine_batch,
            ua.injection_date,
            ua.injection_enddate,
            ua.reason,
            ua.immune_card,
            CASE 
                WHEN ua.injection_enddate is null THEN ua.status
                WHEN ua.injection_enddate &lt; CURDATE() THEN '5'
                WHEN DATEDIFF(ua.injection_enddate, CURDATE()) &lt;= 30 THEN '6'
                ELSE ua.status 
            END as immuneStatus,
            ua.id as immuneId,
            CASE 
                WHEN ua.injection_enddate is null THEN 5
                WHEN ua.injection_enddate &gt;= CURDATE() and DATEDIFF(ua.injection_enddate, CURDATE()) &lt;= 30 THEN 1
                WHEN ua.injection_enddate &lt; CURDATE() and DATEDIFF(CURDATE(), ua.injection_enddate) &lt;= 90 THEN 2
                WHEN DATEDIFF(ua.injection_enddate, CURDATE()) &gt; 30 THEN 3
                ELSE 4
            END as sort_priority
        from pet_certificates a
        LEFT JOIN sys_dept sd ON a.pet_dept = sd.dept_id
        LEFT JOIN immune_register ua ON ua.id = a.immune_id
        where a.del_flag = 1
        <if test="ownerName != null and ownerName != ''">
            and a.owner_name like concat('%',#{ownerName},'%')
        </if>
        <if test="petIds != null and petIds.size()>0">
            AND a.id in
            <foreach item="id" collection="petIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="tel != null and tel != ''">
            and a.tel like concat('%',#{tel},'%')
        </if>
        <if test="petAddress != null and petAddress != ''">
            and (a.pet_address like concat('%',#{petAddress},'%')
            or sd.dept_name like concat('%',#{petAddress},'%'))
        </if>
        <if test="petName != null and petName != ''">
            and a.pet_name like concat('%',#{petName},'%')
        </if>
        <if test="petNum != null and petNum != ''">
            and a.pet_num like concat('%',#{petNum},'%')
        </if>
        <if test="hospitalName!=null and hospitalName!=''">
            and ua.hospital like concat('%',#{hospitalName},'%')
        </if>
        <if test="registerBegin!=null and registerEnd!=null">
            and ua.injection_date between #{registerBegin} and #{registerEnd}
        </if>
        <if test="petIdCard != null and petIdCard != ''">
            and a.pet_id_card = #{petIdCard}
        </if>
        <choose>
          <when test="immuneStatus=='0'.toString()">
              and a.immune_id is null
          </when>
          <when test="immuneStatus=='1'.toString()">
              and a.immune_id is not null
              and ua.injection_enddate &gt;= CURDATE()
          </when>
          <when test="immuneStatus=='2'.toString()">
              and ua.injection_enddate &lt; CURDATE()
          </when>
          <when test="immuneStatus=='3'.toString()">
              and ua.injection_enddate &gt;= CURDATE()
              and DATEDIFF(ua.injection_enddate, CURDATE()) &lt;= 30
          </when>
        </choose>
        <choose>
          <when test="applyStatus=='1'.toString()">
              and a.apply_status = 1
              and (a.pet_num is null or a.pet_num ='')
          </when>
          <when test="applyStatus=='2'.toString()">
              and a.apply_status = 2
          </when>
          <when test="applyStatus=='3'.toString()">
              and a.apply_status = 4
          </when>
          <when test="applyStatus=='4'.toString()">
              and a.apply_status = 3
              and (a.pet_num is null or a.pet_num ='')
          </when>
          <when test="applyStatus=='5'.toString()">
              and a.apply_status = 3
              and a.activation = 2
          </when>
           <when test="applyStatus=='6'.toString()">
              and a.apply_status = 3
              and a.activation = 1
          </when>
            <when test="applyStatus=='7'.toString()">
                and a.apply_status != 2
            </when>
        </choose>
        <if test="handleStatus != null and handleStatus != ''">
            and a.immune_id is not null
            and ua.injection_enddate &gt;= CURDATE()
        </if>
        <if test="activaType != null and activaType != ''">
            and a.apply_status = #{activaType}
        </if>
        <if test="aboutMake != null and aboutMake != ''">
            and IFNULL(a.about_make,0) = #{aboutMake}
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            and a.hospital_id = #{hospitalId}
        </if>
        <if test="petDept != null and petDept != ''">
            and a.pet_dept = #{petDept}
        </if>
        <if test="startTime != null and endTime != null">
            and ua.injection_date between #{startTime} and #{endTime}
        </if>
        <if test="activation != null and activation != ''">
            and a.activation = #{activation}
        </if>
        <if test="aboutStatus != null and aboutStatus != ''">
            and a.about_status in ('1','2','3')
        </if>
        <if test="aboutHospital != null and aboutHospital != ''">
            and a.about_hospital = #{aboutHospital}
        </if>
         <if test="zfckId != null and zfckId != ''">
            and a.zfck_id = #{zfckId}
        </if>
        order by 
            CASE WHEN a.apply_status in ('4','2') THEN 0 ELSE 1 END,
            sort_priority,
            ua.injection_enddate desc
    </select>

    <select id="getPetAndRegister" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates" resultType="com.ruoyi.modules.certificates.entity.PetCertificates">
        SELECT <include refid="certificates" />,
        ir.id as "immuneRegister.id",
        ir.hospital as "immuneRegister.hospital",
        ir.doctor as "immuneRegister.doctor",
        ir.vaccine_brand as "immuneRegister.vaccineBrand",
        ir.vaccine_batch as "immuneRegister.vaccineBatch",
        ir.injection_date as "immuneRegister.injectionDate",
        ir.injection_enddate as "immuneRegister.injectionEnddate",
        (CASE
            <![CDATA[
            WHEN ir.injection_enddate is null THEN ir.status
            WHEN TO_DAYS(ir.injection_enddate) - TO_DAYS(CURDATE()) < 0 THEN '5'
            WHEN TO_DAYS(ir.injection_enddate) - TO_DAYS(CURDATE()) < 30 THEN '6'
            ELSE ir.status
            ]]>
        END) as "immuneRegister.status",
        ir.reason as "immuneRegister.reason",
        ir.hospital_id as "immuneRegister.hospitalId",
        ir.about_make as "immuneRegister.aboutMake",
        ir.about_date as "immuneRegister.aboutDate",
        ir.immune_card as "immuneRegister.immuneCard"
        FROM
        pet_certificates a
        LEFT JOIN
        immune_register ir ON a.immune_id = ir.id
        WHERE
        a.del_flag = 1
        <if test="petIdCard != null and petIdCard != ''">
            and a.pet_id_card = #{petIdCard}
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            and a.hospital_id = #{hospitalId}
        </if>
        <if test="petSex != null">
            and a.pet_sex = #{petSex}
        </if>
        <if test="type != null and type != ''">
            and ir.type = #{type}
        </if>
        <if test="ownerName != null and ownerName != ''">
            and a.owner_name like concat('%',#{ownerName},'%')
        </if>
        <if test="tel != null and tel != ''">
            and a.tel like concat('%',#{tel},'%')
        </if>
        <if test="petName != null and petName != ''">
            and a.pet_name like concat('%',#{petName},'%')
        </if>
        <if test="petNum != null and petNum != ''">
            and a.pet_num like concat('%',#{petNum},'%')
        </if>
        <if test="petType != null and petType != ''">
            and a.pet_type  = #{petType}
        </if>
        <if test="petAge != null and petAge != ''">
            and a.pet_age = #{petAge}
        </if>
        <if test="isReissue != null and isReissue != ''">
            and a.is_reissue = #{isReissue}
        </if>
        <if test="isReissueFlag != null and isReissueFlag != ''">
            and a.is_reissue is not null
        </if>
        <if test="petDept != null and petDept != ''">
            and a.pet_dept  = #{petDept}
        </if>
        <if test='yearStatus != null and yearStatus !="" and yearStatus !="3"'>
            and a.year_status = #{yearStatus}
        </if>
        <if test='yearStatus != null and yearStatus !="" and yearStatus =="3"'>
            and a.year_status in (1,2)
        </if>
        <if test="keywordOne != null and keywordOne !=''">
            and year(ir.update_date) = #{keywordOne}
        </if>
        order by a.create_date desc
    </select>

    <update id="updatePetNum" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates">
        update pet_certificates set
                pet_num = #{petNum},
                is_reissue = #{isReissue},
                <if test="activation != null and activation != ''">
                   activation = #{activation},
                </if>
                <if test="expresType != null and expresType != ''">
                    expres_type = #{expresType},
                </if>
                <if test="expresAddress != null and expresAddress != ''">
                    expres_address = #{expresAddress},
                </if>
                <if test="expresRegion != null and expresRegion != ''">
                    expres_region = #{expresRegion},
                </if>
                <if test="zfckId != null and zfckId != ''">
                    zfck_id = #{zfckId},
                </if>
                update_date = #{updateDate},
                update_by = #{updateBy}
        where id = #{id}
    </update>

    <update id="updateAbout" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates">
        update pet_certificates a
        <set>
            <if test="aboutStatus != null and aboutStatus != ''">
                a.about_status = #{aboutStatus},
            </if>
            <if test="aboutReason != null and aboutReason != ''">
                a.about_reason = #{aboutReason},
            </if>
        </set>
        where a.id = #{id}
    </update>
    <select id="getQZDJ" resultType="java.util.HashMap">
        select (select count(id)  from pet_certificates c  where c.del_flag=1) total
             ,(select count(id) from pet_certificates where del_flag=1 and (immune_id is  null or immune_id ='') ) wmy
             ,(select count(id) from pet_certificates where del_flag=1 and immune_id is not null and immune_id!='' and (pet_num is null or pet_num='')) wbz
             ,(select count(id) from pet_certificates where del_flag=1 and immune_id is not null and immune_id!='' and pet_num is not null and pet_num!='') ybz
                                                          ,(select count(id) from pet_certificates where del_flag=1 and cancel_id is not null and cancel_id !='' ) yzx from DUAL
    </select>
    <select id="getZFSR" resultType="java.util.HashMap">
        select (select count(id) from take_in where del_flag=1) sr
             ,(select count(id) from punish where del_flag=1) cf
             ,(select count(id) from take_in where del_flag=1 and take_in_type=1) cfsr
             ,(select count(id) from take_in where del_flag=1 and take_in_type=2) wzsr
        from DUAL
    </select>

    <select id="getShowMapData" resultType="com.ruoyi.modules.certificates.entity.ShowMapData">
        select
            sd.id as "id",
            sd.dept_name as "name",
            (SELECT count(p.id) from pet_certificates p where p.del_flag = 1 and p.pet_dept = sd.id) as "dogCount",
            (SELECT count(p.id) from pet_certificates p where p.del_flag = 1 and immune_id is not NULL and p.pet_dept = sd.id) as "myCount",
            (SELECT count(p.id) from pet_certificates p where p.del_flag = 1 and pet_num is not null and pet_num != '' and p.pet_dept = sd.id) as "qpCount",
            (SELECT count(DISTINCT u.pet_id_card) from pet_certificates u where u.del_flag = 1 and u.pet_dept = sd.id) AS "userCount",
            (SELECT count(q.id) from qualifi q where q.del_flag = 1 and q.type = 0 and q.`status` = 2 and q.county = sd.id) AS "yyCount"
        from sys_dept sd
        where sd.`level` = 2
          and sd.del_flag = 1
    </select>

    <select id="queryPetCount" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates"  resultType="com.ruoyi.modules.certificates.entity.PetCertificates">
        select  a.*,(SELECT count( 1 ) FROM pet_certificates d WHERE  d.del_flag = 1
            AND (d.apply_status = '2' OR (d.pet_num  != '' AND d.pet_num IS NOT NULL ))
            AND (d.id != a.id or d.apply_status = '3' or apply_status = '2')
            AND d.pet_id_card = a.pet_id_card) as petCount, <!--同一个身份证是否可以申请犬牌-->
        (select c.dept_name from sys_dept c where c.dept_id=a.pet_dept) as petDeptName
        from pet_certificates a
        where a.del_flag = 1
          and a.id = #{id}
    </select>


    <update id="getpetAge" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates">
        update pet_certificates a set pet_age = pet_age + 1
    </update>

    <select id="findViewData" resultType="java.util.Map">
        SELECT
        p.id,
        p.owner_name,
        p.pet_id_card,
        p.pet_num,
        CASE
            WHEN p.end_date > NOW() THEN '有效'
            WHEN p.del_flag = 0 THEN '无效'
            ELSE '无效'
        END AS status,
        p.cert_date,
        p.end_date,
        CASE
            WHEN dept.dept_name LIKE '%婺城区%' THEN '330702'
            WHEN dept.dept_name LIKE '%金东区%' THEN '330703'
            WHEN dept.dept_name LIKE '%兰溪市%' THEN '330781'
            WHEN dept.dept_name LIKE '%义乌市%' THEN '330782'
            WHEN dept.dept_name LIKE '%东阳市%' THEN '330783'
            WHEN dept.dept_name LIKE '%永康市%' THEN '330784'
            WHEN dept.dept_name LIKE '%武义县%' THEN '330723'
            WHEN dept.dept_name LIKE '%浦江县%' THEN '330726'
            WHEN dept.dept_name LIKE '%磐安县%' THEN '330727'
            WHEN dept.dept_name LIKE '%开发区%' THEN '330770'
            ELSE '330700' -- 金华市默认行政编码
        END AS dept_name
        FROM
        pet_certificates p LEFT JOIN sys_dept dept ON p.pet_dept = dept.id
        WHERE 1 = 1
        <if test="formattedDate != null  and formattedDate != ''"> and p.create_date > #{formattedDate}</if>
    </select>

    <select id="getByBrandNum" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates"
            resultType="java.util.HashMap">
        select
        a.id as "id",
        a.owner_name as "ownerName",
        a.pet_id_card as "petIdCard",
        a.tel as "tel",
        b.brand_num as "petNum",
        a.pet_name as "petName",
        a.pet_type as "petType",
        a.pet_sex as "petSex",
        (select dict_key  from sys_dict where dict_type='varieties_type' and dict_key=a.pet_varieties) as "petVarieties",
        a.pet_hair as "petHair",
        a.pet_age as "petAge",
        a.raise_date as "raiseDate",
        a.pet_address as "petAddress",
        a.pet_dept as "petDept",
        a.is_agency as "isAgency",
        a.agency_com as "agencyCom",
        a.source as "source",
        a.is_reissue as "isReissue",
        a.year_status as "yearStatus",
        a.status as "status",
        a.create_date as "createDate",
        a.immune_id as immuneId,
        a.transfer_id as transferId,
        a.cancel_id as cancelId,
        a.end_date as endDate,
        a.tel as tel,
        a.owner_name as "ownerName",
        a.owner_address as "ownerAddress",
        a.expres_type as "expresType",
        a.expres_address as "expresAddress",
        a.hospital_id as "hospitalId",
        a.about_make as "aboutMake",
        a.about_date as "aboutDate",
        a.pet_img as "petImg"
        ,b.is_use as  isUse
        ,a.activation as activation
        from pet_brand b
        left join pet_certificates a on a.pet_num =b.brand_num
        where b.brand_num=#{brandId}
    </select>

    <select id="listByPetNumList" resultMap="petMap">
        select *
        from pet_certificates a
        where a.pet_num in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by a.pet_num
    </select>
    <select id="selectHuKouNumberIsOnly" resultType="java.lang.Integer">
        select count(hukou_number)
        from pet_certificates a
        where hukou_number = #{hukouNumber}
    </select>

    <select id="listByDate" resultMap="petMap">
        select * from pet_certificates
        where create_date >= #{startTime}
        and create_date &lt;= #{endTime}
        and create_date is not null
    </select>

    <select id="countByDate" resultType="java.lang.Long">
        select count(1) from pet_certificates
        where 1=1
        <if test="startTime != null">
            and create_date >= #{startTime}
        </if>
        <if test="endTime != null">
            and create_date &lt;= #{endTime}
        </if>

    </select>

    <select id="groupByPetDept" resultType="com.ruoyi.common.vo.BusinessCountVO">
        select pet_dept as "key",count(1) as "value" from pet_certificates
        where 1=1
        group by pet_dept
    </select>
    <select id="findPetCountByIdCard" resultType="java.lang.Long">
        SELECT
            count( 1 )
        FROM
            pet_certificates
        WHERE
            del_flag = 1
          AND STATUS IN ( 1, 2 )
          AND pet_id_card = #{petIdCard}
          <if test="excludeId != null and excludeId != ''">
              AND id != #{excludeId}
          </if>
    </select>
    <select id="findImmuneStatusByPetId" resultType="java.lang.String">
        SELECT
            CASE
            WHEN injection_enddate IS NULL THEN STATUS
            WHEN TO_DAYS(injection_enddate) - TO_DAYS(CURDATE()) &lt; 0 THEN '5'
            WHEN TO_DAYS(injection_enddate) - TO_DAYS(CURDATE()) &lt; 30 THEN '6'
            ELSE STATUS
            END AS immuneStatus
        FROM
            immune_register
        WHERE
            pet_id = #{petId}
    </select>

    <select id="jy" resultType="java.lang.Integer">
        SELECT count(*)   from dog_jy where dog_name=#{otherVarieties}
    </select>
</mapper>
