<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.takeIn.dao.TakeInDao">

    <sql id="columns">
        <trim suffixOverrides=",">
            a.id as id,
            a.type as type,
            a.take_in_type as takeInType,
            a.status as status,
            a.pet_num as petNum,
            a.name as name,
            a.id_card as idCard,
            a.contact_number as contactNumber,
            a.reason as reason,
            a.address as address,
            a.time as time,
            a.remarks as remarks,
            a.processing_record as processingRecord,
            a.is_release as isRelease,
            a.del_flag as delFlag,
            a.create_date as createDate,
            a.create_by as createBy,
            a.update_date as updateDate,
            a.update_by as updateBy,
            a.pet_hair as petHair,
            a.pet_img as petImg,
            a.pet_length as petLength,
            a.pet_varieties as petVarieties,
            a.pet_name as petName,
            a.pet_age as petAge,
            a.biz_code as bizCode,
            a.approval_status as approvalStatus,
            a.pet_varieties_one as "petVarietiesOne",
            a.take_in_user_id as takeInUserId,
            a.take_in_user_name as takeInUserName,
            a.take_in_dept_id as takeInDeptId,
            a.take_in_dept_name as takeInDeptName,
            a.sign_user_name as signUserName,
            a.sign_user_id as signUserId
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.takeIn.entity.TakeIn"
            resultType="com.ruoyi.modules.takeIn.entity.TakeIn">
        select
        <include refid="columns"/>

        from take_in a
        where a.id =#{id}
    </select>


    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.takeIn.entity.TakeIn"
            resultType="com.ruoyi.modules.takeIn.entity.TakeIn">
        select
        <include refid="columns"/>
        ,u.dept_name as deptName
        from take_in a
        left join sys_user u on u.user_id=a.create_by
        where a.del_flag =1
        <if test="deptName!=null and deptName!=''">
            and u.dept_name like concat('%',#{deptName},'%')
        </if>
        <if test="deptId!=null and deptId!=''">
            and u.dept_id =#{deptId}
        </if>
        <if test="keyword!=null and keyword!=''">
            and (a.name like concat('%',#{keyword},'%') or
            a.id_card like concat('%',#{keyword},'%') or
            a.contact_number like concat('%',#{keyword},'%') or
            a.reason like concat('%',#{keyword},'%') or
            a.address like concat('%',#{keyword},'%')or
            a.pet_num like concat('%',#{keyword},'%')or
            a.remarks like concat('%',#{keyword},'%') )
        </if>
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="takeInType != null and takeInType != ''">
            and a.take_in_type = #{takeInType}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        <if test="petNum != null and petNum != ''">
            and a.pet_num = #{petNum}
        </if>
        <if test="name != null and name != ''">
            and a.name = #{name}
        </if>
        <if test="idCard != null and idCard != ''">
            and a.id_card = #{idCard}
        </if>
        <if test="contactNumber != null and contactNumber != ''">
            and a.contact_number = #{contactNumber}
        </if>
        <if test="reason != null and reason != ''">
            and a.reason = #{reason}
        </if>
        <if test="address != null and address != ''">
            and a.address = #{address}
        </if>
        <if test="time != null">
            and a.time = #{time}
        </if>
        <if test="remarks != null and remarks != ''">
            and a.remarks = #{remarks}
        </if>
        <if test="processingRecord != null and processingRecord != ''">
            and a.processing_record = #{processingRecord}
        </if>
        <if test="isRelease != null and isRelease != ''">
            and a.is_release = #{isRelease}
        </if>

        <if test="delFlag != null">
            and a.del_flag = #{delFlag}
        </if>
        <if test="createDate != null">
            and a.create_date = #{createDate}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by = #{createBy}
        </if>
        <if test="updateDate != null">
            and a.update_date = #{updateDate}
        </if>
        <if test="updateBy != null and updateBy != ''">
            and a.update_by = #{updateBy}
        </if>
        order by a.create_date desc
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.takeIn.entity.TakeIn">
        insert into take_in(id, type, take_in_type, status, pet_num, name, id_card, contact_number, reason, address,
                            time, remarks, processing_record,is_release, del_flag, create_date, create_by, update_date,
                            update_by, pet_hair, pet_varieties, pet_img, pet_length, pet_name, pet_age, biz_code,
                            pet_varieties_one, take_in_user_id, take_in_user_name, take_in_dept_id, take_in_dept_name)
        values (#{id}, #{type}, #{takeInType}, #{status}, #{petNum}, #{name}, #{idCard}, #{contactNumber}, #{reason},
                #{address}, #{time}, #{remarks}, #{processingRecord}, #{isRelease},#{delFlag}, #{createDate}, #{createBy},
                #{updateDate}, #{updateBy}, #{petHair}, #{petVarieties}, #{petImg}, #{petLength}, #{petName}, #{petAge},
                #{bizCode}, #{petVarietiesOne}, #{takeInUserId}, #{takeInUserName}, #{takeInDeptId}, #{takeInDeptName})
    </insert>
    <update id="updateByEntity" parameterType="com.ruoyi.modules.takeIn.entity.TakeIn">
        update take_in set
        <trim suffixOverrides=",">
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="takeInType != null and takeInType != ''">
                take_in_type = #{takeInType},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="petNum != null and petNum != ''">
                pet_num = #{petNum},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="idCard != null and idCard != ''">
                id_card = #{idCard},
            </if>
            <if test="contactNumber != null and contactNumber != ''">
                contact_number = #{contactNumber},
            </if>
            <if test="reason != null and reason != ''">
                reason = #{reason},
            </if>
            <if test="address != null and address != ''">
                address = #{address},
            </if>
            <if test="time != null">
                time = #{time},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
            <if test="processingRecord != null and processingRecord != ''">
                processing_record = #{processingRecord},
            </if>
            <if test="isRelease != null and isRelease != ''">
                is_release = #{isRelease},
            </if>

            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="approvalStatus != null and approvalStatus != ''">
                approval_status = #{approvalStatus},
            </if>
        </trim>
        where id = #{id}
    </update>
    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.takeIn.entity.TakeIn">
        update take_in set
        <trim suffixOverrides=",">
            type = #{type},
            take_in_type = #{takeInType},
            status = #{status},
            pet_num = #{petNum},
            name = #{name},
            id_card = #{idCard},
            contact_number = #{contactNumber},
            reason = #{reason},
            address = #{address},
            time = #{time},
            remarks = #{remarks},
            processing_record = #{processingRecord},
            is_release = #{isRelease},
            del_flag = #{delFlag},
            create_date = #{createDate},
            create_by = #{createBy},
            update_date = #{updateDate},
            update_by = #{updateBy},
            pet_hair = #{petHair},
            pet_varieties = #{petVarieties},
            pet_img = #{petImg},
            pet_length = #{petLength},
            pet_name = #{petName},
            pet_age = #{petAge}
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.takeIn.entity.TakeIn">
        UPDATE take_in
        SET del_flag=#{delFlag},
            update_date = #{updateDate},
            update_by = #{updateBy}
        where id = #{id}
    </delete>

    <select id="getReclaimList" resultType="com.ruoyi.modules.takeIn.entity.TakeIn">
        select
        <include refid="columns"/>
        ,u.dept_name as deptName
        from take_in a
        left join sys_user u on u.user_id=a.create_by
        left join reclaim_record rr on a.id = rr.take_in_id
        where a.del_flag =1
<!--        and rr.id is not null-->
        <if test="deptName!=null and deptName!=''">
            and u.dept_name like concat('%',#{deptName},'%')
        </if>
        <if test="deptId!=null and deptId!=''">
            and u.dept_id =#{deptId}
        </if>
        <if test="keyword!=null and keyword!=''">
            and (a.name like concat('%',#{keyword},'%') or
            a.id_card like concat('%',#{keyword},'%') or
            a.contact_number like concat('%',#{keyword},'%') or
            a.reason like concat('%',#{keyword},'%') or
            a.address like concat('%',#{keyword},'%')or
            a.pet_num like concat('%',#{keyword},'%')or
            a.remarks like concat('%',#{keyword},'%') )
        </if>
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="takeInType != null and takeInType != ''">
            and a.take_in_type = #{takeInType}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        <if test="petNum != null and petNum != ''">
            and a.pet_num = #{petNum}
        </if>
        <if test="name != null and name != ''">
            and a.name = #{name}
        </if>
        <if test="idCard != null and idCard != ''">
            and a.id_card = #{idCard}
        </if>
        <if test="contactNumber != null and contactNumber != ''">
            and a.contact_number = #{contactNumber}
        </if>
        <if test="reason != null and reason != ''">
            and a.reason = #{reason}
        </if>
        <if test="address != null and address != ''">
            and a.address = #{address}
        </if>
        <if test="time != null">
            and a.time = #{time}
        </if>
        <if test="remarks != null and remarks != ''">
            and a.remarks = #{remarks}
        </if>
        <if test="processingRecord != null and processingRecord != ''">
            and a.processing_record = #{processingRecord}
        </if>
        <if test="isRelease != null and isRelease != ''">
            and a.is_release = #{isRelease}
        </if>

        <if test="delFlag != null">
            and a.del_flag = #{delFlag}
        </if>
        <if test="createDate != null">
            and a.create_date = #{createDate}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by = #{createBy}
        </if>
        <if test="updateDate != null">
            and a.update_date = #{updateDate}
        </if>
        <if test="updateBy != null and updateBy != ''">
            and a.update_by = #{updateBy}
        </if>
        <if test="statusList != null and statusList.size() != 0">
            and a.status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
<!--        <if test="reclaimRecordStatus != null and reclaimRecordStatus != ''">-->
<!--            and rr.status = #{reclaimRecordStatus}-->
<!--        </if>-->
        group by a.id
        order by a.create_date desc
    </select>

    <select id="getNeedHarmList" resultType="com.ruoyi.modules.takeIn.entity.TakeIn">
        select
        <include refid="columns"/>
        ,u.dept_name as deptName
        from take_in a
        left join sys_user u on u.user_id=a.create_by
        left join harm_trea h on a.id = h.take_in_id
        where a.del_flag =1
        and a.status = 1
        and h.id is null
        <if test="keywords!=null and keywords!=''">
            and (a.name like concat('%',#{keywords},'%') or
            a.id_card like concat('%',#{keywords},'%') or
            a.contact_number like concat('%',#{keywords},'%') or
            a.reason like concat('%',#{keywords},'%') or
            a.address like concat('%',#{keywords},'%')or
            a.pet_num like concat('%',#{keywords},'%')or
            a.remarks like concat('%',#{keywords},'%') )
        </if>
        group by a.id
        order by a.create_date desc
    </select>

    <select id="getByBizCode" resultType="com.ruoyi.modules.takeIn.entity.TakeIn">
        select
        <include refid="columns"/>
        from take_in a
        where biz_code = #{bizCode}
    </select>

    <select id="getPageList" resultType="com.ruoyi.modules.takeIn.entity.TakeIn">
        select
        a.id as id,
        a.type as type,
        a.take_in_type as takeInType,
        a.status as status,
        a.pet_num as petNum,
        a.name as name,
        a.id_card as idCard,
        a.contact_number as contactNumber,
        a.reason as reason,
        a.address as address,
        a.time as time,
        a.remarks as remarks,
        a.processing_record as processingRecord,
        a.is_release as isRelease,
        a.del_flag as delFlag,
        a.create_date as createDate,
        a.create_by as createBy,
        a.update_date as updateDate,
        a.update_by as updateBy,
        a.pet_hair as petHair,
        a.pet_img as petImg,
        a.pet_length as petLength,
        a.pet_varieties as petVarieties,
        a.pet_name as petName,
        a.pet_age as petAge,
        a.biz_code as bizCode,
        a.approval_status as approvalStatus,
        a.take_in_user_id as takeInUserId,
        a.take_in_user_name as takeInUserName,
        a.take_in_dept_id as takeInDeptId,
        a.take_in_dept_name as takeInDeptName
        from take_in a
        where a.del_flag = 1
<!--        and a.status = 0-->
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="isRelease != null and isRelease != ''">
            and a.is_release = #{isRelease}
        </if>
        <if test="deptName != null and deptName != ''">
            and a.address like concat('%',#{deptName},'%')
        </if>
        <if test="keyword != null and keyword != ''">
            and a.pet_name like concat('%',#{keyword},'%')
        </if>
        <if test="takeInType != null and takeInType != ''">
            and a.take_in_type = #{takeInType}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        <if test="contactNumber != null and contactNumber != ''">
            and a.contact_number = #{contactNumber}
        </if>
        <if test="idCard != null and idCard != ''">
            and a.id_card = #{idCard}
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and a.status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

        order by create_date desc
    </select>

    <select id="listNonComplete" resultType="com.ruoyi.modules.takeIn.entity.TakeIn">
        select  <include refid="columns"/>
        from take_in a
        where status != 3
        and del_flag = 1
        <if test="createBy != null and createBy != ''">
            and create_by = #{createBy}
        </if>
    </select>

    <select id="countByPetNum" resultType="java.lang.Long">
        select count(1) from take_in a
        where a.pet_num = #{petNum}
    </select>

    <update id="sign">
        update take_in set sign_flag = 1, sign_date = now(), `status` = 1,
        sign_user_id = #{userId}, sign_user_name = #{nickName} where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="listSendMessage" resultType="com.ruoyi.modules.takeIn.entity.TakeIn">
        select  <include refid="columns"/>
        from take_in a
        where status = 1
        and del_flag = 1
        and sign_flag = 1
        and sign_date between DATE_SUB(NOW(), INTERVAL 3 DAY) and DATE_SUB(NOW(), INTERVAL 1 DAY)
    </select>

    <select id="getNeedHarmListV2" resultType="com.ruoyi.modules.harmTrea.vo.HarmTreaVO">
        select
        a.id as id,
        a.sign_date as signDate,
        a.create_date as takeInDate,
        a.type as type,
        a.pet_num as petNum,
        h.id as harmTreaId,
        if(h.id is null, 0, 1) as `status`
        from take_in a
        left join harm_trea h on a.id = h.take_in_id
        where a.del_flag =1
        and a.status = 1
        and
            (take_in_type = 1 and a.create_date <![CDATA[ <= ]]> DATE_SUB(NOW(), INTERVAL 1 MONTH) or
             take_in_type = 2 and a.create_date <![CDATA[ <= ]]> DATE_SUB(NOW(), INTERVAL 15 day))
        <if test="petNum != null and petNum != ''">
            and a.pet_num = #{petNum}
        </if>
        <if test="startDate != null">
            and a.sign_date >= #{startDate}
        </if>
        <if test="endDate != null">
            and a.sign_date <![CDATA[ <= ]]> #{endDate}
        </if>
        group by a.id
        order by status, takeInDate desc
    </select>

    <select id="getHarmVOById" resultType="com.ruoyi.modules.harmTrea.vo.HarmTreaVO">
        select
            a.id as id,
            a.sign_date as signDate,
            a.create_date as takeInDate,
            a.type as type,
            a.pet_num as petNum
        from take_in a
        where a.id = #{id}
        limit 1
    </select>

    <select id="getNeedSignByIdList" resultType="com.ruoyi.modules.takeIn.entity.TakeIn">
        select <include refid="columns"/>
        from take_in a
        where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
        and status = 0
    </select>

    <select id="listUnSign" resultType="com.ruoyi.modules.takeIn.entity.TakeIn">
        select  <include refid="columns"/>
        from take_in a
        where status = 0
        and del_flag = 1
        <if test="createBy != null and createBy != ''">
            and create_by = #{createBy}
        </if>
    </select>
</mapper>
