package com.ruoyi.modules.brand.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.brand.entity.PetBrandApply;

import java.util.HashMap;
import java.util.List;

public interface PetBrandApplyDao extends BaseDao<PetBrandApply> {
    public Integer getNotDistributionNum(String area);

    public List<HashMap> getManufacturerPageList(PetBrandApply entity);

    public List<HashMap> getMergeList(PetBrandApply entity);

    /**
     * @author: tongsiyu
     * @date: 2022/10/31 14:13
     * @Description:犬牌使用统计跟据区县
     */
    public List<HashMap> countyStatistics(PetBrandApply entity);

    /**
     * @author: tongsiyu
     * @date: 2022/10/31 15:59
     * @Description:犬牌使用统计跟据单位
     */
    public List<HashMap> unitStatistics(PetBrandApply entity);

    String getUnUsedBrandByQualifiId(String qualifiId);

    String getUnUsedBrandByCounty(String county);
}
