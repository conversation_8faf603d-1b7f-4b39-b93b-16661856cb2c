package com.ruoyi.modules.notice.entity;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.user.entity.SysUploadFile;

import java.util.Date;
import java.util.List;

/**
 * 违法处罚表(<PERSON><PERSON>sh)实体类
 *
 * <AUTHOR>
 * @since 2022-08-12 14:40:02
 */
public class Notice extends BaseEntity {

    private String noticeType; //公告类型：1法律法规 2曝光台 3收容公告
    private String title;//公告标题
    private String content;//公告内容
    private String isFirstPage;//是否显示 1-是 2-否
    private String status;//状态：1待发送通知，2已发送通知
    private String summary;//公告摘要
    private String promulgator;//发布人名称
    private String promulgatorId;//发布人ID
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;//创建日期
    private String createBy;//创建人

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date startDate;//有效期开始
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date endDate;//有效期结束

    private List<SysUploadFile> uploadFileList;  //附件
    private String uploadFileStr;                //附件字符串
    public String getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getIsFirstPage() {
        return isFirstPage;
    }

    public void setIsFirstPage(String isFirstPage) {
        this.isFirstPage = isFirstPage;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getPromulgator() {
        return promulgator;
    }

    public void setPromulgator(String promulgator) {
        this.promulgator = promulgator;
    }

    @Override
    public Date getCreateDate() {
        return createDate;
    }

    @Override
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Override
    public String getCreateBy() {
        return createBy;
    }

    @Override
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public List<SysUploadFile> getUploadFileList() {
        return uploadFileList;
    }

    public void setUploadFileList(List<SysUploadFile> uploadFileList) {
        this.uploadFileList = uploadFileList;
    }

    public String getUploadFileStr() {
        return uploadFileStr;
    }

    public void setUploadFileStr(String uploadFileStr) {
        this.uploadFileStr = uploadFileStr;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getPromulgatorId() {
        return promulgatorId;
    }

    public void setPromulgatorId(String promulgatorId) {
        this.promulgatorId = promulgatorId;
    }
}
