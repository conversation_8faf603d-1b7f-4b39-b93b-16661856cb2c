package com.ruoyi.modules.msg.mapper;

import java.util.List;

import com.ruoyi.modules.msg.domain.SendMsgInfo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 短信发送记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
@Mapper
public interface SendMsgInfoMapper 
{
    /**
     * 查询短信发送记录
     * 
     * @param id 短信发送记录主键
     * @return 短信发送记录
     */
    public SendMsgInfo selectSendMsgInfoById(Long id);

    /**
     * 查询短信发送记录列表
     * 
     * @param sendMsgInfo 短信发送记录
     * @return 短信发送记录集合
     */
    public List<SendMsgInfo> selectSendMsgInfoList(SendMsgInfo sendMsgInfo);

    /**
     * 新增短信发送记录
     * 
     * @param sendMsgInfo 短信发送记录
     * @return 结果
     */
    public int insertSendMsgInfo(SendMsgInfo sendMsgInfo);

    /**
     * 修改短信发送记录
     * 
     * @param sendMsgInfo 短信发送记录
     * @return 结果
     */
    public int updateSendMsgInfo(SendMsgInfo sendMsgInfo);

    /**
     * 删除短信发送记录
     * 
     * @param id 短信发送记录主键
     * @return 结果
     */
    public int deleteSendMsgInfoById(Long id);

    /**
     * 批量删除短信发送记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSendMsgInfoByIds(Long[] ids);
}
