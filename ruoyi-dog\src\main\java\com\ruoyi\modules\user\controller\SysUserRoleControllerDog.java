package com.ruoyi.modules.user.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.user.entity.SysUserRole;
import com.ruoyi.modules.user.service.SysUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by Administrator on 2021/3/23/023.
 */
@RestController
@RequestMapping("/sysUserRole")
public class SysUserRoleControllerDog {

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @RequestMapping("/insert")
    public AjaxResult insert(SysUserRole sysUserRole){
        sysUserRoleService.insert(sysUserRole);
        return AjaxResult.success();
    }

    @RequestMapping("/insertOrUpdate")
    public AjaxResult insertOrUpdate(SysUserRole sysUserRole){
        sysUserRoleService.insertOrUpdate(sysUserRole);
        return AjaxResult.success();
    }

    @RequestMapping("/getAllList")
    public AjaxResult getAllList(SysUserRole sysUserRole){
        return AjaxResult.success(sysUserRoleService.getList(sysUserRole));
    }
}
