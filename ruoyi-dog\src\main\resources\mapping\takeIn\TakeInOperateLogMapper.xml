<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.takeIn.dao.TakeInOperateLogMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.modules.takeIn.entity.TakeInOperateLog">
    <!--@mbg.generated-->
    <!--@Table take_in_operate_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="VARCHAR" property="businessId" />
    <result column="operate_type" jdbcType="VARCHAR" property="operateType" />
    <result column="operate_content" jdbcType="LONGVARCHAR" property="operateContent" />
    <result column="operate_user_id" jdbcType="BIGINT" property="operateUserId" />
    <result column="operate_user_name" jdbcType="VARCHAR" property="operateUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, business_id, operate_type, operate_content, operate_user_id, operate_user_name,
    create_time, create_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from take_in_operate_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from take_in_operate_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.modules.takeIn.entity.TakeInOperateLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into take_in_operate_log (business_id, operate_type, operate_content,
      operate_user_id, operate_user_name, create_time,
      create_by)
    values (#{businessId,jdbcType=VARCHAR}, #{operateType,jdbcType=VARCHAR}, #{operateContent,jdbcType=LONGVARCHAR},
      #{operateUserId,jdbcType=BIGINT}, #{operateUserName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.modules.takeIn.entity.TakeInOperateLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into take_in_operate_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="operateContent != null">
        operate_content,
      </if>
      <if test="operateUserId != null">
        operate_user_id,
      </if>
      <if test="operateUserName != null">
        operate_user_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="operateContent != null">
        #{operateContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="operateUserId != null">
        #{operateUserId,jdbcType=BIGINT},
      </if>
      <if test="operateUserName != null">
        #{operateUserName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.modules.takeIn.entity.TakeInOperateLog">
    <!--@mbg.generated-->
    update take_in_operate_log
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="operateContent != null">
        operate_content = #{operateContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="operateUserId != null">
        operate_user_id = #{operateUserId,jdbcType=BIGINT},
      </if>
      <if test="operateUserName != null">
        operate_user_name = #{operateUserName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.modules.takeIn.entity.TakeInOperateLog">
    <!--@mbg.generated-->
    update take_in_operate_log
    set business_id = #{businessId,jdbcType=VARCHAR},
      operate_type = #{operateType,jdbcType=VARCHAR},
      operate_content = #{operateContent,jdbcType=LONGVARCHAR},
      operate_user_id = #{operateUserId,jdbcType=BIGINT},
      operate_user_name = #{operateUserName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listTakeInList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from take_in_operate_log
    where business_id = #{id,jdbcType=VARCHAR}
  </select>
</mapper>
