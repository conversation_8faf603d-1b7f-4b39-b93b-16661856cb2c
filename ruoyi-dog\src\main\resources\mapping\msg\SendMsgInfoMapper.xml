<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.msg.mapper.SendMsgInfoMapper">
    
    <resultMap type="com.ruoyi.modules.msg.domain.SendMsgInfo" id="SendMsgInfoResult">
        <result property="id"    column="id"    />
        <result property="cTime"    column="c_time"    />
        <result property="uTime"    column="u_time"    />
        <result property="type"    column="type"    />
        <result property="dataId"    column="data_id"    />
        <result property="phone"    column="phone"    />
    </resultMap>

    <sql id="selectSendMsgInfoVo">
        select id, c_time, u_time, type, data_id, phone from send_msg_info
    </sql>

    <select id="selectSendMsgInfoList" parameterType="com.ruoyi.modules.msg.domain.SendMsgInfo" resultMap="SendMsgInfoResult">
        <include refid="selectSendMsgInfoVo"/>
        <where>  
            <if test="cTime != null "> and c_time = #{cTime}</if>
            <if test="uTime != null "> and u_time = #{uTime}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="dataId != null  and dataId != ''"> and data_id = #{dataId}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
        </where>
    </select>
    
    <select id="selectSendMsgInfoById" parameterType="Long" resultMap="SendMsgInfoResult">
        <include refid="selectSendMsgInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertSendMsgInfo" parameterType="com.ruoyi.modules.msg.domain.SendMsgInfo" useGeneratedKeys="true" keyProperty="id">
        insert into send_msg_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cTime != null">c_time,</if>
            <if test="uTime != null">u_time,</if>
            <if test="type != null">type,</if>
            <if test="dataId != null">data_id,</if>
            <if test="phone != null">phone,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cTime != null">#{cTime},</if>
            <if test="uTime != null">#{uTime},</if>
            <if test="type != null">#{type},</if>
            <if test="dataId != null">#{dataId},</if>
            <if test="phone != null">#{phone},</if>
         </trim>
    </insert>

    <update id="updateSendMsgInfo" parameterType="com.ruoyi.modules.msg.domain.SendMsgInfo">
        update send_msg_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="cTime != null">c_time = #{cTime},</if>
            <if test="uTime != null">u_time = #{uTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="phone != null">phone = #{phone},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSendMsgInfoById" parameterType="Long">
        delete from send_msg_info where id = #{id}
    </delete>

    <delete id="deleteSendMsgInfoByIds" parameterType="String">
        delete from send_msg_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>