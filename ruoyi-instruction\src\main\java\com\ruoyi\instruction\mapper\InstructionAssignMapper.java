package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.InstructionAssign;
import com.ruoyi.instruction.domain.rspVo.InstructionAssignVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 指令交办Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
@DataSource(value = DataSourceType.SLAVE)
public interface InstructionAssignMapper 
{
    /**
     * 查询指令交办
     * 
     * @param id 指令交办主键
     * @return 指令交办
     */
    public InstructionAssign selectInstructionAssignById(Long id);

    /**
     * 查询指令交办列表
     * 
     * @param instructionAssign 指令交办
     * @return 指令交办集合
     */
    public List<InstructionAssign> selectInstructionAssignList(InstructionAssign instructionAssign);

    /**
     * 新增指令交办
     * 
     * @param instructionAssign 指令交办
     * @return 结果
     */
    public int insertInstructionAssign(InstructionAssign instructionAssign);

    /**
     * 修改指令交办
     * 
     * @param instructionAssign 指令交办
     * @return 结果
     */
    public int updateInstructionAssign(InstructionAssign instructionAssign);

    /**
     * 删除指令交办
     * 
     * @param id 指令交办主键
     * @return 结果
     */
    public int deleteInstructionAssignById(Long id);

    /**
     * 批量删除指令交办
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionAssignByIds(Long[] ids);

    /**
     * 根据指令id获取交办信息
     * @param id
     * @return
     */
    InstructionAssign selectInstructionAssignByInstructionId(Long id);

    /**
     * 根据指令ids查询交办记录
     * @param instructionInfoIds
     * @return
     */
    List<InstructionAssign> selectByIds(Long[] instructionInfoIds);

    /**
     * 根据时间查询
     * @param date
     * @return
     */
    List<InstructionAssignVo> selectList(@Param("date") String date,@Param("receiveUnit") String receiveUnit);

    /**
     * 根据指令id删除交办信息
     * @param id
     */
    void deleteInstructionAssignByInstructionId(@Param("id") Long id);
}
