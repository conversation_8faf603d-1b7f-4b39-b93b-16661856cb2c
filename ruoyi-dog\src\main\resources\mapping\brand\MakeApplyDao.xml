<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.brand.dao.MakeApplyDao">

    <sql id="columns">
        <trim suffixOverrides=",">
            a.id as id,
            a.user_id as userId,
            a.dept_id as deptId,
            a.num as num,
            a.status as status,
            a.is_pay as isPay,
            a.dog_codes as dogCodes,
            a.create_date as createDate,
            a.create_by as createBy,
            a.update_date as updateDate,
            a.update_by as updateBy,
            a.del_flag as delFlag,a.send_address as sendAddress,
a.courier_name as "courierName",
            a.courier_number as "courierNumber",
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.brand.entity.MakeApply"
            resultType="com.ruoyi.modules.brand.entity.MakeApply">
        select
        <include refid="columns"/>
        ,u.user_name as "user.realName"
        ,u.phonenumber as "user.mobile"
        ,(select dept_name from sys_dept where dept_id=a.dept_id) as "user.deptName"
        from make_apply a
        left join sys_user u on a.user_id=u.dog_user_id
        where a.id =#{id}
    </select>


    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.brand.entity.MakeApply"
            resultType="com.ruoyi.modules.brand.entity.MakeApply">
        select
        <include refid="columns"/>
        ,(select dept_name from sys_dept where dept_id=a.dept_id) as "user.deptName"
        ,(select count(id) from pet_brand where is_use=2 and is_receipt=2  and area=a.dept_id and brand_com is null) as surplusNum
        from make_apply a
        where a.del_flag =1
        <if test="queryType=='cj'">
           and a.status not in (1)
        </if>
        <if test="deptName!=null and deptName!=''">
            and a.dept_id in (select id from sys_dept where dept_name like concat('%',#{deptName},'%'))
        </if>
        <if test="bDate!=null and eDate !=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bDate},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eDate},'%Y-%m-%d')
        </if>
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="userId != null and userId != ''">
            and a.user_id = #{userId}
        </if>
        <if test="deptId != null and deptId != ''">
            and a.dept_id = #{deptId}
        </if>
        <if test="num != null">
            and a.num = #{num}
        </if>
        <if test="status != null">
            and a.status = #{status}
        </if>
        <if test="isPay != null">
            and a.is_pay = #{isPay}
        </if>
        <if test="dogCodes != null and dogCodes != ''">
            and a.dog_codes = #{dogCodes}
        </if>
        <if test="createDate != null">
            and a.create_date = #{createDate}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by = #{createBy}
        </if>
        <if test="updateDate != null">
            and a.update_date = #{updateDate}
        </if>
        <if test="updateBy != null and updateBy != ''">
            and a.update_by = #{updateBy}
        </if>
        <if test="delFlag != null">
            and a.del_flag = #{delFlag}
        </if>
        <if test="deptIds != null and deptIds.size > 0">
            AND a.dept_id IN
            <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
order by a.create_date desc
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.brand.entity.MakeApply">
        insert into make_apply(id, user_id, dept_id, num, status, is_pay, dog_codes, create_date, create_by,
                               update_date, update_by, del_flag, send_address)
        values (#{id}, #{userId}, #{deptId}, #{num}, #{status}, #{isPay}, #{dogCodes}, #{createDate}, #{createBy},
                #{updateDate}, #{updateBy}, #{delFlag}, #{sendAddress})
    </insert>
    <update id="updateByEntity" parameterType="com.ruoyi.modules.brand.entity.MakeApply">
        update make_apply set
        <trim suffixOverrides=",">
            <if test="courierName != null and courierName != ''">
                courier_name = #{courierName},
            </if>
            <if test="courierNumber != null and courierNumber != ''">
                courier_number = #{courierNumber},
            </if>
            <if test="sendAddress != null and sendAddress != ''">
                send_address = #{sendAddress},
            </if>
            <if test="deptId != null and deptId != ''">
                dept_id = #{deptId},
            </if>
            <if test="num != null">
                num = #{num},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="isPay != null">
                is_pay = #{isPay},
            </if>
            <if test="dogCodes != null and dogCodes != ''">
                dog_codes = #{dogCodes},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
        </trim>
        where id = #{id}
    </update>
    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.brand.entity.MakeApply">
        update make_apply set
        <trim suffixOverrides=",">
            send_address = #{sendAddress},
            user_id = #{userId},
            dept_id = #{deptId},
            num = #{num},
            status = #{status},
            is_pay = #{isPay},
            dog_codes = #{dogCodes},
            create_date = #{createDate},
            create_by = #{createBy},
            update_date = #{updateDate},
            update_by = #{updateBy},
            del_flag = #{delFlag},
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.brand.entity.MakeApply">
        UPDATE make_apply
        SET del_flag=#{delFlag},
            update_date = #{updateDate},
            update_by = #{updateBy}
        where id = #{id}
    </delete>

</mapper>
