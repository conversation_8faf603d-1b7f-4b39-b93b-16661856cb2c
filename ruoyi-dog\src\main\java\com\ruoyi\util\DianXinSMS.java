package com.ruoyi.util;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.yw.domain.XxInfo;
import com.ruoyi.common.yw.mapper.XxInfoMapper;
//import sun.misc.BASE64Encoder;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.util.Base64;
import java.util.Objects;

/**
 * <AUTHOR>
 * @创建时间 2023/8/25
 * @作用：中国电信短信 工具类
 */
public class DianXinSMS {

    private static String smsUrl = "http://smservice.zjhcsoft.com/smsservice/httpservices/capService";

    public static void main(String[] args) {
        sendMessage("17805893997","测试电信短信发送666", null);
    }

    public static String sendMessage(String telephone, String content, XxInfoMapper xxInfoMapper) {
        try {
            String timeStamp = String.valueOf(System.currentTimeMillis());
            String transactionID = IdGen.uuid();
            String streamingNo = IdGen.uuid();

//            String authenticator = encoderByMd5(timeStamp+transactionID+streamingNo+"ZjHc1!2@");
            String authenticator = encoderByMd5(timeStamp+transactionID+streamingNo+"jhzhxzzfj12!@");
            JSONObject obj = new JSONObject();
            // 客户编号
//            obj.put("siid", "ZJHCJSJXTYXGS7");
            obj.put("siid", "JHSZHXZZFJ");
            // HTTP账号
//            obj.put("user", "zjhc7");
            obj.put("user", "jhzhxzzfj");
            // BASE64(MD5( timeStamp＋transactionID＋streamingNo＋接口密钥)
            obj.put("authenticator", authenticator);
            // 时间戳
            obj.put("timeStamp", timeStamp);
            // 事务号
            obj.put("transactionID", transactionID);
            // 流水号，标识操作唯一性，只能使用一次，由SI负责生成，防止重复提交
            obj.put("streamingNo", streamingNo);
            // 多个号码时用英文,分割（最多支持50个手机号码）
            obj.put("mobile", telephone);
            // 扩展码
            obj.put("extcode", "001");
            obj.put("content", content);

            String res = httpPost(obj.toJSONString());
            System.out.println("sendMessage调用短信接口返回结果："+res);
            JSONObject objs = JSON.parseObject(res);

            if (Objects.nonNull(xxInfoMapper)) {
                XxInfo xxInfo=new XxInfo();
                xxInfo.setFsr("金华市综合行政执法局");
                xxInfo.setJsr(telephone);
                xxInfo.setNr(content);
                xxInfo.setBt("金华市综合行政执法消息通知");
                xxInfo.setSource(1);
                xxInfoMapper.insertXxInfo(xxInfo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static String encoderByMd5(String str) {
        try {
            // 确定计算方法
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            // 加密后的字符串
            return  Base64.getEncoder().encodeToString(md5.digest(str.getBytes("UTF-8")));
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * httpPost
     * @param params
     * @return
     */
    private static String httpPost(String params) {
        try {
            BufferedReader bufferedReader = null;
            StringBuffer result = new StringBuffer();
            // 创建连接
            URL url = new URL(smsUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestMethod("POST"); // 设置请求方式
            connection.setRequestProperty("Accept", "application/json"); // 设置接收数据的格式
            connection.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式
            connection.connect();
            OutputStreamWriter out = new OutputStreamWriter(connection.getOutputStream(), "UTF-8"); // utf-8编码
            out.append(params);
            out.flush();
            out.close();

            // 接受连接返回参数
            bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                result.append(line);
            }
            bufferedReader.close();
            return result.toString();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}
