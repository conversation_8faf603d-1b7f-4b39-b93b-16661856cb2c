package com.ruoyi.modules.brand.entity;

import com.ruoyi.base.entity.BaseEntity;

import java.util.Date;
import java.io.Serializable;

/**
 * 犬牌申请审核记录(PetAuditRecords)实体类
 *
 * <AUTHOR>
 * @since 2022-10-26 14:21:06
 */
public class PetAuditRecords extends BaseEntity {


    /**
    * 犬牌申请id
    */
    private String applyId;
    /**
    * 发起状态： 1.待审核 2.拒绝 3.已提交制作(通过) 4.制作中 5.运输中 6.运输中由厂家操作
    */
    private Integer status;
    /**
    * 审核备注
    */
    private String remarks;




    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }



}
