<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.certificates.dao.DongyangDogDao">

    <!-- 查询东阳犬主信息 -->
    <select id="selectDongyangOwners" resultType="java.util.Map">
        SELECT
            OID,
            ONAME,
            OIDCARD,
            OMOBILE,
            OJD,
            OADDRESS,
            OIDPICS,
            OTHERPICS,
            STYPE,
            SNAME,
            SMOBILE,
            SADDRESS,
            ODATE,
            SYOADDRESS,
            XXSYOADDRESS
        FROM downerb
        ORDER BY OID
    </select>

    <!-- 查询东阳犬只信息 -->
    <select id="selectDongyangPets" resultType="java.util.Map">
        SELECT
            DID,
            DNAME,
            DTYPE,
            DCOLOR,
            DBDAY,
            DSEX,
            DUSE,
            QZPI<PERSON>,
            CNUM,
            DMYNUM,
            DMYDAY,
            MYIMG,
            OID,
            DDATE,
            FLAG
        FROM petb
        ORDER BY DID
    </select>

    <select id="selectDongyangApproves" resultType="java.util.Map">
        select CID, CFLAG, CRESULT, CNOTE, DID, CDATE
        from dcheck
    </select>
</mapper>
