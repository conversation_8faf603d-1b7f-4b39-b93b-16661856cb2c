package com.ruoyi.modules.petLose.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;

import java.util.Date;

/**
 * 宠物丢失表(PetLose)实体类
 *
 * <AUTHOR>
 * @since 2022-12-09 13:51:27
 */
public class PetLose extends BaseEntity {

    /**
    * id
    */
    private String id;
    /**
    * 宠物照片（正面）
    */
    private String petImgz;
    /**
    * 宠物照片（侧面）
    */
    private String petImgc;
    /**
    * 宠物名称
    */
    private String petName;
    /**
    * 品种（大类）(对应字典表varieties)
    */
    private String petVarietiesOne;
    /**
    * 品种(对应字典表varieties_type)
    */
    private String petVarieties;
    /**
    * 毛色(对应字典表hair_type)
    */
    private String petHair;
    /**
    * 丢失日期
    */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date loseDate;
    /**
    * 丢失地区
    */
    private String loseDept;
    /**
    * 详细地址
    */
    private String address;
    /**
    * 饲主名称
    */
    private String ownerName;
    /**
    * 饲主身份证号
    */
    private String petIdCard;
    /**
    * 饲主联系电话
    */
    private String tel;
    /**
    * 状态
    */
    private String status;

    /**
     * 犬只线索
     */
    private String petClue;

    /**
    * 数据状态：1正常，2 注销标识
    */
    private Integer delFlag;
    /**
    * 创建时间
    */
    private Date createDate;
    /**
    * 创建人
    */
    private String createBy;
    /**
    * 修改时间
    */
    private Date updateDate;
    /**
    * 修改人
    */
    private String updateBy;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPetImgz() {
        return petImgz;
    }

    public void setPetImgz(String petImgz) {
        this.petImgz = petImgz;
    }

    public String getPetImgc() {
        return petImgc;
    }

    public void setPetImgc(String petImgc) {
        this.petImgc = petImgc;
    }

    public String getPetName() {
        return petName;
    }

    public void setPetName(String petName) {
        this.petName = petName;
    }

    public String getPetVarietiesOne() {
        return petVarietiesOne;
    }

    public void setPetVarietiesOne(String petVarietiesOne) {
        this.petVarietiesOne = petVarietiesOne;
    }

    public String getPetVarieties() {
        return petVarieties;
    }

    public void setPetVarieties(String petVarieties) {
        this.petVarieties = petVarieties;
    }

    public String getPetHair() {
        return petHair;
    }

    public void setPetHair(String petHair) {
        this.petHair = petHair;
    }

    public Date getLoseDate() {
        return loseDate;
    }

    public void setLoseDate(Date loseDate) {
        this.loseDate = loseDate;
    }

    public String getLoseDept() {
        return loseDept;
    }

    public void setLoseDept(String loseDept) {
        this.loseDept = loseDept;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getPetIdCard() {
        return petIdCard;
    }

    public void setPetIdCard(String petIdCard) {
        this.petIdCard = petIdCard;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getPetClue() {
        return petClue;
    }

    public void setPetClue(String petClue) {
        this.petClue = petClue;
    }
}
