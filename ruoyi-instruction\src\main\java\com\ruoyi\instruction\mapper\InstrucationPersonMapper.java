package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.domain.rspVo.BigScreenJazzCommonVo;
import com.ruoyi.instruction.domain.rspVo.GroupDataRspVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 指令关联人员信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@Repository
@DataSource(value = DataSourceType.SLAVE)
public interface InstrucationPersonMapper {
    /**
     * 查询指令关联人员信息
     *
     * @param id 指令关联人员信息主键
     * @return 指令关联人员信息
     */
    public InstrucationPerson selectInstrucationPersonById(Long id);

    /**
     * 查询指令关联人员信息列表
     *
     * @param instrucationPerson 指令关联人员信息
     * @return 指令关联人员信息集合
     */
    public List<InstrucationPerson> selectInstrucationPersonList(InstrucationPerson instrucationPerson);

    /**
     * 新增指令关联人员信息
     *
     * @param instrucationPerson 指令关联人员信息
     * @return 结果
     */
    @Log(title = "指令关联人员信息", businessType = BusinessType.INSERT)
    public int insertInstrucationPerson(InstrucationPerson instrucationPerson);

    /**
     * 修改指令关联人员信息
     *
     * @param instrucationPerson 指令关联人员信息
     * @return 结果
     */
    public int updateInstrucationPerson(InstrucationPerson instrucationPerson);

    /**
     * 删除指令关联人员信息
     *
     * @param id 指令关联人员信息主键
     * @return 结果
     */
    public int deleteInstrucationPersonById(Long id);

    /**
     * 批量删除指令关联人员信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstrucationPersonByIds(Long[] ids);

    /**
     * 通过身份证号查询用户
     *
     * @param idCard
     * @return
     */
    InstrucationPerson selectInstrucationPersonByIdCard(@Param("personName") String personName, @Param("idCard") String idCard);

    /**
     * 通过人员ids以及名称查询人员
     *
     * @param ids
     * @return
     */
    List<InstrucationPerson> selectInstrucationPersonByIds(@Param("ids") String[] ids, @Param("personName") String personName, @Param("dutyPlace") String dutyPlace);


    /**
     * 获取各省的信息
     *
     * @return
     */
    List<HashMap> getProvInfo();

    /**
     * 获取各市信息
     *
     * @param code
     * @return
     */
    List<HashMap> getCityByCode(@Param("code") String code);

    /**
     * 根据code查询县市区
     *
     * @param code
     * @return
     */
    List<HashMap> getCountryByCode(@Param("code") String code);

    /**
     * 通过人员id、人员姓名、身份证号查询人员信息
     *
     * @param id
     * @param personName
     * @param idCard
     * @return
     */
    List<InstrucationPerson> findPersonByParam(@Param("id") Long id, @Param("personName") String personName, @Param("idCard") String idCard);

    /**
     * 更新重点人员管控等级
     *
     * @param level
     * @param lowerPersonIds
     */
    void updatePersonControlLevel(@Param("level") String level, @Param("list") List<Long> lowerPersonIds);

    /**
     * 获取总人数、高、中、低人数
     * @return
     */
    GroupDataRspVo getPersonData(Map<String,Object> map);

    /**
     * 每日新增人数
     * @return
     */
    List<Map<String, Integer>> getAddPerson();

    /**
     * 获取区域重点人员数量
     * @return
     */
    List<Map<String, Integer>> getAreaPersonCount();

    /**
     * 人员关联事件排名
     * @return
     */
    List<Map<String, Object>> getEventCount();

    /**
     * 获取频繁重点人员
     * @return
     */
    List<Map<String, Integer>> getOftenPerson();

    /**
     * 查询人员的责任所在地，去重
     * @param lowerPersonIds
     * @return
     */
    List<String> selectDutyPlace(@Param("list") List<Long> lowerPersonIds);

    /**
     * 统计人员的责任所在地
     * @param lowerPersonIds
     * @return
     */
    List<BigScreenJazzCommonVo> countDutyPlace(@Param("list") List<Long> lowerPersonIds);

    /**
     * 根据人员ids查询数量
     * @param collect
     * @return
     */
    int findPersonCountById(@Param("list") List<String> collect);

    /**
     * 查询人员的群体
     * @param id
     * @return
     */
    List<String> selectGroupNameById(@Param("id") String id);

    /**
     * 查询人员关联群体数量
     * @param date
     * @param thresholdValue
     * @param thresholdRule
     * @return
     */
    List<Map<String, Object>> groupPersonCount(@Param("date") LocalDate date, @Param("thresholdValue") Integer thresholdValue, @Param("thresholdRule") String thresholdRule);

    /**
     * 根据事件类型统计人员数量，获取前10
     * @return
     */
    List<BigScreenJazzCommonVo> countPersonByEventTop10(InstrucationPerson instrucationPerson);

    /**
     * 根据人员id查询关联群体名称
     * @param id
     * @return
     */
    String findGroupNameByPersonId(@Param("id") Long id);

    List<InstrucationPerson> selectPersonIds(@Param("ids") String[] split,@Param("dutyPlace") String dutyPlace);

    List<HashMap<String,Object>> selectPersonIdsGroup(@Param("ids") String[] split);
}
