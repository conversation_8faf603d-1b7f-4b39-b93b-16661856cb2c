<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.takeIn.dao.ReclaimRecordDao">

    <sql id="columns">
        <trim suffixOverrides=",">
            a.id as id,
            a.type as type,
            a.take_in_id as takeInId,
            a.name as name,
            a.contact_number as contactNumber,
            a.appointment_time as appointmentTime,
            a.remarks as remarks,a.status as status,a.audit_records as auditRecords,
            a.del_flag as delFlag,
            a.create_date as createDate,
            a.create_by as createBy,
            a.update_date as updateDate,
            a.update_by as updateBy,
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.takeIn.entity.ReclaimRecord"
            resultType="com.ruoyi.modules.takeIn.entity.ReclaimRecord">
        select
        <include refid="columns"/>
        ,t.type as "takeIn.type",
        t.pet_num as "takeIn.petNum",
        t.id_card as "takeIn.idCard",
        t.reason as "takeIn.reason",
        t.address as "takeIn.address",
        t.time as "takeIn.time",
        t.remarks as "takeIn.remarks"
        from reclaim_record a
        join take_in t on t.id=a.take_in_id
        where a.id =#{id}
    </select>


    <!--通过实体作为筛选条件查询-->
    <select id="getByEntity" parameterType="com.ruoyi.modules.takeIn.entity.ReclaimRecord"
            resultType="com.ruoyi.modules.takeIn.entity.ReclaimRecord">
        select
        <include refid="columns"/>
        from reclaim_record a
        where a.del_flag =1
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="takeInId != null and takeInId != ''">
            and a.take_in_id = #{takeInId}
        </if>
        <if test="name != null and name != ''">
            and a.name = #{name}
        </if>
        <if test="contactNumber != null and contactNumber != ''">
            and a.contact_number = #{contactNumber}
        </if>
        <if test="appointmentTime != null">
            and a.appointment_time = #{appointmentTime}
        </if>

        <if test="remarks != null and remarks != ''">
            and a.remarks = #{remarks}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>

        <if test="delFlag != null">
            and a.del_flag = #{delFlag}
        </if>
        <if test="createDate != null">
            and a.create_date = #{createDate}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by = #{createBy}
        </if>
        <if test="updateDate != null">
            and a.update_date = #{updateDate}
        </if>
        <if test="updateBy != null and updateBy != ''">
            and a.update_by = #{updateBy}
        </if>
        order by a.create_date desc
    </select>
    <select id="getList" parameterType="com.ruoyi.modules.takeIn.entity.ReclaimRecord"
            resultType="com.ruoyi.modules.takeIn.entity.ReclaimRecord">
        select
        <include refid="columns"/>
        ,t.type as "takeIn.type",
        t.pet_num as "takeIn.petNum",
        t.id_card as "takeIn.idCard",
        t.reason as "takeIn.reason",
        t.address as "takeIn.address",
        t.time as "takeIn.time",
        t.remarks as "takeIn.remarks"
        from reclaim_record a
        join take_in t on t.id=a.take_in_id
        where a.del_flag =1
        <if test="keyword!=null and keyword!=''">
            and ( t.type like concat('%',#{keyword},'%')or
            t.pet_num like concat('%',#{keyword},'%')or
            t.id_card like concat('%',#{keyword},'%')or
            t.reason like concat('%',#{keyword},'%')or
            t.address like concat('%',#{keyword},'%')or
            t.time like concat('%',#{keyword},'%')or
            t.remarks like concat('%',#{keyword},'%')or
            a.name like concat('%',#{keyword},'%')or
            a.contact_number like concat('%',#{keyword},'%')
            )
        </if>
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="takeInId != null and takeInId != ''">
            and a.take_in_id = #{takeInId}
        </if>
        <if test="name != null and name != ''">
            and a.name = #{name}
        </if>
        <if test="contactNumber != null and contactNumber != ''">
            and a.contact_number = #{contactNumber}
        </if>
        <if test="appointmentTime != null">
            and a.appointment_time = #{appointmentTime}
        </if>

        <if test="remarks != null and remarks != ''">
            and a.remarks = #{remarks}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>

        <if test="delFlag != null">
            and a.del_flag = #{delFlag}
        </if>
        <if test="createDate != null">
            and a.create_date = #{createDate}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by = #{createBy}
        </if>
        <if test="updateDate != null">
            and a.update_date = #{updateDate}
        </if>
        <if test="updateBy != null and updateBy != ''">
            and a.update_by = #{updateBy}
        </if>
        order by a.create_date desc
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.takeIn.entity.ReclaimRecord">
        insert into reclaim_record(id, type, take_in_id, name, contact_number, appointment_time, id_card, remarks, status,
                                   audit_records,
                                   del_flag,
                                   create_date, create_by, update_date, update_by, street, county, address)
        values (#{id}, #{type}, #{takeInId}, #{name}, #{contactNumber}, #{appointmentTime}, #{idCard}, #{remarks}, #{status},
                #{auditRecords},
                #{delFlag},
                #{createDate}, #{createBy}, #{updateDate}, #{updateBy}, #{street}, #{county}, #{address})
    </insert>
    <update id="updateByEntity" parameterType="com.ruoyi.modules.takeIn.entity.ReclaimRecord">
        update reclaim_record set
        <trim suffixOverrides=",">
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="takeInId != null and takeInId != ''">
                take_in_id = #{takeInId},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="contactNumber != null and contactNumber != ''">
                contact_number = #{contactNumber},
            </if>
            <if test="appointmentTime != null">
                appointment_time = #{appointmentTime},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>

            <if test="auditRecords != null and auditRecords != ''">
                audit_records = #{auditRecords},
            </if>

            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
        </trim>
        where id = #{id}
    </update>
    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.takeIn.entity.ReclaimRecord">
        update reclaim_record set
        <trim suffixOverrides=",">
            type = #{type},
            take_in_id = #{takeInId},
            name = #{name},
            contact_number = #{contactNumber},
            appointment_time = #{appointmentTime},
            remarks = #{remarks}, status = #{status},audit_records = #{auditRecords},
            del_flag = #{delFlag},
            create_date = #{createDate},
            create_by = #{createBy},
            update_date = #{updateDate},
            update_by = #{updateBy},
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.takeIn.entity.ReclaimRecord">
        UPDATE reclaim_record
        SET del_flag=#{delFlag},
            update_date = #{updateDate},
            update_by = #{updateBy}
        where id = #{id}
    </delete>

    <select id="listByTakeInList" resultType="com.ruoyi.modules.takeIn.entity.ReclaimRecord">
        select
        <include refid="columns"/>
        from reclaim_record a
        where a.del_flag =1
        and take_in_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateByTakeInId">
        update reclaim_record set
            status = #{status}
        where take_in_id = #{takeInId}
    </update>
</mapper>
