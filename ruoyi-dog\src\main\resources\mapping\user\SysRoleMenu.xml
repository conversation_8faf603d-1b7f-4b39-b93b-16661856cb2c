<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.user.dao.SysRoleMenuDao" >

    <sql id="sysRoleMenuColumns">
        a.id AS "id",
        a.role_id AS "roleId",
        a.menu_id AS "menuId"
    </sql>

    <select id="getList" resultType="com.ruoyi.modules.user.entity.SysRoleMenu">
        select
        <include refid="sysRoleMenuColumns"/>
        from sys_role_menu a
        where 1=1
        <if test='roleId != null and roleId != ""'>
            AND a.role_id = #{roleId}
        </if>
    </select>

    <update id="deleteByRoleId">
        delete from sys_role_menu where role_id = #{roleId}
    </update>

    <insert id="saveList" parameterType="java.util.List">
        INSERT INTO sys_role_menu (
            id,
            role_id,
            menu_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
                #{item.id},
                #{item.roleId},
                #{item.menuId}
            )
        </foreach>
    </insert>

</mapper>
