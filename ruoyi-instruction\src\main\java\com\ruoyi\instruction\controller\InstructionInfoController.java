package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.InstructionCountyFeedback;
import com.ruoyi.instruction.domain.InstructionInfo;
import com.ruoyi.instruction.domain.reqVo.InstructionFlowTestVo;
import com.ruoyi.instruction.domain.rspVo.InstructionInfoRspVo;
import com.ruoyi.instruction.service.IInstructionInfoService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 指令基本信息Controller
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@RestController
@RequestMapping("/instruction/info")
public class InstructionInfoController extends BaseController {

    @Autowired
    private IInstructionInfoService instructionInfoService;


    /**
     * 获取指令基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(instructionInfoService.selectInstructionInfoById(id));
    }

    /**
     * 新增指令基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:info:add')")
    @Log(title = "指令基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionInfo instructionInfo) {
        AjaxResult result = instructionInfoService.insertInstructionInfo(instructionInfo);
        return result;
    }

    /**
     * 修改指令基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:info:edit')")
    @Log(title = "指令基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionInfo instructionInfo) {
        return toAjax(instructionInfoService.updateInstructionInfo(instructionInfo));
    }

    /**
     * 修改是否发布状态
     * @return
     */
    @PreAuthorize("@ss.hasPermi('instruction:info:isRelease')")
    @PutMapping("editIsRelease")
    public AjaxResult editIsRelease(@RequestBody InstructionInfo instructionInfo){
        return toAjax(instructionInfoService.updateIsRelease(instructionInfo));
    }

    /**
     * 删除指令基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:info:remove')")
    @Log(title = "指令基本信息", businessType = BusinessType.DELETE)
    @PutMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Long ids) {
        return toAjax(instructionInfoService.deleteInstructionInfoById(ids));
    }


    /**
     * 测试提交流程接口
     *
     * @return
     */
    @PostMapping("/testSubmit")
    public AjaxResult testSubmit(@RequestBody InstructionFlowTestVo instructionFlowTestVo) {
        return instructionInfoService.testSubmit(instructionFlowTestVo);
    }

    /**
     * 处置、反馈变为多条记录 获取指令流程记录
     *
     * @param id
     * @return
     */
    @GetMapping("testGetProcessById/{id}/{type}")
    public AjaxResult testGetProcessById(@PathVariable("id") Long id, @PathVariable("type") Integer type) {
        InstructionFlowTestVo flowTestVo = instructionInfoService.testGetProcessById(id, type);
        return AjaxResult.success(flowTestVo);
    }


    /**
     * 测试 指令列表
     *
     * @param instructionInfo
     * @return
     */
    @GetMapping("/testInstructionListNew")
    public TableDataInfo testInstructionListNew(InstructionInfo instructionInfo) {
        return instructionInfoService.testInstructionListNew(instructionInfo);
    }


    /**
     * 查询出人员代办事件
     *
     * @return
     */
    @GetMapping("/testRedStatistics")
    public AjaxResult testRedStatistics() {
        Map<String, List<Long>> ids = instructionInfoService.testRedStatistics();
        return AjaxResult.success().put("cityIds", ids.get("cityIds").size()).put("countyIds",ids.get("countyIds").size());
    }

    /**
     * 导出指令列表
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response,@RequestBody InstructionInfo instructionInfo)
    {
        List<InstructionInfoRspVo> instructionInfoRspVoList = instructionInfoService.testInstructionListNotPage(instructionInfo);
        ExcelUtil<InstructionInfoRspVo> util = new ExcelUtil<InstructionInfoRspVo>(InstructionInfoRspVo.class);
        util.exportExcel(response, instructionInfoRspVoList, "导出指令列表");
    }


//    /**
//     * 导出一网统管-储备资源列表
//     */
//    @Log(title = "导出指令列表", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public AjaxResult export(InstructionInfo instructionInfo)
//    {
//        List<InstructionInfoRspVo> list = instructionInfoService.testInstructionListNotPage(instructionInfo);
//        ExcelUtil<InstructionInfoRspVo> util = new ExcelUtil<InstructionInfoRspVo>(InstructionInfoRspVo.class);
//        return util.exportExcel(list, "ywtgCbzy");
//    }

    /**
     * 删除指令基本信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:info:batchRemove')")
    @PutMapping("/batchRemove")
    public AjaxResult batchRemove(Long[] ids) {
        return toAjax(instructionInfoService.batchRemove(ids));
    }

}
