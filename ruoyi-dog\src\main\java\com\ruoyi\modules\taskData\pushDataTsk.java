package com.ruoyi.modules.taskData;

import com.ruoyi.modules.brand.dao.PetBrandDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 将犬类数据推送至数浙
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/19 9:20
 */
@Component
public class pushDataTsk {

    @Autowired
    private PetBrandDao petBrandDao;


    @Scheduled(cron = "0 0 1 * * ?")
    public void qLDataTsk() throws Exception {

        System.out.println("执行任务");
        Connection connection = null;
        PreparedStatement preparedStatement = null;

        String databaseURL = "********************************************************************************************************************************************************************************";
        String user = "ytj_qzk_xzzf";
        String password = "ytj_qzk_xzzf123";

        try {
            connection = DriverManager.getConnection(databaseURL, user, password);
            // 关闭自动提交事务，改为手动提交
            connection.setAutoCommit(false);
            System.out.println("===== 获取最大的create_date ====");
            String sql = "SELECT MAX(update_date) AS max_create_date FROM biz_0700_jhsyqgl_qzxx";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            String formattedDate = "";
            if (rs.next()) {
                java.sql.Timestamp maxTimestamp = rs.getTimestamp("max_create_date");
                // 格式化日期时间输出
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                formattedDate = sdf.format(maxTimestamp);
                System.out.println("最大的创建日期时间是: " + formattedDate);
            } else {
                System.out.println("没有记录在表biz_0700_jhsyqgl_qzxx中。");
            }
            //查询视图数据
            List<Map<String, Object>> list = petBrandDao.findViewData(formattedDate);
            if (list == null && list.size() == 0) {
                System.out.println("没有数据");
                return;
            }
            System.out.println("===== 开始插入数据 =====");
            long startTime = System.currentTimeMillis();
            String sqlInsert = "INSERT INTO biz_0700_jhsyqgl_qzxx ( pet_num, dogId,create_date,update_date,owner_name,pet_id_card,name,area,id) VALUES ( ?, ?,?,?, ?,?,?, ?,?)";
            preparedStatement = connection.prepareStatement(sqlInsert);
            for (Map<String, Object> map : list) {
                System.out.println(map.toString());
                String pet_num = String.valueOf(map.get("pet_num"));
                String id = (String) map.get("id");
                String pet_id_card = (String) map.get("pet_id_card");
                String dogId = (String) map.get("dogId");
                Date create_date = (Date) map.get("create_date");
                java.sql.Timestamp createTime = new java.sql.Timestamp(create_date.getTime());
                Date update_date = (Date) map.get("update_date");
                java.sql.Timestamp updateDate = new java.sql.Timestamp(update_date.getTime());
                String name = (String) map.get("name");
                String area = (String) map.get("area");
                String owner_name = (String) map.get("owner_name");
                preparedStatement.setString(1, pet_num);
                preparedStatement.setString(2, dogId);
                preparedStatement.setTimestamp(3, createTime);
                preparedStatement.setTimestamp(4, updateDate);
                preparedStatement.setString(5, owner_name);
                preparedStatement.setString(6, pet_id_card);
                preparedStatement.setString(7, name);
                preparedStatement.setString(8, area);
                preparedStatement.setString(9, id);
                // 添加到批处理中
                preparedStatement.addBatch();
            }
            preparedStatement.executeBatch();
            connection.commit();
            long spendTime = System.currentTimeMillis() - startTime;
            System.out.println("耗时：" + spendTime + "毫秒");
        } catch (SQLException e) {
            System.out.println("Error: " + e.getMessage());
        } finally {
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                    System.out.println("关闭连接");
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }

            if (connection != null) {
                try {
                    connection.close();
                    System.out.println("关闭连接");
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
