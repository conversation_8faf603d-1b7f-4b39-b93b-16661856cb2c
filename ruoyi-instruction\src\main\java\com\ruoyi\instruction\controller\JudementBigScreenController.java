package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.instruction.domain.InstructionInfo;
import com.ruoyi.instruction.domain.JudementInfo;
import com.ruoyi.instruction.service.IInstructionInfoService;
import com.ruoyi.instruction.service.IJudementInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 分析研判大屏接口
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/11 9:09
 */
@RestController
@RequestMapping("/bigScreen/judement")
public class JudementBigScreenController extends BaseController {

    @Autowired
    private IJudementInfoService judementInfoService;

    @Autowired
    private IInstructionInfoService instructionInfoService;

    /**
     * 研判信息概况
     * @return
     */
    @GetMapping("/overviewInfo")
    public AjaxResult overviewInfo(){
        return judementInfoService.overviewInfo();
    }

    /**
     * 获取研判信息集合
     * @return
     */
    @GetMapping("overviewList")
    public TableDataInfo overviewList(@RequestParam("type") Long type) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        // 获取今日零点时间
        Date todayZero = calendar.getTime();
        JudementInfo judementInfo = new JudementInfo();
        if (type == 1) {
            judementInfo.setCreateTime(todayZero);
            judementInfo.setIsJudgment("2");
        } else if (type == 2) {
            judementInfo.setCreateTime(todayZero);
            judementInfo.setIsJudgment("1");
        } else if (type == 3) {
            judementInfo.setIsJudgment("1");
        }
        startPage();
        List<JudementInfo> judementInfos = judementInfoService.selectJudementInfoList(judementInfo);
        return getDataTable(judementInfos);
    }

    /**
     * 获取列表
     * @param judementInfo
     * @return
     */
    @GetMapping("list")
    public AjaxResult getList(JudementInfo judementInfo){
        List<JudementInfo> judementInfos = judementInfoService.selectJudementInfoList(judementInfo);
        return AjaxResult.success(judementInfos);
    }

    /**
     * 查询指令基本信息列表
     */
    @GetMapping("/instructionList")
    public TableDataInfo list(InstructionInfo instructionInfo)
    {
        startPage();
        List<InstructionInfo> list = instructionInfoService.selectInstructionInfoList(instructionInfo);
        return getDataTable(list);
    }


    @GetMapping(value = "/instruction/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(instructionInfoService.selectInstructionInfoById(id));
    }


}
