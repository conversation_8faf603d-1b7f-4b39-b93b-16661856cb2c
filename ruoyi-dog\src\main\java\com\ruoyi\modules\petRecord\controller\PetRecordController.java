package com.ruoyi.modules.petRecord.controller;

import com.ruoyi.modules.petRecord.entity.PetRecord;
import com.ruoyi.modules.petRecord.service.PetRecordService;
import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * 犬只操作记录表(pet_record)表控制层
 * <AUTHOR>
 */
@RestController
@RequestMapping("petRecord")
public class PetRecordController {
    /**
     * 服务对象
     */
    @Resource
    private PetRecordService service;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("/getPageList")
    public AjaxResult getPageList(PetRecord entity) {
        return AjaxResult.success(service.getPageList(entity));
    }

    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(PetRecord entity) {
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }

    @RequestMapping("/delete")
    public AjaxResult delete(PetRecord entity) {
        entity.setDelFlag(2);
        service.delete(entity);
        return AjaxResult.success();
    }

    @RequestMapping("/getList")
    public AjaxResult getList(PetRecord entity) {
        return AjaxResult.success(service.getList(entity));
    }

}
