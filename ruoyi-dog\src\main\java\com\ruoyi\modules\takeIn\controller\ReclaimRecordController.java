package com.ruoyi.modules.takeIn.controller;

import com.google.common.collect.Lists;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.takeIn.entity.ReclaimRecord;
import com.ruoyi.modules.takeIn.entity.TakeIn;
import com.ruoyi.modules.takeIn.service.ReclaimRecordService;
import com.ruoyi.modules.takeIn.vo.ReclaimRecordVO;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * 认领、收养记录(reclaim_record)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("reclaimRecord")
public class ReclaimRecordController {
    /**
     * 服务对象
     */
    @Resource
    private ReclaimRecordService service;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

//    @RequestMapping("/getPageList")
//    public AjaxResult getPageList(ReclaimRecord entity) {
//        return AjaxResult.success(service.getPageList(entity));
//    }

    /**
     * 认领申请列表
     * @param entity
     * @return
     */
    @RequestMapping("/getApplyList")
    public AjaxResult getApplyList(TakeIn entity) {
        entity.setStatusList(Lists.newArrayList("1", "2", "3"));
//        entity.setReclaimRecordStatus("0");
        return AjaxResult.success(service.getApplyList(entity));
    }

    /**
     * 认领登记列表
     * @param entity
     * @return
     */
    @RequestMapping("/getRegistList")
    public AjaxResult getRegistList(TakeIn entity) {
        entity.setStatusList(Lists.newArrayList("2", "3"));
//        entity.setReclaimRecordStatus("1");
        return AjaxResult.success(service.getApplyList(entity));
    }

    /**
     * 认领申请
     * @param entity
     * @return
     */
    @PostMapping("/updateApply")
    public AjaxResult updateApply(@RequestBody TakeIn entity) {
        service.updateApply(entity);
        return AjaxResult.success();
    }

//    /**
//     * 认领登记
//     * @param entity
//     * @return
//     */
//    @PostMapping("/updateRegist")
//    public AjaxResult updateRegist(@RequestBody TakeIn entity) {
//        service.updateRegist(entity);
//        return AjaxResult.success();
//    }

    /**
     * 认领登记
     * @param recordVO
     * @return
     */
    @PostMapping("/updateRegist")
    public AjaxResult updateRegist(@RequestBody ReclaimRecordVO recordVO) {
        service.updateRegistV2(recordVO);
        return AjaxResult.success();
    }

    @RequestMapping("/getByEntity")
    public AjaxResult getByEntity(ReclaimRecord entity) {
        return AjaxResult.success(service.getByEntity(entity));
    }

    @PostMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(@RequestBody ReclaimRecord entity) {
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }
    @RequestMapping("/updateByEntity")
    public AjaxResult updateByEntity(ReclaimRecord entity) {
        service.updateByEntity(entity);
        return AjaxResult.success();
    }
    @RequestMapping("/delete")
    public AjaxResult delete(ReclaimRecord entity) {
        entity.setDelFlag(2);
        service.delete(entity);
        return AjaxResult.success();
    }

}
