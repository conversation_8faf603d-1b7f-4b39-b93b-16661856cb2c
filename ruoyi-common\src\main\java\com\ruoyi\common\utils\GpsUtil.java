package com.ruoyi.common.utils;

import java.math.BigDecimal;
import java.util.ArrayList;

/**
 * 经纬度判断
 *
 * <AUTHOR>
 * @date 2021-07-01
 */
public class GpsUtil {

	// 圆周率
	public static final double PI = 3.14159265358979324;
	// 赤道半径(单位m)
	private static final  double EARTH_RADIUS = 6378137;

	/**
	 * 转化为弧度(rad)
	 * */
	private static double rad(double d) {
		return d * Math.PI / 180.0;
	}
	/**
	 * 基于googleMap中的算法得到两经纬度之间的距离,
	 * 计算精度与谷歌地图的距离精度差不多，相差范围在0.2米以下
	 * @param lon1 第一点的经度
	 * @param lat1 第一点的纬度
	 * @param lon2 第二点的经度
	 * @param lat2 第二点的纬度
	 * @return 返回的距离，单位m
	 * */
	public static double GetDistance(double lon1,double lat1,double lon2, double lat2) {
		double radLat1 = rad(lat1);
		double radLat2 = rad(lat2);
		double a = radLat1 - radLat2;
		double b = rad(lon1) - rad(lon2);
		double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a/2),2)+Math.cos(radLat1)*Math.cos(radLat2)*Math.pow(Math.sin(b/2),2)));
		s = s * EARTH_RADIUS;
		s = Math.round(s * 100) / 100;
		return s;
	}

	/**
	 * 是否有 横断<br/> 参数为四个点的坐标
	 *
	 * @param px1
	 * @param py1
	 * @param px2
	 * @param py2
	 * @param px3
	 * @param py3
	 * @param px4
	 * @param py4
	 * @return
	 */
	public static boolean isIntersect(double px1, double py1, double px2, double py2,
			double px3, double py3, double px4, double py4) {
		boolean flag = false;
		double d = (px2 - px1) * (py4 - py3) - (py2 - py1) * (px4 - px3);
		if (d != 0) {
			double r = ((py1 - py3) * (px4 - px3) - (px1 - px3) * (py4 - py3))
					/ d;
			double s = ((py1 - py3) * (px2 - px1) - (px1 - px3) * (py2 - py1))
					/ d;
			if ((r >= 0) && (r <= 1) && (s >= 0) && (s <= 1)) {
				flag = true;
			}
		}
		return flag;
	}

	/**
	 * 目标点是否在目标边上边上<br/>
	 *
	 * @param px0
	 *            目标点的经度坐标
	 * @param py0
	 *            目标点的纬度坐标
	 * @param px1
	 *            目标线的起点(终点)经度坐标
	 * @param py1
	 *            目标线的起点(终点)纬度坐标
	 * @param px2
	 *            目标线的终点(起点)经度坐标
	 * @param py2
	 *            目标线的终点(起点)纬度坐标
	 * @return
	 */
	public static boolean isPointOnLine(double px0, double py0, double px1,
			double py1, double px2, double py2) {
		boolean flag = false;
		double ESP = 1e-9;// 无限小的正数
		if ((Math.abs(Multiply(px0, py0, px1, py1, px2, py2)) < ESP)
				&& ((px0 - px1) * (px0 - px2) <= 0)
				&& ((py0 - py1) * (py0 - py2) <= 0)) {
			flag = true;
		}
		return flag;
	}

	public static double Multiply(double px0, double py0, double px1, double py1,
			double px2, double py2) {
		return ((px1 - px0) * (py2 - py0) - (px2 - px0) * (py1 - py0));
	}

	/**
	 * 判断目标点是否在多边形内(由多个点组成)<br/>
	 *
	 * @param px
	 *            目标点的经度坐标
	 * @param py
	 *            目标点的纬度坐标
	 * @param polygonXA
	 *            多边形的经度坐标集合
	 * @param polygonYA
	 *            多边形的纬度坐标集合
	 * @return
	 */
	public static boolean isPointInPolygon(double px, double py,
			ArrayList<Double> polygonXA, ArrayList<Double> polygonYA) {
		boolean isInside = false;
		double ESP = 1e-9;
		int count = 0;
		double linePoint1x;
		double linePoint1y;
		double linePoint2x = 180;
		double linePoint2y;

		linePoint1x = px;
		linePoint1y = py;
		linePoint2y = py;

		for (int i = 0; i < polygonXA.size() - 1; i++) {
			double cx1 = polygonXA.get(i);
			double cy1 = polygonYA.get(i);
			double cx2 = polygonXA.get(i + 1);
			double cy2 = polygonYA.get(i + 1);
			// 如果目标点在任何一条线上
			if (isPointOnLine(px, py, cx1, cy1, cx2, cy2)) {
				return true;
			}
			// 如果线段的长度无限小(趋于零)那么这两点实际是重合的，不足以构成一条线段
			if (Math.abs(cy2 - cy1) < ESP) {
				continue;
			}
			// 第一个点是否在以目标点为基础衍生的平行纬度线
			if (isPointOnLine(cx1, cy1, linePoint1x, linePoint1y, linePoint2x,
					linePoint2y)) {
				// 第二个点在第一个的下方,靠近赤道纬度为零(最小纬度)
				if (cy1 > cy2)
					count++;
			}
			// 第二个点是否在以目标点为基础衍生的平行纬度线
			else if (isPointOnLine(cx2, cy2, linePoint1x, linePoint1y,
					linePoint2x, linePoint2y)) {
				// 第二个点在第一个的上方,靠近极点(南极或北极)纬度为90(最大纬度)
				if (cy2 > cy1)
					count++;
			}
			// 由两点组成的线段是否和以目标点为基础衍生的平行纬度线相交
			else if (isIntersect(cx1, cy1, cx2, cy2, linePoint1x, linePoint1y,
					linePoint2x, linePoint2y)) {
				count++;
			}
		}
		if (count % 2 == 1) {
			isInside = true;
		}

		return isInside;
	}

	/**
	 * 传感器扩大经纬度数据：纬度,范围(-90*3600*100～+90*3600*100)，转换公式(latitude=实际度*3600*100+实际分*60*100+实际秒*100)
	 * 这里将其转换回度分秒的格式
	 * @return
	 */
	public static String changeGps(Integer data){
		BigDecimal bigData = new BigDecimal(data);
		BigDecimal degree = bigData.divide(new BigDecimal(360000), 6, BigDecimal.ROUND_DOWN);
		return String.valueOf(degree);
	}


	public static void main(String[] args) {
		System.out.println(changeGps(43067142));
		//System.out.println(changeGps(10480251));
		//System.out.println(changeGps(43067156));

	}

}
