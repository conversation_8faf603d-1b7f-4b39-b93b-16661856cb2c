package com.ruoyi.common.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.xxpt.gateway.shared.api.request.*;
import com.alibaba.xxpt.gateway.shared.api.response.*;
import com.alibaba.xxpt.gateway.shared.client.http.*;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.JsonUtils;
import com.ruoyi.common.vo.DingTalkReceevers;
import com.ruoyi.common.vo.DingtalkSendCommonVO;
import com.ruoyi.common.vo.XzzfjUserInfoReq;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;



@Component("DingTalkHelper")
public class DingtalkHelper {

    private static final Logger log = LoggerFactory.getLogger(DingtalkHelper.class);

//    @Autowired
//    private ISysDeptService iSysDeptService;
//    @Autowired
//    private TXzzfjWorkNoticeMapper tXzzfjWorkNoticeMapper;
//    @Autowired
//    private TXzzfjWorkNoticePersonMapper tXzzfjWorkNoticePersonMapper;
//    @Autowired
//    private TXzzfjWorkNoticeTimelineMapper tXzzfjWorkNoticeTimelineMapper;

    //*/ 金华市城市大脑驾驶舱-获取浙政钉信息，发送工作通知
    @Value("${zzd.appKey}")
    private String appKey;
    @Value("${zzd.appSecret}")
    private String appSecret;
    @Value("${zzd.domainName}")
    private String domainName;
    @Value("${zzd.tenantId}")
    private String tenantId;
    //*/

//    //*/ 金华市城市大脑驾驶舱-浙政钉扫码登录
    @Value("${zzd1.appKey}")
    private String zzd1AppKey;
    @Value("${zzd1.appSecret}")
    private String zzd1AppSecret;
    @Value("${zzd1.domainName}")
    private String zzd1DomainName;
    @Value("${zzd1.tenantId}")
    private String zzd1TenantId;
    //*/

    private ExecutableClient executableClient = null;
    private ExecutableClient zzd;//金华市城市大脑驾驶舱-浙政钉扫码登录
    private ExecutableClient zzd1;//金华市城市大脑家海沧-浙政钉应用免登登录

//    @Autowired
//    private DingHelperMapper dingHelperMapper;
//
//    @Autowired
//    private ISysUserService sysUserService;
//
//    @Resource
//    private TWorkNoticeMapper tWorkNoticeMapper;
//    @Resource
//    private TWorkNoticeTimelineMapper tWorkNoticeTimelineMapper;

    @Value("${upload.jumpLink}")
    private String jumpLink;

    //@PostConstruct注解的方法在项目启动的时候执行这个方法，也可以理解为在spring容器启动的时候执行，可作为一些数据的常规化加载，比如数据字典之类的。
    @PostConstruct
    protected void initInfo() {
        executableClient = ExecutableClient.getInstance();//sdk包静态类 FactoryHodler ->  public static final   ExecutableClient
        JSON a = (JSON) JSON.toJSON(executableClient);

        zzd = a.toJavaObject(ExecutableClient.class);
        zzd.setAccessKey(appKey);
        zzd.setSecretKey(appSecret);
        zzd.setDomainName(domainName);
        zzd.setProtocal("https");
        zzd.init();

        zzd1 = a.toJavaObject(ExecutableClient.class);
        zzd1.setAccessKey(zzd1AppKey);
        zzd1.setSecretKey(zzd1AppSecret);
        zzd1.setDomainName(zzd1DomainName);
        zzd1.setProtocal("https");
        zzd1.init();

        executableClient = zzd;
    }

//    public Double getByMobile(String mobile) {
//        Double accountId = dingHelperMapper.selectBymobile(mobile);
//        return accountId;
//    }

    /**
     * 向浙政钉发送通知
     *
     * @param phone
     * @param msg
     */
    public void SendMsg(String phone, String msg) {
        List<String> phones = new ArrayList<>();
        phones.add(phone);
        List<DingTalkReceevers> accountIds = getByMobiles("jinhuacsdn", phones);
        List<String> lis = accountIds.stream().map(DingTalkReceevers::getAccountId).collect(Collectors.toList());
        String receiverIds = String.join(",", lis);
        String bizMsgid = com.ruoyi.common.utils.StringUtils.getUUid();
        Boolean flag = workNotification("jinhuacsdn", receiverIds, msg,
                bizMsgid);
    }

    /**
     * 调用浙政钉平台接口前设置对应的AppKey和AppSecret
     *
     * @param source
     */
    private void changeAppKey(String source) {
        if (Constants.DING_SDN_DINGOA.equals(source)) {
            executableClient = zzd1;
        } else {
            executableClient = zzd;
        }
//        executableClient = zzd;
    }

    /**
     * 根据source获取浙政钉租户id
     */
    private String getTenantId(String source) {
        if (Constants.DING_SDN_DINGOA.equals(source)) {
            return zzd1TenantId;
        } else {
            return tenantId;
        }
//        return tenantId;
    }

    @Autowired
    private RedisCache redisCache;

    /**
     * ACCESS_TOKEN 存缓存，缓存有不调用阿里云接口，看到的阿里云默认7200秒，过期首次获取比较慢
     * 可能会存在系统时间小范围内不同步,本地没有过期，实际阿里云服务器已经过期情况
     *
     * @return
     */
    public Map<String, Object> gettoken(String source) {
        changeAppKey(source);//切换秘钥和appKey

        String apiResult;
        apiResult = redisCache.getCacheObject(Constants.DING_ACCESS_TOKEN + source);
        Integer expiresIn = 60;//默认60秒
        if (StringUtils.isNotBlank(apiResult)) {
            return JsonUtils.parseJson(apiResult, Map.class);
        }
        GetClient getClient = executableClient.newGetClient("/gettoken.json");
        //设置参数
        //getClient.addParameter("appkey", appKey);
        //getClient.addParameter("appsecret", appSecret);
        //调用API
        log.info("gettokenStart");
        apiResult = getClient.get();
        log.info("gettokenEnd" + apiResult);

        System.out.println("gettoken：" + apiResult);
        JSONObject jsonObject = JSONObject.parseObject(apiResult);
        if (jsonObject != null && jsonObject.getBoolean("success")) {
            JSONObject contentObj = jsonObject.getJSONObject("content");
            JSONObject dataObj = contentObj.getJSONObject("data");
            expiresIn = dataObj.getInteger("expiresIn");//阿里云默认失效看到的是7200,下面减掉30秒
        } else {
            throw new GlobalException("获取accessToken错误");
        }

        redisCache.setCacheObject(Constants.DING_ACCESS_TOKEN + source, apiResult, Math.max(1, expiresIn.intValue() - 300), TimeUnit.SECONDS);//可能会存在系统时间小范围内不同步

        Map<String, Object> map = JsonUtils.parseJson(apiResult, Map.class);
        return map;
    }


    /**
     * 获取用户信息
     *
     * @param accessToken
     * @param code
     * @return
     */
    public Map<String, Object> getZzdUserInfo(String accessToken, String code, String source) {
        changeAppKey(source);//切换秘钥和appKey
        PostClient postClient = executableClient.newPostClient("/rpc/oauth2/dingtalk_app_user.json");
        postClient.addParameter("access_token", accessToken);
        postClient.addParameter("auth_code", code);
        String apiResult = postClient.post();
        log.info(apiResult);
        System.out.println("getOpenId：" + apiResult);
        Map<String, Object> map = JsonUtils.parseJson(apiResult, Map.class);
        return map;
    }


    /**
     * 浙政钉获取授权用户的个人信息
     *
     * @param accessToken 应用access_token
     * @param code        临时授权码code（此code非authcode）
     * @param source      哪个应用appKey
     * @return
     */
    public Map<String, Object> getuserinfo_bycode(String accessToken, String code, String source) {
        changeAppKey(source);//切换秘钥和appKey
        PostClient postClient = executableClient.newPostClient("/rpc/oauth2/getuserinfo_bycode.json");
        postClient.addParameter("access_token", accessToken);
        postClient.addParameter("code", code);
        String apiResult = postClient.post();
        log.info(apiResult);
        System.out.println("getuserinfo_bycode：" + apiResult);
        Map<String, Object> map = JsonUtils.parseJson(apiResult, Map.class);
        return map;
    }


    /**
     * 根据应用获取appkey密钥等
     * 获取accountIds
     *
     * @param phones
     * @return
     */
    public List<DingTalkReceevers> getByMobiles(String source, List<String> phones) {
        Long tenantId = Long.valueOf(getTenantId(source));//租户id
        changeAppKey(source);//切换秘钥和appKey

        IntelligentGetClient getClient = executableClient.newIntelligentGetClient("/mozi/employee/get_by_mobiles");

        OapiMoziEmployeeGetByMobilesRequest oapiMoziEmployeeGetByMobilesRequest = new OapiMoziEmployeeGetByMobilesRequest();
        //手机区号(没有特别说明，固定填写86)
        oapiMoziEmployeeGetByMobilesRequest.setAreaCode("86");
        //手机号码列表，逗号分隔，最多50个
        String p = String.join(",", phones);
        oapiMoziEmployeeGetByMobilesRequest.setMobiles(p);
        //租户ID
        oapiMoziEmployeeGetByMobilesRequest.setTenantId(tenantId);
        //账号类型（没有特别说明,固定填写local）
        oapiMoziEmployeeGetByMobilesRequest.setNamespace("local");
        OapiMoziEmployeeGetByMobilesResponse apiResult = getClient.get(oapiMoziEmployeeGetByMobilesRequest);
        log.info("getByMobilesAccountIds：" + JsonUtils.toJsonWithSerializeNulls(apiResult));
        if (!apiResult.getSuccess()) {
            throw new GlobalException("获取accountIds出错");
        }
        if (!apiResult.getContent().getSuccess()) {
            throw new GlobalException(apiResult.getContent().getResponseMessage());
        }
        JSONArray jsonArray = JSONArray.parseArray(apiResult.getContent().getData());
        List<DingTalkReceevers> accountIds = new ArrayList<>();

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = (JSONObject) jsonArray.get(i);
            DingTalkReceevers receevers = new DingTalkReceevers();
            receevers.setAccountId(jsonObject.getString("accountId"));
            receevers.setMobile(jsonObject.getString("mobile"));
           /* if(!redisCache.hasKey(DING_ACCOUNT_ID+jsonObject.getString("mobile"))){
                redisCache.setCacheObject(DING_ACCOUNT_ID + jsonObject.getString("mobile"), receevers.getAccountId(), 30, TimeUnit.DAYS);
            }*/
            accountIds.add(receevers);

        }
        return accountIds;
    }

    /**
     * 发布工作通知
     *
     * @param receiverIds 接收人
     * @param message     消息
     * @param bizMsgId    业务编号
     * @return
     */
    public boolean workNotification(String source, String receiverIds, String message, String bizMsgId) {
        String tenantId = getTenantId(source);//租户id

        changeAppKey(source);//切换秘钥和appKey
        //executableClient保证单例
        IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient("/message/workNotification");
        OapiMessageWorkNotificationRequest oapiMessageWorkNotificationRequest = new OapiMessageWorkNotificationRequest();
        //接收者的部门id列表
        //oapiMessageWorkNotificationRequest.setOrganizationCodes("字符串");
        //接收人用户ID
        oapiMessageWorkNotificationRequest.setReceiverIds(receiverIds);
        //租户ID
        oapiMessageWorkNotificationRequest.setTenantId(tenantId);
        //业务消息id
        oapiMessageWorkNotificationRequest.setBizMsgId(bizMsgId);
        //消息对象
        oapiMessageWorkNotificationRequest.setMsg("{\"msgtype\":\"text\",\"text\":{\"content\":\"" + message + "\"}}");
        //获取结果
        OapiMessageWorkNotificationResponse apiResult = intelligentGetClient.get(oapiMessageWorkNotificationRequest);

        log.info("workNotification：" + JsonUtils.toJsonWithSerializeNulls(apiResult));


        return null != apiResult && apiResult.getSuccess();
    }

//
//    /**
//     * 发布工作通知
//     *
//     * @param receiverIds 接收人
//     * @param message     消息
//     * @param bizMsgId    业务编号
//     * @return
//     */
//    public Long workNotification1(String source, List<DingTalkReceevers> receiverIds, String message, String bizMsgId, HashMap<String, List<DingtalkSendUserVO>> hashMap) {
//        changeAppKey( source);//切换秘钥和appKey
//        String tenantId = getTenantId(source);//租户id
//        TWorkNotice tWorkNotice = new TWorkNotice();
//        tWorkNotice.setMsg(message);
//        tWorkNotice.setInstructionsUuid(bizMsgId);
//        tWorkNotice.setStartTime(new Date());
//        tWorkNoticeMapper.insertTWorkNotice(tWorkNotice);
//        changeAppKey(source);//切换秘钥和appKey
//        //executableClient保证单例
//        for (DingTalkReceevers d : receiverIds) {
//            IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient("/message/workNotification");
//            String uUid = com.ruoyi.common.utils.StringUtils.getUUid();//
//            OapiMessageWorkNotificationRequest oapiMessageWorkNotificationRequest = new OapiMessageWorkNotificationRequest();
//            //接收者的部门id列表
//            //oapiMessageWorkNotificationRequest.setOrganizationCodes("字符串");
//            //接收人用户ID
//            oapiMessageWorkNotificationRequest.setReceiverIds(d.getAccountId());
//            //租户ID
//            oapiMessageWorkNotificationRequest.setTenantId(tenantId);
//            //业务消息id
//            oapiMessageWorkNotificationRequest.setBizMsgId(uUid);
//            //消息对象
//            oapiMessageWorkNotificationRequest.setMsg(getJsonWorkNotificationMsg("指令", message, jumpLink + "?id=" + tWorkNotice.getId() + "&mobile=" + d.getMobile(), jumpLink + "?id=" + tWorkNotice.getId() + "&mobile=" + d.getMobile()));
//            //获取结果
//            OapiMessageWorkNotificationResponse apiResult = intelligentGetClient.get(oapiMessageWorkNotificationRequest);
//            List<DingtalkSendUserVO> tCommandOrganizationals = hashMap.get(d.getMobile());
//            for (DingtalkSendUserVO t : tCommandOrganizationals) {
////                TWorkNoticeDetails tWorkNoticeDetails=new TWorkNoticeDetails();
////                BeanUtils.copyProperties(t,tWorkNoticeDetails);
////                tWorkNoticeDetails.setWorkNoticeId(tWorkNotice.getId());
////                tWorkNoticeDetails.setMobile(t.getPhone());
////                tWorkNoticeDetails.setSendMsgUuid(uUid);
////                tWorkNoticeDetails.setStatus(0);
////                tWorkNoticeDetailsMapper.insert(tWorkNoticeDetails);
//                TWorkNoticeTimeline tWorkNoticeTimeline = new TWorkNoticeTimeline();
//                tWorkNoticeTimeline.setWorkNoticeId(tWorkNotice.getId());
//                tWorkNoticeTimeline.setCreateTime(new Date());
//                tWorkNoticeTimeline.setMobile(t.getMobile());
//                tWorkNoticeTimeline.setStep(0);
//                tWorkNoticeTimeline.setContent("系统自动下发任务");
//                tWorkNoticeTimeline.setOffice(t.getOffice());
//                tWorkNoticeTimeline.setName(t.getName());
//                tWorkNoticeTimeline.setRole(t.getRole());
//                tWorkNoticeTimelineMapper.insertTWorkNoticeTimeline(tWorkNoticeTimeline);
//            }
//            log.info("workNotification：" + JsonUtils.toJsonWithSerializeNulls(apiResult));
//        }
//
//        return tWorkNotice.getId();
//
//
//    }

    /**
     * 发布工作通知
     *
     * @param receiverIds 接收人
     * @param bizMsgId    业务编号
     * @return
     */
    public void workNotificationCommon(String source, List<DingTalkReceevers> receiverIds, String bizMsgId, HashMap<String, List<DingtalkSendCommonVO>> hashMap) {
        source = "jinhuacsdn";
//        changeAppeky( source);//切换秘钥和appKey
        String tenantId = getTenantId(source);//租户id
        changeAppKey(source);//切换秘钥和appKey
        //executableClient保证单例
        for (DingTalkReceevers d : receiverIds) {
            List<DingtalkSendCommonVO> dingtalkSendCommonVOList = hashMap.get(d.getMobile());
            if (CollectionUtils.isEmpty(dingtalkSendCommonVOList)) {
                continue;
            }
            DingtalkSendCommonVO dingtalkSendCommonVO = dingtalkSendCommonVOList.get(0);
            IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient("/message/workNotification");
            String uUid = com.ruoyi.common.utils.StringUtils.getUUid();//
            OapiMessageWorkNotificationRequest oapiMessageWorkNotificationRequest = new OapiMessageWorkNotificationRequest();
            //接收者的部门id列表
            //oapiMessageWorkNotificationRequest.setOrganizationCodes("字符串");
            //接收人用户ID
            oapiMessageWorkNotificationRequest.setReceiverIds(d.getAccountId());
            //租户ID
            oapiMessageWorkNotificationRequest.setTenantId(tenantId);
            //业务消息id
            oapiMessageWorkNotificationRequest.setBizMsgId(uUid);
            //消息对象
            oapiMessageWorkNotificationRequest.setMsg(getJsonWorkNotificationMsg(dingtalkSendCommonVO.getTitle(), dingtalkSendCommonVO.getMsg(), dingtalkSendCommonVO.getUrl(), dingtalkSendCommonVO.getUrl()));
            //获取结果
            OapiMessageWorkNotificationResponse apiResult = intelligentGetClient.get(oapiMessageWorkNotificationRequest);

            log.info("workNotification：" + JsonUtils.toJsonWithSerializeNulls(apiResult));
        }

    }

    /**
     * 工作通知
     * json形式的
     *
     * @param title
     * @param content
     * @return
     */
    public String getJsonWorkNotificationMsg(String title, String content, String single_url, String single_pc_url) {
        String msg = "{\n" +
                "    \"msgtype\": \"action_card\",\n" +
                "    \"action_card\": {\n" +
                "        \"title\": \"" + title + "\",\n" +
                "        \"markdown\": \"" + content + "\" ,\n" +
                "        \"single_url\":\"" + single_url + "\", \n" +
                "        \"single_pc_url\":\"" + single_pc_url + "\",\n" +
//                    "        \"single_pc_url\": \"http://jkd.wjw.jinhua.gov.cn:8095/jhfy/yujingfankui/feedback.html?id="+dbId+"\" \n" +
                "        \"single_title\": \"查看详情\"\n" +
                // "        \"single_title\":\""+single_title+"\"\n" +
                "    }\n" +
                "}";
        return msg;
    }

//    /**
//     * 同步浙政钉用户体系通讯录
//     * @param source 浙政钉源
//     * @param tmpTenantId 租户id
//     * @param deptId 部门表组织架构根节点id
//     */
//    public void syncZzdUserData(String source, Long tmpTenantId, Long deptId) {
//        log.info("========= syncZzdUserData start =========");
//        changeAppKey(source);
//        Map<String, Object> organizationMap = getScopes(tmpTenantId);
//        List<String> organizationCodes = (List<String>) organizationMap.get("deptVisibleScopes");
//        for (String organizationCode : organizationCodes) {
//            List<String> orgCodeList = new ArrayList<>();
//            orgCodeList.add(organizationCode);
//            List<Map> orgDetails = getOrganizationDetails(orgCodeList, tmpTenantId);
//            String orgName = (String) orgDetails.get(0).get("organizationName");
//
//            SysDept sysDept = new SysDept();
//            sysDept.setParentId(deptId);
//            sysDept.setDeptName(orgName);
//            String orderNum = String.valueOf(organizationCodes.indexOf(organizationCode));
//            sysDept.setOrderNum(orderNum);
//
//            // 判断该组织是否为叶子节点
//            boolean isLeaf = (boolean) orgDetails.get(0).get("leaf");
//
//            // 判断该部门是否已存在
//            boolean isExist = UserConstants.NOT_UNIQUE.equals(iSysDeptService.checkDeptNameUnique(sysDept));
//
//            if (isExist) {
//                 List<SysDept> list = iSysDeptService.selectDeptList(sysDept);
//                 Long deptId1 = list.get(0).getDeptId();
//                 if (!isLeaf) {
//                     getOrganizationTree(organizationCode, orgName, tmpTenantId, deptId1);
//                 } else {
//                     syncAndUpdateSysUser(organizationCode, orgName, deptId1, tmpTenantId);
//                 }
//            } else {
//                int result = iSysDeptService.insertDept(sysDept);
//                if (result == 1) {
//                    Long deptId1 = sysDept.getDeptId();
//                    if (!isLeaf) {
//                        getOrganizationTree(organizationCode, orgName, tmpTenantId, deptId1);
//                    } else {
//                        syncAndUpdateSysUser(organizationCode, orgName, deptId1, tmpTenantId);
//                    }
//                }
//            }
//        }
//        log.info("========= syncZzdUserData finish =========");
//    }

    /**
     * 获取应用通讯录范围
     *
     * @param tenantId 租户id
     * @return
     */
    private Map<String, Object> getScopes(Long tenantId) {
        //executableClient保证单例
        IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient("/auth/scopesV2");
        OapiAuthScopesV2Request oapiAuthScopesV2Request = new OapiAuthScopesV2Request();
        //租户ID
        oapiAuthScopesV2Request.setTenantId(tenantId);
        //获取结果
        OapiAuthScopesV2Response apiResult = intelligentGetClient.get(oapiAuthScopesV2Request);
        log.info(String.valueOf(apiResult));
        Map<String, Object> map = (Map<String, Object>) JsonUtils.parseJson(apiResult.getContent(), Map.class);
        return map;
    }

    /**
     * 根据组织编码获取组织下人员信息
     *
     * @param organizationCode 组织代码
     * @param tenantId 租户id
     * @param pageNo 查询结果起始页
     * @return
     */
    private List<Map> getOrganizationEmployees(String organizationCode, Long tenantId, Integer pageNo) {
        //executableClient保证单例
        IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient("/mozi/organization/pageOrganizationEmployeePositions");
        OapiMoziOrganizationPageOrganizationEmployeePositionsRequest oapiMoziOrganizationPageOrganizationEmployeePositionsRequest = new OapiMoziOrganizationPageOrganizationEmployeePositionsRequest();
        //是否请求总数，默认是false
        oapiMoziOrganizationPageOrganizationEmployeePositionsRequest.setReturnTotalSize(true);
        //分页大小，默认是20，最大100
//        oapiMoziOrganizationPageOrganizationEmployeePositionsRequest.setPageSize(100);
        //员工状态，A为有效，F为无效，默认是所有
        oapiMoziOrganizationPageOrganizationEmployeePositionsRequest.setEmployeeStatus("A");
        //组织code
        oapiMoziOrganizationPageOrganizationEmployeePositionsRequest.setOrganizationCode(organizationCode);
        //请求起始页，默认是1
        oapiMoziOrganizationPageOrganizationEmployeePositionsRequest.setPageNo(pageNo);
        //租户id
        oapiMoziOrganizationPageOrganizationEmployeePositionsRequest.setTenantId(tenantId);
        //获取结果
        OapiMoziOrganizationPageOrganizationEmployeePositionsResponse apiResult = intelligentGetClient.get(oapiMoziOrganizationPageOrganizationEmployeePositionsRequest);
        List<Map> mapList = JsonUtils.parseJsonArray(apiResult.getContent().getData(), Map.class);
        return mapList;
    }

//    /**
//     * 更新用户表
//     *
//     * @param sysUser
//     */
//    private void insertOrUpdateSysUser(SysUser sysUser) {
//        SysUser user = sysUserService.selectUserByEmployeeCode(sysUser.getEmployeeCode());
//        if (user != null) {
//            user.setNickName(sysUser.getNickName());
//            user.setSex(sysUser.getSex());
//            user.setDeptId(sysUser.getDeptId());
//            sysUserService.updateUser(user);
//        } else {
//            String username = getStringRandom(5);
//            sysUser.setUserName(username);
//            sysUserService.insertUser(sysUser);
//        }
//    }

    /**
     * 生成随机用户名
     *
     * @param length
     * @return
     */
    public static String getStringRandom(int length) {
        String val = "";
        Random random = new Random();

        //参数length，表示生成几位随机数
        for (int i = 0; i < length; i++) {
            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            //输出字母还是数字
            if ("char".equalsIgnoreCase(charOrNum)) {
                //输出是大写字母还是小写字母
                int temp = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val += (char) (random.nextInt(26) + temp);
            } else if ("num".equalsIgnoreCase(charOrNum)) {
                val += String.valueOf(random.nextInt(10));
            }
        }
        return val;
    }

    /**
     * 将浙政钉性别值转换成若依用户表中性别值
     *
     * @param originSex 浙政钉用户体系性别值 0：未知；1：男；2：女
     * @return 若依用户表性别值 0：男；1：女；2：未知
     */
    private int convertSexValue(Integer originSex) {
        int zzdSex = 0;
        switch (originSex) {
            case 0:
                zzdSex = 2;
                break;
            case 1:
                zzdSex = 0;
                break;
            case 2:
                zzdSex = 1;
                break;
            default:
                break;
        }
        return zzdSex;
    }

    /**
     * 根据组织code查询子组织code
     *
     * @param organizationCode 组织代码
     * @param tenantId 租户id
     * @return
     */
    public List<String> getChildrenOrganizationCodes(String organizationCode, Long tenantId) {
        //executableClient保证单例
        IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient("/mozi/organization/pageSubOrganizationCodes");
        OapiMoziOrganizationPageSubOrganizationCodesRequest oapiMoziOrganizationPageSubOrganizationCodesRequest = new OapiMoziOrganizationPageSubOrganizationCodesRequest();
        oapiMoziOrganizationPageSubOrganizationCodesRequest.setReturnTotalSize(true);
        oapiMoziOrganizationPageSubOrganizationCodesRequest.setPageSize(100);
        oapiMoziOrganizationPageSubOrganizationCodesRequest.setOrganizationCode(organizationCode);
        oapiMoziOrganizationPageSubOrganizationCodesRequest.setPageNo(1);
//        oapiMoziOrganizationPageSubOrganizationCodesRequest.setStatus("字符串");
        oapiMoziOrganizationPageSubOrganizationCodesRequest.setTenantId(tenantId);
        //获取结果
        OapiMoziOrganizationPageSubOrganizationCodesResponse apiResult = intelligentGetClient.get(oapiMoziOrganizationPageSubOrganizationCodesRequest);
        List<String> list = JsonUtils.parseJsonArray(apiResult.getContent().getData(), String.class);
        return list;
    }

    /**
     * 获取组织详情信息
     * @param organizationCodes 组织代码列表
     * @param tenantId 租户id
     * @return
     */
    public List<Map> getOrganizationDetails(List<String> organizationCodes, Long tenantId) {
        //executableClient保证单例
        IntelligentPostClient intelligentPostClient = executableClient.newIntelligentPostClient("/mozi/organization/listOrganizationsByCodes");
        OapiMoziOrganizationListOrganizationsByCodesRequest oapiMoziOrganizationListOrganizationsByCodesRequest = new OapiMoziOrganizationListOrganizationsByCodesRequest();
        oapiMoziOrganizationListOrganizationsByCodesRequest.setOrganizationCodes(organizationCodes);
        oapiMoziOrganizationListOrganizationsByCodesRequest.setTenantId(tenantId);
        //获取结果
        OapiMoziOrganizationListOrganizationsByCodesResponse apiResult = intelligentPostClient.post(oapiMoziOrganizationListOrganizationsByCodesRequest);
        List<Map> mapList = JsonUtils.parseJsonArray(apiResult.getContent().getData(), Map.class);
        return mapList;
    }

//    /**
//     * 遍历浙政钉组织架构和用户体系
//     * @param organizationCode 组织代码
//     * @param organizationName 组织名称
//     * @param tenantId 租户id
//     */
//    private void getOrganizationTree(String organizationCode, String organizationName, Long tenantId, Long deptParentId) {
//        List<String> childrenOrganizationCodes = getChildrenOrganizationCodes(organizationCode, tenantId);
//        Iterator<String> iterator = childrenOrganizationCodes.iterator();
//        while (iterator.hasNext()) {
//            String orgCode = iterator.next();
//            List<String> orgCodeList = new ArrayList<>();
//            orgCodeList.add(orgCode);
//            List<Map> orgDetails = getOrganizationDetails(orgCodeList, tenantId);
//            boolean isLeaf = (boolean) orgDetails.get(0).get("leaf");
////            String orgName = organizationName + "/" + orgDetails.get(0).get("organizationName");
//            String orgName = (String) orgDetails.get(0).get("organizationName");
//
//            SysDept sysDept = new SysDept();
//            sysDept.setParentId(deptParentId);
//            sysDept.setDeptName(orgName);
//            String orderNum = String.valueOf(childrenOrganizationCodes.indexOf(orgCode));
//            sysDept.setOrderNum(orderNum);
//
//            boolean isExist = UserConstants.NOT_UNIQUE.equals(iSysDeptService.checkDeptNameUnique(sysDept));
//            if (isExist) {
//                List<SysDept> list = iSysDeptService.selectDeptList(sysDept);
//                Long deptId = list.get(0).getDeptId();
//                if (!isLeaf) {
//                    getOrganizationTree(orgCode, orgName, tenantId, deptId);
//                } else {
//                    syncAndUpdateSysUser(orgCode, orgName, deptId, tenantId);
//                }
//            } else {
//                int result = iSysDeptService.insertDept(sysDept);
//                if (result == 1) {
//                    Long deptId = sysDept.getDeptId();
//                    if (!isLeaf) {
//                        getOrganizationTree(orgCode, orgName, tenantId, deptId);
//                    } else {
//                        syncAndUpdateSysUser(orgCode, orgName, deptId, tenantId);
//                    }
//                }
//            }
//        }
//    }

//    /**
//     * 获取组织下人员信息详情并且更新用户表
//     * @param organizationCode 组织代码
//     * @param organizationName 组织名称
//     * @param deptId 部门id
//     * @param tenantId 租户id
//     */
//    private void syncAndUpdateSysUser(String organizationCode, String organizationName, Long deptId, Long tenantId) {
//        List<Map> mapList = new ArrayList<>();
//        Integer pageNo = 0;
//        do {
//            pageNo++;
//            mapList = getOrganizationEmployees(organizationCode, tenantId, pageNo);
//            if (mapList != null && !mapList.isEmpty()) {
//                for (Map map : mapList) {
//                    SysUser sysUser = new SysUser();
//                    String employeeName = (String) map.get("employeeName");
//                    String employeeCode = (String) map.get("employeeCode");
//                    String gender = String.valueOf(convertSexValue(Integer.parseInt(map.get("empGender").toString())));
//                    sysUser.setNickName(employeeName);
//                    sysUser.setEmployeeCode(employeeCode);
//                    sysUser.setSex(gender);
//                    sysUser.setDeptId(deptId);
//                    insertOrUpdateSysUser(sysUser);
//                    log.info(organizationName + ", " + employeeName + ", " + employeeCode);
//                }
//            }
//        } while (mapList != null && mapList.size() == 20);
//    }

    /**
     * 批量根据员工Code获取员工账号ID
     * @param employeeCodes 员工code
     * @param tenantId 租户ID
     */
    public List<String> getAccountIdsByEmployeeCodes(String[] employeeCodes, Long tenantId) {
        String api ="/mozi/employee/listEmployeeAccountIds";
        PostClient postClient = executableClient.newPostClient(api);
        //设置参数
        for (String code : employeeCodes) {
            postClient.addParameter("employeeCodes", code);
        }
        postClient.addParameter("tenantId", String.valueOf(tenantId));
        //调用API
        String apiResult = postClient.post();
        JSONObject result = JSONObject.parseObject(apiResult);
        JSONArray data = (JSONArray) ((JSONObject) result.get("content")).get("data");
        List<String> accountIds = new ArrayList<>();
        for (Object jsonObject: data) {
            JSONObject object = JSONObject.parseObject(jsonObject.toString());
            String accountId = String.valueOf(object.get("accountId"));
            accountIds.add(accountId);
        }
        return accountIds;
    }

    /**
     * 批量根据员工Code获取员工账号ID
     * @param employeeCodes 员工code
     * @param source
     */
    public List<String> getAccountIdsByEmployeeCodes(List<String> employeeCodes, String source) {
        Long tenantId = Long.valueOf(getTenantId(source));//租户id
        changeAppKey(source);//切换秘钥和appKey
        String[] integers = employeeCodes.toArray(new String[employeeCodes.size()]);
        return getAccountIdsByEmployeeCodes(integers,tenantId);
    }

    public List<DingTalkReceevers> getAccountIdsByEmployeeCodes1(List<String> employeeCodes, String source) {
        Long tenantId = Long.valueOf(getTenantId(source));//租户id
        changeAppKey(source);//切换秘钥和appKey
        String[] integers = employeeCodes.toArray(new String[employeeCodes.size()]);
        return getAccountIdsByEmployeeCodes1(integers,tenantId);
    }

    /**
     * 批量根据员工Code获取员工账号ID
     * @param employeeCodes 员工code
     * @param tenantId 租户ID
     */
    public List<DingTalkReceevers> getAccountIdsByEmployeeCodes1(String[] employeeCodes, Long tenantId) {
        String api ="/mozi/employee/listEmployeeAccountIds";
        PostClient postClient = executableClient.newPostClient(api);
        //设置参数
        for (String code : employeeCodes) {
            postClient.addParameter("employeeCodes", code);
        }
        postClient.addParameter("tenantId", String.valueOf(tenantId));
        //调用API
        String apiResult = postClient.post();
        JSONObject result = JSONObject.parseObject(apiResult);
        JSONArray data = (JSONArray) ((JSONObject) result.get("content")).get("data");
        List<DingTalkReceevers> accountIds = new ArrayList<>();
        for (Object jsonObject: data) {
            DingTalkReceevers dingTalkReceevers=new DingTalkReceevers();
            JSONObject object = JSONObject.parseObject(jsonObject.toString());
            String accountId = String.valueOf(object.get("accountId"));
            String employeeCode = String.valueOf(object.get("employeeCode"));
            dingTalkReceevers.setAccountId(accountId);
            dingTalkReceevers.setMobile(employeeCode);
            accountIds.add(dingTalkReceevers);
        }
        return accountIds;
    }

//    /**
//     * 行政执法局发布工作通知
//     * @param req 接收消息
//     * @return
//     */
//    public boolean workNotification1(XzzfjDingtalkSendMessageReq req) {
//        String tenantId=getTenantId(req.getSource());//租户id
//        String bizMsgid= com.ruoyi.common.utils.StringUtils.getUUid();
//        TXzzfjWorkNotice tWorkNotice=new TXzzfjWorkNotice();
//        tWorkNotice.setMsg(req.getMsg());
//        tWorkNotice.setInstructionsUuid(bizMsgid);
//        tWorkNotice.setStartTime(new Date());
//        tWorkNotice.setLevel(req.getLevel());
//        tWorkNotice.setStatus(0L);
//        tWorkNotice.setTaskSource(req.getTaskSource());
//        tWorkNotice.setDataType(req.getDataType());
//        tWorkNotice.setDetailsId(req.getDetailsId());
//        tWorkNotice.setArea(req.getArea());
//        tWorkNotice.setParentId(req.getParentId());
//        tWorkNotice.setTitle(req.getMsg());
//        if (StringUtils.isEmpty(req.getTaskNo())){
//            CityEnum cityEnum = CityEnum.find(req.getArea());
//            String s = DateUtils.dateTimeNow(DateUtils.YYYYMMDD);
//            if (cityEnum!=null){
//                List<String> list = tXzzfjWorkNoticeMapper.selectTaskNo(cityEnum.getCode() + s);
//                if (!CollectionUtils.isEmpty(list)){
//                    List<String> collect = list.stream().filter(x -> x.substring(x.length() - 1).equals("1")).collect(Collectors.toList());
//                    if (!CollectionUtils.isEmpty(collect)){
//                        String s1 = collect.get(collect.size() - 1);
//                        String substring1 = s1.substring(14, 18);
//                        Integer integer = Integer.parseInt(substring1 )+ 1;
//                        String code = String.format("%04d",integer);
//                        tWorkNotice.setTaskNo(cityEnum.getCode() + s+code+1);
//                    }
//                }
//                tWorkNotice.setTaskNo(com.ruoyi.common.utils.StringUtils.isEmpty(tWorkNotice.getTaskNo())?cityEnum.getCode() + s+"00011":tWorkNotice.getTaskNo());
//            }
//        }else {
//            tWorkNotice.setTaskNo(req.getTaskNo());
//        }
//        tXzzfjWorkNoticeMapper.insertTXzzfjWorkNotice(tWorkNotice);
//        changeAppKey(req.getSource());//切换秘钥和appKey
//        //executableClient保证单例
//        for (XzzfjUserInfoReq d:req.getList()){
//            if (StringUtils.isBlank(d.getAccountId())){
//                continue;
//            }
//            IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient("/message/workNotification");
//            String uUid= com.ruoyi.common.utils.StringUtils.getUUid();//
//            OapiMessageWorkNotificationRequest oapiMessageWorkNotificationRequest = new OapiMessageWorkNotificationRequest();
//            //接收者的部门id列表
//            //oapiMessageWorkNotificationRequest.setOrganizationCodes("字符串");
//            //接收人用户ID
//            oapiMessageWorkNotificationRequest.setReceiverIds(d.getAccountId());
//            //租户ID
//            oapiMessageWorkNotificationRequest.setTenantId( tenantId);
//            //业务消息id
//            oapiMessageWorkNotificationRequest.setBizMsgId(uUid);
//            //消息对象
//            oapiMessageWorkNotificationRequest.setMsg(getJsonWorkNotificationMsg("金华市行政执法指挥中心",req.getMsg(),req.getUrl()+"?id="+tWorkNotice.getId()+"&mobile="+d.getCodeAndPhone(),req.getUrl()+"?id="+tWorkNotice.getId()+"&mobile="+d.getCodeAndPhone()));
//            //获取结果
//            OapiMessageWorkNotificationResponse apiResult = intelligentGetClient.get(oapiMessageWorkNotificationRequest);
//            TXzzfjWorkNoticePerson tXzzfjWorkNoticePerson=new TXzzfjWorkNoticePerson();
//            tXzzfjWorkNoticePerson.setNoticeId(tWorkNotice.getId());
//            tXzzfjWorkNoticePerson.setName(d.getName());
//            tXzzfjWorkNoticePerson.setCodePhone(d.getCodeAndPhone());
//            tXzzfjWorkNoticePersonMapper.insertTXzzfjWorkNoticePerson(tXzzfjWorkNoticePerson);
//            log.info("workNotification：" + JsonUtils.toJsonWithSerializeNulls(apiResult));
//        }
//        TXzzfjWorkNoticeTimeline tXzzfjWorkNoticeTimeline=new TXzzfjWorkNoticeTimeline();
//        tXzzfjWorkNoticeTimeline.setName("系统");
//        tXzzfjWorkNoticeTimeline.setContent(StringUtils.isEmpty(req.getDeptName())?"系统下发任务":req.getDeptName()+"下发任务");
//        tXzzfjWorkNoticeTimeline.setType(1);
//        tXzzfjWorkNoticeTimeline.setWorkNoticeId(tWorkNotice.getId());
//        tXzzfjWorkNoticeTimeline.setCreateTime(new Date());
//        tXzzfjWorkNoticeTimelineMapper.insertTXzzfjWorkNoticeTimeline(tXzzfjWorkNoticeTimeline);
//        return true;
//
//
//    }

    public OapiMessageWorkNotificationResponse sendWorkNotice(XzzfjUserInfoReq d){
        IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient("/message/workNotification");
        String uUid= com.ruoyi.common.utils.StringUtils.getUUid();//
        OapiMessageWorkNotificationRequest oapiMessageWorkNotificationRequest = new OapiMessageWorkNotificationRequest();
        //接收者的部门id列表
        //oapiMessageWorkNotificationRequest.setOrganizationCodes("字符串");
        //接收人用户ID
        oapiMessageWorkNotificationRequest.setReceiverIds(d.getAccountId());
        //租户ID
        oapiMessageWorkNotificationRequest.setTenantId( tenantId);
        //业务消息id
        oapiMessageWorkNotificationRequest.setBizMsgId(uUid);
        //消息对象
        oapiMessageWorkNotificationRequest.setMsg(getJsonWorkNotificationMsg(d.getTitle(),d.getMsg(),d.getUrl()+"?id="+d.getId()+"&mobile="+d.getCodeAndPhone(),d.getUrl()+"?id="+d.getId()+"&mobile="+d.getCodeAndPhone()));
        //获取结果
        OapiMessageWorkNotificationResponse apiResult = intelligentGetClient.get(oapiMessageWorkNotificationRequest);
        return apiResult;
    }

    /**
     * 根据手机号获取用户employeeCode（用于扫码登录）
     * @param phones
     * @return
     */
    public String getemployeeCodeByPhone(String phones) {
        Long tenantIdL = Long.valueOf(tenantId);//租户id
        String employeeCode = "";
        changeAppKey(null);//切换秘钥和appKey

        IntelligentGetClient getClient = executableClient.newIntelligentGetClient("/mozi/employee/get_by_mobiles");

        OapiMoziEmployeeGetByMobilesRequest oapiMoziEmployeeGetByMobilesRequest = new OapiMoziEmployeeGetByMobilesRequest();
        //手机区号(没有特别说明，固定填写86)
        oapiMoziEmployeeGetByMobilesRequest.setAreaCode("86");
        //手机号码列表，逗号分隔，最多50个
        String p = String.join(",", phones);
        oapiMoziEmployeeGetByMobilesRequest.setMobiles(p);
        //租户ID
        oapiMoziEmployeeGetByMobilesRequest.setTenantId(tenantIdL);
        //账号类型（没有特别说明,固定填写local）
        oapiMoziEmployeeGetByMobilesRequest.setNamespace("local");
        OapiMoziEmployeeGetByMobilesResponse apiResult = getClient.get(oapiMoziEmployeeGetByMobilesRequest);
//       log.info("返回信息："+apiResult.getMessage());
//       log.info("返回信息："+apiResult.getContent().getData());
//       log.info("返回信息："+apiResult.getSuccess());
//       log.info("返回信息："+apiResult.getCode());
//       log.info("返回信息："+apiResult.getBizErrorCode());
//       log.info("返回信息："+apiResult.getContent().getSuccess());
//       log.info("返回信息："+apiResult.getContent().getResponseMessage());
//       log.info("返回信息："+apiResult.getContent().getResponseCode());
        if (!apiResult.getSuccess()) {
            throw new GlobalException(apiResult.getMessage());
//            return employeeCode;
        }
        if (!apiResult.getContent().getSuccess()) {
            throw new GlobalException(apiResult.getContent().getResponseMessage());
//            return employeeCode;
        }
        JSONArray jsonArray = JSONArray.parseArray(apiResult.getContent().getData());
        List<DingTalkReceevers> accountIds = new ArrayList<>();
        JSONObject jsonObject = (JSONObject) jsonArray.get(0);
        employeeCode = (String) jsonObject.get("employeeCode");
        return employeeCode;
    }
}

