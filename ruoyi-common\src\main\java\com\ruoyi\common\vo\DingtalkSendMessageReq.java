package com.ruoyi.common.vo;

import java.util.List;

public class DingtalkSendMessageReq {

    List<String> phones;//要发送的手机号集合

    Long tenantId;//租户id、

    Long accountId;//账号id
    /**
     * 应用来源，什么应用发这个消息通知
     */
    private String source;
    /**
     * 员工code
     */
    List<String> code;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    private String msg;//消息

    public List<String> getPhones() {
        return phones;
    }

    public void setPhones(List<String> phones) {
        this.phones = phones;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public List<String> getCode() {
        return code;
    }

    public void setCode(List<String> code) {
        this.code = code;
    }
}
