package com.ruoyi.modules.vaccine.service;

import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.user.dao.SysUploadFileDao;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.service.SysUploadFileService;
import com.ruoyi.modules.vaccine.dao.VaccineDao;
import com.ruoyi.modules.vaccine.entity.Vaccine;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 10:52
 * @version: 1.0
 **/
@Service
public class VaccineService extends BaseService<VaccineDao, Vaccine> {

    @Autowired
    SysUploadFileService uploadFileService;
    @Autowired
    SysUploadFileDao uploadFileDao;
    public PageInfo<Vaccine> getPageList(Vaccine entity){
        PageHelper.startPage(entity.getPageNum(),entity.getPageSize());
        List<Vaccine> list=getList(entity);
        for(Vaccine v:list){
            SysUploadFile sysUploadFile = new SysUploadFile();
            sysUploadFile.setInstanceId(v.getId());
            v.setUploadFileList(uploadFileService.getList(sysUploadFile));
        }
        PageInfo<Vaccine> pageInfo = new PageInfo<Vaccine>(list);
        return pageInfo;
    }
    @Override
    public void saveOrUpdate(Vaccine entity) {
        super.saveOrUpdate(entity);
        uploadFileService.delByInstanceAndModel(entity.getId(), "");
        if (entity.getUploadFileStr() != null && !"".equals(entity.getUploadFileStr())) {
            List<SysUploadFile> list = JSON.parseArray(entity.getUploadFileStr(), SysUploadFile.class);
            List<SysUploadFile> result = new ArrayList<>();
            for (SysUploadFile s : list) {
                s.preInsert();
                s.setInstanceId(entity.getId());
                result.add(s);
            }
            uploadFileDao.saveAllList(result);
        }
    }

    public Vaccine getById(String id){
        Vaccine vaccine = dao.getById(id);
        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.setInstanceId(id);
        vaccine.setUploadFileList(uploadFileService.getList(sysUploadFile));
        return vaccine;
    }

    public void delete(Vaccine vaccine){
        dao.delete(vaccine);
        uploadFileService.delByInstanceAndModel(vaccine.getId(), "");
    }

    public void updateStatus(Vaccine vaccine){
        dao.updateStatus(vaccine);
    }



    public List<Vaccine> getAllList(Vaccine vaccine){
        vaccine.setPageSize(null);
        vaccine.setPageNum(null);
        return dao.getAllList(vaccine);
    }

    public List<Vaccine> queryUserVaccine(Vaccine vaccine){
       return dao.queryUserVaccine(vaccine);
    }

    public void deleteByQualifyId(String qualifyId) {
        dao.deleteByQualifyId(qualifyId);
    }

    public List<String> getDistinctList() {
        return dao.getDistinctList();
    }
}
