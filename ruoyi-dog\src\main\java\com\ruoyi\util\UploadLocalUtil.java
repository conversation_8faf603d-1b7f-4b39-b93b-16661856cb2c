package com.ruoyi.util;

import com.ruoyi.common.config.RuoYiConfig;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import javax.imageio.ImageIO;
import java.io.*;
import java.net.URLEncoder;
import java.util.Date;

/**
 * <AUTHOR>
 * @创建时间 2022/1/10
 * @作用：上传附件工具类
 */
public class UploadLocalUtil {

    /**
     * yml配置中目录
     */
    public static String uploadFilePath = "D://";

    public static String getUploadFilePath() {
        return uploadFilePath;
    }

    public static void setUploadFilePath(String uploadFilePath) {
        UploadLocalUtil.uploadFilePath = uploadFilePath;
    }

    /**
     * 创建单个文件
     *
     * @param file springboot上传的附件
     * @return 如果创建成功，创建文件的相对目录： /年月日/文件名
     */
    public static String uploadFile(MultipartFile file) {
        // 获取文件名
        String fileName = file.getOriginalFilename();
//        System.out.println("上传的文件名为：" + fileName);
        // 获取文件的后缀名
        String suffixName = fileName.substring(fileName.lastIndexOf("."));
//        System.out.println("上传的后缀名为：" + suffixName);
        // 上传后文件名： 年月日时分秒+6位随机数  防止文件名重复
        int numStr = (int) ((Math.random() * 9 + 1) * 100000);
        String uploadFileName = DateUtil.format(new Date(), "yyyyMMddHHmmss") + numStr;
        // 目录及文件名 拼接  相对路径，结果返回（数据库存储 resultPath）
        String today = DateUtil.format(new Date(), "yyyyMMdd");
        String resultPath = "/" + today + "/" + uploadFileName + "/" + uploadFileName + suffixName;
        // 文件上传后的全路径
        // 上传文件路径
        String filePath = RuoYiConfig.getUploadPath();
        String fullFilePath = filePath + resultPath;
        System.out.println("上传后的文件名：" + fullFilePath);
        File dest = new File(fullFilePath);
        // 检测是否存在目录  目录不存在则创建目录
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdirs();
        }
        try {
            file.transferTo(dest);
            return resultPath;
        } catch (IllegalStateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 创建单个文件
     *
     * @return 如果创建成功，创建文件的相对目录： /年月日/文件名
     */
    public static String uploadFile(byte[] imageBytes) {
//        System.out.println("上传的后缀名为：" + suffixName);
        // 上传后文件名： 年月日时分秒+6位随机数  防止文件名重复
        int numStr = (int) ((Math.random() * 9 + 1) * 100000);
        String uploadFileName = DateUtil.format(new Date(), "yyyyMMddHHmmss") + numStr;
        // 目录及文件名 拼接  相对路径，结果返回（数据库存储 resultPath）
        String today = DateUtil.format(new Date(), "yyyyMMdd");
        String resultPath = today + "/" + uploadFileName + "/" + uploadFileName + ".png";
        // 文件上传后的全路径
        String fullFilePath = uploadFilePath + resultPath;
//        System.out.println("上传后的文件名：" + fullFilePath);
        File dest = new File(fullFilePath);
        // 检测是否存在目录  目录不存在则创建目录
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdirs();
        }

        try {
            FileOutputStream fileOutputStream = new FileOutputStream(dest);

            fileOutputStream.write(imageBytes);

            return resultPath;
        } catch (IllegalStateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 下载附件工具类
     *
     * @param path     文件相对路径
     * @param outName  导出文件名(为null或空时默认为fullPath文件名)
     * @param request  请求
     * @param response 响应
     */
    public static void downLoadFile(String path, String outName, HttpServletRequest request, HttpServletResponse response, String type) {
        String filePath = RuoYiConfig.getUploadPath();
        String fullPath = filePath + path;
        File file = new File(fullPath);
        // 输出得文件名
        String fileName = file.getName();
        if (outName != null && !outName.equals("")) {
            // 获取文件的后缀名
            String suffixName = fileName.substring(fileName.lastIndexOf("."));
            // 修改输出文件名
            fileName = outName + suffixName;
        }
        // 读到流中
        InputStream inStream = null;// 文件的存放路径
        try {
            inStream = new FileInputStream(file);
            if (request.getHeader("User-Agent").toLowerCase().indexOf("firefox") > 0) {
                fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1"); // firefox浏览器
            } else if (request.getHeader("User-Agent").toUpperCase().indexOf("MSIE") > 0) {
                fileName = URLEncoder.encode(fileName, "UTF-8");// IE浏览器
            } else if (request.getHeader("User-Agent").toUpperCase().indexOf("CHROME") > 0) {
                fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");// 谷歌
            } else {
                fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
            }
            // 设置输出的格式
            response.reset();
            if ("downLoad".equals(type)) {
                response.setContentType("application/x-download");
            } else {
                response.setContentType("application/octet-stream");
            }
            response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            // 循环取出流中的数据
            byte[] b = new byte[1024];
            BufferedInputStream bis = new BufferedInputStream(inStream);
            int len;
            try {
                while ((len = bis.read(b)) > 0) {
                    response.getOutputStream().write(b, 0, len);
                }
                inStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } finally {
            if (inStream != null) {
                try {
                    inStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }

    /**
     * 下载附件工具类
     *
     * @param path     文件相对路径
     * @param request  请求
     * @param response 响应
     */
    public static void downLoadFile(String path, HttpServletRequest request, HttpServletResponse response,String type) {
        String outName = request.getParameter("outName");
        downLoadFile(path, outName, request, response,type);
    }

}
