package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.InstructionGroup;
import com.ruoyi.instruction.service.IIndicatorTypeService;
import com.ruoyi.instruction.service.IInstrucationPersonService;
import com.ruoyi.instruction.service.IInstructionGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 群体基本信息Controller
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/instruction/group")
public class InstructionGroupController extends BaseController {

    @Autowired
    private IInstructionGroupService instructionGroupService;

    @Autowired
    private IIndicatorTypeService indicatorTypeService;

    @Autowired
    private IInstrucationPersonService personService;

    /**
     * 查询群体基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:group:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionGroup instructionGroup) {
        startPage();
        List<InstructionGroup> list = instructionGroupService.selectInstructionGroupList(instructionGroup);

        if (list != null && list.size() > 0) {
            for (InstructionGroup group : list) {
                //所有人员ids
                group.setPersonCount(0);
                if (group.getAllPersonIds().length() > 0) {
                    List<String> collect = Arrays.stream(group.getAllPersonIds().split(",")).collect(Collectors.toList());
                    //查询人员数量
                    int count = personService.findPeronCountById(collect);
                    group.setPersonCount(count);
                }

            }
        }

        return getDataTable(list);
    }


    /**
     * 导出群体基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:group:export')")
    @Log(title = "群体基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionGroup instructionGroup) {
        List<InstructionGroup> list = instructionGroupService.selectInstructionGroupList(instructionGroup);
        ExcelUtil<InstructionGroup> util = new ExcelUtil<InstructionGroup>(InstructionGroup.class);
        util.exportExcel(response, list, "群体基本信息数据");
    }

    /**
     * 获取群体基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:group:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(instructionGroupService.selectInstructionGroupById(id));
    }

    /**
     * 新增群体基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:group:add')")
    @Log(title = "群体基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionGroup instructionGroup) {
        return instructionGroupService.insertInstructionGroup(instructionGroup);
    }

    /**
     * 修改群体基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:group:edit')")
    @Log(title = "群体基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionGroup instructionGroup) {
        return toAjax(instructionGroupService.updateInstructionGroup(instructionGroup));
    }

    /**
     * 删除群体基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:group:remove')")
    @Log(title = "群体基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(instructionGroupService.deleteInstructionGroupByIds(ids));
    }

    /**
     * 查询群体基本信息列表
     */
    @GetMapping("/getGroupList")
    public AjaxResult getGroupList(InstructionGroup instructionGroup) {
        List<InstructionGroup> list = instructionGroupService.selectInstructionGroupList(instructionGroup);
        return AjaxResult.success(list);
    }

    /**
     * 下载群体导入模板
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<InstructionGroup> util = new ExcelUtil<InstructionGroup>(InstructionGroup.class);
        util.importTemplateExcel(response, "群体导入模板");
    }



    /**
     * 导入事件数据
     */
    @Log(title = "导入事件数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<InstructionGroup> util = new ExcelUtil<InstructionGroup>(InstructionGroup.class);
        List<InstructionGroup> list = util.importExcel(file.getInputStream());
        //获取操作用户
        String operName = getUsername();
        //存入数据
        String msg = instructionGroupService.importData(list, operName);
        return AjaxResult.success(msg);
    }
}
