package com.ruoyi.util;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Administrator on 2022-2-23.
 */
public class Tset  {
    public static void main(String[] args) throws IOException {
        BufferedReader bf = new BufferedReader(new FileReader("D://test//sequence.txt"));
        String str;
        // 按行读取字符串
        boolean newDataFlag = false;
        boolean zzFlag = false;
        boolean titleFlag = false;
        List<String> list = new ArrayList<>();
        String zz = "";
        while ((str = bf.readLine()) != null) {
            if(str.contains("  AUTHORS   ")){
                zzFlag = true;
                zz = zz + str;
            }
            if(str.contains("  TITLE     ")){
                zzFlag = false;
                zz = zz + str;
                titleFlag = true;
            }
            if (!str.contains("  AUTHORS   ") && zzFlag){
                zz = zz + str;
            }
            if(str.contains("  JOURNAL   ")){
                newDataFlag = true;
                list.add(zz);
                zz = "";
                titleFlag = false;
            }
            if(!str.contains("  TITLE     ") && titleFlag){
                zz = zz + str;
            }
        }
        BufferedWriter out = new BufferedWriter(new FileWriter("D:\\result.txt"));
        for (String t: list){
            t = t.replaceAll(",            ",",");
            t = t.replaceAll("  AUTHORS   ","('");
            t = t.replaceAll("  TITLE     ","','");
            t = t.replaceAll("            "," ");
            t = t + "')";
            System.out.println(t);
            out.write(t);
        }
        out.close();
        System.out.println("文件创建成功！");
    }


}
