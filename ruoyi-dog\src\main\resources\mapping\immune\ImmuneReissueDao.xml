<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.immune.dao.ImmuneReissueDao">

    <sql id="reissue">
        a.id as "id",
        a.user_id as "userId",
        a.pet_id as "petId",
        a.qualifi_id as "qualifiId",
        a.brand_num as "brandNum",
        a.type as "type",
        a.send_type as "sendType",
        a.content as "content",
        a.name as "name",
        a.phone as "phone",
        a.area as "area",
        a.address as "address",
        a.is_collect as "isCollect",
        a.file_url as "fileUrl",
        a.status as "status",
        a.reason as "reason",
        a.create_date as "createDate",
        a.dog_brand as "dogBrand",
        a.zfck_id as "zfckId"
    </sql>

    <select id="getList" resultType="com.ruoyi.modules.immune.entity.ImmuneReissue" parameterType="com.ruoyi.modules.immune.entity.ImmuneReissue">
        select
        <include refid="reissue" />
        from immune_reissue a
        where a.del_flag = 1
        <if test="userId != null and userId != ''">
            and a.user_id = #{userId}
        </if>
        <if test="petId != null and petId != ''">
            and a.pet_Id = #{petId}
        </if>
        <if test="qualifiId != null and qualifiId != ''">
            and a.qualifi_id = #{qualifiId}
        </if>
        <if test="brandNum != null and brandNum != ''">
            and a.brand_num = #{brandNum}
        </if>
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="sendType != null and sendType != ''">
            and a.send_type = #{sendType}
        </if>
        <if test="name != null and name != ''">
            and a.name = #{name}
        </if>
        <if test="phone != null and phone != ''">
            and a.phone = #{phone}
        </if>
    </select>

    <select id="getById" resultType="com.ruoyi.modules.immune.entity.ImmuneReissue" parameterType="com.ruoyi.modules.immune.entity.ImmuneReissue">
        select
        <include refid="reissue" />
        from immune_reissue a
        where a.id = #{id}
    </select>

    <select id="getByPetId" resultType="com.ruoyi.modules.immune.entity.ImmuneReissue" parameterType="com.ruoyi.modules.immune.entity.ImmuneReissue">
        SELECT
        <include refid="reissue"/>
        FROM
        ( SELECT MAX( create_date ) AS create_date, pet_id FROM immune_reissue GROUP BY pet_id DESC ORDER BY
        create_date DESC ) t1
        JOIN immune_reissue a ON  t1.pet_id = a.pet_id AND t1.create_date = a.create_date
        where a.del_flag = 1
        <if test="petId != null and petId != ''">
            and a.pet_Id = #{petId}
        </if>
    </select>

    <insert id="insert" parameterType="com.ruoyi.modules.immune.entity.ImmuneReissue">
        insert into immune_reissue
        (
        id,user_id,pet_id,
        qualifi_id,brand_num,type,
        send_type,content,name,
        phone,address,
        is_collect,file_url,status,
        reason,create_date,create_by,
        del_flag,area,dog_brand,zfck_id
        )values
        (
            #{id},#{userId},#{petId},
            #{qualifiId},#{brandNum},#{type},
            #{sendType},#{content},#{name},
            #{phone},#{address},
            #{isCollect},#{fileUrl},#{status},
            #{reason},#{createDate},#{createBy},
            #{delFlag},#{area},#{dogBrand},#{zfckId}
        )
    </insert>

    <update id="update" parameterType="com.ruoyi.modules.immune.entity.ImmuneReissue">
        update immune_reissue set
        brand_num = #{brandNum},type = #{type},send_type = #{sendType},
        content = #{content},name = #{name},phone = #{phone},address = #{address},
        is_collect = #{isCollect},file_url = #{fileUrl},
        status = #{status},reason = #{reason},update_date = #{updateDate},
        update_by = #{updateBy}, area = #{area},dog_brand = #{dogBrand},zfck_id = #{zfckId}
        where id = #{id}
    </update>

    <update id="updateStatus" parameterType="com.ruoyi.modules.immune.entity.ImmuneReissue">
        update immune_reissue
        set status      = #{status},
            reason      = #{reason},
            dog_brand   = #{dogBrand},
            update_date = #{updateDate},
            update_by   = #{updateBy}
        where id = #{id}
    </update>

    <delete id="delete" parameterType="com.ruoyi.modules.immune.entity.ImmuneReissue">
        update immune_reissue set
        del_flag = #{delFlag}, update_date = #{updateDate}, update_by = #{updateBy}
        where id = #{id}
    </delete>
</mapper>
