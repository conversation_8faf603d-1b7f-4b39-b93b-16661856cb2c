package com.ruoyi.common.vo.ocr.req;

import lombok.Data;

@Data
public class OcrAtoCapPdfReqVO {

    /**
     * PDF文件二进制数据的base64编码，要求PDF大小不超过10M，单个文件不超过100页
     */
    private String fileBase64;

    /**
     * 是否需要置信度
     */
    private String prob;

    /**
     * 是否需要单字输出
     */
    private String charInfo;

    /**
     * 是否需要自动旋转功能
     */
    private String rotate;

    /**
     * 是否需要表格功能
     */
    private String table;

    /**
     * 是否需要分页功能
     */
    private String page;

    /**
     * 是否需要分段功能
     */
    private String paragraph;

    /**
     * 是否需要分行功能
     */
    private String row;

    /**
     * advanced(全文识别，默认)，multi_scene(混贴票据识别，目前支持火车票、飞机行程单、出租车发票、定额发票、增值税发票、身份证正面、身份证反面、行驶证正面、行驶证反面、银行卡、驾驶证正面、卷票、户主页、常住人口页14种票据的检测识别。)
     */
    private String recType = "advanced";

    /**
     * 固定值
     */
    private String method = "ocrPDFService";
}
