package com.ruoyi.common.dtalk;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.xxpt.gateway.shared.api.request.OapiMoziEmployeeListEmployeePositionsByEmployeeCodeRequest;
import com.alibaba.xxpt.gateway.shared.api.request.OapiMoziOrganizationGetOrganizationByCodeRequest;
import com.alibaba.xxpt.gateway.shared.api.request.OapiMoziOrganizationListOrganizationsByCodesRequest;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMoziEmployeeListEmployeePositionsByEmployeeCodeResponse;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMoziOrganizationGetOrganizationByCodeResponse;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMoziOrganizationListOrganizationsByCodesResponse;
import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.alibaba.xxpt.gateway.shared.client.http.IntelligentGetClient;
import com.alibaba.xxpt.gateway.shared.client.http.IntelligentPostClient;
import com.alibaba.xxpt.gateway.shared.client.http.PostClient;
import com.alibaba.xxpt.gateway.shared.client.http.api.OapiSpResultContent;
import com.ruoyi.common.dtalk.properties.DtalkProperties;
import com.ruoyi.common.dtalk.vo.DtalkDeptCodeVo;
import com.ruoyi.common.dtalk.vo.OrganizationNodeInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @Date 2022/3/8
 * @Description 查询浙政钉部门机构信息
 */
@Service
@Slf4j
public class DeptDtalkServiceImpl implements DeptDtalkService {

    /**
     * 分页查询的每页数量
     */
    private Integer pageSize = 100;

//    /**
//     * 煜象租户id
//     */
//    private Long tenantId = 50358116L;

    /**
     * 引入处理部门信息的client信息
     */
    @Resource(name = "executableClientDept")
    private ExecutableClient executableClient;

    @Autowired
    private DtalkProperties dtalkProperties;


    /**
     * 根据EmployeeCode获取organizationCode
     *
     * @param employeeCode
     * @return
     */
    @Override
    public String getOrganizationCode(String employeeCode) throws ExecutionException {

        IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient("/mozi/employee/listEmployeePositionsByEmployeeCode");
        OapiMoziEmployeeListEmployeePositionsByEmployeeCodeRequest oapiMoziEmployeeListEmployeePositionsByEmployeeCodeRequest = new OapiMoziEmployeeListEmployeePositionsByEmployeeCodeRequest();
//        oapiMoziEmployeeListEmployeePositionsByEmployeeCodeRequest.setEmployeePositionStatus("字符串");
//        oapiMoziEmployeeListEmployeePositionsByEmployeeCodeRequest.setEmployeePositionType("字符串");
        oapiMoziEmployeeListEmployeePositionsByEmployeeCodeRequest.setEmployeeCode(employeeCode);
        oapiMoziEmployeeListEmployeePositionsByEmployeeCodeRequest.setTenantId(dtalkProperties.getTenantId());
        //获取结果
        OapiMoziEmployeeListEmployeePositionsByEmployeeCodeResponse apiResult = intelligentGetClient.get(oapiMoziEmployeeListEmployeePositionsByEmployeeCodeRequest);
        if (!apiResult.getSuccess()) {
            throw new RuntimeException(apiResult.getMessage());
        }
        String data = apiResult.getContent().getData();
        //循环查询，查询部门内所有的数据
        List<String> strings = JSONArray.parseArray(data, String.class);
        if (CollectionUtils.isEmpty(strings)) {
            return "";
        }
        DtalkDeptCodeVo dtalkEmployeeVo = JSON.parseObject(strings.get(0), DtalkDeptCodeVo.class);
        return dtalkEmployeeVo.getOrganizationCode();
    }

    /**
     * 根据organizationCode获取部门名称
     *
     * @param organizationCode
     * @return
     */
    @Override
    public String getOrganizationName(String organizationCode) throws ExecutionException {
        IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient("/mozi/organization/getOrganizationByCode");
        OapiMoziOrganizationGetOrganizationByCodeRequest oapiMoziOrganizationGetOrganizationByCodeRequest = new OapiMoziOrganizationGetOrganizationByCodeRequest();
        oapiMoziOrganizationGetOrganizationByCodeRequest.setOrganizationCode(organizationCode);
        oapiMoziOrganizationGetOrganizationByCodeRequest.setTenantId(dtalkProperties.getTenantId());
        //获取结果
        OapiMoziOrganizationGetOrganizationByCodeResponse apiResult = intelligentGetClient.get(oapiMoziOrganizationGetOrganizationByCodeRequest);
        if (!apiResult.getSuccess()) {
            throw new RuntimeException(apiResult.getMessage());
        }
        String data = apiResult.getContent().getData();
        log.info("getOrganizationByCode组织信息--> organizationCode : {},dtalkProperties.getTenantId() : {} ,获取的组织信息: {}",organizationCode,dtalkProperties.getTenantId(),data);
        DtalkDeptCodeVo dtalkEmployeeVo = JSON.parseObject(data, DtalkDeptCodeVo.class);
        if (dtalkEmployeeVo == null) {
            return "";
        } else {
            return dtalkEmployeeVo.getOrganizationName();
        }
    }

    @Override
    public List<OrganizationNodeInfo> getOrganization() {
        String organizationCode = getOrganizationRootCode();

        //组织机构code集合
        List<String> codeList = Lists.newArrayList();
        codeList.add(organizationCode.toString());
        //获取所有的部门名和部门id集合
        List<OrganizationNodeInfo> orgInfo = getOrgInfo(executableClient, codeList);
        return orgInfo;
    }

    @Override
    public List<OrganizationNodeInfo> getPageOrganization(String organizationCode) throws ExecutionException {
        //组织机构code集合
        List<String> codeList = Lists.newArrayList();
        List<String> strings = getCodes(executableClient, codeList, organizationCode);
        //获取所有的部门名和部门id集合
        if(strings.size()==0){
            List<OrganizationNodeInfo> organizationNodeInfoHashMap = new ArrayList<>();
            return organizationNodeInfoHashMap;
        }
        List<OrganizationNodeInfo> orgInfo = getOrgInfo(executableClient, strings);

        return orgInfo;
    }

    /**
     * 获取子级部门id集合
     *
     * @param executableClient
     * @return
     */
    private List<String> getCodes(ExecutableClient executableClient, List<String> codes, String organizationCode) {
        //1.根据根节点获取子级部门的code集合 调用方法 pageSubOrganizationCodes
        String api = "/mozi/organization/pageSubOrganizationCodes";
        PostClient postClient = executableClient.newPostClient(api);
        //Set the parameters
        postClient.addParameter("returnTotalSize", "");
        postClient.addParameter("pageSize", "");
        postClient.addParameter("organizationCode", organizationCode);
        postClient.addParameter("pageNo", "");
        postClient.addParameter("status", "");
        postClient.addParameter("tenantId", dtalkProperties.getTenantId().toString());
        /**
         * 正常返回示例：
         * {
         *   "success": true,
         *   "content": {
         *     "data": [
         *       "GO_0249e9734a2541189d02127fc56cb8b2",
         *       "GO_db242242b93f40f98f96ef206e8218d6"
         *     ],
         *     "success": true,
         *     "requestId": "2e34a8d0-73ce-4008-a583-31e033300862",
         *     "pageSize": 20,
         *     "responseMessage": "OK",
         *     "currentPage": 1,
         *     "responseCode": "0",
         *     "bizErrorCode": "0"
         *   },
         *   "bizErrorCode": "0"
         * }
         *
         * 异常返回示例：
         * // 网关失败返回
         * {
         *     "_RequestId":"ac140b3e15834783672523535d000c",
         *     "Message":"The input parameter 'tenantId' that is mandatory for processing this request is not supplied or value is empty.",
         *     "success":false,
         *     "errorCode":"MissingParameter",
         *     "HostId":"gudao-openplatform-daily",
         *     "Code":"MissingParameter",
         *     "errorMsg":"The input parameter 'tenantId' that is mandatory for processing this request is not supplied or value is empty.",
         *     "errorLevel":"error"
         * }
         *
         * // 业务失败返回
         * {
         *     "success":true,
         *     "content":{
         *         "success":false,
         *         "requestId":"e789954c-4c6f-4563-a78c-560609393399",
         *         "responseMessage":"status must be A or F or TOTAL !",
         *         "responseCode":"231005"
         *     }
         * }
         */
        String apiResult = postClient.post();
        JSONObject pageSubOrganizationCodes = JSONObject.parseObject(apiResult);
        if (!pageSubOrganizationCodes.getBooleanValue("success")) {
            throw new RuntimeException(pageSubOrganizationCodes.getString("errorMsg"));
        }
        JSONObject content = pageSubOrganizationCodes.getJSONObject("content");
        if (!content.getBooleanValue("success")) {
            throw new RuntimeException(pageSubOrganizationCodes.getString("responseMessage"));
        }
        JSONArray data = content.getJSONArray("data");
        List<String> strings = CollectionUtils.isEmpty(data) ? Lists.newArrayList() : data.toJavaList(String.class);

        codes.addAll(strings);
        if (strings.size() == pageSize) {
            getCodes(executableClient, codes, organizationCode);
        }
        return codes;
    }

    /**
     * 根据组织结构code，批量查询详情
     *
     * @param executableClient 调用处理部门信息的客户端
     * @param codeList         部门组织的code集合
     * @return
     */
    private List<OrganizationNodeInfo> getOrgInfo(ExecutableClient executableClient, List<String> codeList) {
        //executableClient保证单例
        IntelligentPostClient intelligentPostClient = executableClient.newIntelligentPostClient("/mozi/organization/listOrganizationsByCodes");
        OapiMoziOrganizationListOrganizationsByCodesRequest oapiMoziOrganizationListOrganizationsByCodesRequest = new OapiMoziOrganizationListOrganizationsByCodesRequest();
        oapiMoziOrganizationListOrganizationsByCodesRequest.setOrganizationCodes(codeList);
        oapiMoziOrganizationListOrganizationsByCodesRequest.setTenantId(dtalkProperties.getTenantId());

        //获取结果
        OapiMoziOrganizationListOrganizationsByCodesResponse apiResult = intelligentPostClient.post(oapiMoziOrganizationListOrganizationsByCodesRequest);
        if (!apiResult.getSuccess()) {
            throw new RuntimeException(apiResult.getMessage());
        }
        OapiSpResultContent content = apiResult.getContent();
        /**
         * // 网关失败返回
         * {
         *     "_RequestId":"ac140b3e15834783672523535d000c",
         *     "Message":"The input parameter 'tenantId' that is mandatory for processing this request is not supplied or value is empty.",
         *     "success":false,
         *     "errorCode":"MissingParameter",
         *     "HostId":"gudao-openplatform-daily",
         *     "Code":"MissingParameter",
         *     "errorMsg":"The input parameter 'tenantId' that is mandatory for processing this request is not supplied or value is empty.",
         *     "errorLevel":"error"
         * }
         *
         * // 业务失败返回
         * {
         *     "success":true,
         *     "content":{
         *         "success":false,
         *         "requestId":"e789954c-4c6f-4563-a78c-560609393399",
         *         "responseMessage":"status must be A or F or TOTAL !",
         *         "responseCode":"231005"
         *     }
         * }
         */
        if (!content.getSuccess()) {
            throw new RuntimeException(content.getResponseMessage());
        }
        /**
         * 成功返回案例
         * {
         *     "success": true, "content": {
         *         "totalSize": 2,
         *         "data": ["GO_003cb7c1face4e428957af23ab5ab528",
         *                  "GO_0080ac885c8e4bacb83a97711f720b5e",
         *                  "GO_01d6672769f240f9b033522bd522cbea"],
         *         "success": true,
         *         "requestId": "4e9209a7-b8bf-4557-94c2-2bb9faffe39d",
         *         "pageSize": 10,
         *         "responseMessage": "OK",
         *         "currentPage": 1,
         *         "responseCode": "0"
         *     }
         * }
         */
        String data = content.getData();
        JSONArray jsonArray = JSONArray.parseArray(data);
        List<OrganizationNodeInfo> organizationNodeInfoHashMap = new ArrayList<>();
        jsonArray.forEach(item -> {
            JSONObject jsonObject = JSONObject.parseObject(item.toString());
            OrganizationNodeInfo organizationNodeInfo = OrganizationNodeInfo.builder().organizationName(jsonObject.getString("organizationName"))
                    .organizationCode(jsonObject.getString("organizationCode")).build();
            organizationNodeInfoHashMap.add(organizationNodeInfo);
        });
        return organizationNodeInfoHashMap;
    }

    /**
     * 获取根节点code值
     *
     * @return
     */
    public String getOrganizationRootCode() {
        PostClient postClient = executableClient.newPostClient("/auth/scopesV2");
        //Set the parameters
        postClient.addParameter("tenantId", dtalkProperties.getTenantId().toString());
        //Call API
        String apiResult = postClient.post();
        log.info(apiResult);
        /**
         * 正常响应数据
         * {
         *     "success":true,
         *     "errorCode":"0",
         *     "content":{
         *         "stripLineAddressEmployeeScopes":[
         *
         *         ],
         *         "stripLineAddressScopes":[
         *
         *         ],
         *         "userVisibleScopes":[
         *
         *         ],
         *         "deptVisibleScopes":[
         *             "GO_836bd141d4614baa9977bd1eb78c463e"
         *         ]
         *     },
         *     "bizErrorCode":"0",
         *     "errorMsg":"成功"
         * }
         * 错误响应数据
         * {
         *     "success":true,
         *     "errorCode":"0",
         *     "bizErrorCode":"0",
         *     "errorMsg":"成功"
         * }
         */
        JSONObject jsonObject = JSONObject.parseObject(apiResult);
        if (Objects.nonNull(jsonObject.getString("content"))) {
            JSONObject content = jsonObject.getJSONObject("content");
            JSONArray deptVisibleScopes = content.getJSONArray("deptVisibleScopes");
            return deptVisibleScopes.getString(0);
        }
        return null;
    }

}
