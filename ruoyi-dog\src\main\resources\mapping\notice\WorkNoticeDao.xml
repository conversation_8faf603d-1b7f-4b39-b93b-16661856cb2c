<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.notice.dao.WorkNoticeDao">
    <sql id="columns">
        <trim suffixOverrides=",">
            a.id as id,
            a.title as title,
            a.content as content,
            a.status as status,
            a.summary as summary,
            a.notice_org as noticeOrg,
            a.notice_orgid as noticeOrgid,
            a.del_flag as delFlag,
            a.create_date as createDate,
            a.create_by as createBy,
            a.update_date as updateDate,
            a.update_by as updateBy,
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.notice.entity.WorkNotice" resultType="com.ruoyi.modules.notice.entity.WorkNotice">
        select
        <include refid="columns"/>, b.real_name as createName
        from work_notice a  left join sys_user b on a.create_by = b.id
        where a.id =#{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.notice.entity.WorkNotice" resultType="com.ruoyi.modules.notice.entity.WorkNotice">
        select
        <include refid="columns"/>, b.real_name as createName
        from work_notice a left join sys_user b on a.create_by = b.id
        where a.del_flag =1
        <if test="title != null and title != ''">
            and  a.title like concat('%',#{title},'%')
        </if>
        <if test="content != null and content != ''">
            and a.content like concat('%',#{content},'%')
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        <if test="messageDept != null and messageDept != ''">
            and exists (select 1 from push_message_dept b where b.detp_id =  #{messageDept} and b.message_id = a.id)
        </if>
        order by a.create_date desc
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.notice.entity.WorkNotice">
        insert into work_notice
              (id,
               title,
               content,
               status,
               summary,
               notice_org,
               notice_orgid,
               del_flag,
               create_date,
               create_by,
               update_date,
               update_by)
        values (#{id},
                #{title},
                #{content},
                #{status},
                #{summary},
                #{noticeOrg},
                #{noticeOrgid},
                #{delFlag},
                #{createDate}, #{createBy},
                #{updateDate}, #{updateBy})
    </insert>

    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.notice.entity.WorkNotice">
        update work_notice set
        <trim suffixOverrides=",">
            title = #{title},
            content = #{content},
            status = #{status},
            summary = #{summary},
            notice_org = #{noticeOrg},
            notice_orgid = #{noticeOrgid},
            del_flag = #{delFlag},
            create_by = #{createBy},
            update_date = #{updateDate},
            update_by = #{updateBy},
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.notice.entity.WorkNotice">
        UPDATE work_notice
        SET del_flag=#{delFlag}, update_date = #{updateDate}, update_by = #{updateBy}
        where id = #{id}
    </delete>

    <update id="updateStatus" parameterType="com.ruoyi.modules.notice.entity.WorkNotice">
        update work_notice set status = #{status} where id = #{id}
    </update>

</mapper>
