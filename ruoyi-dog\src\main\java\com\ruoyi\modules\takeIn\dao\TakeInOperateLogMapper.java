package com.ruoyi.modules.takeIn.dao;

import com.ruoyi.modules.takeIn.entity.TakeInOperateLog;

import java.util.List;

public interface TakeInOperateLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TakeInOperateLog record);

    int insertSelective(TakeInOperateLog record);

    TakeInOperateLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TakeInOperateLog record);

    int updateByPrimaryKey(TakeInOperateLog record);

    List<TakeInOperateLog> listTakeInList(String id);
}
