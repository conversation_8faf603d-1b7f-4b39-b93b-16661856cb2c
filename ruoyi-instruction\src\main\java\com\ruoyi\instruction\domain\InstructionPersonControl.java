package com.ruoyi.instruction.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 重点人员-管控人员信息对象 t_instruction_person_control
 * 
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
public class InstructionPersonControl extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 职位 */
    @Excel(name = "职位")
    private String position;

    /** 电话 */
    @Excel(name = "电话")
    private String telephone;

    /** 情况反馈 */
    @Excel(name = "情况反馈")
    private String situationFeedback;

    /** 状态1：正常 9：删除 */
    @Excel(name = "状态1：正常 9：删除")
    private String status;

    /** 关联重点人员id */
    @Excel(name = "关联重点人员id")
    private Long personId;


}
