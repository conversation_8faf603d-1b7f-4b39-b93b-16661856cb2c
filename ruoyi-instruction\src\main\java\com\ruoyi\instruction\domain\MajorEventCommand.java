package com.ruoyi.instruction.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 重大事件-跟踪指挥对象 t_major_event_command
 *
 * <AUTHOR>
 * @date 2023-03-02
 */
@Data
public class MajorEventCommand extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 跟踪指挥类型
     * 1：统筹协调
     * 2：事故协调
     * 3：善后处理
     * 4：应急处置
     * 5：舆情管控
     * 6：复盘反馈
     */
    private Integer type;

    /**
     * 责任部门id
     */
    @Excel(name = "责任部门id")
    private String deptName;

    /**
     * 反馈意见
     */
    @Excel(name = "反馈意见")
    private String feedback;

    /**
     * 重大事件id
     */
    @Excel(name = "重大事件id")
    private Long majorEventId;

    /**
     * 状态1：正常 9：删除
     */
    @Excel(name = "状态1：正常 9：删除")
    private String status;

}
