package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.UndercoverPerson;
import com.ruoyi.instruction.service.IUndercoverPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 暗访督察人员Controller
 * 
 * <AUTHOR>
 * @date 2023-05-23
 */
@RestController
@RequestMapping("/instruction/undercoverPerson")
public class UndercoverPersonController extends BaseController
{
    @Autowired
    private IUndercoverPersonService undercoverPersonService;

    /**
     * 查询暗访督察人员列表
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverPerson:list')")
    @GetMapping("/list")
    public TableDataInfo list(UndercoverPerson undercoverPerson)
    {
        startPage();
        List<UndercoverPerson> list = undercoverPersonService.selectUndercoverPersonList(undercoverPerson);
        return getDataTable(list);
    }

    /**
     * 导出暗访督察人员列表
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverPerson:export')")
    @Log(title = "暗访督察人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UndercoverPerson undercoverPerson)
    {
        List<UndercoverPerson> list = undercoverPersonService.selectUndercoverPersonList(undercoverPerson);
        ExcelUtil<UndercoverPerson> util = new ExcelUtil<UndercoverPerson>(UndercoverPerson.class);
        util.exportExcel(response, list, "暗访督察人员数据");
    }

    /**
     * 获取暗访督察人员详细信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverPerson:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(undercoverPersonService.selectUndercoverPersonById(id));
    }

    /**
     * 新增暗访督察人员
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverPerson:add')")
    @Log(title = "暗访督察人员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UndercoverPerson undercoverPerson)
    {
        return toAjax(undercoverPersonService.insertUndercoverPerson(undercoverPerson));
    }

    /**
     * 修改暗访督察人员
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverPerson:edit')")
    @Log(title = "暗访督察人员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UndercoverPerson undercoverPerson)
    {
        return toAjax(undercoverPersonService.updateUndercoverPerson(undercoverPerson));
    }

    /**
     * 删除暗访督察人员
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverPerson:remove')")
    @Log(title = "暗访督察人员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(undercoverPersonService.deleteUndercoverPersonByIds(ids));
    }

    /**
     * 下载暗访督察人员导入模板
     *
     * @param response
     */
    @PostMapping("/importPersonTemplate")
    public void importPersonTemplate(HttpServletResponse response) {
        ExcelUtil<UndercoverPerson> util = new ExcelUtil<>(UndercoverPerson.class);
        util.importTemplateExcel(response, "暗访督察人员导入模板");
    }

    /**
     * 通过人员id查询人员集合
     */
    @GetMapping("/selectPersonListByIds")
    public TableDataInfo selectPersonListByIds(String ids, String personName, Integer pageNum, Integer pageSize) {
        //查询关联人员信息
        return undercoverPersonService.selectInstrucationPersonByIds(ids, personName, pageNum, pageSize);

    }

    /**
     * 并返回导入数据集合 通过用户id查询
     */
    @Log(title = "人员信息导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<UndercoverPerson> util = new ExcelUtil<>(UndercoverPerson.class);
        List<UndercoverPerson> personList = util.importExcel(file.getInputStream());
        return AjaxResult.success(personList);
    }

    /**
     * 根据人员姓名搜索人员库列表信息
     *
     * @param undercoverPerson
     * @return
     */
    @GetMapping("/selectPersonByName")
    public AjaxResult selectPersonByName(UndercoverPerson undercoverPerson) {
        List<UndercoverPerson> list = undercoverPersonService.selectUndercoverPersonList(undercoverPerson);
        return AjaxResult.success(list);
    }
}
