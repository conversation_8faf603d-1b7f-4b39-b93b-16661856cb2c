package com.ruoyi.xzzfj.controller;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.xzzfj.domain.dh.DhVideo;
import com.ruoyi.xzzfj.domain.dh.DhVideoOrg;
import com.ruoyi.xzzfj.domain.dh.DhVideoVo;
import com.ruoyi.xzzfj.domain.req.DhVideoReqVo;
import com.ruoyi.xzzfj.domain.req.LzczReq;
import com.ruoyi.xzzfj.service.DhService;
import com.ruoyi.xzzfj.service.IAjhfLzCzService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.domain.R;

import java.util.List;

/**
 * 大华视频
 * 
 * <AUTHOR>
 * @date 2024-03-27
 */
@RestController
@RequestMapping("/dh")
public class DhController extends BaseController
{
    @Autowired
    private DhService dhService;
    @Autowired
    private ISysDeptService  iSysDeptService;

    @Value("${image.url}")
    private  String imageUrl;




    /**
     * 获取大华视频部门
     * @param parentId
     * @return
     * @throws Exception
     */
    @GetMapping("/getDhOrg")
    public R<List<DhVideoOrg>> getDhOrg(String parentId) throws Exception {
      return  R.ok( dhService.getDhOrg(parentId)) ;
    }
    /**
     * 根据组织机构编码获取视频信息
     * @param  orgCode 组织编码
     * @return
     * @throws Exception
     */
    @GetMapping("/getDhVideoByOrgCode")
    public R<List<DhVideoVo>> getDhVideoByOrgCode(DhVideoReqVo orgCode) throws Exception {
        List<DhVideoVo> dhVideoByOrgCode = dhService.getDhVideoByOrgCode(orgCode);
        return  R.ok( dhVideoByOrgCode) ;
    }
    /**
     * 智慧遥感获取视频
     * @param  orgCode 组织编码
     * @return
     * @throws Exception
     */
    @GetMapping("/getDhVideoYg")
    public R<List<DhVideoVo>> getDhVideoYg(DhVideoReqVo orgCode) throws Exception {
      return  R.ok( dhService.getDhVideoYg(orgCode)) ;
    }
}
