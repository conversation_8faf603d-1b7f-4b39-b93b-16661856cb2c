package com.ruoyi.modules.express.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.certificates.service.PetCertificatesService;
import com.ruoyi.modules.express.entity.Express;
import com.ruoyi.modules.express.service.ExpressService;
import com.ruoyi.modules.harmTrea.entity.HarmTrea;
import com.ruoyi.modules.user.entity.SysUser;
import com.ruoyi.modules.user.service.SysUserService;
import com.ruoyi.util.wuliu.ExpressUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/12/9 16:56
 */
@RestController
@RequestMapping("express")
public class ExpressController {
    @Autowired
    private ExpressService service;
    @Autowired
    private PetCertificatesService petCertificatesService;
    @Autowired
    private SysUserService sysUserService;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("/getList")
    public AjaxResult getList(Express entity) {
        return AjaxResult.success(service.getPageList(entity));
    }

    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(Express entity) {
        if (entity.getPetId() != null) {
            PetCertificates petCertificates = petCertificatesService.getById(entity.getPetId());
            entity.setPhone(petCertificates.getTel());
            entity.setName(petCertificates.getOwnerName());
        }
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }

    @RequestMapping("/delete")
    public AjaxResult delete(Express entity) {
        entity.setDelFlag(2);
        service.delete(entity);
        return AjaxResult.success();
    }



    @RequestMapping("/sendExpress")
    public AjaxResult sendExpress(Express entity) {
        SysUser sysUser = sysUserService.getById(entity.getSendUserId());
        if (sysUser.getCustomerNo() == null || "".equals(sysUser.getCustomerNo())) {
            return AjaxResult.error("此账号暂无快递权限");
        }
        entity.setCustomerNo(sysUser.getCustomerNo());
        SysUser user = sysUserService.getByUserName(entity.getPhone());
        entity.setIdCard(user.getIdCard());
        entity.setDeptName(sysUser.getDeptName());
        service.update(entity);
        ExpressUtil.customerOrder(entity);
        return AjaxResult.success();
    }

}
