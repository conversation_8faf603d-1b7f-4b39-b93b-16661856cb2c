package com.ruoyi.common.core.domain;
public class LoginOtherUnifyAuthorize {
    /**
     * 分配给第三方的appId->也用于查询密钥
     */
    private String appId;

    public String getAuthorizeToken() {
        return authorizeToken;
    }

    public void setAuthorizeToken(String authorizeToken) {
        this.authorizeToken = authorizeToken;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    /**
     * 加密令牌
     */
    private String authorizeToken;

}
