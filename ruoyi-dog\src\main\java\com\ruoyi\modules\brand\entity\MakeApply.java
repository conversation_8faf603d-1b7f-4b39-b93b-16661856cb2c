package com.ruoyi.modules.brand.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.user.entity.SysUser;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * (MakeApply)实体类
 *
 * <AUTHOR>
 * @since 2023-01-11 10:08:55
 */
public class MakeApply extends BaseEntity {


    /**
     * 创建人id
     */
    private String userId;
    /**
     * 单位id
     */
    private String deptId;
    /**
     * 申请数量
     */
    private Integer num;
    /**
     * 审核状态 ：make_apply_status
     */
    private Integer status;
    /**
     * 付款状态 ：1.已付款 2.未付款
     */
    private Integer isPay;
    /**
     * 生成的犬牌号
     */
    private String dogCodes;
    /**
     * 寄送地址
     */
    private String sendAddress;
    /**
     * 快递名称
     */
    private String courierName;
    /**
     * 快递单号
     */
    private String courierNumber;

    private List<Long> deptIds;
    /**/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date bDate;//
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date eDate;//
    private SysUser user;//userId详细信息
    private Integer surplusNum;//剩余犬牌数
private String deptName;//
    private String queryType;//查询类型。cj：厂家权限的数据，差除“已提交审核”之外的数据

    public String getCourierName() {
        return courierName;
    }

    public void setCourierName(String courierName) {
        this.courierName = courierName;
    }

    public String getCourierNumber() {
        return courierNumber;
    }

    public void setCourierNumber(String courierNumber) {
        this.courierNumber = courierNumber;
    }

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Integer getSurplusNum() {
        return surplusNum;
    }

    public void setSurplusNum(Integer surplusNum) {
        this.surplusNum = surplusNum;
    }

    public SysUser getUser() {
        return user;
    }

    public void setUser(SysUser user) {
        this.user = user;
    }

    public String getSendAddress() {
        return sendAddress;
    }

    public void setSendAddress(String sendAddress) {
        this.sendAddress = sendAddress;
    }

    public Date getbDate() {
        return bDate;
    }

    public void setbDate(Date bDate) {
        this.bDate = bDate;
    }

    public Date geteDate() {
        return eDate;
    }

    public void seteDate(Date eDate) {
        this.eDate = eDate;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsPay() {
        return isPay;
    }

    public void setIsPay(Integer isPay) {
        this.isPay = isPay;
    }

    public String getDogCodes() {
        return dogCodes;
    }

    public void setDogCodes(String dogCodes) {
        this.dogCodes = dogCodes;
    }


    public List<Long> getDeptIds() {
        return deptIds;
    }

    public void setDeptIds(List<Long> deptIds) {
        this.deptIds = deptIds;
    }
}
