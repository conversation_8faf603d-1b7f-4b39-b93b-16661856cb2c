package com.ruoyi.modules.immune.service;


import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.modules.brand.dao.PetBrandDao;
import com.ruoyi.modules.brand.entity.PetBrand;
import com.ruoyi.modules.certificates.dao.PetCertificatesDao;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.certificates.service.PetCertificatesService;
import com.ruoyi.modules.immune.dao.ImmuneRegisterDao;
import com.ruoyi.modules.immune.entity.ImmuneRegister;
import com.ruoyi.modules.petRecord.dao.PetRecordDao;
import com.ruoyi.modules.petRecord.entity.PetRecord;
import com.ruoyi.modules.sysDict.dao.SysDictDao;
import com.ruoyi.modules.sysDict.entity.SysDict;
import com.ruoyi.modules.user.dao.SysUploadFileDao;
import com.ruoyi.modules.user.dao.SysUserDao;
import com.ruoyi.modules.user.dao.SysUserRoleDao;
import com.ruoyi.modules.user.entity.SysDept;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.modules.user.entity.SysUserRole;
import com.ruoyi.modules.user.service.SysUploadFileService;
import com.ruoyi.util.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;


/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 10:52
 * @version: 1.0
 **/
@Service
public class ImmuneRegisterService extends BaseService<ImmuneRegisterDao, ImmuneRegister> {

    @Autowired
    PetCertificatesService petCertificatesService;
    @Autowired
    PetCertificatesDao petCertificatesDao;
    @Autowired
    SysUploadFileService uploadFileService;
    @Autowired
    PetBrandDao petBrandDao;
    @Autowired
    SysUserDao sysUserDao;
    @Autowired
    SysUserRoleDao sysUserRoleDao;
    @Value("${petBrandServiceUrl}")
    private String petBrandServiceUrl;
    @Autowired
    private SysDictDao sysDictDao;
    @Autowired
    PetRecordDao petRecordDao;

    public void downloadFile(ImmuneRegister immuneRegister, HttpServletRequest request, HttpServletResponse response) {
        SysDict petType = new SysDict();
        petType.setDictType("varieties");
        List<SysDict> petTypeList = sysDictDao.getList(petType);
        SysDict varietiesType = new SysDict();
        varietiesType.setDictType("varieties_type");
        List<SysDict> varietiesTypeList = sysDictDao.getList(varietiesType);

        List<PetCertificates> list = dao.getPetPageList(immuneRegister);
        String fileName = "导出";
        List<String> headList = new ArrayList();
        headList.add("宠物名");
        headList.add("宠物类别");
        headList.add("性别");
        headList.add("品种");
        headList.add("免疫证号");
        headList.add("免疫有效期至");
        headList.add("状态");
        headList.add("犬牌编号");
        headList.add("宠物主人联系方式");
        headList.add("饲养地址");
        headList.add("宠物主人姓名");
        List<List<String>> content = new ArrayList<>();
        for (PetCertificates d : list) {
            List<String> row = new ArrayList();
            row.add(d.getPetName());
            String type = "";
            for (SysDict dict : petTypeList) {
                if (dict.getDictKey().equals(d.getPetVarietiesOne())) {
                    type = dict.getName();
                    break;
                }
            }
            row.add(type);
            String petSex = "";
            if (d.getPetSex().intValue() == 1) {
                petSex = "雄";
            }
            if (d.getPetSex().intValue() == 2) {
                petSex = "雌";
            }
            row.add(petSex);
            String varieties = "";
            for (SysDict dict : varietiesTypeList) {
                if (dict.getDictKey().equals(d.getPetVarieties())) {
                    varieties = dict.getName();
                    break;
                }
            }
            row.add(varieties);
            row.add(d.getImmuneRegister() == null ? "" : d.getImmuneRegister().getImmuneCard());
            if (d.getImmuneRegister() != null && d.getImmuneRegister().getInjectionEnddate() != null) {
                row.add(DateUtil.format(d.getImmuneRegister().getInjectionEnddate(), "yyyy-MM-dd HH:mm:ss"));
            } else {
                row.add("");
            }
            //if (row.immuneStatus === '' || row.immuneStatus === null) {
            //    return '未免疫'
            //} else if (row.immuneStatus === '1') {
            //    return '待审批'
            //} else if (row.immuneStatus === '2') {
            //    return '已通过'
            //} else if (row.immuneStatus === '3') {
            //    return '未通过'
            //}
            String status = "";
            if (d.getImmuneRegister() != null) {
                if (d.getImmuneRegister().getStatus() == null || d.getImmuneRegister().getStatus().equals(""))
                    status = "未免疫";
                if (d.getImmuneRegister().getStatus().equals("1")) status = "未提交审核";
                if (d.getImmuneRegister().getStatus().equals("2")) status = "待审核";
                if (d.getImmuneRegister().getStatus().equals("3")) status = "已通过";
                if (d.getImmuneRegister().getStatus().equals("4")) status = "未通过";
            }else{
                status = "未免疫";
            }
            row.add(status);
            row.add(d.getPetNum());
            row.add(d.getTel());
            row.add(d.getPetAddress());
            row.add(d.getOwnerName());
            content.add(row);
        }
        ExcelUtil.downloadExecl(fileName, headList, content, response, request);
    }

    public PageInfo<PetCertificates> getPetPageList(ImmuneRegister immuneRegister) {
        PageHelper.startPage(immuneRegister.getPageNum(), immuneRegister.getPageSize());
        PageInfo<PetCertificates> pageInfo = new PageInfo<PetCertificates>(dao.getPetPageList(immuneRegister));
        return pageInfo;
    }

    public List<HashMap> getDeptSort(ImmuneRegister immuneRegister) {
        return dao.getDeptSort(immuneRegister);
    }
    public List<HashMap> getDeptSortJD(ImmuneRegister immuneRegister) {
        return dao.getDeptSortJD(immuneRegister);
    }
    public HashMap getTotalNum(ImmuneRegister immuneRegister) {
        return dao.getTotalNum(immuneRegister);
    }

    /**
     * 免疫登记保存
     *
     * @param immuneRegister
     * @return
     */
    @Override
    @Transactional
    public void saveOrUpdate(ImmuneRegister immuneRegister) {
        PetCertificates pet = petCertificatesDao.getById(immuneRegister.getPetId());
        if (pet != null && StringUtils.isNotEmpty(pet.getImmuneStatus())) {
            immuneRegister.setStatus(pet.getImmuneStatus());
        }
        if (immuneRegister.getId() == null || "".equals(immuneRegister.getId())) {
            dao.nextval("pet_card");//录入免疫信息的时候改变免疫号序列
        }
        super.saveOrUpdate(immuneRegister);
        //更新犬只表免疫相关信息
        PetCertificates dog = new PetCertificates();
        dog.setId(immuneRegister.getPetId());//犬只ID
        dog.setImmuneId(immuneRegister.getId());//最新免疫关联ID
        dog.setHospitalId(immuneRegister.getHospitalId());//最新医院关联ID
        dog.setEndDate(immuneRegister.getInjectionEnddate());//免疫到期时间
        if (immuneRegister.getType() == null) {
            petCertificatesService.updateImmune(dog);
        } else {
            dog.setAboutDate(immuneRegister.getAboutDate());
            dog.setAboutMake(immuneRegister.getAboutMake());
            dog.setYearStatus(2);
            petCertificatesService.updateImmune(dog);
        }
    }

    @Transactional
    public AjaxResult savePet(PetCertificates petCertificates) {
        //判断名下是否已有犬只，排除当前正在编辑的犬只
        Long petCount = petCertificatesDao.findPetCountByIdCard(petCertificates.getPetIdCard(), petCertificates.getId());
        if (petCount > 0){
            return AjaxResult.warn("该用户名下已有犬只");
        }
        //检查户口本号唯一性
        Integer num = petCertificatesDao.selectHuKouNumberIsOnly(petCertificates);  //检查户号是否已存在
        if (!Objects.equals(num, 0)) {
            return AjaxResult.warn("户号已存在");
        }else{
            if (!StringUtils.isEmpty(petCertificates.getOtherVarieties())){
                //判断是否是禁养犬
                Integer jy = petCertificatesDao.jy(petCertificates.getOtherVarieties());
                if (jy > 0){
                    throw new GlobalException("该犬在禁养名单");
                }
            }
            //下面是原本的逻辑
            if (petCertificates.getImmuneStr() == null || "".equals(petCertificates.getImmuneStr())) {
                petCertificatesService.saveOrUpdate(petCertificates);
            } else {
                ImmuneRegister immuneRegister = JSON.parseObject(petCertificates.getImmuneStr(), ImmuneRegister.class);
                //如果免疫ID 为空新增免疫信息
                if ("".equals(petCertificates.getImmuneId()) || null == petCertificates.getImmuneId()) {
                    //如果疫苗品牌不为空时，录入免疫信息
                    if (!"".equals(immuneRegister.getVaccineBrand()) && null != immuneRegister.getVaccineBrand()) {
                        //获取免疫ID
                        immuneRegister.preInsert();
                        //犬只存入免疫登记ID
                        petCertificates.setImmuneId(immuneRegister.getId());
                        //保存犬只信息
                        petCertificatesService.saveOrUpdate(petCertificates);
                        //犬只ID存入免疫信息
                        immuneRegister.setPetId(petCertificates.getId());
                        immuneRegister.setAboutMake(petCertificates.getAboutMake());//预约信息
                        immuneRegister.setHospitalId(petCertificates.getHospitalId());//免疫医院ID
                        immuneRegister.setAboutDate(petCertificates.getAboutDate());//预约时间
                        dao.insert(immuneRegister);
                        dao.nextval("pet_card");//录入免疫信息的时候改变免疫号序列
                    } else {
                        petCertificatesService.saveOrUpdate(petCertificates);
                    }
                } else {
                    petCertificatesService.saveOrUpdate(petCertificates);
                    immuneRegister.setPetId(petCertificates.getId());
                    dao.update(immuneRegister);
                }
            }
            String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
            //流程记录
            PetRecord petRecord = new PetRecord();
            petRecord.preInsert();
            petRecord.setCreateName(nickName);
            petRecord.setPetId(petCertificates.getId());//犬牌ID
            petRecord.setPetIdCard(petCertificates.getPetIdCard());
            petRecord.setPetNum(petCertificates.getPetNum());
            petRecord.setNode(3);
            petRecord.setStatus(0);
            petRecord.setStrDate(JSON.toJSONString(petCertificates));
            petRecordDao.insert(petRecord);
            return AjaxResult.success(petCertificates.getId());
        }
    }

    /**
     * posiCard 正面身份证
     * sideCard 侧面身份证
     * petImgZ 狗正面
     * petImgF 狗侧面
     * @param id
     * @return
     */
    public PetCertificates getByRegister(String id) {//犬只ID
        PetCertificates petCertificates = petCertificatesDao.getById(id);//根据犬只ID，查询犬只信息。
        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.setInstanceId(id);
        List<SysUploadFile> upList = uploadFileService.getList(sysUploadFile);
        petCertificates.setUploadFiles(upList);
        petCertificates.setImmuneRegister(dao.getPetImmune(id, petCertificates.getImmuneId()));//根据犬只ID，查询最新一条免疫信息。
        return petCertificates;
    }

    public ImmuneRegister getByPetId(String petId, String immuneId) {
        return dao.getByPetId(petId, immuneId);
    }


    /**
     * 办证审核（执法端）
     *
     * @param immuneRegister
     * @return
     */
    @Transactional
    public String updateStatus(ImmuneRegister immuneRegister) {
        // UserCache user = ThreadLocalUtil.getCurrentUser();
        String str = "";
        PetCertificates dog = new PetCertificates();
        dog.setId(immuneRegister.getPetId());
        dog.setApplyStatus(Integer.parseInt(immuneRegister.getStatus()));//犬证申请状态 3 ：审核通过 4：审核不通过
        dog.setApplyReason(immuneRegister.getReason());//审核原因
        if ("3".equals(immuneRegister.getStatus()) && "".equals(immuneRegister.getPetNum())) {
//            PetBrand petBrand = new PetBrand();
//            petBrand.setArea(immuneRegister.getAreaCode());
//            petBrand.setNum(1);
//            petBrand.setBrandCom(user.getId());//执法队根据用户ID，查询犬牌
//            List<PetBrand> list = petBrandDao.getListByNum(petBrand);
//            if (list.size() <= 0) {
//                return "审核失败，暂无此区域犬牌。";
//            }
//            PetBrand brand = list.get(0);
//            brand.setQrCode(QRCode.createQrImg(petBrandServiceUrl + brand.getBrandNum()));
//            petBrandDao.assignDogCard(brand);/*生成犬牌码*/
//            dog.setPetNum(brand.getBrandNum());
//            petBrandDao.updatePetNumUse(dog.getPetNum(), "1");
            str = "审核成功。";
        } else if ("3".equals(immuneRegister.getStatus()) && !"".equals(immuneRegister.getPetNum())) {
            /*生成犬牌码*/
            PetBrand p = new PetBrand();
            p.setIsReceipt(2);
            p.setBrandNum(immuneRegister.getPetNum());
            PetBrand brand = petBrandDao.getByEntity(p);
            if (brand != null) {
                brand.setQrCode(QRCode.createQrImg(petBrandServiceUrl + immuneRegister.getPetNum()));
                petBrandDao.assignDogCard(brand);
            }
            petBrandDao.updatePetNumUse(immuneRegister.getPetNum(), "1");
            str = "审核成功";
        } else if ("4".equals(immuneRegister.getStatus()) && !"".equals(immuneRegister.getPetNum())) {//审核不通过
//            petBrandDao.updatePetNumUse(immuneRegister.getPetNum(), "2");//还原犬牌状态
//            dog.setPetNum("");//删除犬只表犬牌
            str = "审核成功";
        } else {
            str = "审核成功";
        }
        petCertificatesDao.updateImmune(dog);//犬只分配犬牌
        immuneRegister.preUpdate();
        dao.updateStatus(immuneRegister);

        //审核通过激活犬牌
        PetCertificates certificates = petCertificatesDao.getById(immuneRegister.getPetId());
        if ("3".equals(immuneRegister.getStatus()) && StringUtils.isNotEmpty(immuneRegister.getPetNum())) {
            if (certificates != null) {
                certificates.setActivation("2");
                petCertificatesDao.updateImmune(certificates);
            }
        }
        String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
        //流程记录
        PetRecord petRecord = new PetRecord();
        petRecord.preInsert();
        petRecord.setCreateName(nickName);
        petRecord.setPetId(immuneRegister.getPetId());//犬牌ID
        petRecord.setPetIdCard(certificates.getPetIdCard());
        petRecord.setPetNum(certificates.getPetNum());
        petRecord.setNode(2);
        if ("3".equals(immuneRegister.getStatus())) {//审核通过
            petRecord.setStatus(2);
        } else if ("4".equals(immuneRegister.getStatus())) {//审核不通过
            petRecord.setStatus(3);
        }
        petRecord.setRemark(immuneRegister.getReason());//原因
        petRecord.setStrDate(JSON.toJSONString(certificates));
        petRecordDao.insert(petRecord);

        return str;
    }

    @Transactional
    public void createUser(String tel, String realName, String petIdCard) {
        // TODO:创建用户(免疫过户会创建用户，逻辑需要梳理)
        // SysUser sysUser = sysUserDao.getUserInfo(petIdCard);
        // if (sysUser == null) {
        //     SysUser sys = new SysUser();
        //     sys.setUserName(tel);
        //     sys.setRealName(realName);
        //     sys.setMobile(tel);
        //     sys.setUserType(1);
        //     sys.setIdCard(petIdCard);
        //     sys.preInsert();
        //     sys.setPassword(DigestUtils.md5DigestAsHex("123456".getBytes()));
        //     sysUserDao.insert(sys);
        //     //创建医院饲主自动分权限。
        //     List<SysUserRole> list = new ArrayList<SysUserRole>();
        //     SysUserRole userRole = new SysUserRole();
        //     userRole.preInsert();
        //     userRole.setUserId(sys.getId());
        //     userRole.setRoleId("b71e95194d2444e893eeca686427c370");
        //     list.add(userRole);
        //     if (list.size() > 0) {
        //         sysUserRoleDao.saveList(list);
        //     }
        // }
    }

    /**
     * 犬牌申请 （医院，个人）
     *
     * @param petCertificates
     * @return
     */
    @Transactional
    public void savePetApply(PetCertificates petCertificates) {
        // UserCache user = ThreadLocalUtil.getCurrentUser();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        PetCertificates pet = petCertificatesDao.getById(petCertificates.getId());
        if (pet.getApplyStatus() == 2) {
            return;
        }
        if (StringUtils.isNotEmpty(pet.getPetNum()) && !pet.getPetNum().equals(petCertificates.getPetNum())) {//把旧的犬牌变为未使用
            petBrandDao.updatePetNumUse(pet.getPetNum(), "2");
        }
        petCertificates.setCertId(user.getDogUserId());//犬牌申请用户ID
        petCertificates.setCertDate(new Date());//犬牌申请时间
        petCertificates.setApplyStatus(2);//犬牌申请状态 2：待审核
        petCertificatesService.updateImmune(petCertificates);//修改犬只信息
        dao.updateApply(petCertificates.getImmuneId());//修改申请状态
        //更改犬牌状态，更改为已使用
        if (StringUtils.isNotEmpty(petCertificates.getPetNum())) {
            petBrandDao.updatePetNumUse(petCertificates.getPetNum(), "1");
        }
        String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
        //流程记录
        PetRecord petRecord = new PetRecord();
        petRecord.preInsert();
        petRecord.setCreateName(nickName);
        petRecord.setPetId(pet.getId());//犬牌ID
        petRecord.setPetIdCard(pet.getPetIdCard());
        petRecord.setPetNum(petCertificates.getPetNum());
        petRecord.setNode(1);
        petRecord.setStatus(1);
        petRecord.setStrDate(JSON.toJSONString(pet));
        petRecordDao.insert(petRecord);

    }


    /**
     * 获取免疫证号
     *
     * @param
     * @return
     */
    public String randomMY() {
        String str = dao.currval("pet_card");
        if (str.contains("999999")) {
            String seqCode = this.getNextUpEn(str.substring(2, 3).toLowerCase()).toUpperCase();
            dao.updateval("pet_card", "MY" + seqCode);
        }
        return str;
    }

    public String getNextUpEn(String en) {
        char lastE = 'a';
        char st = en.toCharArray()[0];
        if (Character.isUpperCase(st)) {
            if (en.equals("Z")) {
                return "A";
            }
            if (en == null || en.equals("")) {
                return "A";
            }
            lastE = 'Z';
        } else {
            if (en.equals("z")) {
                return "a";
            }
            if (en == null || en.equals("")) {
                return "a";
            }
            lastE = 'z';
        }
        int lastEnglish = (int) lastE;
        char[] c = en.toCharArray();
        if (c.length > 1) {
            return null;
        } else {
            int now = (int) c[0];
            if (now >= lastEnglish)
                return null;
            char uppercase = (char) (now + 1);
            return String.valueOf(uppercase);
        }
    }


    /**
     * 犬只大屏查询
     *
     * @param
     * @return
     */
    public List<HashMap> getHospital() {
        return dao.getHospital();
    }

    public List<HashMap> getImmune() {
        return dao.getImmune();
    }

    public List<HashMap> getPetNum() {
        return dao.getPetNum();
    }
}
