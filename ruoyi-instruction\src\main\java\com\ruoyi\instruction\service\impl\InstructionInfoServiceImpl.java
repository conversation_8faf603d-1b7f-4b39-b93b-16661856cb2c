package com.ruoyi.instruction.service.impl;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.dtalk.UserDtalkService;
import com.ruoyi.common.dtalk.vo.MobileUserResp;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.IDCardUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.instruction.domain.*;
import com.ruoyi.instruction.domain.reqVo.InstructionFlowTestVo;
import com.ruoyi.instruction.domain.rspVo.InstructionInfoRspVo;
import com.ruoyi.instruction.domain.rspVo.InstructionInfomiddleResVo;
import com.ruoyi.instruction.mapper.*;
import com.ruoyi.instruction.service.IInstructionInfoService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysDictDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.DictUtils.getDictValue;

/**
 * 指令基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@Service
public class InstructionInfoServiceImpl implements IInstructionInfoService {

    private static final Logger log = LoggerFactory.getLogger(InstructionInfoServiceImpl.class);

    /**
     * 指令mapper
     */
    @Autowired
    private InstructionInfoMapper instructionInfoMapper;

    /**
     * 人员mapper
     */
    @Autowired
    private InstrucationPersonMapper instrucationPersonMapper;

    /**
     * 事件mapper
     */
    @Autowired
    private InstructionEventMapper eventMapper;

    /**
     * 交办mapper
     */
    @Autowired
    private InstructionAssignMapper assignMapper;

    /**
     * 接收mapper
     */
    @Autowired
    private InstructionReceiveMapper receiveMapper;

    /**
     * 转交mapper
     */
    @Autowired
    private InstructionTransferMapper transferMapper;

    /**
     * 处置mapper
     */
    @Autowired
    private InstructionDisposeMapper disposeMapper;

    /**
     * 反馈mapper
     */
    @Autowired
    private InstructionFeedbackMapper feedbackMapper;

    /**
     * 销号mapper
     */
    @Autowired
    private InstructionEndMapper endMapper;

    @Autowired
    private IndicatorTypeMapper typeMapper;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private InstructionEventServiceImpl eventService;

    @Autowired
    private InstructionCountyFeedbackMapper countyFeedbackMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    UserDtalkService userDtalkService;

    @Value("${single.url}")
    private String singleUrl;
    @Value("${single.pcUrl}")
    private String singlePcUrl;

    /**
     * 查询指令基本信息
     *
     * @param id 指令基本信息主键
     * @return 指令基本信息
     */
    @Override
    public InstructionInfo selectInstructionInfoById(Long id) {
        InstructionInfo instructionInfo = instructionInfoMapper.selectInstructionInfoById(id);
        //查询事件类型
        IndicatorType indicatorType = typeMapper.selectIndicatorTypeById(Long.valueOf(instructionInfo.getType()));
        instructionInfo.setType(indicatorType.getTypeName());
        String receiveUnit = instructionInfo.getReceiveUnit();
        instructionInfo.setTypeId(indicatorType.getId());
        if (receiveUnit.length() > 0) {
            String substring = receiveUnit.substring(1, receiveUnit.length() - 1);
            String replace = substring.replace(" ", "");
            String[] split = replace.split(",");
            instructionInfo.setUnit(split);
        }

        return instructionInfo;
    }

    /**
     * 通过指令接收部门获取正在处置的区县 过滤 委政法委/政法委
     *
     * @param receiveUnit
     * @return
     */
    private String getUnit(final String receiveUnit) {
        String finaUint = "";
        String[] split = receiveUnit.substring(1, receiveUnit.length() - 1).split(",");
        if (split.length > 0) {
            StringBuilder stringBuilder = new StringBuilder();
            for (String str : split) {
                if (str.contains("委政法委")) {
                    String s = str.replace("委政法委", "");
                    stringBuilder.append(s + ",");
                } else {
                    String s = str.replace("政法委", "");
                    stringBuilder.append(s + ",");
                }
            }
            finaUint = stringBuilder.substring(0, stringBuilder.length() - 1).toString();

        }
        return finaUint;
    }


    /**
     * 新增指令基本信息
     *
     * @param instructionInfo 指令基本信息
     * @return 结果
     */
    @Override
    public AjaxResult insertInstructionInfo(InstructionInfo instructionInfo) {
        AjaxResult result = new AjaxResult();

        //获取当前操作用户名、用户部门id
        String username = SecurityUtils.getUsername();
        Long deptId = SecurityUtils.getDeptId();
        String deptName = SecurityUtils.getLoginUser().getUser().getDept().getDeptName();
        //批量插入用户信息
        Set<Long> idsSet = new HashSet<>();
        Set<String> places = new HashSet<>();
        Set<Long> leadIds = new HashSet<>();
        List<InstrucationPerson> personList = instructionInfo.getPersonList();
        if (personList != null && personList.size() > 0) {
            for (InstrucationPerson person : personList) {
                try {
                    //根据人员信息查询责任所属地
                    String personDutyPlace = getDutyPlaceByPerson(person);
                    if (personDutyPlace.equals("")) {
                        return AjaxResult.error("无法正确匹配" + person.getPersonName() + "人员责任所属地,请重新填写");
                    }
                    if (deptId != 202) {
                        //当前交办员不是金华市市委政法委部门时,需要判断该用户是不是通过县市区
                        if (!deptName.contains(personDutyPlace)) {
                            return AjaxResult.error("添加人员责任归属地应在当前县市区内");
                        }
                    }
                    if (person.getIdCard() != null) {
                        String encryptedIDCard = IDCardUtils.encryptIDCard(person.getIdCard());
                        person.setIdCard(encryptedIDCard);
                    }
                    InstrucationPerson instrucationPerson = instrucationPersonMapper.selectInstrucationPersonByIdCard(person.getPersonName(), person.getIdCard());
                    person.setDutyPlace(personDutyPlace);
                    if (person.getId() != null) {
                        person.setUpdateBy(username);
                        person.setUpdateTime(new Date());
                        instrucationPersonMapper.updateInstrucationPerson(person);
                    } else if (instrucationPerson != null) {
                        person.setId(instrucationPerson.getId());
                        person.setUpdateBy(username);
                        person.setUpdateTime(new Date());
                        instrucationPersonMapper.updateInstrucationPerson(person);
                    } else {
                        person.setCreateBy(username);
                        person.setCreateTime(new Date());
                        instrucationPersonMapper.insertInstrucationPerson(person);
                    }
                    idsSet.add(person.getId());
                    if (person.getIsLead() != null) {
                        //是领头人
                        leadIds.add(person.getId());
                    }
                    if (personDutyPlace != null && personDutyPlace.length() > 0) {
                        //通过字典表查询出
                        String place = getDictValueByLable(personDutyPlace, "zl_department_received");
                        places.add(place);
                    }
                } catch (Exception e) {
                    log.error("msg", e);
                }

            }

            if (leadIds.size() > 0) {
                String leadIdsStr = leadIds.toString().replace(" ", "");
                String leadId = leadIdsStr.substring(1, leadIdsStr.length() - 1);
                instructionInfo.setLeadPersonIds(leadId);
            }
            if (places.size() > 0) {
                instructionInfo.setReceiveUnit(places.toString());
            }
            String s1 = idsSet.toString().replace(" ", "");
            String substring1 = s1.substring(1, s1.length() - 1);
            instructionInfo.setPersonIds(substring1);
        }


        instructionInfo.setCreatorBy(username);
        //生成指令编码
        int num = (int) (Math.random() * 9000) + 1000;
        String instructionCode = "JHZFW" + DateUtils.dateTimeNow() + num;
        instructionInfo.setCreateDeptId(deptId);
        instructionInfo.setInstructionCode(instructionCode);
        instructionInfo.setCreateTime(DateUtils.getNowDate());

        int row = instructionInfoMapper.insertInstructionInfo(instructionInfo);
        /**
         * 如果有事件id则表示从事件列表中带入 无需再次创建事件信息
         */
        addEventInfo(instructionInfo);

        if (row > 0) {
            InstructionInfo instructionInfo1 = instructionInfoMapper.selectInstructionInfoById(instructionInfo.getId());
            String receiveUnit = instructionInfo1.getReceiveUnit();
            String[] split = receiveUnit.substring(1, receiveUnit.length() - 1).replace(" ", "").split(",");
            for (String s : split) {
                //根据接收单位名称、指令id 查询是否有接收记录 无则新增
                InstructionReceive receive = new InstructionReceive();
                receive.setStatus(1);
                receive.setReceiveDept(s);
                receive.setInstrucationId(instructionInfo1.getId());
                //通过接收部门查询接收部门Id
                Long receiveDeptId = StringUtils.getDeptId(s);
                receive.setReceiveDeptId(receiveDeptId);
                List<InstructionReceive> receiveList = receiveMapper.selectInstructionReceiveList(receive);
                if (receiveList.size() == 0) {
                    receive.setCreateTime(new Date());
                    receive.setCreateBy(username);
                    int i = receiveMapper.insertInstructionReceive(receive);
                }
                //发送工作通知
                //获取需要推送的人员列表
                List<SysUser> deptNameGetUserList = sysUserMapper.getDeptNameGetUserList(s);
                if (!CollectionUtils.isEmpty(deptNameGetUserList)){
                    for (SysUser sysUser : deptNameGetUserList){
                        try {
                            MobileUserResp mobileUserResp = userDtalkService.getDingtalkAppUserByMobile(sysUser.getPhonenumber());
                            String uniqueId = com.ruoyi.common.utils.zlb.Constants.XIE_TONG_WORD_ZZD + IdUtils.fastUUID();
                            CompletableFuture.runAsync(() -> {
                                userDtalkService.workNotification(mobileUserResp.getAccountId() + "", uniqueId,
                                        "指令下达",
                                        "你有一条指令，请及时处置！",
                                        singleUrl, singlePcUrl);
                            });
                        }catch (Exception e) {
                            log.error("msg", e);
                        }

                    }
                }
            }

            result.put("data", instructionInfo1);
            result.put("msg", "操作成功");
            result.put("code", 200);
        } else {
            return AjaxResult.error();
        }

        return result;
    }

    /**
     * 通过人员信息查询出责任所属地
     *
     * @param person
     * @return
     */
    private String getDutyPlaceByPerson(final InstrucationPerson person) {
        String dutyPlace = "";
        /**
         * 获取责任归属地
         * 户籍所在地为金华的用户籍所在地，户籍不是金华的选居住地，都没有的责任所属地提示必填
         */
        String housePlace = person.getHousePlace();
        String currentPlace = person.getCurrentPlace();
        if (person.getDutyPlace() != null && person.getDutyPlace().length() > 0) {
            dutyPlace = person.getDutyPlace();
        } else if (housePlace != null && housePlace.contains("金华")) {
            //判断户籍所在地是否在金华
            dutyPlace = getCountyDistrict(housePlace);
        } else if (currentPlace != null && currentPlace.contains("金华")) {
            dutyPlace = getCountyDistrict(currentPlace);
        }
        return dutyPlace;
    }

    /**
     * 获取县市区信息
     *
     * @param place
     * @return
     */
    private String getCountyDistrict(final String place) {
        String dutyPlace = "";
        if (place.contains("婺城")) {
            dutyPlace = "婺城区";
        } else if (place.contains("金东")) {
            dutyPlace = "金东区";
        } else if (place.contains("兰溪")) {
            dutyPlace = "兰溪市";
        } else if (place.contains("东阳")) {
            dutyPlace = "东阳市";
        } else if (place.contains("义乌")) {
            dutyPlace = "义乌市";
        } else if (place.contains("永康")) {
            dutyPlace = "永康市";
        } else if (place.contains("浦江")) {
            dutyPlace = "浦江县";
        } else if (place.contains("磐安")) {
            dutyPlace = "磐安县";
        } else if (place.contains("开发")) {
            dutyPlace = "开发区";
        }
        return dutyPlace;
    }

    /**
     * 通过字典标签、字典类型查询字典键值
     *
     * @param dictLabel
     * @param dictType
     * @return
     */
    private String getDictValueByLable(final String dictLabel, final String dictType) {
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictLabel(dictLabel);
        sysDictData.setDictType(dictType);
        List<SysDictData> sysDictData1 = dictDataService.selectDictDataList(sysDictData);
        return sysDictData1.get(0).getDictValue();
    }

    /**
     * 根据指令信息创建事件信息
     *
     * @param instructionInfo
     */
    private void addEventInfo(final InstructionInfo instructionInfo) {
        String username = SecurityUtils.getUsername();
        InstructionEvent instructionEvent = new InstructionEvent();
        BeanUtils.copyProperties(instructionInfo, instructionEvent);
        instructionEvent.setInfoSource(instructionInfo.getSourceInfo());
        instructionEvent.setInstructionId(instructionInfo.getId());
        instructionEvent.setDutyUnit(instructionInfo.getReceiveUnit());
        instructionEvent.setEventTitle(instructionInfo.getInstructionTitle());
        instructionEvent.setPushTime(instructionInfo.getAssignTime());
        instructionEvent.setBaseSituation(instructionInfo.getBaseInfo());
        instructionEvent.setDisposeSituation(instructionInfo.getInstructionContent());
        if (instructionInfo.getEventId() != null) {
            instructionEvent.setUpdateTime(new Date());
            instructionEvent.setUpdateBy(username);
            instructionEvent.setId(instructionInfo.getEventId());
            eventMapper.updateInstructionEvent(instructionEvent);
        } else {
            Long deptId = SecurityUtils.getDeptId();
            instructionEvent.setCreateDeptId(deptId);
            instructionEvent.setCreateBy(username);
            instructionEvent.setCreateTime(new Date());
            eventMapper.insertInstructionEvent(instructionEvent);
        }
        eventService.SetPersonControl(instructionEvent);

    }


    /**
     * 修改指令基本信息
     *
     * @param instructionInfo 指令基本信息
     * @return 结果
     */
    @Override
    public int updateInstructionInfo(InstructionInfo instructionInfo) {

        //获取当前操作用户名、
        String username = SecurityUtils.getUsername();
        //批量插入用户信息
        Set<Long> idsSet = new HashSet<>();
        Set<String> places = new HashSet<>();
        Set<Long> leadIds = new HashSet<>();
        List<InstrucationPerson> personList = instructionInfo.getPersonList();
        if (personList != null && personList.size() > 0) {
            for (InstrucationPerson person : personList) {
                try {
                    //根据人员信息查询责任所属地
                    String personDutyPlace = getDutyPlaceByPerson(person);
                    person.setDutyPlace(personDutyPlace);
                    if (person.getIdCard() != null) {
                        String encryptedIDCard = IDCardUtils.encryptIDCard(person.getIdCard());
                        person.setIdCard(encryptedIDCard);
                    }
                    InstrucationPerson instrucationPerson = instrucationPersonMapper.selectInstrucationPersonByIdCard(person.getPersonName(), person.getIdCard());
                    if (person.getId() != null) {
                        person.setUpdateTime(new Date());
                        person.setUpdateBy(username);
                        instrucationPersonMapper.updateInstrucationPerson(person);
                    } else if (instrucationPerson != null) {
                        person.setUpdateTime(new Date());
                        person.setUpdateBy(username);
                        person.setId(instrucationPerson.getId());
                        instrucationPersonMapper.updateInstrucationPerson(person);
                    } else {
                        person.setCreateBy(username);
                        person.setCreateTime(new Date());
                        instrucationPersonMapper.insertInstrucationPerson(person);
                    }
                    idsSet.add(person.getId());
                    if (person.getIsLead() != null) {
                        //是领头人
                        leadIds.add(person.getId());
                    }
                    if (personDutyPlace != null && personDutyPlace.length() > 0) {
                        //通过字典表查询出
                        String place = getDictValueByLable(personDutyPlace, "zl_department_received");
                        places.add(place);
                    }

                } catch (Exception e) {
                    log.error("msg", e);
                }

            }

            if (leadIds.size() > 0) {
                String leadIdsStr = leadIds.toString().replace(" ", "");
                String leadId = leadIdsStr.substring(1, leadIdsStr.length() - 1);
                instructionInfo.setLeadPersonIds(leadId);
            }
            //接收单位判断逻辑,先看人员责任归属地,如果没有人员归属地则用用户选择的接收单位
            if (places.size() > 0) {
                instructionInfo.setReceiveUnit(places.toString());
            }
            String s1 = idsSet.toString().replace(" ", "");
            String substring1 = s1.substring(1, s1.length() - 1);
            instructionInfo.setPersonIds(substring1);
        } else {
            instructionInfo.setPersonIds("-1");
            instructionInfo.setLeadPersonIds("-1");
        }


        //现在的责任归属地信息
        String replace = instructionInfo.getReceiveUnit().replace(" ", "");
        String[] nowReceiveUnit = replace.substring(1, replace.length() - 1).split(",");
        //之前的责任归属地
        String receiveUnit = instructionInfoMapper.selectInstructionInfoById(instructionInfo.getId()).getReceiveUnit().replace(" ", "");
        String[] oldReceiveUnit = receiveUnit.substring(1, receiveUnit.length() - 1).split(",");
        //比较获取两者区别
        Map<String, List<String>> compareElement = StringUtils.getCompareElement(nowReceiveUnit, oldReceiveUnit);
        //删除记录->strArr2Def
        List<String> delUnit = compareElement.get("strArr2Def");
        if (delUnit.size() > 0) {
            for (String unit : delUnit) {
                //1、根据指令id、status=1、部门 查询出接收记录
                InstructionReceive receive = new InstructionReceive();
                receive.setStatus(1);
                receive.setReceiveDept(unit);
                receive.setInstrucationId(instructionInfo.getId());
                List<InstructionReceive> receiveList = receiveMapper.selectInstructionReceiveList(receive);
                if (receiveList != null && receiveList.size() > 0) {
                    InstructionReceive receive1 = receiveList.get(0);
                    //删除接收记录
                    receiveMapper.deleteInstructionReceiveById(receive1.getId());
                    //查询其下的转交记录
                    InstructionTransfer transfer = new InstructionTransfer();
                    transfer.setStatus(1);
                    transfer.setReceiveId(receive1.getId());
                    List<InstructionTransfer> transferList = transferMapper.selectInstructionTransferList(transfer);
                    if (transferList != null && transferList.size() > 0) {
                        for (InstructionTransfer transferInfo : transferList) {
                            //删除转交记录
                            transferMapper.deleteInstructionTransferById(transferInfo.getId());
                            //查询处置信息
                            InstructionDispose dispose = disposeMapper.selectInstructionDisposeByTransferId(transferInfo.getId());
                            if (dispose != null) {
                                //删除反馈记录
                                InstructionFeedback feedback = feedbackMapper.selectInstructionFeedbackByDisposeId(dispose.getId());
                                if (feedback != null) {
                                    feedbackMapper.deleteInstructionFeedbackById(feedback.getId());
                                }
                            }
                        }
                    }
                }
            }
        }
        //新增记录->取strArr1Def
        List<String> addUnit = compareElement.get("strArr1Def");
        if (addUnit.size() > 0) {
            for (String unit : addUnit) {
                InstructionReceive receive = new InstructionReceive();
                receive.setStatus(1);
                receive.setReceiveDept(unit);
                receive.setInstrucationId(instructionInfo.getId());
                receiveMapper.insertInstructionReceive(receive);
            }
        }

        //查询出指令对应的事件id
        InstructionEvent event = eventMapper.selectInstructionEventByInstrucationId(instructionInfo.getId());
        if (event != null && event.getId() != null) {
            instructionInfo.setEventId(event.getId());
            addEventInfo(instructionInfo);
        }
        instructionInfo.setUpdateTime(DateUtils.getNowDate());
        return instructionInfoMapper.updateInstructionInfo(instructionInfo);
    }


    /**
     * 批量删除指令基本信息
     *
     * @param ids 需要删除的指令基本信息主键
     * @return 结果
     */
    @Override
    public int deleteInstructionInfoByIds(Long[] ids) {
        return instructionInfoMapper.deleteInstructionInfoByIds(ids);
    }

    /**
     * 删除指令基本信息信息
     *
     * @param id 指令基本信息主键
     * @return 结果
     */
    @Override
    public int deleteInstructionInfoById(Long id) {

        delPerson(id);
        //同步删除 交办、接收、转交、反馈、销号
        //1、删除 交办信息
        assignMapper.deleteInstructionAssignByInstructionId(id);
        //2、删除接收记录
        receiveMapper.deleteInstructionReceiveByInstructionId(id);
        //3、删除转交记录
        transferMapper.deleteInstructionTransferByInstructionId(id);
        //4、删除反馈记录
        feedbackMapper.deleteInstructionFeedbackByInstructionId(id);
        //删除事件记录
        // InstructionEvent event = eventMapper.selectInstructionEventByInstrucationId(id);
        // if (event!=null){
        //     eventService.deleteInstructionEventById(event.getId());
        // }
        return instructionInfoMapper.deleteInstructionInfoById(id);
    }

    /**
     * 删除指令关联人员id
     *
     * @param id
     */
    private void delPerson(final Long id) {
        InstructionInfo instructionInfo = instructionInfoMapper.selectInstructionInfoById(id);
        if (instructionInfo.getPersonIds() != null && instructionInfo.getPersonIds().length() > 0) {
            String[] split = instructionInfo.getPersonIds().split(",");
            InstrucationPerson instrucationPerson = new InstrucationPerson();
            Map<String, Object> params = instrucationPerson.getParams();
            params.put("ids", split);
            instrucationPerson.setParams(params);
            List<InstrucationPerson> personList = instrucationPersonMapper.selectInstrucationPersonList(instrucationPerson);
            List<Long> delPersonIds = personList.stream().filter(person -> person.getEventNum() == 0).map(InstrucationPerson::getId).collect(Collectors.toList());
            if (delPersonIds.size() > 0) {
                //删除人员
                Long[] longs = delPersonIds.toArray(new Long[delPersonIds.size()]);
                instrucationPersonMapper.deleteInstrucationPersonByIds(longs);
            }
        }
    }


    /**
     * 测试提交指令流程
     *
     * @param instructionFlowTestVo
     * @return
     */
    @Override
    public AjaxResult testSubmit(final InstructionFlowTestVo instructionFlowTestVo) {
        try {
            //获取用户信息
            List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
            List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
            String username = SecurityUtils.getUsername();
            String deptName = SecurityUtils.getLoginUser().getUser().getDept().getDeptName();
            Long deptId = SecurityUtils.getDeptId();
            //根据操作用户角色执行相应逻辑
            if (roleList.contains(InstructionRolesConstants.ASSIGN) || roleList.contains(InstructionRolesConstants.ADMIN)) {
                //交办员权限可以新增交办信息
                //判断交办信息主键是否已存在
                InstructionAssign assign = instructionFlowTestVo.getAssign();
                if (assign.getId() == null) {
                    assign.setInstructionId(instructionFlowTestVo.getInfoId());
                    assign.setCreateBy(username);
                    assign.setCreateTime(new Date());
                    assign.setAssignDeptId(deptId);
                    assign.setInstructionId(instructionFlowTestVo.getInfoId());
                    assignMapper.insertInstructionAssign(assign);
                } else {
                    assign.setUpdateTime(new Date());
                    assign.setUpdateBy(username);
                    assignMapper.updateInstructionAssign(assign);
                }

            }
            if (roleList.contains(InstructionRolesConstants.RECEIVE)) {
                //接收员权限：创建、修改接收信息，选择转交部门部门创建转交部门信息
                List<InstructionReceive> receiveList = instructionFlowTestVo.getReceiveList();
                if (receiveList != null && receiveList.size() > 0) {
                    for (InstructionReceive receive : receiveList) {
                        //如果是同部门的接收单才可操作
                        if (receive.getReceiveDeptId().equals(deptId)) {
                            //更新接收信息(接收记录已在创建指令时创建)
                            receiveMapper.updateInstructionReceive(receive);
                            if (receive.getTransferList() != null && receive.getTransferList().size() > 0) {
                                List<InstructionTransfer> transferList = receive.getTransferList();
                                for (InstructionTransfer transfer : transferList) {
                                    if (transfer.getId() == null) {
                                        //通过接收id、转交部门进行查询
                                        InstructionTransfer transfer1 = new InstructionTransfer();
                                        transfer1.setStatus(1);
                                        transfer1.setReceiveId(receive.getId());
                                        transfer1.setTransferDeptId(transfer.getTransferDeptId());
                                        transfer1.setTransferDept(transfer.getTransferDept());
                                        List<InstructionTransfer> transferList1 = transferMapper.selectInstructionTransferList(transfer1);
                                        if (CollectionUtils.isEmpty(transferList1)) {
                                            transfer.setReceiveId(receive.getId());
                                            transfer.setInstructionId(instructionFlowTestVo.getInfoId());
                                            transferMapper.insertInstructionTransfer(transfer);
                                        }
                                    } else {
                                        //转交记录有删除则删除该转交记录下的处置、反馈记录
                                        if (transfer.getStatus() != null) {
                                            if (transfer.getStatus() == 9) {
                                                //删除转交记录
                                                delTransfer(transfer);
                                            } else {
                                                transferMapper.updateInstructionTransfer(transfer);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (roleList.contains(InstructionRolesConstants.FEEDBACK)) {
                //反馈权限 新增、修改反馈记录
                List<InstructionFeedback> feedbackList = instructionFlowTestVo.getFeedbackList().stream().filter(instructionFeedback -> instructionFeedback.getFeedbackDeptId().equals(deptId)).collect(Collectors.toList());
                //进行筛选判断
                boolean flag = JudeFeedBack(feedbackList);
                if (!flag) {
                    return AjaxResult.error("反馈记录仅最后一条记录可选择已办结");
                }
                //查询接收记录
                List<InstructionReceive> receiveList = instructionFlowTestVo.getReceiveList();
                InstructionReceive receive = new InstructionReceive();
                for (InstructionReceive receiveInfo : receiveList) {
                    List<InstructionTransfer> collect = receiveInfo.getTransferList().stream().filter(transfer -> transfer.getTransferDeptId().equals(deptId)).collect(Collectors.toList());
                    if (collect != null && collect.size() > 0) {
                        receive = receiveInfo;
                        break;
                    }
                }
                if (feedbackList != null && feedbackList.size() > 0) {
                    for (InstructionFeedback feedback : feedbackList) {
                        if (feedback.getId() == null) {
                            feedback.setInstructionId(instructionFlowTestVo.getInfoId());
                            List<InstructionTransfer> transferList = receive.getTransferList();
                            InstructionTransfer transfer = transferList.stream().filter(transfer1 -> transfer1.getTransferDeptId().equals(feedback.getFeedbackDeptId())).findFirst().orElse(null);
                            feedback.setTransferId(transfer.getId());
                            feedback.setCreateBy(username);
                            feedback.setCreateTime(new Date());
                            feedbackMapper.insertInstructionFeedback(feedback);
                        } else {
                            //判断反馈信息、相关证明文件、是否办结字段是否有更新
                            feedback.setInstructionId(instructionFlowTestVo.getInfoId());
                            feedbackMapper.updateInstructionFeedback(feedback);
                        }
                    }
                }

            }
            //县市区反馈
            List<InstructionCountyFeedback> countyFeedbackList = instructionFlowTestVo.getCountyFeedbackList();
            if (countyFeedbackList != null && countyFeedbackList.size() > 0) {
                for (InstructionCountyFeedback county : countyFeedbackList) {
                    county.setInstructionId(instructionFlowTestVo.getInfoId());
                    List<Map<String, Object>> maps = county.getMaps();
                    if (county.getId() == null) {
                        if (county.getIsEnd() == 2) {
                            //将对应乡镇街道办结结果设置为否
                            for (Map<String, Object> map : maps) {
                                Integer feedBackDeptId = (Integer) map.get("feedBackDeptId");
                                feedbackMapper.updateIsEnd(county.getInstructionId(), Long.valueOf(feedBackDeptId));
                            }
                        }
                        if (maps != null && maps.size() > 0) {
                            StringBuilder keysBuilder = new StringBuilder();
                            for (Map<String, Object> map : maps) {
                                String feedBackObject = (String) map.get("feedBackObject");
                                if (keysBuilder.length() > 0) {
                                    keysBuilder.append(", ");
                                }
                                keysBuilder.append(feedBackObject);
                            }
                            String keysSeparatedByComma = keysBuilder.toString();
                            county.setFeedbackObject(keysSeparatedByComma);
                        }else {
                            county.setFeedbackObject("市委政法委");
                        }
                        String remark = JSON.toJSONString(maps);
                        county.setRemark(remark);
                        //进行插入
                        county.setCreateBy(username);
                        county.setCreateTime(new Date());
                        county.setFeedbackDeptId(deptId);
                        county.setFeedbackDept(deptName);
                        countyFeedbackMapper.insertInstructionCountyFeedback(county);
                    }
                }
            }
            if (roleList.contains(InstructionRolesConstants.END)) {
                //查询指令基本信息 销号:用户有限号权限其用户部门为创建部门才可销号
                InstructionInfo instructionInfo = instructionInfoMapper.selectInstructionInfoById(instructionFlowTestVo.getInfoId());
                if (instructionInfo.getCreateDeptId().equals(deptId)) {
                    List<InstructionEnd> instructionEndList = instructionFlowTestVo.getInstructionEnd();
                    if (instructionEndList != null && instructionEndList.size() > 0) {
                        for (InstructionEnd instructionEnd : instructionEndList) {
                            //销号进行判断 如果创建部门非市本级账号则无县市区反馈,如果创建部门为市本级 则需判断县市区反馈（IsEnd = 1）个数是否与接收单位个数相同，相同则可以进行反馈
                            if (instructionEnd.getId() == null) {
                                String remark = JSON.toJSONString(instructionEnd.getMaps());
                                if (instructionInfo.getCreateDeptId() != 202L) {
                                    //县市区交办指令 没有县市区反馈流程
                                    if (instructionEnd.getFeedbackDept() != null && instructionEnd.getFeedbackDept() != "") {
                                        EndIsFalse(instructionEnd, instructionFlowTestVo);
                                    }
                                    instructionEnd.setRemark(remark);
                                    instructionEnd.setInstructionId(instructionFlowTestVo.getInfoId());
                                    endMapper.insertInstructionEnd(instructionEnd);
                                } else {
                                    //市本级交办指令 需要判断县市区反馈个数（IsEnd=1）
                                    //查询已反馈个数
                                    InstructionCountyFeedback instructionCountyFeedback = new InstructionCountyFeedback();
                                    instructionCountyFeedback.setStatus(1);
                                    instructionCountyFeedback.setInstructionId(instructionInfo.getId());
                                    instructionCountyFeedback.setIsEnd(1);
                                    List<InstructionCountyFeedback> instructionCountyFeedbacks = countyFeedbackMapper.selectInstructionCountyFeedbackList(instructionCountyFeedback);
                                    long receiveCount = instructionCountyFeedbacks.stream().map(InstructionCountyFeedback::getFeedbackDept).distinct().count();
                                    if (receiveCount == 0) {
                                        //县市区反馈个数不足
                                        return AjaxResult.error("存在县市区未反馈,不允许销号");
                                    }
                                    //查询需反馈个数
                                    String[] split = instructionInfo.getReceiveUnit().replace("[", "").replace("]", "").split(",");
                                    long count = split.length;
                                    if (count == receiveCount) {
                                        //允许销号
                                        //县市区交办指令 没有县市区反馈流程
                                        if (instructionEnd.getFeedbackDept() != null && instructionEnd.getFeedbackDept() != "") {
                                            EndCountyIsFalse(instructionEnd, instructionFlowTestVo);
                                        }
                                        instructionEnd.setRemark(remark);
                                        instructionEnd.setInstructionId(instructionFlowTestVo.getInfoId());
                                        endMapper.insertInstructionEnd(instructionEnd);
                                    } else {
                                        return AjaxResult.error("存在县市区未反馈,不允许销号");
                                    }
                                }

                                //如果进行销号、则将其处置、反馈回填至指令对应事件 单独用方法执行
                                if (instructionEnd.getInstrucationIsEnd() != null && instructionEnd.getInstrucationIsEnd().equals(1)) {
                                    updateInfoForEvent(instructionFlowTestVo.getInfoId());
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("错误信息", e);
        }
        return AjaxResult.success();
    }

    /**
     * 将县市区反馈内容状态设置为否
     *
     * @param instructionEnd
     * @param instructionFlowTestVo
     */
    private void EndCountyIsFalse(final InstructionEnd instructionEnd, final InstructionFlowTestVo instructionFlowTestVo) {
        List<Map<String, Object>> maps = instructionEnd.getMaps();
        for (Map<String, Object> map : maps) {
            Integer deptId = (Integer) map.get("feedBackDeptId");
            countyFeedbackMapper.updateByIstructionIdAndDept(instructionFlowTestVo.getInfoId(), Long.valueOf(deptId), 2);
        }
    }

    /**
     * 判断反馈是否符合条件
     *
     * @param feedbackList
     * @return
     */
    private boolean JudeFeedBack(final List<InstructionFeedback> feedbackList) {
        //查询出反馈集合中部门为同部门且已经已经办结的记录
        List<InstructionFeedback> collect = feedbackList.stream()
                .filter(instructionFeedback -> instructionFeedback.getFeedbackIsEnd() == 1)
                .collect(Collectors.toList());
        if (collect.size() > 1) {
            return false;
        } else if (collect.size() == 0) {
            return true;
        }
        //反馈记录已办结的记录有且只有一条
        int feedBackSize = feedbackList.size();
        Integer feedbackIsEnd = feedbackList.get(feedBackSize - 1).getFeedbackIsEnd();
        if (feedbackIsEnd == 1) {
            return true;
        }
        return false;
    }

    /**
     * 县市区销号为否
     * 如果销号填否时,创建反馈部门
     * 查询销号反馈部门对应的反馈记录
     *
     * @param instructionFlowTestVo
     */
    private void EndIsFalse(final InstructionEnd instructionEnd, final InstructionFlowTestVo instructionFlowTestVo) {
        List<Map<String, Object>> maps = instructionEnd.getMaps();
        for (Map<String, Object> map : maps) {
            Integer deptId = (Integer) map.get("feedBackDeptId");
            String dept = (String) map.get("feedBackObject");
            // 遍历Map的键值对
            List<InstructionFeedback> feedbackList = instructionFlowTestVo.getFeedbackList().stream().filter(instructionFeedback -> instructionFeedback.getFeedbackDeptId().equals(deptId)).collect(Collectors.toList());
            InstructionFeedback feedback = feedbackList.get(0);
            //更新之前的反馈记录填为否
            feedbackMapper.updateFeedBackStatus(feedback.getTransferId());
            InstructionFeedback newFeedback = new InstructionFeedback();
            newFeedback.setFeedbackDept(dept);
            newFeedback.setFeedbackDeptId(Long.valueOf(deptId));
            newFeedback.setInstructionId(instructionFlowTestVo.getInfoId());
            newFeedback.setTransferId(feedback.getTransferId());
            feedbackMapper.insertInstructionFeedback(newFeedback);
        }
    }

    /**
     * 指令销号将其处置、反馈内容更新至事件中
     *
     * @param infoId
     */
    private void updateInfoForEvent(final Long infoId) {
        //1、查询出该条指令对应的事件
        InstructionEvent event = eventMapper.selectInstructionEventByInstrucationId(infoId);
        //2、查询出该条指令对应的处置记录
        String disposeInfos = disposeMapper.findDisposeInfosByInstructionId(infoId);
        //3、查询出该条指令对应的反馈记录
        String feedbackInfos = feedbackMapper.findFeedBackInfosByInstructionId(infoId);
        event.setDisposeSituation(disposeInfos);
        event.setFeedBack(feedbackInfos);
        eventMapper.updateInstructionEvent(event);
    }

    /**
     * 删除转交记录（处置、反馈记录同时删除）
     *
     * @param transfer
     */
    private void delTransfer(final InstructionTransfer transfer) {
        //1、删除转交记录
        transferMapper.deleteInstructionTransferById(transfer.getId());
        //2、删除反馈记录
        feedbackMapper.deleteByTransferId(transfer.getId());
    }

    /**
     * 测试 处置、反馈变为多条记录时获取指令流程记录
     *
     * @param id
     * @param viewType 1:获取详情 2、获取处理
     * @return
     */
    @Override
    public InstructionFlowTestVo testGetProcessById(final Long id, final Integer viewType) {
        //获取用户角色
        List<String> roleList = SecurityUtils.getLoginUser().getUser().getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        //获取用户所属部门
        String deptName = SecurityUtils.getLoginUser().getUser().getDept().getDeptName();
        Long deptId = SecurityUtils.getDeptId();
        InstructionFlowTestVo flowTestVo = new InstructionFlowTestVo();
        flowTestVo.setInfoId(id);
        //根据指令获取交办、销号信息
        InstructionAssign assign = assignMapper.selectInstructionAssignByInstructionId(id);
        flowTestVo.setAssign(assign);
        InstructionEnd end = new InstructionEnd();
        end.setInstructionId(id);
        end.setStatus(1);
        List<InstructionEnd> instructionEnds = endMapper.selectInstructionEndList(end);
        if (instructionEnds != null && instructionEnds.size() > 0) {
            flowTestVo.setInstructionEnd(instructionEnds);
        }

        //判断权限
        if (roleList.contains(InstructionRolesConstants.ASSIGN) || roleList.contains(InstructionRolesConstants.END) || viewType == 1) {
            //交办权限:根据指令id查询出所有该条指令所有的记录
            //1、查询出所有接收记录
            InstructionReceive receive = new InstructionReceive();
            receive.setStatus(1);
            receive.setInstrucationId(id);
            //如果是金华市本级的交办员账号可以看到所有的接收信息,其他县市区交办员不可看到非本接收单位的接收信息
            if (deptId != 202 && viewType == 2) {
                receive.setReceiveDept(deptName);
            }
            List<InstructionReceive> receiveList = receiveMapper.selectInstructionReceiveList(receive);
            //获取指令流程相关信息
            getInstructionInfo(receiveList, flowTestVo);

        } else if (roleList.contains(InstructionRolesConstants.RECEIVE)) {
            //转交权限:根据用户部门查询出该部门对应的接收记录以及下级记录
            //1、根据用户部门查询该部门的接收记录
            InstructionReceive receive = new InstructionReceive();
            receive.setStatus(1);
            if (viewType == 2) {
                receive.setReceiveDeptId(deptId);
                // receive.setReceiveDept(deptName);
            }
            receive.setInstrucationId(id);
            List<InstructionReceive> receiveList = receiveMapper.selectInstructionReceiveList(receive);
            //获取指令流程相关信息
            getInstructionInfo(receiveList, flowTestVo);
        } else if (roleList.contains(InstructionRolesConstants.FEEDBACK)) {
            //处置权限:根据用户部门找出对应的反馈记录，推出接收记录
            //2、查询出该用户部门、指令id对应的反馈记录
            InstructionFeedback feedback = new InstructionFeedback();
            feedback.setStatus(1);
            if (viewType == 2) {
                feedback.setFeedbackDeptId(deptId);
                // feedback.setFeedbackDept(deptName);
            }
            feedback.setInstructionId(id);
            List<InstructionFeedback> feedbackList = feedbackMapper.selectInstructionFeedbackList(feedback);
            flowTestVo.setFeedbackList(feedbackList);
            //3、查询出转交记录 如果是处置人员,处置信息仅能对应一条转交记录
            InstructionTransfer transfer1 = new InstructionTransfer();
            transfer1.setInstructionId(id);
            if (viewType == 2) {
                transfer1.setTransferDeptId(deptId);
                // transfer1.setTransferDept(deptName);
            }
            transfer1.setStatus(1);
            List<InstructionTransfer> transferList1 = transferMapper.selectInstructionTransferList(transfer1);
            //4、查询出接收记录
            InstructionReceive receive = receiveMapper.selectInstructionReceiveById(transferList1.get(0).getReceiveId());
            receive.setTransferList(transferList1);
            List<InstructionReceive> receiveList = new ArrayList<>();
            receiveList.add(receive);
            flowTestVo.setReceiveList(receiveList);

        }
        flowTestVo.setEndStatus(2);
        //根据指令id获取指令详情
        InstructionInfo instructionInfo = instructionInfoMapper.selectInstructionInfoById(id);
        //1、获取指令接收单位个数
        String[] receiveStr = instructionInfo.getReceiveUnit().split(",");
        long receiveCount = receiveStr.length;
        if (instructionInfo.getCreateDeptId() == 202L) {
            //市本级创建交办
            InstructionCountyFeedback countyFeedback = new InstructionCountyFeedback();
            countyFeedback.setStatus(1);
            countyFeedback.setInstructionId(instructionInfo.getId());
            List<InstructionCountyFeedback> instructionCountyFeedbacks = new ArrayList<>();
            if (viewType == 1 || deptId == 202L) {
                //查询全部县市区反馈数据
                instructionCountyFeedbacks = countyFeedbackMapper.selectInstructionCountyFeedbackList(countyFeedback);
                flowTestVo.setCountyFeedbackList(instructionCountyFeedbacks);
            } else {
                countyFeedback.setFeedbackDept(deptName);
                instructionCountyFeedbacks = countyFeedbackMapper.selectInstructionCountyFeedbackList(countyFeedback);
                flowTestVo.setCountyFeedbackList(instructionCountyFeedbacks);
            }
            //判断用户是否可以进行销号
            //2、查询县市区反馈的结果
            long count = instructionCountyFeedbacks.stream().filter(feedbacks -> feedbacks.getIsEnd() == 1)
                    .map(InstructionCountyFeedback::getFeedbackDept)
                    .distinct()
                    .count();
            if (count == receiveCount) {
                flowTestVo.setEndStatus(1);
            }
            //判断当前县市区是否可以反馈（处置数量=流转数量）
            flowTestVo.setCountyFeedbackStatus(2);
            if (deptId == 213L || deptId == 214L || deptId == 215L || deptId == 216L || deptId == 217L || deptId == 218L || deptId == 219L || deptId == 220L || deptId == 221L || deptId == 262L) {
                Integer countyFeedBackStatus = instructionInfoMapper.findCountyFeedBackStatus(instructionInfo.getId(), deptName);
                flowTestVo.setCountyFeedbackStatus(countyFeedBackStatus);
            }
        } else {
            //县市区交办
            //1、判断该条指令是否可以销号
            Integer endStatus = instructionInfoMapper.isToEnd(instructionInfo.getId());
            flowTestVo.setEndStatus(endStatus);
        }
        return flowTestVo;
    }


    @Override
    public TableDataInfo testInstructionListNew(InstructionInfo instructionInfo) {

        //1、判断用户具有哪些权限进行查询
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String deptName = user.getDept().getDeptName();
        Long deptId = SecurityUtils.getDeptId();
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());

        List<InstructionInfomiddleResVo> instructionInfoList = new ArrayList<>();
        ArrayList<Long> arrayList = new ArrayList();
        List<InstructionInfoRspVo> collect = new ArrayList<>();
        if (instructionInfo.getProcess() == null) {
            instructionInfo.setProcess(0);
        }
        if (instructionInfo.getProcess() == 6) {
            //查询出未处理的指令ids  代办事件
            List<Long> ids = null;
            if (instructionInfo.getPageType() == 1) {
                //市本级未处理
                ids = testRedStatistics().get("cityIds");
            } else if (instructionInfo.getPageType() == 2) {
                //显示取代办
                ids = testRedStatistics().get("countyIds");
            }
            if (ids != null && ids.size() > 0) {
                Map<String, Object> params = instructionInfo.getParams();
                params.put("ids", ids);
                instructionInfo.setParams(params);
                //获取指令列表返回类
                instructionInfoList = instructionInfoMapper.testInstructionListNew(instructionInfo);
                for (InstructionInfomiddleResVo i : instructionInfoList) {
                    InstructionInfoRspVo instructionInfoRspVo = new InstructionInfoRspVo();
                    String receiveUnit = i.getReceiveUnit();
                    int unitCount = 0;
                    String receiveUnitStr = "";
                    if (receiveUnit.length() > 1) {
                        receiveUnitStr = receiveUnit.substring(1, receiveUnit.length() - 1);
                        String[] receiveUnits = receiveUnit.substring(1, receiveUnit.length() - 1).split(",");
                        unitCount = receiveUnits.length;
                    }
                    BeanUtils.copyProperties(i, instructionInfoRspVo);
                    instructionInfoRspVo.setReceiveUnit(receiveUnitStr);
                    //2、获取关联人数
                    int personCount = 0;
                    if (i.getPersonIds() != null && i.getPersonIds().length() > 0) {
                        String[] split = i.getPersonIds().split(",");
                        personCount = split.length;
                    }
                    String finalUnit = receiveUnitStr.replace("综合行政执法局", "").replace("行政执法指挥中心", "");
                    instructionInfoRspVo.setCurrentCounty(finalUnit);
                    if (i.getUnitCount() == null || !i.getUnitCount().equals(unitCount)) {
                        //没有接收单位或接收流程未完成
                        instructionInfoRspVo.setReveiveTime(null);
                        instructionInfoRspVo.setFeedbackTime(null);
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getReveiveTime() == null) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getFeedbackTime() == null) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getCountDeptNum() != null && !i.getCountDeptNum().equals(i.getReceiveUnitCount())) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getInstrucationIsEnd() != null && i.getInstrucationIsEnd() == 2) {
                        //销号办结为否->需判断反馈记录是否已反馈
                        int dateResult = 0;
                        if (i.getCreateDeptId() == 202L && i.getEndTime() != null && i.getCountyFeedbackTime() != null) {
                            //市级创建指令 需用销号时间和反馈时间进行判断
                            dateResult = i.getEndTime().compareTo(i.getCountyFeedbackTime());
                            if (dateResult < 0) {
                                //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                                String substring = i.getFeedbackDept().substring(1, i.getFeedbackDept().length() - 1);
                                int needFeedBackCount = 0;
                                if (substring.contains(",")) {
                                    needFeedBackCount = substring.split(",").length;
                                } else {
                                    needFeedBackCount = 1;
                                }
                                //查询销号为后的县市区反馈已办结记录部门
                                if (i.getCountDeptNum() == needFeedBackCount) {
                                    instructionInfoRspVo.setEndTime(null);
                                } else {
                                    //县市区下发指令
                                    instructionInfoRspVo.setCountyFeedbackTime(null);
                                    instructionInfoRspVo.setEndTime(null);
                                }
                            }
                        } else if (i.getCreateDeptId() != 202L && i.getEndTime() != null && i.getFeedbackTime() != null) {
                            //非市本级创建
                            dateResult = i.getEndTime().compareTo(i.getFeedbackTime());
                            if (dateResult < 0) {
                                //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                                String substring = i.getFeedbackDept().substring(1, i.getFeedbackDept().length() - 1);
                                int needFeedBackCount = 0;
                                if (substring.contains(",")) {
                                    needFeedBackCount = substring.split(",").length;
                                } else {
                                    needFeedBackCount = 1;
                                }
                                //查询销号为后的反馈已办结记录部门
                                int feedBackDeptCount = feedbackMapper.findEndFeedDeptCount(i.getEndTime(), i.getId());
                                if (feedBackDeptCount == needFeedBackCount) {
                                    instructionInfoRspVo.setEndTime(null);
                                } else {
                                    //县市区下发指令
                                    instructionInfoRspVo.setFeedbackTime(null);
                                    instructionInfoRspVo.setEndTime(null);
                                }
                            } else {
                                //县市区下发指令
                                instructionInfoRspVo.setFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            }
                        } else {
                            if (i.getCreateDeptId() != 202L) {
                                //县市区下发指令
                                instructionInfoRspVo.setFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            } else {
                                //市本级下发指令
                                instructionInfoRspVo.setCountyFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            }
                        }

                    }
                    collect.add(instructionInfoRspVo);
                }
            }

        } else if (instructionInfo.getProcess() == 7) {
            //查询出已处理的指令 已处理指令 = 全部指令-未处理指令
            //全部指令
            List<InstructionInfomiddleResVo> finallyList = getInstructionInfoList(deptId, deptName, roleList, instructionInfo);
            //查询出未处理的指令ids  代办事件
            List<Long> ids = null;
            if (instructionInfo.getPageType() == 1) {
                //市本级未处理
                ids = testRedStatistics().get("cityIds");
            } else if (instructionInfo.getPageType() == 2) {
                //显示取代办
                ids = testRedStatistics().get("countyids");
            }
            // List<Long> ids = testRedStatistics().stream().collect(Collectors.toList());

            //进行过滤
            if (!CollectionUtils.isEmpty(finallyList)) {
                for (InstructionInfomiddleResVo resVo : finallyList) {
                    boolean flag = true;
                    for (Long id : ids) {
                        if (resVo.getId().equals(id)) {
                            flag = false;
                            break;
                        }
                    }
                    if (flag) {
                        if (resVo.getReveiveTime() == null && resVo.getFeedbackTime() == null && resVo.getEndTime() == null) {
                            break;
                        }
                        instructionInfoList.add(resVo);
                    }
                }
            }
            if (instructionInfoList.size() > 0 && instructionInfoList != null) {
                for (InstructionInfomiddleResVo i : instructionInfoList) {
                    InstructionInfoRspVo instructionInfoRspVo = new InstructionInfoRspVo();
                    String receiveUnit = i.getReceiveUnit();
                    int unitCount = 0;
                    String receiveUnitStr = "";
                    if (receiveUnit.length() > 1) {
                        receiveUnitStr = receiveUnit.substring(1, receiveUnit.length() - 1);
                        String[] receiveUnits = receiveUnit.substring(1, receiveUnit.length() - 1).split(",");
                        unitCount = receiveUnits.length;
                    }

                    BeanUtils.copyProperties(i, instructionInfoRspVo);
                    instructionInfoRspVo.setReceiveUnit(receiveUnitStr);
                    //2、获取关联人数
                    int personCount = 0;
                    if (i.getPersonIds() != null && i.getPersonIds().length() > 0) {
                        String[] split = i.getPersonIds().split(",");
                        personCount = split.length;
                    }
                    instructionInfoRspVo.setPersonCount(personCount);
                    String finalUnit = receiveUnitStr.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
                    instructionInfoRspVo.setCurrentCounty(finalUnit);
                    if (i.getUnitCount() == null || !i.getUnitCount().equals(unitCount)) {
                        //没有接收单位或接收流程未完成
                        instructionInfoRspVo.setReveiveTime(null);
                        instructionInfoRspVo.setFeedbackTime(null);
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getReveiveTime() == null) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getFeedbackTime() == null) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getCountDeptNum() != null && !i.getCountDeptNum().equals(i.getReceiveUnitCount())) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getInstrucationIsEnd() != null && i.getInstrucationIsEnd() == 2) {
                        //销号办结为否->需判断反馈记录是否已反馈
                        int dateResult = 0;
                        if (i.getCreateDeptId() == 202L && i.getEndTime() != null && i.getCountyFeedbackTime() != null) {
                            //市级创建指令 需用销号时间和反馈时间进行判断
                            dateResult = i.getEndTime().compareTo(i.getCountyFeedbackTime());
                            if (dateResult < 0) {
                                //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                                String substring = i.getFeedbackDept().substring(1, i.getFeedbackDept().length() - 1);
                                int needFeedBackCount = 0;
                                if (substring.contains(",")) {
                                    needFeedBackCount = substring.split(",").length;
                                } else {
                                    needFeedBackCount = 1;
                                }
                                //查询销号为后的县市区反馈已办结记录部门
                                if (i.getCountDeptNum() == needFeedBackCount) {
                                    instructionInfoRspVo.setEndTime(null);
                                } else {
                                    //县市区下发指令
                                    instructionInfoRspVo.setCountyFeedbackTime(null);
                                    instructionInfoRspVo.setEndTime(null);
                                }
                            }
                        } else if (i.getCreateDeptId() != 202L && i.getEndTime() != null && i.getFeedbackTime() != null) {
                            //非市本级创建
                            dateResult = i.getEndTime().compareTo(i.getFeedbackTime());
                            if (dateResult < 0) {
                                //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                                String substring = i.getFeedbackDept().substring(1, i.getFeedbackDept().length() - 1);
                                int needFeedBackCount = 0;
                                if (substring.contains(",")) {
                                    needFeedBackCount = substring.split(",").length;
                                } else {
                                    needFeedBackCount = 1;
                                }
                                //查询销号为后的反馈已办结记录部门
                                int feedBackDeptCount = feedbackMapper.findEndFeedDeptCount(i.getEndTime(), i.getId());
                                if (feedBackDeptCount == needFeedBackCount) {
                                    instructionInfoRspVo.setEndTime(null);
                                } else {
                                    //县市区下发指令
                                    instructionInfoRspVo.setFeedbackTime(null);
                                    instructionInfoRspVo.setEndTime(null);
                                }
                            } else {
                                //县市区下发指令
                                instructionInfoRspVo.setFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            }
                        } else {
                            if (i.getCreateDeptId() != 202L) {
                                //县市区下发指令
                                instructionInfoRspVo.setFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            } else {
                                //市本级下发指令
                                instructionInfoRspVo.setCountyFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            }
                        }

                    }
                    collect.add(instructionInfoRspVo);
                }
            }


        } else {

            instructionInfoList = getInstructionInfoList(deptId, deptName, roleList, instructionInfo);
            if (!CollectionUtils.isEmpty(instructionInfoList)) {

                for (InstructionInfomiddleResVo i : instructionInfoList) {
                    InstructionInfoRspVo instructionInfoRspVo = new InstructionInfoRspVo();
                    if (arrayList.contains(i.getId())) {
                        continue;
                    }
                    String receiveUnit = i.getReceiveUnit();

                    int unitCount = 0;
                    String receiveUnitStr = "";
                    if (receiveUnit.length() > 1) {
                        receiveUnitStr = receiveUnit.substring(1, receiveUnit.length() - 1);
                        String[] receiveUnits = receiveUnit.substring(1, receiveUnit.length() - 1).split(",");
                        unitCount = receiveUnits.length;
                    }

                    BeanUtils.copyProperties(i, instructionInfoRspVo);
                    instructionInfoRspVo.setReceiveUnit(receiveUnitStr);
                    //2、获取关联人数
                    int personCount = 0;
                    if (i.getPersonIds() != null && i.getPersonIds().length() > 0) {
                        String[] split = i.getPersonIds().split(",");
                        personCount = split.length;
                    }
                    instructionInfoRspVo.setPersonCount(personCount);
                    String finalUnit = receiveUnitStr.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
                    instructionInfoRspVo.setCurrentCounty(finalUnit);
                    if (i.getUnitCount() == null || !i.getUnitCount().equals(unitCount)) {
                        //没有接收单位或接收流程未完成
                        instructionInfoRspVo.setReveiveTime(null);
                        instructionInfoRspVo.setFeedbackTime(null);
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getReveiveTime() == null) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getFeedbackTime() == null) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getCountDeptNum() != null && !i.getCountDeptNum().equals(i.getReceiveUnitCount())) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getInstrucationIsEnd() != null && i.getInstrucationIsEnd() == 2) {
                        //销号办结为否->需判断反馈记录是否已反馈
                        int dateResult = 0;
                        if (i.getCreateDeptId() == 202L && i.getEndTime() != null && i.getCountyFeedbackTime() != null) {
                            //市级创建指令 需用销号时间和反馈时间进行判断
                            dateResult = i.getEndTime().compareTo(i.getCountyFeedbackTime());
                            if (dateResult < 0) {
                                //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                                String substring = i.getFeedbackDept().substring(1, i.getFeedbackDept().length() - 1);
                                int needFeedBackCount = 0;
                                if (substring.contains(",")) {
                                    needFeedBackCount = substring.split(",").length;
                                } else {
                                    needFeedBackCount = 1;
                                }
                                //查询销号为后的县市区反馈已办结记录部门
                                if (i.getCountDeptNum() == needFeedBackCount) {
                                    instructionInfoRspVo.setEndTime(null);
                                } else {
                                    //县市区下发指令
                                    instructionInfoRspVo.setCountyFeedbackTime(null);
                                    instructionInfoRspVo.setEndTime(null);
                                }
                            }
                        } else if (i.getCreateDeptId() != 202L && i.getEndTime() != null && i.getFeedbackTime() != null) {
                            //非市本级创建
                            dateResult = i.getEndTime().compareTo(i.getFeedbackTime());
                            if (dateResult < 0) {
                                //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                                String substring = i.getFeedbackDept().substring(1, i.getFeedbackDept().length() - 1);
                                int needFeedBackCount = 0;
                                if (substring.contains(",")) {
                                    needFeedBackCount = substring.split(",").length;
                                } else {
                                    needFeedBackCount = 1;
                                }
                                //查询销号为后的反馈已办结记录部门
                                int feedBackDeptCount = feedbackMapper.findEndFeedDeptCount(i.getEndTime(), i.getId());
                                if (feedBackDeptCount == needFeedBackCount) {
                                    instructionInfoRspVo.setEndTime(null);
                                } else {
                                    //县市区下发指令
                                    instructionInfoRspVo.setFeedbackTime(null);
                                    instructionInfoRspVo.setEndTime(null);
                                }
                            } else {
                                //县市区下发指令
                                instructionInfoRspVo.setFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            }
                        } else {
                            if (i.getCreateDeptId() != 202L) {
                                //县市区下发指令
                                instructionInfoRspVo.setFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            } else {
                                //市本级下发指令
                                instructionInfoRspVo.setCountyFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            }
                        }

                    }
                    switch (instructionInfo.getProcess()) {
                        case 0:
                            break;
                        case 1:
                            if (instructionInfoRspVo.getAssignTime() == null || instructionInfoRspVo.getReveiveTime() != null || instructionInfoRspVo.getFeedbackTime() != null || instructionInfoRspVo.getEndTime() != null) {
                                continue;
                            }
                            break;
                        case 2:
                            if (instructionInfoRspVo.getReveiveTime() == null || instructionInfoRspVo.getFeedbackTime() != null || instructionInfoRspVo.getEndTime() != null) {
                                continue;
                            }
                            break;
                        case 3:
                            if (instructionInfoRspVo.getFeedbackTime() != null || instructionInfoRspVo.getEndTime() != null) {
                                continue;
                            }
                            break;
                        case 4:
                            if (instructionInfoRspVo.getFeedbackTime() == null || instructionInfoRspVo.getEndTime() != null) {
                                continue;
                            }
                            break;
                        case 5:
                            if (instructionInfoRspVo.getEndTime() == null) {
                                continue;
                            }
                            break;
                        case 8:
                            if (instructionInfoRspVo.getCountyFeedbackTime() == null || instructionInfoRspVo.getEndTime() != null) {
                                continue;
                            }
                            break;
                    }
                    collect.add(instructionInfoRspVo);
                    arrayList.add(i.getId());
                }
            }
        }

        //1交办，2接收，3处置，4反馈，5销号 排序
        Integer sortType = 0;
        if (instructionInfo.getSortType() == null || instructionInfo.getSortType() == 0) {
            sortType = 0;
        } else if (instructionInfo.getSortType() == 1 || instructionInfo.getSortType() == 2) {
            sortType = 1;
        } else if (instructionInfo.getSortType() == 3 || instructionInfo.getSortType() == 4) {
            sortType = 2;
        } else if (instructionInfo.getSortType() == 5 || instructionInfo.getSortType() == 6) {
            sortType = 3;
        } else if (instructionInfo.getSortType() == 7 || instructionInfo.getSortType() == 8) {
            sortType = 4;
        } else if (instructionInfo.getSortType() == 9 || instructionInfo.getSortType() == 10) {
            sortType = 5;
        } else if (instructionInfo.getSortType() == 11 || instructionInfo.getSortType() == 12) {
            sortType = 6;
        }
        List<InstructionInfoRspVo> collect1 = new ArrayList<>();
        List<InstructionInfoRspVo> collectFinally = new ArrayList<>();
        //进行数据过滤 查看市级、非市级页面数据展示不同,市账号查看市级页面-》仅能查看自己创建的指令 市账号查看县市区页面-》可以查看各县市区自己创建的指令 县市区账号同理
        if (instructionInfo.getPageType() == 1) {
            //是市本级账号 只查询createDeptId=202的指令
            collectFinally = collect.stream().filter(instructionInfoRspVo -> instructionInfoRspVo.getCreateDeptId() == 202).collect(Collectors.toList());
        } else {
            //查看县市区页面
            if (deptId.equals(Constants.JINHUA_CITY_DEPT_ID) || (
                    !deptId.equals(Constants.KAIFA_CITY_DEPT_ID)) &&
                    !deptId.equals(Constants.WUYI_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.YONGKAN_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.DONGYANG_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.JINDON_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.LANXI_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.PANAN_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.PUJIANG_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.WUCHENG_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.YIWU_CITY_DEPT_ID)
            ) {
                //是市本级账号 只查询createDeptId=202的指令
                collectFinally = collect.stream().filter(instructionInfoRspVo -> instructionInfoRspVo.getCreateDeptId() != 202).collect(Collectors.toList());
            } else {
                //县市区账号 只能查看createDeptId!=自己部门id
                collectFinally = collect.stream().filter(instructionInfoRspVo -> instructionInfoRspVo.getCreateDeptId().equals(deptId)).collect(Collectors.toList());
            }
        }

        int start = instructionInfo.getEnd() * (instructionInfo.getStart() - 1);
        //进行排序

        if (sortType == 0) {
            collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getAssignTime, Comparator.nullsFirst(Date::compareTo)).reversed()).skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
        } else if (sortType == 1) {
            if (instructionInfo.getSortType() == 1) {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getAssignTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime)).skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
            } else {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getAssignTime, Comparator.nullsFirst(Date::compareTo)).reversed().thenComparing(InstructionInfoRspVo::getCreateTime, Comparator.reverseOrder())).skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
            }
        } else if (sortType == 2) {
            if (instructionInfo.getSortType() == 3) {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getReveiveTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime)).skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
            } else {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getReveiveTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime, Comparator.nullsFirst(Date::compareTo)).reversed()).skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
            }
        } else if (sortType == 3) {
            if (instructionInfo.getSortType() == 5) {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getDisposeTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime)).skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
            } else {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getDisposeTime, Comparator.nullsFirst(Date::compareTo)).reversed().thenComparing(InstructionInfoRspVo::getCreateTime, Comparator.reverseOrder())).skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
            }
        } else if (sortType == 4) {
            if (instructionInfo.getSortType() == 7) {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getFeedbackTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime)).skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
            } else {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getFeedbackTime, Comparator.nullsFirst(Date::compareTo)).reversed().thenComparing(InstructionInfoRspVo::getCreateTime, Comparator.reverseOrder())).skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
            }
        } else if (sortType == 5) {
            if (instructionInfo.getSortType() == 9) {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getEndTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime)).skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
            } else {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getEndTime, Comparator.nullsFirst(Date::compareTo)).reversed().thenComparing(InstructionInfoRspVo::getCreateTime, Comparator.reverseOrder())).skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
            }
        } else if (sortType == 6) {
            if (instructionInfo.getSortType() == 11) {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getCountyFeedbackTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime)).skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
            } else {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getCountyFeedbackTime, Comparator.nullsFirst(Date::compareTo)).reversed().thenComparing(InstructionInfoRspVo::getCreateTime, Comparator.reverseOrder())).skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
            }
        }

        if (roleList.contains(InstructionRolesConstants.ASSIGN) && !roleList.contains(InstructionRolesConstants.RECEIVE) && !roleList.contains(InstructionRolesConstants.DISPOSE) && !roleList.contains(InstructionRolesConstants.FEEDBACK)) {
            //如果是交办员需要判断是否可以处理
            for (InstructionInfoRspVo rsp : collect1) {
                if (!rsp.getCreateDeptId().equals(deptId)) {
                    rsp.setIsDeal(0);
                }
            }
        }

        TableDataInfo tableDataInfo = new TableDataInfo();
        //添加记录
        tableDataInfo.setRows(collect1);
        tableDataInfo.setTotal(collectFinally.size());
        tableDataInfo.setMsg("操作成功");
        tableDataInfo.setCode(200);
        return tableDataInfo;
    }

    /**
     * 获取指令列表返回类
     *
     * @param deptName
     * @param roleList
     * @param instructionInfo
     * @return
     */
    @Override
    public List<InstructionInfomiddleResVo> getInstructionInfoList(final Long deptId, final String deptName, final List<String> roleList, final InstructionInfo instructionInfo) {
        List<InstructionInfomiddleResVo> instructionInfoList = new ArrayList<>();
        if (roleList.contains(InstructionRolesConstants.ASSIGN) || roleList.contains(InstructionRolesConstants.END) || roleList.contains(InstructionRolesConstants.ADMIN)) {
            //交办员可以查看所有的指令无需处理
            //金华市委政法委可以查看下级各县市区的指令 但不能进行修改 202:金华市委政法委部门id
            if (deptId != 202) {
                //如果不是金华市委政法委则仅查询该交办员所在县市区的指令内容
                instructionInfo.setCreateDeptId(deptId);
            }
            instructionInfo.setStatus("1");
            List<InstructionInfomiddleResVo> instructionInfoList1 = instructionInfoMapper.testInstructionListNew(instructionInfo);
            if (instructionInfoList1 != null && instructionInfoList1.size() > 0) {
                instructionInfoList.addAll(instructionInfoList1);
            }

        }
        if (roleList.contains(InstructionRolesConstants.RECEIVE) || roleList.contains(InstructionRolesConstants.ADMIN)) {
            //接收权限:当前操作人员所属部门是否存在指令信息责任部门中
            instructionInfo.setCreateDeptId(null);
            instructionInfo.setReceiveUnit(deptName);
            instructionInfo.setStatus("1");
            List<InstructionInfomiddleResVo> instructionInfoList1 = instructionInfoMapper.testInstructionListNew(instructionInfo);
            if (instructionInfoList1 != null && instructionInfoList1.size() > 0) {
                instructionInfoList.addAll(instructionInfoList1);
            }
        }

        if (roleList.contains(InstructionRolesConstants.FEEDBACK) || roleList.contains(InstructionRolesConstants.ADMIN)) {
            //反馈人员：通过反馈人员部门->处置id->转交id->接收id->指令ids
            //查询转交单位
            InstructionTransfer transfer = new InstructionTransfer();
            transfer.setTransferDept(deptName);
            transfer.setStatus(1);
            List<Long> collect = transferMapper.selectInstructionTransferList(transfer).stream().map(InstructionTransfer::getInstructionId).collect(Collectors.toList());
            //反馈人员：通过部门去查询
            InstructionFeedback feedback = new InstructionFeedback();
            feedback.setFeedbackDept(deptName);
            feedback.setStatus(1);
            List<Long> ids = feedbackMapper.selectInstructionFeedbackList(feedback).stream().map(InstructionFeedback::getInstructionId).collect(Collectors.toList());
            ids.addAll(collect);
            if (ids.size() > 0) {
                Map<String, Object> params = instructionInfo.getParams();
                params.put("ids", ids);
                instructionInfo.setParams(params);
                instructionInfo.setStatus("1");
                instructionInfo.setCreateDeptId(null);
                List<InstructionInfomiddleResVo> instructionInfoList1 = instructionInfoMapper.testInstructionListNew(instructionInfo);
                if (instructionInfoList1 != null && instructionInfoList1.size() > 0) {
                    instructionInfoList.addAll(instructionInfoList1);
                }
            }
        }
        return instructionInfoList;
    }


    /**
     * 查询出人员代办事件指令id集合
     *
     * @return
     */
    @Override
    public Map<String, List<Long>> testRedStatistics() {
        //1、判断用户具有哪些权限进行查询
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String deptName = user.getDept().getDeptName();
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        //指令id集合
        List<Long> idsList = new ArrayList<>();

        if (roleList.contains(InstructionRolesConstants.RECEIVE)) {
            //接收员需要办理的指令id
            Set<Long> ids = instructionInfoMapper.ReceiveNotHandleIds(deptName);
            if (ids != null && ids.size() > 0) {
                idsList.addAll(ids);
            }
        }
        if (roleList.contains(InstructionRolesConstants.FEEDBACK)) {
            //反馈员
            Set<Long> ids = instructionInfoMapper.FeedBackNotHandleIds(deptName);
            if (ids != null && ids.size() > 0) {
                idsList.addAll(ids);
            }
        }
        if (roleList.contains(InstructionRolesConstants.END)) {
            //销号员
            Long deptId = SecurityUtils.getDeptId();
            InstructionInfo instructionInfo = new InstructionInfo();
            instructionInfo.setCreateDeptId(deptId);
            instructionInfo.setStatus("1");
            List<InstructionInfomiddleResVo> instructionInfoList = instructionInfoMapper.testInstructionListNew(instructionInfo);
            //遍历集合赋值
            for (InstructionInfomiddleResVo resVo : instructionInfoList) {
                String receiveUnit = resVo.getReceiveUnit();
                int unitCount = 0;
                if (receiveUnit.length() > 1) {
                    String[] receiveUnits = receiveUnit.substring(1, receiveUnit.length() - 1).split(",");
                    unitCount = receiveUnits.length;
                }
                if (resVo.getUnitCount() == null || !resVo.getUnitCount().equals(unitCount)) {
                    resVo.setReveiveTime(null);
                    resVo.setDisposeTime(null);
                    resVo.setFeedbackTime(null);
                    resVo.setEndTime(null);
                } else if (resVo.getInstrucationIsEnd() != null && resVo.getInstrucationIsEnd() == 2) {
                    if (resVo.getEndTime() != null && resVo.getFeedbackTime() != null) {
                        int dateResult = resVo.getEndTime().compareTo(resVo.getFeedbackTime());
                        if (dateResult < 0) {
                            //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                            String substring = resVo.getFeedbackDept().substring(1, resVo.getFeedbackDept().length() - 1);
                            int needFeedBackCount = 0;
                            if (substring.contains(",")) {
                                needFeedBackCount = substring.split(",").length;
                            } else {
                                needFeedBackCount = 1;
                            }
                            //查询销号为后的反馈已办结记录部门
                            int feedBackDeptCount = feedbackMapper.findEndFeedDeptCount(resVo.getEndTime(), resVo.getId());
                            if (feedBackDeptCount == needFeedBackCount) {
                                resVo.setEndTime(null);
                            } else {
                                resVo.setFeedbackTime(null);
                                resVo.setEndTime(null);
                            }

                        } else {
                            resVo.setFeedbackTime(null);
                            resVo.setEndTime(null);
                        }
                    } else {
                        resVo.setFeedbackTime(null);
                        resVo.setEndTime(null);
                    }
                }

            }
            //查询出已经反馈且未销号的指令
            Set<Long> ids = instructionInfoList.stream()
                    .filter(rsp -> (rsp.getReveiveTime() != null && rsp.getFeedbackTime() != null && rsp.getEndTime() == null)).map(InstructionInfomiddleResVo::getId).collect(Collectors.toSet());
            if (ids != null && ids.size() > 0) {
                idsList.addAll(ids);
            }
        }
        Map<String, List<Long>> map = new HashMap<>();
        List<Long> cityIds = new ArrayList<>();
        List<Long> countyIds = new ArrayList<>();
        if (idsList.size() > 0) {
            //将市本级/县市区创建的指令进行过滤
            InstructionInfo instructionInfo = new InstructionInfo();
            instructionInfo.setStatus("1");
            Map<String, Object> params = instructionInfo.getParams();
            params.put("ids", idsList);
            List<InstructionInfo> instructionInfoList = instructionInfoMapper.selectInstructionInfoList(instructionInfo);
            //筛选出金华市政法委的指令
            cityIds = instructionInfoList.stream().filter(info -> info.getCreateDeptId() == 202).map(InstructionInfo::getId).collect(Collectors.toList());
            countyIds = instructionInfoList.stream().filter(info -> info.getCreateDeptId() != 202).map(InstructionInfo::getId).collect(Collectors.toList());
        }
        map.put("cityIds", cityIds);
        map.put("countyIds", countyIds);
        return map;
    }

    /**
     * 获取指令列表
     *
     * @return
     */
    public List<InstructionInfoRspVo> getList() {
        InstructionInfo instructionInfo = new InstructionInfo();
        List<InstructionInfomiddleResVo> instructionInfoList = new ArrayList<>();
        //1、判断用户具有哪些权限进行查询
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String deptName = user.getDept().getDeptName();
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.ASSIGN) || roleList.contains(InstructionRolesConstants.END) || roleList.contains(InstructionRolesConstants.ADMIN)) {
            //交办员可以查看所有的指令无需处理
            instructionInfo.setStatus("1");
            List<InstructionInfomiddleResVo> instructionInfoList1 = instructionInfoMapper.testInstructionListNew(instructionInfo);
            if (instructionInfoList1 != null && instructionInfoList1.size() > 0) {
                instructionInfoList.addAll(instructionInfoList1);
            }
        }
        if (roleList.contains(InstructionRolesConstants.RECEIVE) || roleList.contains(InstructionRolesConstants.ADMIN)) {
            //接收权限:当前操作人员所属部门是否存在指令信息责任部门中
            instructionInfo.setReceiveUnit(deptName);
            instructionInfo.setStatus("1");
            List<InstructionInfomiddleResVo> instructionInfoList1 = instructionInfoMapper.testInstructionListNew(instructionInfo);
            if (instructionInfoList1 != null && instructionInfoList1.size() > 0) {
                instructionInfoList.addAll(instructionInfoList1);
            }
        }
        if (roleList.contains(InstructionRolesConstants.DISPOSE) || roleList.contains(InstructionRolesConstants.ADMIN)) {
            //处置人员：通过处置人员部门->转交表->接收表->查出指令ids
            //接收人员：通过部门去查询指令ID
            InstructionDispose instructionDispose = new InstructionDispose();
            instructionDispose.setDisposeDept(deptName);
            instructionDispose.setStatus(1);
            List<Long> ids = disposeMapper.selectInstructionDisposeList(instructionDispose).stream().map(InstructionDispose::getInstructionId).collect(Collectors.toList());
            if (ids.size() > 0) {
                Map<String, Object> params = instructionInfo.getParams();
                params.put("ids", ids);
                instructionInfo.setParams(params);
                instructionInfo.setStatus("1");
                List<InstructionInfomiddleResVo> instructionInfoList1 = instructionInfoMapper.testInstructionListNew(instructionInfo);
                if (instructionInfoList1 != null && instructionInfoList1.size() > 0) {
                    instructionInfoList.addAll(instructionInfoList1);
                }
            }
        }
        if (roleList.contains(InstructionRolesConstants.FEEDBACK) || roleList.contains(InstructionRolesConstants.ADMIN)) {
            //反馈人员：通过反馈人员部门->处置id->转交id->接收id->指令ids

            //反馈人员：通过部门去查询
            InstructionFeedback feedback = new InstructionFeedback();
            feedback.setFeedbackDept(deptName);
            feedback.setStatus(1);
            List<Long> ids = feedbackMapper.selectInstructionFeedbackList(feedback).stream().map(InstructionFeedback::getInstructionId).collect(Collectors.toList());
            if (ids.size() > 0) {
                Map<String, Object> params = instructionInfo.getParams();
                params.put("ids", ids);
                instructionInfo.setParams(params);
                instructionInfo.setStatus("1");
                List<InstructionInfomiddleResVo> instructionInfoList1 = instructionInfoMapper.testInstructionListNew(instructionInfo);
                if (instructionInfoList1 != null && instructionInfoList1.size() > 0) {
                    instructionInfoList.addAll(instructionInfoList1);
                }
            }
        }
        ArrayList<Long> arrayList = new ArrayList(instructionInfoList.size());
        List<InstructionInfoRspVo> collect = new ArrayList<>(instructionInfoList.size());
        if (!CollectionUtils.isEmpty(instructionInfoList)) {
            for (InstructionInfomiddleResVo i : instructionInfoList) {
                if (arrayList.contains(i.getId())) {
                    continue;
                }
                String receiveUnit = i.getReceiveUnit();
                int unitCount = 0;
                String receiveUnitStr = "";
                if (receiveUnit.length() > 1) {
                    receiveUnitStr = receiveUnit.substring(1, receiveUnit.length() - 1);
                    String[] receiveUnits = receiveUnit.substring(1, receiveUnit.length() - 1).split(",");
                    unitCount = receiveUnits.length;
                }
                InstructionInfoRspVo instructionInfoRspVo = new InstructionInfoRspVo();
                BeanUtils.copyProperties(i, instructionInfoRspVo);
                //2、获取关联人数
                int personCount = 0;
                if (i.getPersonIds() != null && i.getPersonIds().length() > 0) {
                    String[] split = i.getPersonIds().split(",");
                    personCount = split.length;
                }
                instructionInfoRspVo.setPersonCount(personCount);
                String finalUnit = receiveUnitStr.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
                instructionInfoRspVo.setCurrentCounty(finalUnit);
                if (i.getUnitCount() == null || !i.getUnitCount().equals(unitCount)) {
                    instructionInfoRspVo.setReveiveTime(null);
                }
                if (instructionInfoRspVo.getEndTime() != null) {
                    continue;
                }
                collect.add(instructionInfoRspVo);
                arrayList.add(i.getId());
            }
        }
        return collect;
    }

    /**
     * 封装指令列表返回数据
     *
     * @param instructionInfoList
     * @return
     */
    private List<InstructionInfoRspVo> testGetInstructionVo(final List<InstructionInfo> instructionInfoList) {
        List<InstructionInfoRspVo> rspVoList = new ArrayList<>();
        if (instructionInfoList != null && instructionInfoList.size() > 0) {
            List<Long> instructionInfoIds = instructionInfoList.stream().map(InstructionInfo::getId).collect(Collectors.toList());
            Long[] ids = instructionInfoIds.toArray(new Long[instructionInfoIds.size()]);

            List<InstructionAssign> assignList = assignMapper.selectByIds(ids);
            for (InstructionInfo info : instructionInfoList) {
                InstructionInfoRspVo rspVo = new InstructionInfoRspVo();
                //接收未完成查询出未完成的部门
                String receiveUnit = info.getReceiveUnit();

                int unitCount = 0;
                String receiveUnitStr = "";
                if (receiveUnit.length() > 1) {
                    receiveUnitStr = receiveUnit.substring(1, receiveUnit.length() - 1);
                    String[] receiveUnits = receiveUnit.substring(1, receiveUnit.length() - 1).split(",");
                    unitCount = receiveUnits.length;
                }

                //1、获取指令标题、指令id
                rspVo.setId(info.getId());
                rspVo.setInstructionTitle(info.getInstructionTitle());
                //2、获取关联人数
                int personCount = 0;
                if (info.getPersonIds() != null) {
                    String[] split = info.getPersonIds().split(",");
                    personCount = split.length;
                }
                rspVo.setPersonCount(personCount);
                //3、查询交办时间
                InstructionAssign assign = assignList.stream().filter(assignInfo -> assignInfo.getInstructionId().equals(info.getId())).findFirst().orElse(null);

                if (assign != null) {
                    rspVo.setAssignTime(assign.getAssignTime());
                    //指令已经接收则将接收单位展示
                    //去除委政法委/政法委
                    String finalUnit = receiveUnitStr.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
                    rspVo.setCurrentCounty(finalUnit);
                    //4、查询是否已经接收
                    Date receiveTime = receiveMapper.findReceiveTime(info.getId(), unitCount);
                    if (receiveTime != null) {
                        rspVo.setReveiveTime(receiveTime);
                        //5、查询是否已经处置
                        Date disposeTime = disposeMapper.findDisposeTime(info.getId());
                        if (disposeTime != null) {
                            rspVo.setDisposeTime(disposeTime);
                            //6、查询是否已经反馈
                            Date feedbackTime = feedbackMapper.testFindTime(info.getId());
                            if (feedbackTime != null) {
                                rspVo.setFeedbackTime(feedbackTime);
                                //7、查询销号时间
                                InstructionEnd end = endMapper.selectInstructionEndByInstructionId(info.getId());
                                if (end != null) {
                                    rspVo.setEndTime(end.getEndTime());
                                }
                            }
                        }
                    }
                } else {
                    rspVo.setCurrentCounty("金华市");
                }
                rspVoList.add(rspVo);
            }
        }
        return rspVoList;
    }

    /**
     * 通过接收信息获取指令流程 转交、处置、反馈信息
     *
     * @param receiveList
     * @param flowTestVo
     */
    private void getInstructionInfo(final List<InstructionReceive> receiveList, final InstructionFlowTestVo flowTestVo) {
        List<InstructionFeedback> feedbackList = new ArrayList<>();
        //2、查询出所有的转交记录
        if (receiveList != null && receiveList.size() > 0) {
            for (InstructionReceive receiveInfo : receiveList) {
                //3、通过接收记录id 查询出转交记录
                //通过接收id查询转交信息
                InstructionTransfer instructionTransfer = new InstructionTransfer();
                instructionTransfer.setReceiveId(receiveInfo.getId());
                instructionTransfer.setStatus(1);
                instructionTransfer.setInstructionId(receiveInfo.getInstrucationId());
                List<InstructionTransfer> instructionTransfers = transferMapper.selectInstructionTransferList(instructionTransfer);
                receiveInfo.setTransferList(instructionTransfers);
                if (instructionTransfers != null && instructionTransfers.size() > 0) {
                    for (InstructionTransfer transfer : instructionTransfers) {
                        //通过转交id查询出处置、反馈记录
                        InstructionFeedback feedback = new InstructionFeedback();
                        feedback.setStatus(1);
                        feedback.setTransferId(transfer.getId());
                        List<InstructionFeedback> feedbackList1 = feedbackMapper.selectInstructionFeedbackList(feedback);
                        feedbackList.addAll(feedbackList1);
                    }
                }

            }
            flowTestVo.setReceiveList(receiveList);
            flowTestVo.setFeedbackList(feedbackList);
        }
    }

    /**
     * 指令列表(没有权限)
     *
     * @param instructionInfo
     * @return
     */
    @Override
    public List<InstructionInfoRspVo> instructionListNoPower(InstructionInfo instructionInfo) {
        List<InstructionInfomiddleResVo> instructionInfoList = new ArrayList<>();
        ArrayList<Long> arrayList = new ArrayList();
        List<InstructionInfoRspVo> collect = new ArrayList<>();
        if (instructionInfo.getProcess() == null) {
            instructionInfo.setProcess(0);
        }
        instructionInfo.setStatus("1");
        instructionInfoList = instructionInfoMapper.testInstructionListNew(instructionInfo);
        if (!CollectionUtils.isEmpty(instructionInfoList)) {
            for (InstructionInfomiddleResVo i : instructionInfoList) {
                InstructionInfoRspVo instructionInfoRspVo = new InstructionInfoRspVo();
                if (arrayList.contains(i.getId())) {
                    continue;
                }
                String receiveUnit = i.getReceiveUnit();

                int unitCount = 0;
                String receiveUnitStr = "";
                if (receiveUnit.length() > 1) {
                    receiveUnitStr = receiveUnit.substring(1, receiveUnit.length() - 1);
                    String[] receiveUnits = receiveUnit.substring(1, receiveUnit.length() - 1).split(",");
                    unitCount = receiveUnits.length;
                }

                BeanUtils.copyProperties(i, instructionInfoRspVo);
                instructionInfoRspVo.setReceiveUnit(receiveUnitStr);
                //2、获取关联人数
                int personCount = 0;
                if (i.getPersonIds() != null && i.getPersonIds().length() > 0) {
                    String[] split = i.getPersonIds().split(",");
                    personCount = split.length;
                }
                instructionInfoRspVo.setPersonCount(personCount);
                String finalUnit = receiveUnitStr.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
                instructionInfoRspVo.setCurrentCounty(finalUnit);
                if (i.getUnitCount() == null || !i.getUnitCount().equals(unitCount)) {
                    instructionInfoRspVo.setReveiveTime(null);
                    instructionInfoRspVo.setDisposeTime(null);
                    instructionInfoRspVo.setFeedbackTime(null);
                    instructionInfoRspVo.setEndTime(null);

                } else if (i.getReveiveTime() == null) {
                    instructionInfoRspVo.setFeedbackTime(null);
                    instructionInfoRspVo.setEndTime(null);
                } else if (i.getInstrucationIsEnd() != null && i.getInstrucationIsEnd() == 2) {

                    if (i.getEndTime() != null && i.getFeedbackTime() != null) {
                        int dateResult = i.getEndTime().compareTo(i.getFeedbackTime());
                        if (dateResult < 0) {
                            //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                            String substring = i.getFeedbackDept().substring(1, i.getFeedbackDept().length() - 1);
                            int needFeedBackCount = 0;
                            if (substring.contains(",")) {
                                needFeedBackCount = substring.split(",").length;
                            } else {
                                needFeedBackCount = 1;
                            }
                            //查询销号为后的反馈已办结记录部门
                            int feedBackDeptCount = feedbackMapper.findEndFeedDeptCount(i.getEndTime(), i.getId());
                            if (feedBackDeptCount == needFeedBackCount) {
                                instructionInfoRspVo.setEndTime(null);
                            } else {
                                instructionInfoRspVo.setFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            }
                        } else {
                            instructionInfoRspVo.setFeedbackTime(null);
                            instructionInfoRspVo.setEndTime(null);
                        }
                    } else {
                        instructionInfoRspVo.setFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    }
                }
                if (instructionInfoRspVo.getAssignTime() != null) {
                    if (instructionInfoRspVo.getAssignTime().before(instructionInfo.getAssignStartTime()) || instructionInfoRspVo.getAssignTime().after(instructionInfo.getAssignEndTime())) {
                        continue;
                    }
                } else {
                    continue;
                }
                switch (instructionInfo.getProcess()) {
                    case 0:
                        markStatusName(instructionInfoRspVo);
                        break;
                    case 1:
                        if (instructionInfoRspVo.getAssignTime() == null || instructionInfoRspVo.getReveiveTime() != null || instructionInfoRspVo.getFeedbackTime() != null || instructionInfoRspVo.getEndTime() != null) {
                            continue;
                        }
                        instructionInfoRspVo.setStatusName("交办");
                        break;
                    case 2:
                        if (instructionInfoRspVo.getReveiveTime() == null || instructionInfoRspVo.getFeedbackTime() != null || instructionInfoRspVo.getEndTime() != null) {
                            continue;
                        }
                        instructionInfoRspVo.setStatusName("接收");
                        break;
                    case 3:
                        if (instructionInfoRspVo.getFeedbackTime() != null || instructionInfoRspVo.getEndTime() != null) {
                            continue;
                        }
                        break;
                    case 4:
                        if (instructionInfoRspVo.getFeedbackTime() == null || instructionInfoRspVo.getEndTime() != null) {
                            continue;
                        }
                        instructionInfoRspVo.setStatusName("反馈");
                        break;
                    case 5:
                        if (instructionInfoRspVo.getEndTime() == null) {
                            continue;
                        }
                        instructionInfoRspVo.setStatusName("销号");
                        break;
                }
                collect.add(instructionInfoRspVo);
                arrayList.add(i.getId());
            }
        }

//        int start = instructionInfo.getEnd() * (instructionInfo.getStart() - 1);

        List<InstructionInfoRspVo> collect1 = new ArrayList<>();
        List<InstructionInfoRspVo> collectFinally = new ArrayList<>();
        //进行数据过滤 查看市级、非市级页面数据展示不同,市账号查看市级页面-》仅能查看自己创建的指令 市账号查看县市区页面-》可以查看各县市区自己创建的指令 县市区账号同理
        //是市本级账号 只查询createDeptId=202的指令

        collectFinally = collect.stream().collect(Collectors.toList());
//        collect1 = collectFinally.stream().collect(Collectors.toList());
//        collect1 = collectFinally.stream().skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());

//        TableDataInfo tableDataInfo = new TableDataInfo();
//        //添加记录
//        tableDataInfo.setRows(collect1);
//        tableDataInfo.setTotal(collectFinally.size());
//        tableDataInfo.setMsg("操作成功");
//        tableDataInfo.setCode(200);
        return collectFinally;
    }

    @Override
    public List<InstructionInfo> selectInstructionInfoList(final InstructionInfo instructionInfo) {
        return instructionInfoMapper.selectInstructionInfoList(instructionInfo);
    }

    /**
     * 更改指令是否发布状态
     *
     * @param instructionInfo
     * @return
     */
    @Override
    public int updateIsRelease(final InstructionInfo instructionInfo) {
        return instructionInfoMapper.updateInstructionInfo(instructionInfo);
    }

    public void markStatusName(InstructionInfoRspVo instructionInfoRspVo) {
        if (instructionInfoRspVo.getAssignTime() != null && instructionInfoRspVo.getReveiveTime() == null && instructionInfoRspVo.getFeedbackTime() == null && instructionInfoRspVo.getEndTime() == null) {
            instructionInfoRspVo.setStatusName("交办");
        } else if (instructionInfoRspVo.getReveiveTime() != null && instructionInfoRspVo.getFeedbackTime() == null && instructionInfoRspVo.getEndTime() == null) {
            instructionInfoRspVo.setStatusName("接收");
        } else if (instructionInfoRspVo.getFeedbackTime() != null && instructionInfoRspVo.getEndTime() == null) {
            instructionInfoRspVo.setStatusName("反馈");
        } else if (instructionInfoRspVo.getEndTime() != null) {
            instructionInfoRspVo.setStatusName("销号");
        }
//        case 1:
//        if (instructionInfoRspVo.getAssignTime() == null || instructionInfoRspVo.getReveiveTime() != null || instructionInfoRspVo.getFeedbackTime() != null || instructionInfoRspVo.getEndTime() != null) {
//            continue;
//        }
//        break;
//        case 2:
//        if (instructionInfoRspVo.getReveiveTime() == null || instructionInfoRspVo.getFeedbackTime() != null || instructionInfoRspVo.getEndTime() != null) {
//            continue;
//        }
//        break;
//        case 3:
//        if (instructionInfoRspVo.getFeedbackTime() != null || instructionInfoRspVo.getEndTime() != null) {
//            continue;
//        }
//        break;
//        case 4:
//        if (instructionInfoRspVo.getFeedbackTime() == null || instructionInfoRspVo.getEndTime() != null) {
//            continue;
//        }
//        break;
//        case 5:
//        if (instructionInfoRspVo.getEndTime() == null) {
//            continue;
//        }


    }

    @Override
    public List<InstructionInfoRspVo> testInstructionListNotPage(InstructionInfo instructionInfo) {

        //1、判断用户具有哪些权限进行查询
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String deptName = user.getDept().getDeptName();
        Long deptId = SecurityUtils.getDeptId();
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());

        List<InstructionInfomiddleResVo> instructionInfoList = new ArrayList<>();
        ArrayList<Long> arrayList = new ArrayList();
        List<InstructionInfoRspVo> collect = new ArrayList<>();
        if (instructionInfo.getProcess() == null) {
            instructionInfo.setProcess(0);
        }
        if (instructionInfo.getProcess() == 6) {
            //查询出未处理的指令ids  代办事件
            List<Long> ids = null;
            if (instructionInfo.getPageType()!=null&&instructionInfo.getPageType() == 1) {
                //市本级未处理
                ids = testRedStatistics().get("cityIds");
            } else if (instructionInfo.getPageType() == 2) {
                //显示取代办
                ids = testRedStatistics().get("countyIds");
            }
            if (ids != null && ids.size() > 0) {
                Map<String, Object> params = instructionInfo.getParams();
                params.put("ids", ids);
                instructionInfo.setParams(params);
                //获取指令列表返回类
                instructionInfoList = instructionInfoMapper.testInstructionListNew(instructionInfo);
                for (InstructionInfomiddleResVo i : instructionInfoList) {
                    InstructionInfoRspVo instructionInfoRspVo = new InstructionInfoRspVo();
                    String receiveUnit = i.getReceiveUnit();
                    int unitCount = 0;
                    String receiveUnitStr = "";
                    if (receiveUnit.length() > 1) {
                        receiveUnitStr = receiveUnit.substring(1, receiveUnit.length() - 1);
                        String[] receiveUnits = receiveUnit.substring(1, receiveUnit.length() - 1).split(",");
                        unitCount = receiveUnits.length;
                    }
                    BeanUtils.copyProperties(i, instructionInfoRspVo);
                    instructionInfoRspVo.setReceiveUnit(receiveUnitStr);
                    //2、获取关联人数
                    int personCount = 0;
                    if (i.getPersonIds() != null && i.getPersonIds().length() > 0) {
                        String[] split = i.getPersonIds().split(",");
                        personCount = split.length;
                    }
                    String finalUnit = receiveUnitStr.replace("综合行政执法局", "").replace("行政执法指挥中心", "");
                    instructionInfoRspVo.setCurrentCounty(finalUnit);
                    if (i.getUnitCount() == null || !i.getUnitCount().equals(unitCount)) {
                        //没有接收单位或接收流程未完成
                        instructionInfoRspVo.setReveiveTime(null);
                        instructionInfoRspVo.setFeedbackTime(null);
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getReveiveTime() == null) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getFeedbackTime() == null) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getCountDeptNum() != null && !i.getCountDeptNum().equals(i.getReceiveUnitCount())) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getInstrucationIsEnd() != null && i.getInstrucationIsEnd() == 2) {
                        //销号办结为否->需判断反馈记录是否已反馈
                        int dateResult = 0;
                        if (i.getCreateDeptId() == 202L && i.getEndTime() != null && i.getCountyFeedbackTime() != null) {
                            //市级创建指令 需用销号时间和反馈时间进行判断
                            dateResult = i.getEndTime().compareTo(i.getCountyFeedbackTime());
                            if (dateResult < 0) {
                                //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                                String substring = i.getFeedbackDept().substring(1, i.getFeedbackDept().length() - 1);
                                int needFeedBackCount = 0;
                                if (substring.contains(",")) {
                                    needFeedBackCount = substring.split(",").length;
                                } else {
                                    needFeedBackCount = 1;
                                }
                                //查询销号为后的县市区反馈已办结记录部门
                                if (i.getCountDeptNum() == needFeedBackCount) {
                                    instructionInfoRspVo.setEndTime(null);
                                } else {
                                    //县市区下发指令
                                    instructionInfoRspVo.setCountyFeedbackTime(null);
                                    instructionInfoRspVo.setEndTime(null);
                                }
                            }
                        } else if (i.getCreateDeptId() != 202L && i.getEndTime() != null && i.getFeedbackTime() != null) {
                            //非市本级创建
                            dateResult = i.getEndTime().compareTo(i.getFeedbackTime());
                            if (dateResult < 0) {
                                //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                                String substring = i.getFeedbackDept().substring(1, i.getFeedbackDept().length() - 1);
                                int needFeedBackCount = 0;
                                if (substring.contains(",")) {
                                    needFeedBackCount = substring.split(",").length;
                                } else {
                                    needFeedBackCount = 1;
                                }
                                //查询销号为后的反馈已办结记录部门
                                int feedBackDeptCount = feedbackMapper.findEndFeedDeptCount(i.getEndTime(), i.getId());
                                if (feedBackDeptCount == needFeedBackCount) {
                                    instructionInfoRspVo.setEndTime(null);
                                } else {
                                    //县市区下发指令
                                    instructionInfoRspVo.setFeedbackTime(null);
                                    instructionInfoRspVo.setEndTime(null);
                                }
                            } else {
                                //县市区下发指令
                                instructionInfoRspVo.setFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            }
                        } else {
                            if (i.getCreateDeptId() != 202L) {
                                //县市区下发指令
                                instructionInfoRspVo.setFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            } else {
                                //市本级下发指令
                                instructionInfoRspVo.setCountyFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            }
                        }

                    }
                    collect.add(instructionInfoRspVo);
                }
            }

        } else if (instructionInfo.getProcess() == 7) {
            //查询出已处理的指令 已处理指令 = 全部指令-未处理指令
            //全部指令
            List<InstructionInfomiddleResVo> finallyList = getInstructionInfoList(deptId, deptName, roleList, instructionInfo);
            //查询出未处理的指令ids  代办事件
            List<Long> ids = null;
            if (instructionInfo.getPageType() == 1) {
                //市本级未处理
                ids = testRedStatistics().get("cityIds");
            } else if (instructionInfo.getPageType() == 2) {
                //显示取代办
                ids = testRedStatistics().get("countyids");
            }
            // List<Long> ids = testRedStatistics().stream().collect(Collectors.toList());

            //进行过滤
            if (!CollectionUtils.isEmpty(finallyList)) {
                for (InstructionInfomiddleResVo resVo : finallyList) {
                    boolean flag = true;
                    for (Long id : ids) {
                        if (resVo.getId().equals(id)) {
                            flag = false;
                            break;
                        }
                    }
                    if (flag) {
                        if (resVo.getReveiveTime() == null && resVo.getFeedbackTime() == null && resVo.getEndTime() == null) {
                            break;
                        }
                        instructionInfoList.add(resVo);
                    }
                }
            }
            if (instructionInfoList.size() > 0 && instructionInfoList != null) {
                for (InstructionInfomiddleResVo i : instructionInfoList) {
                    InstructionInfoRspVo instructionInfoRspVo = new InstructionInfoRspVo();
                    String receiveUnit = i.getReceiveUnit();
                    int unitCount = 0;
                    String receiveUnitStr = "";
                    if (receiveUnit.length() > 1) {
                        receiveUnitStr = receiveUnit.substring(1, receiveUnit.length() - 1);
                        String[] receiveUnits = receiveUnit.substring(1, receiveUnit.length() - 1).split(",");
                        unitCount = receiveUnits.length;
                    }

                    BeanUtils.copyProperties(i, instructionInfoRspVo);
                    instructionInfoRspVo.setReceiveUnit(receiveUnitStr);
                    //2、获取关联人数
                    int personCount = 0;
                    if (i.getPersonIds() != null && i.getPersonIds().length() > 0) {
                        String[] split = i.getPersonIds().split(",");
                        personCount = split.length;
                    }
                    instructionInfoRspVo.setPersonCount(personCount);
                    String finalUnit = receiveUnitStr.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
                    instructionInfoRspVo.setCurrentCounty(finalUnit);
                    if (i.getUnitCount() == null || !i.getUnitCount().equals(unitCount)) {
                        //没有接收单位或接收流程未完成
                        instructionInfoRspVo.setReveiveTime(null);
                        instructionInfoRspVo.setFeedbackTime(null);
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getReveiveTime() == null) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getFeedbackTime() == null) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getCountDeptNum() != null && !i.getCountDeptNum().equals(i.getReceiveUnitCount())) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getInstrucationIsEnd() != null && i.getInstrucationIsEnd() == 2) {
                        //销号办结为否->需判断反馈记录是否已反馈
                        int dateResult = 0;
                        if (i.getCreateDeptId() == 202L && i.getEndTime() != null && i.getCountyFeedbackTime() != null) {
                            //市级创建指令 需用销号时间和反馈时间进行判断
                            dateResult = i.getEndTime().compareTo(i.getCountyFeedbackTime());
                            if (dateResult < 0) {
                                //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                                String substring = i.getFeedbackDept().substring(1, i.getFeedbackDept().length() - 1);
                                int needFeedBackCount = 0;
                                if (substring.contains(",")) {
                                    needFeedBackCount = substring.split(",").length;
                                } else {
                                    needFeedBackCount = 1;
                                }
                                //查询销号为后的县市区反馈已办结记录部门
                                if (i.getCountDeptNum() == needFeedBackCount) {
                                    instructionInfoRspVo.setEndTime(null);
                                } else {
                                    //县市区下发指令
                                    instructionInfoRspVo.setCountyFeedbackTime(null);
                                    instructionInfoRspVo.setEndTime(null);
                                }
                            }
                        } else if (i.getCreateDeptId() != 202L && i.getEndTime() != null && i.getFeedbackTime() != null) {
                            //非市本级创建
                            dateResult = i.getEndTime().compareTo(i.getFeedbackTime());
                            if (dateResult < 0) {
                                //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                                String substring = i.getFeedbackDept().substring(1, i.getFeedbackDept().length() - 1);
                                int needFeedBackCount = 0;
                                if (substring.contains(",")) {
                                    needFeedBackCount = substring.split(",").length;
                                } else {
                                    needFeedBackCount = 1;
                                }
                                //查询销号为后的反馈已办结记录部门
                                int feedBackDeptCount = feedbackMapper.findEndFeedDeptCount(i.getEndTime(), i.getId());
                                if (feedBackDeptCount == needFeedBackCount) {
                                    instructionInfoRspVo.setEndTime(null);
                                } else {
                                    //县市区下发指令
                                    instructionInfoRspVo.setFeedbackTime(null);
                                    instructionInfoRspVo.setEndTime(null);
                                }
                            } else {
                                //县市区下发指令
                                instructionInfoRspVo.setFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            }
                        } else {
                            if (i.getCreateDeptId() != 202L) {
                                //县市区下发指令
                                instructionInfoRspVo.setFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            } else {
                                //市本级下发指令
                                instructionInfoRspVo.setCountyFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            }
                        }

                    }
                    collect.add(instructionInfoRspVo);
                }
            }


        } else {

            instructionInfoList = getInstructionInfoList(deptId, deptName, roleList, instructionInfo);
            if (!CollectionUtils.isEmpty(instructionInfoList)) {

                for (InstructionInfomiddleResVo i : instructionInfoList) {
                    InstructionInfoRspVo instructionInfoRspVo = new InstructionInfoRspVo();
                    if (arrayList.contains(i.getId())) {
                        continue;
                    }
                    String receiveUnit = i.getReceiveUnit();

                    int unitCount = 0;
                    String receiveUnitStr = "";
                    if (receiveUnit.length() > 1) {
                        receiveUnitStr = receiveUnit.substring(1, receiveUnit.length() - 1);
                        String[] receiveUnits = receiveUnit.substring(1, receiveUnit.length() - 1).split(",");
                        unitCount = receiveUnits.length;
                    }

                    BeanUtils.copyProperties(i, instructionInfoRspVo);
                    instructionInfoRspVo.setReceiveUnit(receiveUnitStr);
                    //2、获取关联人数
                    int personCount = 0;
                    if (i.getPersonIds() != null && i.getPersonIds().length() > 0) {
                        String[] split = i.getPersonIds().split(",");
                        personCount = split.length;
                    }
                    instructionInfoRspVo.setPersonCount(personCount);
                    String finalUnit = receiveUnitStr.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
                    instructionInfoRspVo.setCurrentCounty(finalUnit);
                    if (i.getUnitCount() == null || !i.getUnitCount().equals(unitCount)) {
                        //没有接收单位或接收流程未完成
                        instructionInfoRspVo.setReveiveTime(null);
                        instructionInfoRspVo.setFeedbackTime(null);
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getReveiveTime() == null) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getFeedbackTime() == null) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getCountDeptNum() != null && !i.getCountDeptNum().equals(i.getReceiveUnitCount())) {
                        instructionInfoRspVo.setCountyFeedbackTime(null);
                        instructionInfoRspVo.setEndTime(null);
                    } else if (i.getInstrucationIsEnd() != null && i.getInstrucationIsEnd() == 2) {
                        //销号办结为否->需判断反馈记录是否已反馈
                        int dateResult = 0;
                        if (i.getCreateDeptId() == 202L && i.getEndTime() != null && i.getCountyFeedbackTime() != null) {
                            //市级创建指令 需用销号时间和反馈时间进行判断
                            dateResult = i.getEndTime().compareTo(i.getCountyFeedbackTime());
                            if (dateResult < 0) {
                                //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                                String substring = i.getFeedbackDept().substring(1, i.getFeedbackDept().length() - 1);
                                int needFeedBackCount = 0;
                                if (substring.contains(",")) {
                                    needFeedBackCount = substring.split(",").length;
                                } else {
                                    needFeedBackCount = 1;
                                }
                                //查询销号为后的县市区反馈已办结记录部门
                                if (i.getCountDeptNum() == needFeedBackCount) {
                                    instructionInfoRspVo.setEndTime(null);
                                } else {
                                    //县市区下发指令
                                    instructionInfoRspVo.setCountyFeedbackTime(null);
                                    instructionInfoRspVo.setEndTime(null);
                                }
                            }
                        } else if (i.getCreateDeptId() != 202L && i.getEndTime() != null && i.getFeedbackTime() != null) {
                            //非市本级创建
                            dateResult = i.getEndTime().compareTo(i.getFeedbackTime());
                            if (dateResult < 0) {
                                //判断销号为否后 反馈部门个数与需反馈部门是否相等 相等则表示需反馈部门已全部反馈,添加反馈时间 反之则反馈时间为null
                                String substring = i.getFeedbackDept().substring(1, i.getFeedbackDept().length() - 1);
                                int needFeedBackCount = 0;
                                if (substring.contains(",")) {
                                    needFeedBackCount = substring.split(",").length;
                                } else {
                                    needFeedBackCount = 1;
                                }
                                //查询销号为后的反馈已办结记录部门
                                int feedBackDeptCount = feedbackMapper.findEndFeedDeptCount(i.getEndTime(), i.getId());
                                if (feedBackDeptCount == needFeedBackCount) {
                                    instructionInfoRspVo.setEndTime(null);
                                } else {
                                    //县市区下发指令
                                    instructionInfoRspVo.setFeedbackTime(null);
                                    instructionInfoRspVo.setEndTime(null);
                                }
                            } else {
                                //县市区下发指令
                                instructionInfoRspVo.setFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            }
                        } else {
                            if (i.getCreateDeptId() != 202L) {
                                //县市区下发指令
                                instructionInfoRspVo.setFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            } else {
                                //市本级下发指令
                                instructionInfoRspVo.setCountyFeedbackTime(null);
                                instructionInfoRspVo.setEndTime(null);
                            }
                        }

                    }
                    switch (instructionInfo.getProcess()) {
                        case 0:
                            break;
                        case 1:
                            if (instructionInfoRspVo.getAssignTime() == null || instructionInfoRspVo.getReveiveTime() != null || instructionInfoRspVo.getFeedbackTime() != null || instructionInfoRspVo.getEndTime() != null) {
                                continue;
                            }
                            break;
                        case 2:
                            if (instructionInfoRspVo.getReveiveTime() == null || instructionInfoRspVo.getFeedbackTime() != null || instructionInfoRspVo.getEndTime() != null) {
                                continue;
                            }
                            break;
                        case 3:
                            if (instructionInfoRspVo.getFeedbackTime() != null || instructionInfoRspVo.getEndTime() != null) {
                                continue;
                            }
                            break;
                        case 4:
                            if (instructionInfoRspVo.getFeedbackTime() == null || instructionInfoRspVo.getEndTime() != null) {
                                continue;
                            }
                            break;
                        case 5:
                            if (instructionInfoRspVo.getEndTime() == null) {
                                continue;
                            }
                            break;
                        case 8:
                            if (instructionInfoRspVo.getCountyFeedbackTime() == null || instructionInfoRspVo.getEndTime() != null) {
                                continue;
                            }
                            break;
                    }
                    collect.add(instructionInfoRspVo);
                    arrayList.add(i.getId());
                }
            }
        }

        //1交办，2接收，3处置，4反馈，5销号 排序
        Integer sortType = 0;
        if (instructionInfo.getSortType() == null || instructionInfo.getSortType() == 0) {
            sortType = 0;
        } else if (instructionInfo.getSortType() == 1 || instructionInfo.getSortType() == 2) {
            sortType = 1;
        } else if (instructionInfo.getSortType() == 3 || instructionInfo.getSortType() == 4) {
            sortType = 2;
        } else if (instructionInfo.getSortType() == 5 || instructionInfo.getSortType() == 6) {
            sortType = 3;
        } else if (instructionInfo.getSortType() == 7 || instructionInfo.getSortType() == 8) {
            sortType = 4;
        } else if (instructionInfo.getSortType() == 9 || instructionInfo.getSortType() == 10) {
            sortType = 5;
        } else if (instructionInfo.getSortType() == 11 || instructionInfo.getSortType() == 12) {
            sortType = 6;
        }
        List<InstructionInfoRspVo> collect1 = new ArrayList<>();
        List<InstructionInfoRspVo> collectFinally = new ArrayList<>();
        //进行数据过滤 查看市级、非市级页面数据展示不同,市账号查看市级页面-》仅能查看自己创建的指令 市账号查看县市区页面-》可以查看各县市区自己创建的指令 县市区账号同理
        if (instructionInfo.getPageType() != null&&instructionInfo.getPageType() == 1) {
            //是市本级账号 只查询createDeptId=202的指令
            collectFinally = collect.stream().filter(instructionInfoRspVo -> instructionInfoRspVo.getCreateDeptId() == 202).collect(Collectors.toList());
        } else {
            //查看县市区页面
            if (deptId.equals(Constants.JINHUA_CITY_DEPT_ID) || (
                    !deptId.equals(Constants.KAIFA_CITY_DEPT_ID)) &&
                    !deptId.equals(Constants.WUYI_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.YONGKAN_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.DONGYANG_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.JINDON_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.LANXI_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.PANAN_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.PUJIANG_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.WUCHENG_CITY_DEPT_ID) &&
                    !deptId.equals(Constants.YIWU_CITY_DEPT_ID)
            ) {
                //是市本级账号 只查询createDeptId=202的指令
                collectFinally = collect.stream().filter(instructionInfoRspVo -> instructionInfoRspVo.getCreateDeptId() != 202).collect(Collectors.toList());
            } else {
                //县市区账号 只能查看createDeptId!=自己部门id
                collectFinally = collect.stream().filter(instructionInfoRspVo -> instructionInfoRspVo.getCreateDeptId().equals(deptId)).collect(Collectors.toList());
            }
        }

//        int start = instructionInfo.getEnd() * (instructionInfo.getStart() - 1);
        //进行排序

        if (sortType == 0) {
            collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getAssignTime, Comparator.nullsFirst(Date::compareTo)).reversed()).collect(Collectors.toList());
        } else if (sortType == 1) {
            if (instructionInfo.getSortType() == 1) {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getAssignTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime)).collect(Collectors.toList());
            } else {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getAssignTime, Comparator.nullsFirst(Date::compareTo)).reversed().thenComparing(InstructionInfoRspVo::getCreateTime, Comparator.reverseOrder())).collect(Collectors.toList());
            }
        } else if (sortType == 2) {
            if (instructionInfo.getSortType() == 3) {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getReveiveTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime)).collect(Collectors.toList());
            } else {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getReveiveTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime, Comparator.nullsFirst(Date::compareTo)).reversed()).collect(Collectors.toList());
            }
        } else if (sortType == 3) {
            if (instructionInfo.getSortType() == 5) {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getDisposeTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime)).collect(Collectors.toList());
            } else {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getDisposeTime, Comparator.nullsFirst(Date::compareTo)).reversed().thenComparing(InstructionInfoRspVo::getCreateTime, Comparator.reverseOrder())).collect(Collectors.toList());
            }
        } else if (sortType == 4) {
            if (instructionInfo.getSortType() == 7) {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getFeedbackTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime)).collect(Collectors.toList());
            } else {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getFeedbackTime, Comparator.nullsFirst(Date::compareTo)).reversed().thenComparing(InstructionInfoRspVo::getCreateTime, Comparator.reverseOrder())).collect(Collectors.toList());
            }
        } else if (sortType == 5) {
            if (instructionInfo.getSortType() == 9) {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getEndTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime)).collect(Collectors.toList());
            } else {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getEndTime, Comparator.nullsFirst(Date::compareTo)).reversed().thenComparing(InstructionInfoRspVo::getCreateTime, Comparator.reverseOrder())).collect(Collectors.toList());
            }
        } else if (sortType == 6) {
            if (instructionInfo.getSortType() == 11) {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getCountyFeedbackTime, Comparator.nullsFirst(Date::compareTo)).thenComparing(InstructionInfoRspVo::getCreateTime)).collect(Collectors.toList());
            } else {
                collect1 = collectFinally.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getCountyFeedbackTime, Comparator.nullsFirst(Date::compareTo)).reversed().thenComparing(InstructionInfoRspVo::getCreateTime, Comparator.reverseOrder())).collect(Collectors.toList());
            }
        }

        if (roleList.contains(InstructionRolesConstants.ASSIGN) && !roleList.contains(InstructionRolesConstants.RECEIVE) && !roleList.contains(InstructionRolesConstants.DISPOSE) && !roleList.contains(InstructionRolesConstants.FEEDBACK)) {
            //如果是交办员需要判断是否可以处理
            for (InstructionInfoRspVo rsp : collect1) {
                if (!rsp.getCreateDeptId().equals(deptId)) {
                    rsp.setIsDeal(0);
                }
            }
        }


        return  collect1;

    }

    /**
     * 删除指令基本信息
     * @param ids
     * @return
     */
    @Override
    public int batchRemove(Long[] ids) {
        int  result = 0;
        for (Long id : ids){
            delPerson(id);
            //同步删除 交办、接收、转交、反馈、销号
            //1、删除 交办信息
            assignMapper.deleteInstructionAssignByInstructionId(id);
            //2、删除接收记录
            receiveMapper.deleteInstructionReceiveByInstructionId(id);
            //3、删除转交记录
            transferMapper.deleteInstructionTransferByInstructionId(id);
            //4、删除反馈记录
            feedbackMapper.deleteInstructionFeedbackByInstructionId(id);
            //删除事件记录
            // InstructionEvent event = eventMapper.selectInstructionEventByInstrucationId(id);
            // if (event!=null){
            //     eventService.deleteInstructionEventById(event.getId());
            // }
            instructionInfoMapper.deleteInstructionInfoById(id);
            result= result+1;
        }
        return result;
    }

}
