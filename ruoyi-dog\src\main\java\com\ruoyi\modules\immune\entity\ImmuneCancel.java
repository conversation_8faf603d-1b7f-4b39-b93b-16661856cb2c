package com.ruoyi.modules.immune.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;

import java.util.Date;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 9:52
 * @version: 1.0
 **/
public class ImmuneCancel extends BaseEntity {

    private Integer type;                             //注销类型：1走失，2死亡
    private String petId;                             //犬只ID
    private String cancelName;                         //注销人
    private String cancelReason;                       //注销原因
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createDate;                          //注销时间
    private String status;                            //审核状态：1待审批，2已通过，3未通过
    private String reason;                            //审核意见
    private String cancelId;                            //注销人ID

    private String brandNum;                          //犬牌编号(成功注销时获取犬牌编号)

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getPetId() {
        return petId;
    }

    public void setPetId(String petId) {
        this.petId = petId;
    }

    public String getCancelName() {
        return cancelName;
    }

    public void setCancelName(String cancelName) {
        this.cancelName = cancelName;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    @Override
    public Date getCreateDate() {
        return createDate;
    }

    @Override
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getCancelId() {
        return cancelId;
    }

    public void setCancelId(String cancelId) {
        this.cancelId = cancelId;
    }

    public String getBrandNum() {
        return brandNum;
    }

    public void setBrandNum(String brandNum) {
        this.brandNum = brandNum;
    }
}
