package com.ruoyi.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * 类名称：QRCodeMax
 * 类描述：生成二维码图片+背景+文字描述工具类
 * 创建人：一个除了帅气，一无是处的男人
 * 创建时间：2018年12月x日x点x分x秒
 * 修改时间：2019年2月x日x点x分x秒
 * 修改备注：更新有参数构造
 * @version： 2.0
 *
 */
public class QRCodeMax {


    //文字显示
    private static final int QRCOLOR = 0x201f1f; // 二维码颜色:黑色
    private static final int BGWHITE = 0xFFFFFF; //二维码背景颜色:白色

    // 设置QR二维码参数信息
    private static Map<EncodeHintType, Object> hints = new HashMap<EncodeHintType, Object>() {
        private static final long serialVersionUID = 1L;
        {
            put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);// 设置QR二维码的纠错级别(H为最高级别)
            put(EncodeHintType.CHARACTER_SET, "utf-8");// 设置编码方式
            put(EncodeHintType.MARGIN, 1);// 白边
        }
    };

    /**
     * 生成二维码图片+背景+文字描述
     * @param outputStream 生成图片输出流
     * @param bgImgFile 背景图地址
     * @param WIDTH 二维码宽度
     * @param HEIGHT 二维码高度
     * @param qrUrl 二维码识别地址
     * @param imagesX 二维码x轴方向
     * @param imagesY 二维码y轴方向
     */
    public static void creatQRCode(OutputStream outputStream, InputStream bgImgFile,Integer WIDTH,Integer HEIGHT,String qrUrl,
                                    Integer imagesX,Integer imagesY,InputStream logoFile) {
        try {
            MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
            // 参数顺序分别为: 编码内容,编码类型,生成图片宽度,生成图片高度,设置参数
            BitMatrix bm = multiFormatWriter.encode(qrUrl, BarcodeFormat.QR_CODE, WIDTH, HEIGHT, hints);

            BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);


        /*
            问题：生成二维码正常,生成带logo的二维码logo变成黑白
            原因：MatrixToImageConfig默认黑白，需要设置BLACK、WHITE
            解决：https://ququjioulai.iteye.com/blog/2254382
         */
//            MatrixToImageConfig matrixToImageConfig = new MatrixToImageConfig(0xFF000000, 0xFFFFFFFF);
//             开始利用二维码数据创建Bitmap图片，分别设为黑(0xFFFFFFFF) 白(0xFF000000)两色
            for (int x = 0; x < WIDTH; x++) {
                for (int y = 0; y < HEIGHT; y++) {
                    image.setRGB(x, y, bm.get(x, y) ? QRCOLOR : BGWHITE);
                }
            }
            image = logoMatrix(image, logoFile);
//            ImageIO.write(image, "png", new File("C:\\busqr\\zxing1.png"));//输出带logo图片

            /*
	         * 	添加背景图片
	        */
            BufferedImage backgroundImage = ImageIO.read(bgImgFile);
            int bgWidth=backgroundImage.getWidth();
            int qrWidth=image.getWidth();
            //距离背景图片x边的距离，居中显示
            int disx=(bgWidth-qrWidth)-imagesX;
            //距离y边距离 * * * *
            int disy=imagesY;
            Graphics2D rng=backgroundImage.createGraphics();
            rng.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP));
            rng.drawImage(image,disx,disy,WIDTH,HEIGHT,null);
            rng.dispose();
            image=backgroundImage;
            image.flush();
            ImageIO.write(image, "png", outputStream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 二维码添加logo
     * @param matrixImage 源二维码图片
     * @param logoFile logo图片
     * @return 返回带有logo的二维码图片
     * 参考：https://blog.csdn.net/weixin_39494923/article/details/79058799
     */
    public static BufferedImage logoMatrix(BufferedImage matrixImage, InputStream logoFile) throws IOException {
        /**
         * 读取二维码图片，并构建绘图对象
         */
        Graphics2D g2 = matrixImage.createGraphics();

        int matrixWidth = matrixImage.getWidth();
        int matrixHeigh = matrixImage.getHeight();

        /**
         * 读取Logo图片
         */
        BufferedImage logo = ImageIO.read(logoFile);

        //开始绘制图片
        g2.drawImage(logo,matrixWidth/5*2,matrixHeigh/5*2, matrixWidth/5, matrixHeigh/5, null);//绘制
        BasicStroke stroke = new BasicStroke(5,BasicStroke.CAP_ROUND,BasicStroke.JOIN_ROUND);
        g2.setStroke(stroke);// 设置笔画对象
        //指定弧度的圆角矩形
        RoundRectangle2D.Float round = new RoundRectangle2D.Float(matrixWidth/5*2, matrixHeigh/5*2, matrixWidth/5, matrixHeigh/5,20,20);
        g2.setColor(Color.white);
        g2.draw(round);// 绘制圆弧矩形

//        //设置logo 有一道灰色边框
//        BasicStroke stroke2 = new BasicStroke(1,BasicStroke.CAP_ROUND,BasicStroke.JOIN_ROUND);
//        g2.setStroke(stroke2);// 设置笔画对象
//        RoundRectangle2D.Float round2 = new RoundRectangle2D.Float(matrixWidth/5*2+2, matrixHeigh/5*2+2, matrixWidth/5-4, matrixHeigh/5-4,20,20);
//        g2.setColor(new Color(128,128,128));
//        g2.draw(round2);// 绘制圆弧矩形

        g2.dispose();
        matrixImage.flush() ;
        return matrixImage ;
    }

    /**
     * 根据
     * @param qrUrl
     * @param bgImgFile
     * @param logoFile
     * @param outputStream
     */
    public static void initBusQr(String qrUrl, InputStream bgImgFile, InputStream logoFile, OutputStream outputStream){
        //生成图地址,背景图地址,二维码宽度,二维码高度,二维码识别地址,图片x轴方向,图片y轴方向
        creatQRCode(outputStream,bgImgFile, 840, 840, qrUrl, 776, 1052,logoFile);
    }

    /**
     * 测试
     * @param args
     */
    public static void main(String[] args)  {
        //二维码生成
        String qrUrl = "http://admin.chinau8.cn";                       //二维码链接
        File outFile = new File("C://busqr//myqrcode.png");  //生成图片位置
        File bgImgFile=new File("C://busqr//background.png");//背景图片
        File logoFile = new File("C:\\busqr\\logo.png");     //logo图片
        OutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(outFile);
            initBusQr(qrUrl,new FileInputStream(bgImgFile),new FileInputStream(logoFile),outputStream);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }finally {
            if(outputStream != null){
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}