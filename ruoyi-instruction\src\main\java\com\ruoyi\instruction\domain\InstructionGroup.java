package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 群体基本信息对象 t_instruction_group
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@Data
public class InstructionGroup extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 群体id自增
     */
    private Long id;

    /**
     * 群体名称
     */
    @Excel(name = "群体名称")
    private String groupName;

    /**
     * 类型名称
     */
    @Excel(name = "群体类型")
    private String typeName;

    /**
     * 群体类型（由类型表中选取填入）
     */
    private String type;

    /**
     * 管控级别（低、中、高）
     */
    @Excel(name = "管控级别", combo = {"高", "中", "低"})
    private String controlLevel;

    /**
     * 管控级别中文名称
     */
    private String controlLevelName;

    /**
     * 首次填报日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "首次填报日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstSubmitTime;

    /**
     * 基本情况
     */
    @Excel(name = "基本情况")
    private String baseInfo;

    /**
     * 群体特征
     */
    @Excel(name = "群体特征")
    private String groupCharact;

    /**
     * 防控措施
     */
    @Excel(name = "防控措施")
    private String preventionControlMeasures;

    /**
     * 人员id集合
     */
    private String personIds;

    /**
     * 1:正常 9：删除
     */
    private String status;


    private String leadPersonIds;

    /**
     * 人员信息
     */
    private List<InstrucationPerson> personList;


    /**
     * 群体关联事件数
     */
    private Integer eventCount;

    /**
     * 群体关联人数
     */
    private Integer personCount;

    /**
     * 群体关联所有的人员ids
     */
    private String allPersonIds;

    /**
     * 群体关联的牵头人员
     */
    private String allLeadPersonIds;

    /**
     * 部门
     */
    private  String dutyUnit;

}