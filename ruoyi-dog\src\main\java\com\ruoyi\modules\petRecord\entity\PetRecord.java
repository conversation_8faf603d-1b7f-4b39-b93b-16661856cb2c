package com.ruoyi.modules.petRecord.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;

import java.util.Date;
import java.io.Serializable;

/**
 * 犬只操作记录表(PetRecord)实体类
 *
 * <AUTHOR>
 * @since 2023-01-05 17:19:42
 */
public class PetRecord extends BaseEntity {


    private String id;
    /**
    * 流程节点 1：犬牌申请 2：办证审核 3：犬牌补办  4：犬牌补办审核
    */
    private Integer node;
    /**
    * 犬类ID
    */
    private String petId;
    /**
    * 身份证号
    */
    private String petIdCard;
    /**
    * 犬牌号
    */
    private String petNum;
    /**
    * 状态 1：待审核 2：通过 3 ：未通过
    */
    private Integer status;
    /**
    * 审批意见
    */
    private String remark;
    /**
    * 数据
    */
    private String strDate;
    /**
    * 创建人名称
    */
    private String createName;
    /**
    * 创建时间
    */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    /**
    * 创建人
    */
    private String createBy;
    /**
    * 修改时间
    */
    private Date updateDate;

    private String updateBy;
    /**
    * 状态： 1正常
    */
    private Integer delFlag;

    /**
     * 旧犬牌
     */
    private String oldPetNum;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getNode() {
        return node;
    }

    public void setNode(Integer node) {
        this.node = node;
    }

    public String getPetId() {
        return petId;
    }

    public void setPetId(String petId) {
        this.petId = petId;
    }

    public String getPetIdCard() {
        return petIdCard;
    }

    public void setPetIdCard(String petIdCard) {
        this.petIdCard = petIdCard;
    }

    public String getPetNum() {
        return petNum;
    }

    public void setPetNum(String petNum) {
        this.petNum = petNum;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStrDate() {
        return strDate;
    }

    public void setStrDate(String strDate) {
        this.strDate = strDate;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getOldPetNum() {
        return oldPetNum;
    }

    public void setOldPetNum(String oldPetNum) {
        this.oldPetNum = oldPetNum;
    }
}
