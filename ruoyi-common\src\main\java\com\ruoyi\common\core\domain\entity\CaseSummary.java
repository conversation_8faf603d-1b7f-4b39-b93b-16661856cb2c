package com.ruoyi.common.core.domain.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.ArrayList;
import java.util.List;

/**
 * 案由对象 case_summary
 *
 * <AUTHOR>
 * @date 2021-06-25
 */
public class CaseSummary extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long summaryId;

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 父级id
     */
    @Excel(name = "父级id")
    private Long parentId;

    /**
     * 祖级列表
     */
    @Excel(name = "祖级列表")
    private String ancestors;

    /**
     * 内容 权利事项
     */
    @Excel(name = "内容")
    private String content;

    /**
     * 案由编号
     */
    @Excel(name = "案由编号")
    private String code;

    /**
     * 违则
     */
    @Excel(name = "违则")
    private String violate;

    /**
     * 违则内容
     */
    @Excel(name = "违则内容")
    private String violateContent;

    /**
     * 罚则
     */
    @Excel(name = "罚则")
    private String punish;

    /**
     * 罚则内容
     */
    @Excel(name = "罚则内容")
    private String punishContent;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 部门id
     */
    @Excel(name = "部门id")
    private Long deptId;

    /**
     * 部门名称
     */
    @Excel(name = "部门名称")
    private String deptName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer sort;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String summaryType;

    /**
     * 类型
     */
    private String type;

    /**
     * 类型 1=菜单 2=内容
     */
    @Excel(name = "类型 1=菜单 2=内容")
    private String menuType;

    private List<CaseSummary> children = new ArrayList<>();

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getViolate() {
        return violate;
    }

    public void setViolate(String violate) {
        this.violate = violate;
    }

    public String getViolateContent() {
        return violateContent;
    }

    public void setViolateContent(String violateContent) {
        this.violateContent = violateContent;
    }

    public String getPunish() {
        return punish;
    }

    public void setPunish(String punish) {
        this.punish = punish;
    }

    public String getPunishContent() {
        return punishContent;
    }

    public void setPunishContent(String punishContent) {
        this.punishContent = punishContent;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public List<CaseSummary> getChildren() {
        return children;
    }

    public void setChildren(List<CaseSummary> children) {
        this.children = children;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getSummaryType() {
        return summaryType;
    }

    public void setSummaryType(String summaryType) {
        this.summaryType = summaryType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMenuType() {
        return menuType;
    }

    public void setMenuType(String menuType) {
        this.menuType = menuType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("summaryId", getSummaryId())
                .append("title", getTitle())
                .append("parentId", getParentId())
                .append("ancestors", getAncestors())
                .append("content", getContent())
                .append("code", getCode())
                .append("violate", getViolate())
                .append("violateContent", getViolateContent())
                .append("punish", getPunish())
                .append("punishContent", getPunishContent())
                .append("delFlag", getDelFlag())
                .append("deptId", getDeptId())
                .append("deptName", getDeptName())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
