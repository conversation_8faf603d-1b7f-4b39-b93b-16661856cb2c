package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 指令交办对象 t_instruction_assign
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
@Data
public class InstructionAssign extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 交办表主键 */
    private Long id;

    /** 交办时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "交办时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    /** 交办部门 */
    @Excel(name = "交办部门")
    private String assignDept;

    /** 交办人员 */
    @Excel(name = "交办人员")
    private String assignPerson;

    /** 指令id */
    @Excel(name = "指令id")
    private Long instructionId;

    /** 交办状态1：正常 9：删除 */
    @Excel(name = "交办状态1：正常 9：删除")
    private String status;

    /** 交办部门id */
    @Excel(name = "交办部门id")
    private Long assignDeptId;


}
