package com.ruoyi.config;


import com.ruoyi.modules.user.entity.SysUser;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;

/**
 * Created by Administrator on 2020-9-8.
 */
public class MyHttpServletRequest extends HttpServletRequestWrapper {
    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request The request to wrap
     * @throws IllegalArgumentException if the request is null
     */
    public MyHttpServletRequest(HttpServletRequest request) {
        super(request);
    }

    /**
     * 修改此方法主要是因为当RequestMapper中的参数为pojo类型时，
     * 会通过此方法获取所有的请求参数并进行遍历，对pojo属性赋值
     * @return
     */
    @Override
    public Enumeration<String> getParameterNames() {
        Enumeration<String> enumeration = super.getParameterNames();
        ArrayList<String> list = Collections.list(enumeration);
        //添加uid字段
        list.add("managerOrgName");
        return Collections.enumeration(list);
    }
    @Override
    public String[] getParameterValues(String name) {
        if ("managerOrgName".equals(name)){
            SysUser sysUser = (SysUser) getSession().getAttribute("sysUser");
            if(sysUser != null){
                return new String[] { sysUser.getDeptName() };
            }else{
                return null;
            }
        }
        return super.getParameterValues(name);
    }

}
