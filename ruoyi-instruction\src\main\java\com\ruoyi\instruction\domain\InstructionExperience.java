package com.ruoyi.instruction.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【经验创新】对象 t_instruction_experience
 *
 * <AUTHOR>
 * @date 2023-05-17
 */
public class InstructionExperience extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /**
     * 用于基层安全板块下的经验创新小模块的显示出的自增序号，为了与id区分，以拼音形式命名为xuhao
     */
    private long xuhao;

    /** 经验创新表主键 */
    private Long id;

    /** 经验创新名称 */
    @Excel(name = "经验创新名称")
    private String expTitle;

    /** 领域类型(由类型表中取出) */
    @Excel(name = "领域类型(由类型表中取出)")
    private String type;

    /** 报送单位 */
    @Excel(name = "报送单位")
    private String dutyUnit;

    /** 基本情况 */
    @Excel(name = "基本情况")
    private String baseSituation;

    /** 1:正常  9：删除 */
    @Excel(name = "1:正常  9：删除")
    private String status;

    public long getXuhao() {
        return xuhao;
    }

    public void setXuhao(long xuhao) {
        this.xuhao = xuhao;
    }

    /** 1：发布  2：未发布 */
    @Excel(name = "1：发布  2：未发布")
    private String isRelease;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setExpTitle(String expTitle)
    {
        this.expTitle = expTitle;
    }

    public String getExpTitle()
    {
        return expTitle;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setDutyUnit(String dutyUnit)
    {
        this.dutyUnit = dutyUnit;
    }

    public String getDutyUnit()
    {
        return dutyUnit;
    }
    public void setBaseSituation(String baseSituation)
    {
        this.baseSituation = baseSituation;
    }

    public String getBaseSituation()
    {
        return baseSituation;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setIsRelease(String isRelease)
    {
        this.isRelease = isRelease;
    }

    public String getIsRelease()
    {
        return isRelease;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("xuhao", getXuhao())
            .append("id", getId())
            .append("expTitle", getExpTitle())
            .append("type", getType())
            .append("dutyUnit", getDutyUnit())
            .append("baseSituation", getBaseSituation())
            .append("status", getStatus())
            .append("isRelease", getIsRelease())
            .append("createTime", getCreateTime())
            .toString();
    }
}
