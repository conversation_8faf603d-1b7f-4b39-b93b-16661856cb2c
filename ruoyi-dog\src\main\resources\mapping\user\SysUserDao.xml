<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.user.dao.SysUserDao">

    <sql id="sysUserColumns">
        a.id AS "id",
        a.user_name AS "userName",
        a.real_name AS "realName",
        a.password AS "password",
        a.user_type AS "userType",
        a.user_sex AS "userSex",
        a.user_email AS "userEmail",
        a.dept_id AS "deptId",
        a.org_type AS "orgType",
        a.headimg AS "headimg",
        a.readnum AS "readnum",
        a.downnum AS "downnum",
        a.mobile AS "mobile",
        a.del_flag AS "delFlag",
        a.create_by AS "createBy",
        a.create_date AS "createDate",
        a.update_by AS "updateBy",
        a.update_date AS "updateDate",
        a.id_card as idCard,
        a.address as address,
        a.customerNo as customerNo
    </sql>

    <select id="getById" resultType="com.ruoyi.modules.user.entity.SysUser">
        select
        <include refid="sysUserColumns"/>,
        group_concat(sr.role_name) as "userRoleNames",
        sd.dept_name as "deptName"
        from sys_user a
        LEFT join sys_dept sd on a.dept_id = sd.id
        left join sys_user_role sur on a.id = sur.user_id
        LEFT join sys_role sr on sur.role_id = sr.id
        where a.id = #{id}
    </select>

    <select id="getByUserName" resultType="com.ruoyi.modules.user.entity.SysUser">
        select
        <include refid="sysUserColumns"/>
        from sys_user a where a.user_name = #{userName}
    </select>

    <select id="getByEntity" resultType="com.ruoyi.modules.user.entity.SysUser">
        select
        <include refid="sysUserColumns"/>,
        (select qualifi from qualifi ua where ua.account = a.id ) as qualifi
        ,(select dept_name from sys_dept where id=a.dept_id)  as deptName
        from sys_user a
        where a.del_flag = 1
        <if test='userName != null and userName != ""'>
            and a.user_name = #{userName}
        </if>
        <if test='password != null and password != ""'>
            and a.password = #{password}
        </if>
        limit 1
    </select>

    <select id="getList" resultType="com.ruoyi.modules.user.entity.SysUser">
        select
        <include refid="sysUserColumns"/>,
        group_concat(sur.role_id) as "userRoleStr",
        group_concat(sur.manager_orgs) as "managerOrgs",
        group_concat(sr.role_name) as "userRoleNames",
        sd.dept_name as "deptName"
        from sys_user a
        left join sys_user_role sur on a.id = sur.user_id
        LEFT join sys_role sr on sur.role_id = sr.id
        LEFT join sys_dept sd on a.dept_id = sd.id
        where a.del_flag = 1
        <if test='userName != null and userName != ""'>
            and a.user_name =#{userName}
        </if>
        <if test='realName != null and realName != ""'>
            and a.real_name like concat('%',#{realName},'%')
        </if>
        <if test='deptName != null and deptName != ""'>
            and a.dept_name like concat('%',#{deptName},'%')
        </if>
        <if test='userType != null and userType != ""'>
            and a.user_type = #{userType}
        </if>
        <if test='mobile != null and mobile != ""'>
            and a.mobile = #{mobile}
        </if>
        <if test='address != null and address != ""'>
            and a.address = #{address}
        </if>
        <if test='deptId != null and deptId != ""'>
            and a.dept_id = #{deptId}
        </if>
        <if test='showUserType != null and showUserType != ""'>
            and a.user_type != '1'
        </if>

        group by a.id
        order by a.create_date desc
    </select>

    <insert id="insert">
        insert into sys_user(id,
                             user_name,
                             password,
                             real_name,
                             user_type,
                             user_sex,
                             user_email,
                             dept_id,
                             dept_name,
                             org_type,
                             mobile,
                             headimg,
                             readnum,
                             downnum,
                             address,
                             create_date,
                             create_by,
                             del_flag,id_card)
        values (#{id},
                #{userName},
                #{password},
                #{realName},
                #{userType},
                #{userSex},
                #{userEmail},
                #{deptId},
                #{deptName},
                #{orgType},
                #{mobile},
                #{headimg},
                #{readnum},
                #{downnum},
                #{address},
                #{createDate},
                #{createBy},
                #{delFlag},#{idCard})
    </insert>

    <update id="update">
        update sys_user
        set real_name     = #{realName},
            user_type     = #{userType},
            user_sex      = #{userSex},
            user_email    = #{userEmail},
            dept_id       = #{deptId},
            dept_name     = #{deptName},
            org_type      = #{orgType},
            mobile        = #{mobile},
            headimg       = #{headimg},
            address       = #{address},
            update_date   = #{updateDate},
            update_by     = #{updateBy},
            id_card       = #{idCard}
        where id = #{id}
    </update>
    <update id="editPass">
        update sys_user
        set password    = #{password},
            update_date = #{updateDate},
            update_by   = #{updateBy}
        where id = #{id}
    </update>

    <delete id="delete">
        update sys_user
        set del_flag = 2
        where id = #{id}
    </delete>

    <insert id="saveList" parameterType="java.util.List">
        INSERT INTO sys_user (
        id,
        user_name,
        password,
        real_name,
        user_type,
        create_date,
        create_by,
        del_flag
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.userName},
            #{item.password},
            #{item.realName},
            #{item.userType},
            #{item.createDate},
            #{item.createBy},
            #{item.delFlag}
            )
        </foreach>
    </insert>

    <update id="updatePassWord" parameterType="com.ruoyi.modules.user.entity.SysUser">
        update sys_user set
            password = #{password},
            update_date = #{updateDate},
            update_by = #{updateBy}
        where id = #{id}
    </update>

    <select id="getUserInfo" resultType="com.ruoyi.modules.user.entity.SysUser">
        select
        <include refid="sysUserColumns"/>
        from sys_user a where a.id_card = #{petIdCard}
    </select>

     <select id="queryUserAddres" resultType="com.ruoyi.modules.user.entity.SysUser">
        select
        <include refid="sysUserColumns"/>,
         sd.dept_name as "deptName"
        from sys_user a join sys_user_role b on  b.user_id=a.id
            LEFT join sys_dept sd on a.dept_id = sd.id
        where a.del_flag='1'
          and b.role_id = '13198e269824405cb0eac2ee0038d789'
    </select>

     <update id="updateUserPassword">
        update sys_user a
        set a.password    = #{password},
            a.update_date = #{updateDate},
            a.update_by   = #{updateBy}
        where a.id = #{id}
    </update>
    <select id="getEchatByDept" resultType="java.util.HashMap">
        SELECT dept_name name,( SELECT count(DISTINCT pet_id_card ) FROM pet_certificates WHERE del_flag = 1 AND pet_dept = d.id )
            value
        FROM
            sys_dept d
        WHERE
            d.del_flag = 1
          AND `level` = 2
        ORDER BY
            value
    </select>
</mapper>
