package com.ruoyi.modules.takeIn.entity;

import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.user.entity.SysUploadFile;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * 认领、收养记录(ReclaimRecord)实体类
 *
 * <AUTHOR>
 * @since 2022-08-30 11:36:27
 */
@Data
public class ReclaimRecord extends BaseEntity {

    /**
     * 类型 1.认领 2.收养
     */
    private String type;
    /**
     * 收容表id
     */
    private String takeInId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 联系电话
     */
    private String contactNumber;
    /**
     * 预约时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date appointmentTime;
    /**
     * 备注
     */
    private String remarks;
    /*
     * 审核状态 0.待审核 1.通过 2.驳回 3.已领回
     * */
    private String status;
    /**
     * 审核记录
     */
    private String auditRecords;
    /**/
    private JSONArray recordsList;//
    private TakeIn takeIn;
    private String keyword;//关键字

    private String idCard;

    private String street;

    private String county;

    private String address;


}
