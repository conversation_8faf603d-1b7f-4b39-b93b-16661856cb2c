package com.ruoyi.modules.notice.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.notice.entity.Notice;
import com.ruoyi.modules.notice.entity.WorkNotice;
import com.ruoyi.modules.notice.service.NoticeService;
import com.ruoyi.modules.notice.service.WorkNoticeService;
import com.ruoyi.modules.pushMessageDept.entity.PushMessageDept;
import com.ruoyi.modules.pushMessageDept.service.PushMessageDeptService;
import com.ruoyi.modules.user.entity.SysUser;
import com.ruoyi.modules.user.service.SysUserService;
import com.ruoyi.modules.zzd.entity.SysUserZzd;
import com.ruoyi.modules.zzd.service.SysUserZzdService;
import com.ruoyi.util.zzd.ZzdUtil;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 法律法规公告表(notice)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("workNotice")
public class WorkNoticeController {
    /**
     * 服务对象
     */
    @Resource
    private WorkNoticeService service;
    @Resource
    private PushMessageDeptService pushMessageDeptService;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysUserZzdService sysUserZzdService;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("/getPageList")
    public AjaxResult getPageList(WorkNotice entity) {
        return AjaxResult.success(service.getPageList(entity));
    }

    @RequestMapping("/getList")
    public AjaxResult getList(WorkNotice entity) {
        return AjaxResult.success(service.getList(entity));
    }

    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(WorkNotice entity) {
        service.saveOrUpdate(entity);
        // 发布通知
        if(entity.getStatus().equals("2")) {
            sendZZDMsg(entity);
        }
        return AjaxResult.success();
    }

    @RequestMapping("/delete")
    public AjaxResult delete(WorkNotice entity) {
        entity.setDelFlag(2);
        service.delete(entity);
        return AjaxResult.success();
    }

    @RequestMapping("updateStatus")
    public AjaxResult updateStatus(WorkNotice entity){
        service.updateStatus(entity);
        return AjaxResult.success();
    }


    private void sendZZDMsg(WorkNotice entity) {
        PushMessageDept pushMessageDept = new PushMessageDept();
        pushMessageDept.setMessageId(entity.getId());
        List<PushMessageDept> pushMessageDeptList = pushMessageDeptService.getList(pushMessageDept);
        if (pushMessageDeptList.size() > 0) {
            List<SysUser> sysUserList = new ArrayList<>();
            pushMessageDeptList.forEach(dept -> {
                SysUser sysUser = new SysUser();
                sysUser.setDeptId(dept.getDetpId());
                sysUser.setUserType(3); //执法局
                sysUserList.addAll(sysUserService.getList(sysUser));
            });
            if (sysUserList.size() > 0) {
                List<String> accountList = new ArrayList<>();
                sysUserList.forEach(sysUser -> {
                    SysUserZzd sysUserZzd = new SysUserZzd();
                    sysUserZzd.setUserId(sysUser.getId());
                    List<SysUserZzd> sysUserZzds = sysUserZzdService.getList(sysUserZzd);
                    if (sysUserZzds.size() > 0) {
                        accountList.add(sysUserZzds.get(0).getAccount());
                    }
                });
                if (accountList.size() > 0) {
                    SysUser sysUser = sysUserService.getById(entity.getCreateBy());
                    String accounts = accountList.stream().collect(Collectors.joining(","));
                    ZzdUtil.sendZzdMessage(accounts,entity.getTitle(), entity.getContent(), sysUser.getRealName());
                }
            }
        }
    }
}
