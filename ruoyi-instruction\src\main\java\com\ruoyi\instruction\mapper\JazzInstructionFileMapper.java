package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.JazzInstructionFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 指令文件Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
@Mapper
@DataSource(value = DataSourceType.SLAVE)
public interface JazzInstructionFileMapper
{
    /**
     * 查询指令文件
     * 
     * @param id 指令文件主键
     * @return 指令文件
     */
    public JazzInstructionFile selectInstructionFileById(Long id);

    /**
     * 查询指令文件列表
     * 
     * @param instructionFile 指令文件
     * @return 指令文件集合
     */
    public List<JazzInstructionFile> selectInstructionFileList(JazzInstructionFile instructionFile);

    /**
     * 新增指令文件
     * 
     * @param instructionFile 指令文件
     * @return 结果
     */
    public int insertInstructionFile(JazzInstructionFile instructionFile);

    /**
     * 修改指令文件
     * 
     * @param instructionFile 指令文件
     * @return 结果
     */
    public int updateInstructionFile(JazzInstructionFile instructionFile);

    /**
     * 删除指令文件
     * 
     * @param id 指令文件主键
     * @return 结果
     */
    public int deleteInstructionFileById(Long id);

    /**
     * 批量删除指令文件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionFileByIds(Long[] ids);

    /**
     * 根据文件id查询文件信息
     * @param longs
     * @return
     */
    List<JazzInstructionFile> selectByFileIds(@Param("ids") long[] longs);
}
