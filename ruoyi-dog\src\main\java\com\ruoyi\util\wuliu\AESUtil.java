package com.ruoyi.util.wuliu;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.Key;


/**
 * AES加密工具类
 *
 * <AUTHOR>
 * @since 2021-06-18 19:11:03
 */
public class AESUtil {

    private static final String KEY_ALGORITHM = "AES";
    private static final String CIPHER_ALGORITHM_CBC = "AES/CBC/PKCS5Padding";
    private static final String IV = "6u7hxlh59qsg457k";
    private static final String UTF_8 = "UTF-8";

    private AESUtil() {
    }

    static byte[] getIv() throws UnsupportedEncodingException {
        return IV.getBytes(UTF_8);
    }

    static byte[] getKey(String originalStr)
            throws UnsupportedEncodingException {
        return DigestUtils.md5Hex(originalStr).substring(8, 24).getBytes(UTF_8);
    }

    /**
     * 解密
     *
     * @param data待解密数据
     * @param key 密钥
     * @return 明文
     * @throws Exception
     */
    public static String decrypt(String data, String key) throws Exception {
        Key k = new SecretKeySpec(getKey(key), KEY_ALGORITHM);
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM_CBC);
        cipher.init(Cipher.DECRYPT_MODE, k, new IvParameterSpec(getIv()));
        // 执行操作
        byte[] result = cipher.doFinal(Base64.decodeBase64((data)));
        return new String(result, UTF_8);
    }

    /**
     * 加密
     *
     * @param data待加密数据
     *
     * @param key密钥
     * @return 密文
     * @throws Exception
     */
    public static String encrypt(String data, String key) throws Exception {
        Key k = new SecretKeySpec(getKey(key), KEY_ALGORITHM);
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM_CBC);
        cipher.init(Cipher.ENCRYPT_MODE, k, new IvParameterSpec(getIv()));
        // 执行操作
        byte[] result = cipher.doFinal(data.getBytes(UTF_8));
        return Base64.encodeBase64String(result);
    }


    public static void main(String[] args) {
        try {
            String a = encrypt("13777928946", "RuRMYlVRDXV=4g8CklQq718k");
            String b = decrypt("y4sfxQcaO2OXEfpoBtSQrdgzubDQftL0eqLobuplweo=", "RuRMYlVRDXV=4g8CklQq718k");
            System.out.println(a);
            System.out.println(b);
        }catch(Exception e) {
            e.printStackTrace();
        }

    }
}
