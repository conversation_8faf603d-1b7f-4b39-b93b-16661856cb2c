package com.ruoyi.modules.hospital.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
    * 资质年审审批记录
    */
public class QualifiYearRecord {
    private String id;
    /**
     * 医院ID
     */
    private String qualifiId;
    /**
     * 流程节点 1：畜牧局 2：执法局 3 ：宠物医院 4：执法队员 5：执法窗口
     */
    private Integer node;
    /**
     * 操作类型 1：待审核 2：通过 3：不通过 4：免疫注销 5 犬牌注销 6 提交审核
     */
    private Integer recordType;
    /**
     * 审核意见
     */
    private String remark;
    /**
     * 状态 1：未办理 2：已办理
     */
    private Integer status;
    /**
     * 创建人名称
     */
    private String createName;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 修改时间
     */
    private Date updateDate;

    private String updateBy;
    /**
     * 状态： 1正常，2删除
     */
    private Integer delFlag;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getQualifiId() {
        return qualifiId;
    }

    public void setQualifiId(String qualifiId) {
        this.qualifiId = qualifiId;
    }

    public Integer getNode() {
        return node;
    }

    public void setNode(Integer node) {
        this.node = node;
    }

    public Integer getRecordType() {
        return recordType;
    }

    public void setRecordType(Integer recordType) {
        this.recordType = recordType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }
}
