package com.ruoyi.base.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.base.entity.BaseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by Administrator on 2020/1/2/002.
 */
@Service
public abstract class BaseService <D extends BaseDao<T>, T extends BaseEntity> {

    /**
     * 当前持久层对象
     */
    @Autowired(required = false)
    public D dao ;

    /**
     * 根据ID获取单条数据
     * @param id
     * @return
     */
    public T getById(String id){
        return dao.getById(id);
    }

    /**
     * 根据条件获取单条数据
     * @param entity
     * @return
     */
    public T getByEntity(T entity){
        return dao.getByEntity(entity);
    }

    /**
     * 根据条件获取多条数据
     * @param entity
     * @return
     */
    public List<T> getList(T entity){
        return dao.getList(entity);
    }


    /**
     * 分页获取数据对象
     * @param entity
     * @return
     */
    public PageInfo<T> getPageList(T entity){
        PageHelper.startPage(entity.getPageNum(),entity.getPageSize());
        PageInfo<T> pageInfo = new PageInfo<T>(getList(entity));
        return pageInfo;
    }

    /**
     * 插入数据
     * @param entity
     * @return
     */
    @Transactional
    public void insert(T entity){
        entity.preInsert();
        dao.insert(entity);
    }

    /**
     * 更新数据
     * @param entity
     * @return
     */
    @Transactional
    public void update(T entity){
        entity.preUpdate();
        dao.update(entity);
    }

    /**
     * 删除数据
     * @param entity
     * @return
     */
    @Transactional
    public void delete(T entity){
        dao.delete(entity);
    }

    @Transactional
    public void saveOrUpdate(T entity){
        if(entity.getId() != null && !"".equals(entity.getId())){
            // 更新用户
            entity.preUpdate();
            update(entity);
        }else{
            // 新增用户
            entity.preInsert();
            insert(entity);
        }
    }
}
