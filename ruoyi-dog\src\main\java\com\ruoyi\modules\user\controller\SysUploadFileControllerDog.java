package com.ruoyi.modules.user.controller;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.brand.entity.ApplyMerge;
import com.ruoyi.modules.brand.entity.PetBrand;
import com.ruoyi.modules.brand.entity.PetBrandApply;
import com.ruoyi.modules.brand.service.PetBrandApplyService;
import com.ruoyi.modules.brand.service.PetBrandService;
import com.ruoyi.modules.takeIn.service.TakeInService;
import com.ruoyi.modules.takeIn.vo.SysFileBase64VO;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.entity.SysUploadLocal;
import com.ruoyi.modules.user.service.SysUploadFileService;
import com.ruoyi.modules.user.service.SysUploadLocalService;
import com.ruoyi.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by Administrator on 2021-3-11.
 */
@Slf4j
@RestController
@RequestMapping("/sysUploadFile")
public class SysUploadFileControllerDog {

    @Value("${localUploadPrefix}")
    private String localUploadPrefix ;

    @Value("${uploadFilePrefix}")
    private String uploadFilePrefix ;
    @Value("${uploadFilePath}")
    private String uploadFilePath ;

    @Value("${zipFilePath}")
    private String zipFilePath;

    @Autowired
    private SysUploadFileService sysUploadFileService ;

    @Autowired
    private SysUploadLocalService sysUploadLocalService;

    @Autowired
    private PetBrandApplyService petBrandApplyService;

    @Autowired
    private PetBrandService petBrandService;

    /**
     * @author: tongsiyu
     * @date: 2022/05/26 10:28
     * @Description:身份证识别
     */
    // @RequestMapping("/ocrIDCard")
    // public AjaxResult ocrIDCard(String imgFile) {
    //     return AjaxResult.success(OCRUtil.ocrIDCard(uploadFilePath+imgFile));
    // }
    @RequestMapping("/analysisQr")
    public AjaxResult analysisQr(MultipartFile multipartFile) {
        String res=sysUploadFileService.analysisQr(transferToFile(multipartFile));
        return AjaxResult.success(res);
    }
//    /**
//     * 保存附件
//     * @return
//     */
//    @RequestMapping("/saveAllList")
//    public AjaxResult saveAllList(String jsonFileStr){
//        String xid = RootContext.getXID();
//        System.out.println("seata事物控制==》"+xid);
//        System.out.println("---执行保存附件---");
//        sysUploadFileService.saveAllList(jsonFileStr);
//        return AjaxResult.success();
//    }

    /**
     * 根据类型和实例删除附件
     * @return
     */
    @RequestMapping("/delByInstanceAndModel")
    public AjaxResult delByInstanceAndModel(String instanceId,String modelType){
        sysUploadFileService.delByInstanceAndModel(instanceId,modelType);
        return AjaxResult.success();
    }

    @RequestMapping("/uploadFile")
    public AjaxResult uploadQualificationLicensePhoto(MultipartFile multipartFile,
                                                      HttpServletRequest httpServletRequest) throws IOException {
//        JsonResult jsonResult = new JsonResult();

//        try {
//            String key = IdGen.uuid();
//            QiniuFileUtils.upload(multipartFile, httpServletRequest, key, true);
//
//            return AjaxResult.success(QiniuFileUtils.generatePublicURL(key));
//        }catch (IOException e){
//            e.printStackTrace();
//            return AjaxResult.success(e.getMessage());
//        }
        String filePath = UploadLocalUtil.uploadFile(multipartFile);
        return AjaxResult.success().put("data",filePath);
    }

    /**
     * 上传base64格式的图片
     * @param fileVO
     * @return
     * @throws IOException
     */
    @PostMapping("/uploadFileByBase64")
    public AjaxResult uploadFileByBase64(@RequestBody SysFileBase64VO fileVO) throws IOException {
        // 解析base64头部，获取图片类型
        String today = DateUtil.format(new Date(), "yyyyMMdd");
        String uploadFileName = DateUtil.format(new Date(), "yyyyMMddHHmmss") + (int) ((Math.random() * 9 + 1) * 100000);
        String fileSuffix = ".png";
        String base64Data = fileVO.getFileBase64();
        if (base64Data != null && base64Data.contains("base64,")) {
            String[] parts = base64Data.split(",", 2);
            String header = parts[0];
            base64Data = parts[1];
            if (header.contains("image/")) {
                String type = header.substring(header.indexOf("image/") + 6);
                if (type.contains(";")) {
                    type = type.substring(0, type.indexOf(";"));
                }
                fileSuffix = "." + type;
            }
        }
        String resultPath = "/" + today + "/" + uploadFileName + "/" + uploadFileName + fileSuffix;
        String fullFilePath = RuoYiConfig.getUploadPath() + resultPath;

        log.info("uploadFileByBase64, fullFilePath:{}", fullFilePath);

        TakeInService.convertBase64StrToImage(base64Data, fullFilePath);
        return AjaxResult.success().put("data", resultPath);
    }

    public File transferToFile(MultipartFile multipartFile) {
//        选择用缓冲区来实现这个转换即使用java 创建的临时文件 使用 MultipartFile.transferto()方法 。
        File file = null;
        try {
            String originalFilename = multipartFile.getOriginalFilename();
            String[] filename = originalFilename.split("\\.");
            file=File.createTempFile(filename[0], filename[1] + ".");
            multipartFile.transferTo(file);
            file.deleteOnExit();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file;
    }
    /**
     * 分页获取附件数据
     */
    @RequestMapping("/getPageList")
    public AjaxResult getPageList(SysUploadFile sysUploadFile){
        return AjaxResult.success(sysUploadFileService.getPageList(sysUploadFile));
    }

    //获取附件集合
    @RequestMapping("/getAllFileList")
    public String getAllFileList(String sysUploadFileToString){
        SysUploadFile sysUploadFile = JSON.parseObject(sysUploadFileToString,SysUploadFile.class);
        return JSONObject.toJSONString(sysUploadFileService.getList(sysUploadFile));
    }

    // 根据id下载附件
    @RequestMapping("/downloadFile")
    public void downloadFile(SysUploadFile sysUploadFile, HttpServletRequest request, HttpServletResponse response) {
//        SysUploadFile file = sysUploadFileService.getById(fileId);
        if(sysUploadFile != null && sysUploadFile.getFileUrl() != null && sysUploadFile.getFileName() != null){
            String fileUrl = sysUploadFile.getFileUrl();
            String fileName = sysUploadFile.getFileName();
            //从网络Url中下载文件
            InputStream inputStream = null;
            try {
                URL url = new URL(fileUrl);
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                //设置超时间为3秒
                conn.setConnectTimeout(3*1000);
                //得到输入流
                inputStream = conn.getInputStream();
                //获取自己数组
                if (request.getHeader("User-Agent").toLowerCase().indexOf("firefox") > 0) {
                    fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1"); // firefox浏览器
                } else if (request.getHeader("User-Agent").toUpperCase().indexOf("MSIE") > 0) {
                    fileName = URLEncoder.encode(fileName, "UTF-8");// IE浏览器
                }else if (request.getHeader("User-Agent").toUpperCase().indexOf("CHROME") > 0) {
                    fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");// 谷歌
                }else{
                    fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
                }
                response.reset();
                response.setCharacterEncoding("UTF-8");
                response.setContentType("application/msword");
                //response.setContentType("application/octet-stream");
                //3.设置content-disposition响应头控制浏览器以下载的形式打开文件
                response.addHeader("Content-Disposition","attachment;filename=" + fileName);
                OutputStream out = response.getOutputStream();
                byte[] buffer = new byte[1024];
                int len = 0;
                while((len = inputStream.read(buffer)) != -1) {
                    out.write(buffer, 0, len);
                }
                out.close();
            } catch (Exception e) {
                e.printStackTrace();
            }finally {
                if(inputStream != null){
                    try {
                        inputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    // 根据路径下载附件
    @RequestMapping("/downloadLocalFile")
    public void downloadLocalFile(String path,HttpServletRequest request, HttpServletResponse response,String type) {
        // 1. 输入验证 - 使用正则表达式检查路径合法性
        if (path == null || !isValidPath(path)) {
            log.warn("非法文件路径访问尝试: {}", path);
            throw new SecurityException("非法文件路径");
        }

        try {
//            // 2. 获取实际的上传目录路径
//            String uploadPath = RuoYiConfig.getUploadPath();
//
//            // 3. 构建基础路径
//            Path basePath = Paths.get(uploadPath).toAbsolutePath().normalize();
//
//            // 4. 解析并规范化用户提供的路径
//            Path userPath = Paths.get(path).normalize();
//
//            // 5. 构建完整文件路径
//            Path filePath = basePath.resolve(userPath).normalize();
//
//            // 6. 确保文件在允许的目录内（防止目录遍历）
//            if (!filePath.startsWith(basePath)) {
//                log.warn("文件访问超出允许范围: {}", path);
//                throw new SecurityException("文件访问超出允许范围");
//            }
//
//            // 7. 检查文件是否存在且为常规文件
//            if (!Files.exists(filePath) || !Files.isRegularFile(filePath)) {
//                log.warn("请求的文件不存在或不是常规文件: {}", path);
//                throw new RuntimeException("文件不存在");
//            }

            // 8. 调用原有的下载方法
            UploadLocalUtil.downLoadFile(path, request, response, type);

        } catch (Exception e) {
            log.error("文件下载异常: ", e);
            try {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "文件访问被拒绝");
            } catch (IOException ioException) {
                log.error("响应错误信息失败: ", ioException);
            }
        }
    }

    /**
     * 使用正则表达式验证路径合法性
     * @param path 用户提供的路径
     * @return 路径是否合法
     */
    private boolean isValidPath(String path) {
        // 正则表达式说明:
        // ^[a-zA-Z0-9/_\-\.]+$  : 只允许字母、数字、斜杠、下划线、连字符和点号
        // (?!.*\.\.)            : 负向前瞻，确保不包含两个连续的点号（防止 .. 遍历）
        // (?!.*\/\/)            : 负向前瞻，确保不包含两个连续的斜杠（防止绕过）
        // [^/\\]                : 确保不以斜杠或反斜杠开头（相对路径）
        // .*\\.[^/\\]*$         : 确保路径以文件扩展名结尾
        String pathPattern = "^[a-zA-Z0-9/_\\-\\.]+(?!.*\\.\\.)(?!.*//)(?!.*\\\\\\\\)[^/\\\\]\\.[^/\\\\]*$";

        // 对于您提到的路径格式，我们需要允许以 / 开头的路径
        // 所以我们调整正则表达式
        String adjustedPathPattern = "^[a-zA-Z0-9/_\\-\\.]+(?!.*\\.\\.)(?!.*//)(?!.*\\\\\\\\)[^/\\\\]\\.[^/\\\\]*$";

        // 更宽松的验证，允许您提到的两种格式
        String loosePathPattern = "^[a-zA-Z0-9/_\\-\\.]+\\.[a-zA-Z0-9]+$";

        return path.matches(loosePathPattern) && !path.contains("../") && !path.contains("..\\");
    }

    @RequestMapping("save")
    public AjaxResult save(SysUploadFile sysUploadFile){
        sysUploadFileService.insert(sysUploadFile);
        return AjaxResult.success();
    }

    @RequestMapping("/getFileById")
    public void getFileById(String id,HttpServletRequest request,HttpServletResponse response){
        System.out.println(id);
        SysUploadLocal sysUploadLocal = sysUploadLocalService.getByKey(id);
        // 实现文件下载
        byte[] buffer = new byte[1024];
        FileInputStream fis = null;
        BufferedInputStream bis = null;
        try {
            // 浏览器乱码问题处理
            String fileName = sysUploadLocal.getFileName();
            if (request.getHeader("User-Agent").toLowerCase().indexOf("firefox") > 0) {
                fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1"); // firefox浏览器
            } else if (request.getHeader("User-Agent").toUpperCase().indexOf("MSIE") > 0) {
                fileName = URLEncoder.encode(fileName, "UTF-8");// IE浏览器
            }else if (request.getHeader("User-Agent").toUpperCase().indexOf("CHROME") > 0) {
                fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");// 谷歌
            }
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            File file = new File(sysUploadLocal.getFileUrl());
            fis = new FileInputStream(file);
            bis = new BufferedInputStream(fis);
            OutputStream os = response.getOutputStream();
            int i = bis.read(buffer);
            while (i != -1) {
                os.write(buffer, 0, i);
                i = bis.read(buffer);
            }
            System.out.println("Download the song successfully!");
        } catch (Exception e) {
            System.out.println("Download the song failed!");
        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @RequestMapping("/downDogCodeZip")
    public void downDogCodeZip(String id, HttpServletRequest request, HttpServletResponse response ) {
        ApplyMerge applyMerge = new ApplyMerge();
        applyMerge.setId(id);
        List<PetBrandApply> petBrandApplies =  petBrandApplyService.getMergeList(applyMerge);
        List<String> itemList = new ArrayList<>();
        String fatherPath = zipFilePath + System.currentTimeMillis();
        File fatherFile = new File(fatherPath);
        try {
            if (!fatherFile.exists()) {
                fatherFile.mkdirs();
            } else {
                boolean f = fatherFile.delete();
                System.out.println(f);
                fatherFile.mkdirs();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        String fatherPath1 = fatherPath +".zip";
        File fatherFile1 = new File(fatherPath1);
        try {
            if (!fatherFile1.exists()) {
            } else {
                fatherFile1.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (petBrandApplies.size() > 0) {
            petBrandApplies.forEach(res -> {
                itemList.add(res.getUserRealName());
                String dogCodes = res.getDogCodes();
                List<String> dogCodeList  = Arrays.stream(dogCodes.split(",")).collect(Collectors.toList());
                List<File> fileList = new ArrayList<>();
                List<String> list = new ArrayList<>();
                dogCodeList.forEach(e -> {
                    PetBrand petBrand = petBrandService.getById(e);
                    fileList.add(new File(uploadFilePath + petBrand.getQrCode()));
                    list.add(petBrand.getBrandNum() + ".png");
                });
                String url = fatherPath + "/" + res.getUserRealName() + ".zip";
                File zipFile = new File(url);
                ZipMultiFileUtil.zipFiles(fileList.stream().toArray(File[]::new), list.stream().toArray(String[]::new),zipFile);
            });

            //将项目名称的文件夹 压缩为zip
            String fileDir ="";
            ZipMultiFileUtil.fileToZip(fatherPath, fileDir, fatherPath + ".zip");
            ZipMultiFileUtil.downloadPathFile(fatherPath1, request, response);
        }



    }
}
