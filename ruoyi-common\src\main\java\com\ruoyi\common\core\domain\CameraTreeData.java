package com.ruoyi.common.core.domain;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 创建时间：2020/12/8 16:30
 */
public class CameraTreeData implements Serializable {


    private static final long serialVersionUID = -6975206981133656685L;

    private Long id;
    /**
     * 节点编码
     */
    private String nodeKey;
    /**
     * 节点名称
     */
    private String name;
    /**
     * 父级节点编码
     */
    private String parentNodeKey;
    /**
     * 类型 1-区域  2-监控点
     */
    private Integer type;

    /**
     * 状态：
     */
    private Integer status;

    List<CameraTreeData> children;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNodeKey() {
        return nodeKey;
    }

    public void setNodeKey(String nodeKey) {
        this.nodeKey = nodeKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentNodeKey() {
        return parentNodeKey;
    }

    public void setParentNodeKey(String parentNodeKey) {
        this.parentNodeKey = parentNodeKey;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<CameraTreeData> getChildren() {
        return children;
    }

    public void setChildren(List<CameraTreeData> children) {
        this.children = children;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
