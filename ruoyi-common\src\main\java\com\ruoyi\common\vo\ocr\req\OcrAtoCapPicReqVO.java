package com.ruoyi.common.vo.ocr.req;

import lombok.Data;

@Data
public class OcrAtoCapPicReqVO {

    /**
     * 图像二进制数据的base64编码
     */
    private String img;

    /**
     * 是否需要置信度
     */
    private String prob = "0";

    /**
     * 是否需要单字输出
     */
    private String charInfo = "false";

    /**
     * 是否需要自动旋转功能
     */
    private String rotate = "false";

    /**
     * 是否需要表格功能
     */
    private String table = "false";

    /**
     * 是否需要分页功能
     */
    private String page = "false";

    /**
     * 是否需要分段功能
     */
    private String paragraph = "false";

    /**
     * 是否需要分行功能
     */
    private String row = "false";

    /**
     * 是否需要去除边界(对于包含多页的图片，去除边界的页内容)
     */
    private String removeBoundary = "false";

    /**
     * 是否去印章
     */
    private String noStamp = "false";

    /**
     * 版面格式相关信息，目前包含标题提取
     */
    private String layout = "false";

    /**
     * 是否需要图案（指纹和印章）坐标输出
     */
    private String figure = "false";

    /**
     * 自动识别手写体和印刷体
     */
    private String recClassifyhw = "false";

    /**
     * 自动识别手写体、印刷体、公式、多语言，recClassify=true&type=question_ocr(题目识别)时可区分普通文本与公式，返回结果中recClassify=0表示普通文本，recClassify=51表示公式
     */
    private String recClassify = "false";

    /**
     * 要识别的图片类型，括号内表示需要传入的参数值，包括文档识别（advanced）、纯英文识别（eng）、电商图片识别（basic）、题目识别（question_ocr）、手写体识别（shouxie）、zgj识别（supreme_procuratorate）。
     */
    private String type = "advanced";

    /**
     * （1）要识别的字体类型，括号内表示需要传入的参数值，要识别的语言类型，type需为advanced，手写体（sx）、印刷体（chn）、手写体和印刷体（chn,sx），结合recClassfy参数使用；（2）要识别的语言类型，type需为advanced，目前支持的语言类型有("eng", "英文")、("ja", "日语")、("rus", "俄语")、("tai", "泰语")、("kor", "韩语")、("lading", "拉丁语")
     */
    private String language = "sx";

    /**
     * 固定值
     */
    private String method = "ocrService";
}
