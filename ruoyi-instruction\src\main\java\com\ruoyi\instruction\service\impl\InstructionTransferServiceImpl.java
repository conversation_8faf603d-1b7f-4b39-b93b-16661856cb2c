package com.ruoyi.instruction.service.impl;

import com.ruoyi.instruction.domain.InstructionTransfer;
import com.ruoyi.instruction.mapper.InstructionTransferMapper;
import com.ruoyi.instruction.service.IInstructionTransferService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 指令转接Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
@Service
public class InstructionTransferServiceImpl implements IInstructionTransferService 
{
    @Autowired
    private InstructionTransferMapper instructionTransferMapper;

    /**
     * 查询指令转接
     * 
     * @param id 指令转接主键
     * @return 指令转接
     */
    @Override
    public InstructionTransfer selectInstructionTransferById(Long id)
    {
        return instructionTransferMapper.selectInstructionTransferById(id);
    }

    /**
     * 查询指令转接列表
     * 
     * @param instructionTransfer 指令转接
     * @return 指令转接
     */
    @Override
    public List<InstructionTransfer> selectInstructionTransferList(InstructionTransfer instructionTransfer)
    {
        return instructionTransferMapper.selectInstructionTransferList(instructionTransfer);
    }

    /**
     * 新增指令转接
     * 
     * @param instructionTransfer 指令转接
     * @return 结果
     */
    @Override
    public int insertInstructionTransfer(InstructionTransfer instructionTransfer)
    {
        return instructionTransferMapper.insertInstructionTransfer(instructionTransfer);
    }

    /**
     * 修改指令转接
     * 
     * @param instructionTransfer 指令转接
     * @return 结果
     */
    @Override
    public int updateInstructionTransfer(InstructionTransfer instructionTransfer)
    {
        return instructionTransferMapper.updateInstructionTransfer(instructionTransfer);
    }

    /**
     * 批量删除指令转接
     * 
     * @param ids 需要删除的指令转接主键
     * @return 结果
     */
    @Override
    public int deleteInstructionTransferByIds(Long[] ids)
    {
        return instructionTransferMapper.deleteInstructionTransferByIds(ids);
    }

    /**
     * 删除指令转接信息
     * 
     * @param id 指令转接主键
     * @return 结果
     */
    @Override
    public int deleteInstructionTransferById(Long id)
    {
        return instructionTransferMapper.deleteInstructionTransferById(id);
    }
}
