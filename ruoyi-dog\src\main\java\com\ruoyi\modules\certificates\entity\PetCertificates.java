package com.ruoyi.modules.certificates.entity;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.immune.entity.ImmuneCancel;
import com.ruoyi.modules.immune.entity.ImmuneRegister;
import com.ruoyi.modules.immune.entity.ImmuneTransfer;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.entity.SysUser;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PetCertificates extends BaseEntity {

    private String petIdCard;           //饲主身份证号
    private String petNum;              //犬牌编号
    private String petName;             //宠物名
    private String petType;             //宠物类别
    private Integer petSex;             //宠物性别
    private String petVarieties;        //品种：对应字典表(varieties_type)
    private String petVarietiesOne;        //品种（大类）
    private String petHair;             //毛色(对应字典表hair_type)
    private String petAge;              //年龄
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date raiseDate;              //饲养日期
    private String petAddress;           //详细地址
    private String petDept;              //所在地区
    private String street;               //所在地区街道 （sys_dept表关联）
    private String petDeptName;          //所在地名称 （sys_dept表关联）
    private Integer isAgency;            //是否代办：1是，2否
    private String agencyCom;            //代办单位
    private Integer source;              //犬只登记来源:1个人用户登记,2医院登记
    private Integer isReissue;           //犬牌补办：1正常，2已申请，3已通过，4未通过
    private Integer yearStatus;          //年审状态： 1待办理，2已办理
    private String cancelReason;         //申请原因
    private Integer status;              //审批状态：1待审批，2已通过，3已注销，4走失注销
    private String immuneId;             //免疫信息最新ID
    private String transferId;           //过户信息最新ID
    private String cancelId;             //注销信息最新ID
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date endDate;                //疫苗到期日期
    private String tel;                  //饲主联系电话
    private String ownerName;            //饲主姓名
    private String ownerAddress;         //饲主户籍地址
    private String expresType;            //快递方式
    private String expresRegion;        //快递地址（所在地区）所在地区
    private String expresAddress;         //快递地址
    private String zfckId; //执法窗口ID
    private String hospitalId;            //预约医院
    private String aboutMake;             //预约方式
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date aboutDate;             //预约时间
    private String petCount;              //当前人是否存在犬牌
    private String petImg;      //犬只照片
    private String hospital;   //医院名称
    private String activation;    //是否激活 1：未激活 2 ：已激活
    private String receiveStatus; //客户是否领取
    private String aboutStatus;// 预约状态 1：正常 2：预约中 3：预约失败 4：取消预约
    private String aboutReason;// 预约反馈
    private String aboutHospital;//预约医院
    private List<SysUploadFile> uploadFiles; //返显页面上的犬只图片
    private String uploadFilesStr;         //从页面传到的图片值
    private String qrCode;         //犬证二维码
    private Integer tricolor;         //三色状态 1：绿色 2：黄色 3：红色
    private String certId;         //办证ID
    private Date certDate;         //办证时间
    private Integer applyStatus;         //犬牌申请状态 1：保存 2：待审核 3：审核通过 4 审核未通过
    private String applyReason;         //执法度审核原因

    private String immuneStr;//从页面传到的免疫信息
    private ImmuneRegister immuneRegister; ////返显页面上的免疫信息

    private List<ImmuneRegister> immuneRegisterLsit = new ArrayList<>();  //免疫信息集合
    private List<ImmuneTransfer> immuneTransferList = new ArrayList<>();  //过户信息集合
    private List<ImmuneCancel> immuneCancelList = new ArrayList<>();  //注销信息集合
    private ImmuneTransfer immuneTransfer; //过户信息
    private ImmuneCancel immuneCancel; //注销信息
    private SysUser sysUser;  //饲主信息
    private String hukouNumber;  //户口本户号
    private String captcha;  //验证码
    private String otherVarieties; //其他品种

    private String immuneStatus;
    private String transferStatus;
    private String cancelStatus;
    private Integer isReissueFlag;
    private String handleStatus;
    private String petNumRate;//精确查询
    private String telRate;//精确查询
    private String activaType;//激活页面查询审批通过

    private String oldPetNum;//犬只补办旧犬牌

    private Integer type;
    private String keywordOne;//关键字查询

    private String dealPersonId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private Integer delFlag;

    /**
     * 查询类型 1: 登记过户(已过户)  2: 登记过户(未过户)  3: 犬牌补办已审核
     */
    private Integer searchType;

    /** 用于数据权限，存储管辖范围内的部门ID列表 */
    private List<Long> deptIds;

    public String getKeywordOne() {
        return keywordOne;
    }

    public void setKeywordOne(String keywordOne) {
        this.keywordOne = keywordOne;
    }
    /**/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date registerBegin;//注射日期-查询区间
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date registerEnd;//注射日期-查询区间
    private String hospitalName;//注册医院名称
    private String keyword;//关键字查询
    private String socialCreditCode;//统一信用代码

    private String useDescription;//养犬用途说明
    /**
     * 1.发放，2补办，3过户
     */
    private Integer examineType;

    private List<String> petIds;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getReceiveStatus() {
        return receiveStatus;
    }

    public void setReceiveStatus(String receiveStatus) {
        this.receiveStatus = receiveStatus;
    }

    public String getExpresRegion() {
        return expresRegion;
    }

    public void setExpresRegion(String expresRegion) {
        this.expresRegion = expresRegion;
    }

    public String getPetDeptName() {
        return petDeptName;
    }

    public void setPetDeptName(String petDeptName) {
        this.petDeptName = petDeptName;
    }

    public String getAboutStatus() {
        return aboutStatus;
    }

    public void setAboutStatus(String aboutStatus) {
        this.aboutStatus = aboutStatus;
    }

    public String getAboutReason() {
        return aboutReason;
    }

    public void setAboutReason(String aboutReason) {
        this.aboutReason = aboutReason;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public Date getRegisterBegin() {
        return registerBegin;
    }

    public void setRegisterBegin(Date registerBegin) {
        this.registerBegin = registerBegin;
    }

    public Date getRegisterEnd() {
        return registerEnd;
    }

    public void setRegisterEnd(Date registerEnd) {
        this.registerEnd = registerEnd;
    }

    public String getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(String hospitalId) {
        this.hospitalId = hospitalId;
    }

    public String getPetIdCard() {
        return petIdCard;
    }

    public void setPetIdCard(String petIdCard) {
        this.petIdCard = petIdCard;
    }

    public String getPetNum() {
        return petNum;
    }

    public void setPetNum(String petNum) {
        this.petNum = petNum;
    }

    public String getPetName() {
        return petName;
    }

    public void setPetName(String petName) {
        this.petName = petName;
    }

    public String getPetType() {
        return petType;
    }

    public void setPetType(String petType) {
        this.petType = petType;
    }

    public Integer getPetSex() {
        return petSex;
    }

    public void setPetSex(Integer petSex) {
        this.petSex = petSex;
    }

    public String getPetVarieties() {
        return petVarieties;
    }

    public void setPetVarieties(String petVarieties) {
        this.petVarieties = petVarieties;
    }

    public String getPetHair() {
        return petHair;
    }

    public void setPetHair(String petHair) {
        this.petHair = petHair;
    }

    public String getPetAge() {
        return petAge;
    }

    public void setPetAge(String petAge) {
        this.petAge = petAge;
    }

    public Date getRaiseDate() {
        return raiseDate;
    }

    public void setRaiseDate(Date raiseDate) {
        this.raiseDate = raiseDate;
    }

    public String getPetAddress() {
        return petAddress;
    }

    public void setPetAddress(String petAddress) {
        this.petAddress = petAddress;
    }

    public String getPetDept() {
        return petDept;
    }

    public void setPetDept(String petDept) {
        this.petDept = petDept;
    }

    public Integer getIsAgency() {
        return isAgency;
    }

    public void setIsAgency(Integer isAgency) {
        this.isAgency = isAgency;
    }

    public String getAgencyCom() {
        return agencyCom;
    }

    public void setAgencyCom(String agencyCom) {
        this.agencyCom = agencyCom;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getIsReissue() {
        return isReissue;
    }

    public void setIsReissue(Integer isReissue) {
        this.isReissue = isReissue;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getImmuneId() {
        return immuneId;
    }

    public void setImmuneId(String immuneId) {
        this.immuneId = immuneId;
    }

    public String getTransferId() {
        return transferId;
    }

    public void setTransferId(String transferId) {
        this.transferId = transferId;
    }

    public String getCancelId() {
        return cancelId;
    }

    public void setCancelId(String cancelId) {
        this.cancelId = cancelId;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerAddress() {
        return ownerAddress;
    }

    public void setOwnerAddress(String ownerAddress) {
        this.ownerAddress = ownerAddress;
    }

    public List<SysUploadFile> getUploadFiles() {
        return uploadFiles;
    }

    public void setUploadFiles(List<SysUploadFile> uploadFiles) {
        this.uploadFiles = uploadFiles;
    }

    public String getUploadFilesStr() {
        return uploadFilesStr;
    }

    public void setUploadFilesStr(String uploadFilesStr) {
        this.uploadFilesStr = uploadFilesStr;
    }

    public String getImmuneStr() {
        return immuneStr;
    }

    public void setImmuneStr(String immuneStr) {
        this.immuneStr = immuneStr;
    }

    public ImmuneRegister getImmuneRegister() {
        return immuneRegister;
    }

    public void setImmuneRegister(ImmuneRegister immuneRegister) {
        this.immuneRegister = immuneRegister;
    }

    public List<ImmuneRegister> getImmuneRegisterLsit() {
        return immuneRegisterLsit;
    }

    public void setImmuneRegisterLsit(List<ImmuneRegister> immuneRegisterLsit) {
        this.immuneRegisterLsit = immuneRegisterLsit;
    }

    public List<ImmuneTransfer> getImmuneTransferList() {
        return immuneTransferList;
    }

    public void setImmuneTransferList(List<ImmuneTransfer> immuneTransferList) {
        this.immuneTransferList = immuneTransferList;
    }

    public List<ImmuneCancel> getImmuneCancelList() {
        return immuneCancelList;
    }

    public void setImmuneCancelList(List<ImmuneCancel> immuneCancelList) {
        this.immuneCancelList = immuneCancelList;
    }

    public ImmuneTransfer getImmuneTransfer() {
        return immuneTransfer;
    }

    public void setImmuneTransfer(ImmuneTransfer immuneTransfer) {
        this.immuneTransfer = immuneTransfer;
    }

    public ImmuneCancel getImmuneCancel() {
        return immuneCancel;
    }

    public void setImmuneCancel(ImmuneCancel immuneCancel) {
        this.immuneCancel = immuneCancel;
    }

    public SysUser getSysUser() {
        return sysUser;
    }

    public void setSysUser(SysUser sysUser) {
        this.sysUser = sysUser;
    }

    public String getImmuneStatus() {
        return immuneStatus;
    }

    public void setImmuneStatus(String immuneStatus) {
        this.immuneStatus = immuneStatus;
    }

    public String getTransferStatus() {
        return transferStatus;
    }

    public void setTransferStatus(String transferStatus) {
        this.transferStatus = transferStatus;
    }

    public String getCancelStatus() {
        return cancelStatus;
    }

    public void setCancelStatus(String cancelStatus) {
        this.cancelStatus = cancelStatus;
    }

    public String getAboutMake() {
        return aboutMake;
    }

    public void setAboutMake(String aboutMake) {
        this.aboutMake = aboutMake;
    }

    public String getExpresType() {
        return expresType;
    }

    public void setExpresType(String expresType) {
        this.expresType = expresType;
    }

    public String getExpresAddress() {
        return expresAddress;
    }

    public void setExpresAddress(String expresAddress) {
        this.expresAddress = expresAddress;
    }

    public Date getAboutDate() {
        return aboutDate;
    }

    public void setAboutDate(Date aboutDate) {
        this.aboutDate = aboutDate;
    }

    public String getPetCount() {
        return petCount;
    }

    public void setPetCount(String petCount) {
        this.petCount = petCount;
    }

    public String getPetImg() {
        return petImg;
    }

    public void setPetImg(String petImg) {
        this.petImg = petImg;
    }

    public String getHospital() {
        return hospital;
    }

    public void setHospital(String hospital) {
        this.hospital = hospital;
    }

    public Integer getIsReissueFlag() {
        return isReissueFlag;
    }

    public void setIsReissueFlag(Integer isReissueFlag) {
        this.isReissueFlag = isReissueFlag;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getYearStatus() {
        return yearStatus;
    }

    public void setYearStatus(Integer yearStatus) {
        this.yearStatus = yearStatus;
    }

    public String getActivation() {
        return activation;
    }

    public void setActivation(String activation) {
        this.activation = activation;
    }

    public String getHandleStatus() {
        return handleStatus;
    }

    public void setHandleStatus(String handleStatus) {
        this.handleStatus = handleStatus;
    }

    public String getAboutHospital() {
        return aboutHospital;
    }

    public void setAboutHospital(String aboutHospital) {
        this.aboutHospital = aboutHospital;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public String getPetVarietiesOne() {
        return petVarietiesOne;
    }

    public void setPetVarietiesOne(String petVarietiesOne) {
        this.petVarietiesOne = petVarietiesOne;
    }

    public String getPetNumRate() {
        return petNumRate;
    }

    public void setPetNumRate(String petNumRate) {
        this.petNumRate = petNumRate;
    }

    public String getTelRate() {
        return telRate;
    }

    public void setTelRate(String telRate) {
        this.telRate = telRate;
    }

    public Integer getTricolor() {
        return tricolor;
    }

    public void setTricolor(Integer tricolor) {
        this.tricolor = tricolor;
    }

    public String getCertId() {
        return certId;
    }

    public void setCertId(String certId) {
        this.certId = certId;
    }

    public Date getCertDate() {
        return certDate;
    }

    public void setCertDate(Date certDate) {
        this.certDate = certDate;
    }

    public Integer getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(Integer applyStatus) {
        this.applyStatus = applyStatus;
    }

    public String getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getActivaType() {
        return activaType;
    }

    public void setActivaType(String activaType) {
        this.activaType = activaType;
    }

    public String getZfckId() {
        return zfckId;
    }

    public void setZfckId(String zfckId) {
        this.zfckId = zfckId;
    }

    public String getOldPetNum() {
        return oldPetNum;
    }

    public void setOldPetNum(String oldPetNum) {
        this.oldPetNum = oldPetNum;
    }

    public String getSocialCreditCode() {
        return socialCreditCode;
    }

    public void setSocialCreditCode(final String socialCreditCode) {
        this.socialCreditCode = socialCreditCode;
    }

    public String getUseDescription() {
        return useDescription;
    }

    public void setUseDescription(final String useDescription) {
        this.useDescription = useDescription;
    }

    public String getHukouNumber() {
        return hukouNumber;
    }

    public void setHukouNumber(String hukouNumber) {
        this.hukouNumber = hukouNumber;
    }

    public String getCaptcha() {
        return captcha;
    }

    public void setCaptcha(String captcha) {
        this.captcha = captcha;
    }

    public String getOtherVarieties() {
        return otherVarieties;
    }

    public void setOtherVarieties(String otherVarieties) {
        this.otherVarieties = otherVarieties;
    }

    public String getDealPersonId() {
        return dealPersonId;
    }

    public void setDealPersonId(final String dealPersonId) {
        this.dealPersonId = dealPersonId;
    }

    public Integer getExamineType() {
        return examineType;
    }

    public void setExamineType(Integer examineType) {
        this.examineType = examineType;
    }

    @Override
    public Integer getDelFlag() {
        return delFlag;
    }

    @Override
    public void setDelFlag(final Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getSearchType() {
        return searchType;
    }

    public void setSearchType(final Integer searchType) {
        this.searchType = searchType;
    }

    public List<String> getPetIds() {
        return petIds;
    }

    public void setPetIds(final List<String> petIds) {
        this.petIds = petIds;
    }

    public List<Long> getDeptIds() {
        return deptIds;
    }

    public void setDeptIds(List<Long> deptIds) {
        this.deptIds = deptIds;
    }
}
