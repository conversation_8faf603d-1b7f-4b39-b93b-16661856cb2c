package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.JudementInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 分析研判Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-04
 */
@DataSource(value = DataSourceType.SLAVE)
public interface JudementInfoMapper 
{
    /**
     * 查询分析研判
     * 
     * @param id 分析研判主键
     * @return 分析研判
     */
    public JudementInfo selectJudementInfoById(Long id);

    /**
     * 查询分析研判列表
     * 
     * @param judementInfo 分析研判
     * @return 分析研判集合
     */
    public List<JudementInfo> selectJudementInfoList(JudementInfo judementInfo);

    /**
     * 新增分析研判
     * 
     * @param judementInfo 分析研判
     * @return 结果
     */
    public int insertJudementInfo(JudementInfo judementInfo);

    /**
     * 修改分析研判
     * 
     * @param judementInfo 分析研判
     * @return 结果
     */
    public int updateJudementInfo(JudementInfo judementInfo);

    /**
     * 删除分析研判
     * 
     * @param id 分析研判主键
     * @return 结果
     */
    public int deleteJudementInfoById(Long id);

    /**
     * 批量删除分析研判
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJudementInfoByIds(Long[] ids);

    /**
     * 获取待研判数、已研判数
     * @return
     */
    Map<String, Object> getData();

    /**
     * 根据备注查询最后一条记录
     * @param remark
     * @return
     */
    JudementInfo selectByRemark(@Param("remark") String remark);
}
