package com.ruoyi.modules.pushMessageDept.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.user.entity.SysUploadFile;

import java.util.Date;
import java.util.List;

/**
 * 工作通知关联机构(push_message_dept)
 *
 * <AUTHOR>
 * @since 2022-08-12 14:40:02
 */
public class PushMessageDept extends BaseEntity {

    private String messageId;//关联消息主题id
    private String detpId;//消息接机构ID
    private String status;//'消息状态:1未读 2已读'
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private String readTime;//查看时间

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getDetpId() {
        return detpId;
    }

    public void setDetpId(String detpId) {
        this.detpId = detpId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReadTime() {
        return readTime;
    }

    public void setReadTime(String readTime) {
        this.readTime = readTime;
    }
}
