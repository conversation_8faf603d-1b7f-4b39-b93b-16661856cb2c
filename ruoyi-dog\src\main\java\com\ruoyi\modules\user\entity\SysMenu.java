package com.ruoyi.modules.user.entity;


import com.ruoyi.base.entity.BaseEntity;

import java.util.List;

/**
 * Created by Administrator on 2021-3-13.
 * 系统菜单对象
 */
public class SysMenu extends BaseEntity {

    private String systemFlag ; // 所属系统配置的服务名 示例：service-user
    private String parentId ;   // 父级ID
    private String menuName ;   // 菜单名称
    private Integer menuOrder ;   // 菜单层级 1-一级菜单 2-二级菜单
    private Integer showFlag ;   // 是否显示 1-显示 2-不显示
    private Integer sort ;      // 菜单排序
    private String icon ;       // 菜单图标
    private String path ;       // 菜单路径
    private String component;  // 菜单组件

    private List<SysMenu> sonList ;  // 子菜单

    private String userId ;         // 用户ID  查询使用

    public String getSystemFlag() {
        return systemFlag;
    }

    public void setSystemFlag(String systemFlag) {
        this.systemFlag = systemFlag;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public Integer getMenuOrder() {
        return menuOrder;
    }

    public void setMenuOrder(Integer menuOrder) {
        this.menuOrder = menuOrder;
    }

    public Integer getShowFlag() {
        return showFlag;
    }

    public void setShowFlag(Integer showFlag) {
        this.showFlag = showFlag;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public List<SysMenu> getSonList() {
        return sonList;
    }

    public void setSonList(List<SysMenu> sonList) {
        this.sonList = sonList;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
