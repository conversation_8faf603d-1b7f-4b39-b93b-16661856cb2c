package com.ruoyi.modules.harmTrea.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.user.entity.SysUploadFile;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * 无害化处理记录表(HarmTrea)实体类
 *
 * <AUTHOR>
 * @since 2022-12-13 10:10:31
 */
@Data
public class HarmTrea extends BaseEntity {

    /**
    * id
    */
    private String id;
    /**
    * 联系电话
    */
    private String phone;
    /**
    * 联系人
    */
    private String contacts;
    /**
    * 留言内容
    */
    private String content;
    /**
    * 状态 1：待审核 2：已通过 3 ：未通过
    */
    private Integer status;
    /**
    * 数据状态：1正常，2 删除
    */
    private Integer delFlag;
    /**
    * 创建时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    /**
    * 创建人
    */
    private String createBy;
    /**
    * 修改时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
    /**
    * 修改人
    */
    private String updateBy;
    /**
     * 审批原因
     */
    private String reason;

    /**
     * 执行人
     */
    private String handlePerson;

    /**
     * 执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date handleTime;

    /**
     * 执行方式 1-焚烧法 2-化制法 3-掩埋法 4-其他
     */
    private Integer handleType;

    /**
     * 所在县
     */
    private String county;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 地区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 执行状态 1-待执行 2-已执行 3-其他
     */
    private Integer handleStatus;

    /**
     * 收容id
     */
    private String takeInId;

    /**
     * 附件
     */
    private List<SysUploadFile> uploadFileList;

}
