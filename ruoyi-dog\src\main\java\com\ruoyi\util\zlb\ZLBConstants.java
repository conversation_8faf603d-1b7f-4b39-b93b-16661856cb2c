package com.ruoyi.util.zlb;


public interface ZLBConstants {

    /**
     * wx单点登录 ticketId换token的地址
     */
    String ACCESS_TOKEN_URL = "https://ibcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220329000007/uc/sso/access_token";
    /**
     * wx单点登录 token获取用户信息地址
     */
    String GET_USER_INFO_URL = "https://ibcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220329000008/uc/sso/getUserInfo";

    /**
     * zlb单点登录 ticketId换token的地址
     */
    String ZLB_TICKET_VALIDATION_URL = "https://ibcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220228000002/sso/servlet/simpleauth";

    /**
     * zlb单点登录 token获取用户信息地址
     */
    String ZLB_GET_USER_INFO_URL =  "https://ibcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220228000004/sso/servlet/simpleauth";

    /**
     * IRS请求携带的请求头
     */
    String X_BG_HMAC_ACCESS_KEY = "X-BG-HMAC-ACCESS-KEY";
    String X_BG_HMAC_SIGNATURE = "X-BG-HMAC-SIGNATURE";
    String X_BG_HMAC_ALGORITHM = "X-BG-HMAC-ALGORITHM";
    String X_BG_DATE_TIME = "X-BG-DATE-TIME";

    /**
     * IRS签名算法
     */
    String DEFAULT_HMAC_SIGNATURE = "hmac-sha256";

    /**
     * 应用ID
     */
    String APP_ID = "2001809011";
    /**
     * 微信端固定值为weixin
     */
    String WEIXIN_ENDPOINT_TYPE = "weixin";


    /**
     * IRS 申请组件生成的AK
     */
    String IRS_AK = "BCDSGA_1d646b220377dc4731cc8ab867df2c5c";
    /**
     * IRS 申请组件生成的SK
     */
    String IRS_SK = "BCDSGS_ed2856108b572802dd2804de5c9b0562";


    String TOKEN_SESSION_KEY = "sessionAccessToken";
    String USER_INFO_KEY = "sessionUserInfo";

}
