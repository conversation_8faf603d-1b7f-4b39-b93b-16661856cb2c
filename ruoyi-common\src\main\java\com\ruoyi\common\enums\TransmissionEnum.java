package com.ruoyi.common.enums;

/**
 * 角标(透传消息)
 */
public enum TransmissionEnum {

    NOTICE("notice","通知公告"),
    MARK("mark","角标"),
    MESSAGE("message" , "消息");


    private String type;
    private String desc;

    TransmissionEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
