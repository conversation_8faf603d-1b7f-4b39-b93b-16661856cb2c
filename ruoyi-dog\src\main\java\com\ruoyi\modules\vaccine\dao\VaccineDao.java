package com.ruoyi.modules.vaccine.dao;


import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.vaccine.entity.Vaccine;

import java.util.List;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 10:04
 * @version: 1.0
 **/
public interface VaccineDao extends BaseDao<Vaccine> {

    public void updateStatus(Vaccine vaccine);

    public List<Vaccine> getAllList(Vaccine vaccine);

    public List<Vaccine> queryUserVaccine(Vaccine vaccine);

    void deleteByQualifyId(String qualifyId);

    List<String> getDistinctList();

}
