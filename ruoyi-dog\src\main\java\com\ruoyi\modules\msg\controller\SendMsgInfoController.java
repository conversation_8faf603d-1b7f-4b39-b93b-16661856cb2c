package com.ruoyi.modules.msg.controller;

import java.util.List;

import com.ruoyi.modules.msg.domain.SendMsgInfo;
import com.ruoyi.modules.msg.service.ISendMsgInfoService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 短信发送记录Controller
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
@RestController
@RequestMapping("/msg/sendMsgInfo")
public class SendMsgInfoController extends BaseController
{
    @Autowired
    private ISendMsgInfoService sendMsgInfoService;

//    /**
//     * 查询短信发送记录列表
//     */
////    @PreAuthorize("@ss.hasPermi('msg:sendMsgInfo:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(SendMsgInfo sendMsgInfo)
//    {
//        startPage();
//        List<SendMsgInfo> list = sendMsgInfoService.selectSendMsgInfoList(sendMsgInfo);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出短信发送记录列表
//     */
//    @PreAuthorize("@ss.hasPermi('msg:sendMsgInfo:export')")
//    @Log(title = "短信发送记录", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SendMsgInfo sendMsgInfo)
//    {
//        List<SendMsgInfo> list = sendMsgInfoService.selectSendMsgInfoList(sendMsgInfo);
//        ExcelUtil<SendMsgInfo> util = new ExcelUtil<SendMsgInfo>(SendMsgInfo.class);
//        util.exportExcel(response, list, "短信发送记录数据");
//    }
//
//    /**
//     * 获取短信发送记录详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('msg:sendMsgInfo:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return success(sendMsgInfoService.selectSendMsgInfoById(id));
//    }
//
//    /**
//     * 新增短信发送记录
//     */
//    @PreAuthorize("@ss.hasPermi('msg:sendMsgInfo:add')")
//    @Log(title = "短信发送记录", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody SendMsgInfo sendMsgInfo)
//    {
//        return toAjax(sendMsgInfoService.insertSendMsgInfo(sendMsgInfo));
//    }
//
//    /**
//     * 修改短信发送记录
//     */
//    @PreAuthorize("@ss.hasPermi('msg:sendMsgInfo:edit')")
//    @Log(title = "短信发送记录", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody SendMsgInfo sendMsgInfo)
//    {
//        return toAjax(sendMsgInfoService.updateSendMsgInfo(sendMsgInfo));
//    }
//
//    /**
//     * 删除短信发送记录
//     */
//    @PreAuthorize("@ss.hasPermi('msg:sendMsgInfo:remove')")
//    @Log(title = "短信发送记录", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(sendMsgInfoService.deleteSendMsgInfoByIds(ids));
//    }

    /**
     * 发送短信
     */
    @PostMapping("sendMsg")
    public AjaxResult sendMsg(@RequestBody SendMsgInfo sendMsgInfo) {
        return toAjax(sendMsgInfoService.sendMsg(sendMsgInfo));
    }
}
