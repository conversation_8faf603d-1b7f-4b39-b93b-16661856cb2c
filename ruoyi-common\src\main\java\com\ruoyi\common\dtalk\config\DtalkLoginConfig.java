package com.ruoyi.common.dtalk.config;

import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.ruoyi.common.dtalk.properties.DtalkProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> yx-0176
 * @description
 * @date : 2021/10/18
 */
@Configuration
public class DtalkLoginConfig {

    @Autowired
    private DtalkProperties dtalkProperties;

    /**
     * 扫码登录APP
     *
     * @return
     */
    @Bean(name = "executableClientLogin")
    public ExecutableClient executableClientLogin() {
        ExecutableClient executableClient = ExecutableClient.getInstance();
        executableClient.setAccessKey(dtalkProperties.getLoginAppkey());
        executableClient.setSecretKey(dtalkProperties.getLoginAppsecret());
        executableClient.setDomainName(dtalkProperties.getDomain());
        executableClient.setProtocal(dtalkProperties.getProtocal());
        executableClient.init();
        return executableClient;
    }
    /**
     * 钉钉免登录
     *
     * @return
     */
    @Bean(name = "executableClientFreeLogin")
    public ExecutableClient executableClientFreeLogin() {
        ExecutableClient executableClient = ExecutableClient.getInstance();
        executableClient.setAccessKey(dtalkProperties.getDeptAppkey());
        executableClient.setSecretKey(dtalkProperties.getDeptAppsecret());
        executableClient.setDomainName(dtalkProperties.getDomain());
        executableClient.setProtocal(dtalkProperties.getProtocal());
        executableClient.init();
        return executableClient;
    }
    /**
     * 信息调用APP
     *
     * @return
     */
    @Bean(name = "executableClientDept")
    public ExecutableClient executableClientDept() {
        ExecutableClient executableClient = ExecutableClient.getInstance();
        executableClient.setAccessKey(dtalkProperties.getDeptAppkey());
        executableClient.setSecretKey(dtalkProperties.getDeptAppsecret());
        executableClient.setDomainName(dtalkProperties.getDomain());
        executableClient.setProtocal(dtalkProperties.getProtocal());
        executableClient.init();
        return executableClient;
    }

}
