package com.ruoyi.modules.notice.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.notice.entity.Notice;
import com.ruoyi.modules.notice.service.NoticeService;
import com.ruoyi.modules.punish.entity.Punish;
import com.ruoyi.modules.punish.service.PunishService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 法律法规公告表(notice)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("notice")
public class NoticeController {
    /**
     * 服务对象
     */
    @Resource
    private NoticeService service;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("/getPageList")
    public AjaxResult getPageList(Notice entity) {
        return AjaxResult.success(service.getPageList(entity));
    }

    @RequestMapping("/getList")
    public AjaxResult getList(Notice entity) {
        return AjaxResult.success(service.getList(entity));
    }
    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(Notice entity) {
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }

    @RequestMapping("/delete")
    public AjaxResult delete(Notice entity) {
        entity.setDelFlag(2);
        service.delete(entity);
        return AjaxResult.success();
    }

    @RequestMapping("updateStatus")
    public AjaxResult updateStatus(Notice entity){
        service.updateStatus(entity);
        return AjaxResult.success();
    }
}
