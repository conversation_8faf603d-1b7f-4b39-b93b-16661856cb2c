package com.ruoyi.common.enums;

/**
 * 警情来源
 */
public enum CaseSourceEnum {

    FOUR("1", "四位一体"),
    CAPTURE("2", "监控抓拍"),
    UNION("3" , "联合执法"),
    LEADERTASK("4" , "领导交办"),
    TEMPORARY("5" , "临时任务"),
    AUTOCAPTURE("6", "智能抓拍"),
    APPEAL("7", "督查申述"),
    SUPERVISION("8", "督查考核"),
    LETTER("9", "信访"),
    SUBSTITUTE("10", "代班");

    private String type;
    private String desc;

    CaseSourceEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
