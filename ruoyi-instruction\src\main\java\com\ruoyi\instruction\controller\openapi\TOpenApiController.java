package com.ruoyi.instruction.controller.openapi;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.instruction.domain.TOpenApi;
import com.ruoyi.instruction.service.ITOpenApiService;
import com.ruoyi.system.domain.vo.UserInfoVo;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 第三方对接配置Controller
 * 
 * <AUTHOR>
 * @date 2023-07-20
 */
@RestController
@RequestMapping("/instruction/openApi")
@Slf4j
public class TOpenApiController extends BaseController
{
    @Autowired
    private ITOpenApiService tOpenApiService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private SysPermissionService permissionService;
    @Autowired
    private ISysUserService iSysUserService;


    @PostMapping("getToken")
    public  AjaxResult getToken(@RequestBody TOpenApi tOpenApi){
        if (StringUtils.isEmpty(tOpenApi.getAppKey())||StringUtils.isEmpty(tOpenApi.getAppSecret())){
            throw  new GlobalException("参数异常");
        }
        List<TOpenApi> tOpenApis = tOpenApiService.selectTOpenApiList(tOpenApi);
        if (CollectionUtils.isEmpty(tOpenApis)){
            throw  new GlobalException("异常码:100，请联系管理员");
        }
        TOpenApi tOpenApi1 = tOpenApis.get(0);
        Set<String> menuPermission=new HashSet<>();
        SysUser sysUser=new SysUser();
        sysUser.setUserId(Long.parseLong(tOpenApi1.getPermissions()));
        sysUser.setParams(new HashMap<>());
        System.out.println(sysUser==null);
        SysUser sysUsers = sysUserService.selectUserById(Long.parseLong(tOpenApi1.getPermissions()));
        menuPermission = permissionService.getMenuPermission(sysUsers);
        if (CollectionUtils.isEmpty(menuPermission)){
            return  AjaxResult.error("异常码101,请联系管理员");
        }
        sysUser.setUserName(tOpenApi1.getName());
        LoginUser loginUser = new LoginUser(null, null, sysUser, menuPermission);
        // 生成token
        String token = tokenService.createToken(loginUser);
        HashMap hashMap=new HashMap();
        hashMap.put("token",token);
        return  AjaxResult.success(hashMap);
    }

    /**
     * 获取用户信息
     * @return
     */
    @PreAuthorize("@ss.hasPermi('openApi:getUserInfo')")
    @PostMapping("getUserInfo")
    public  AjaxResult getUserInfo(){
        List<UserInfoVo> userInfo = iSysUserService.getUserInfo();
        return  AjaxResult.success(userInfo);
    }

}
