package com.ruoyi.modules.brand.entity;

import com.ruoyi.base.entity.BaseEntity;

import java.util.Date;
import java.io.Serializable;

/**
 * 申请合并处理(ApplyMerge)实体类
 *
 * <AUTHOR>
 * @since 2022-11-22 19:16:57
 */
public class ApplyMerge  extends BaseEntity {
    /**
     * 单位id
     */
    private String deptId;
    /**
     * 申请id合集
     */
    private String applyIds;
    /**
     * 1.待审核 2.拒绝 3.已提交制作(通过) 4.制作中(管理公司通过) 5.制作驳回 6.运输中
     */
    private Integer status;
    /*
    * 付款凭证
    * */
    private String paymentVoucher;
    /*合并数量*/
    private Integer mergeNum;
    /*审核意见*/
    private String remarks;
    /**
    * 合并犬牌数量
    */
    private Integer num;
/**/
   private String userRealName;//
private String courierNumber;//快递单号

    public String getCourierNumber() {
        return courierNumber;
    }

    public void setCourierNumber(String courierNumber) {
        this.courierNumber = courierNumber;
    }

    public String getUserRealName() {
        return userRealName;
    }

    public void setUserRealName(String userRealName) {
        this.userRealName = userRealName;
    }

    public String getPaymentVoucher() {
        return paymentVoucher;
    }

    public void setPaymentVoucher(String paymentVoucher) {
        this.paymentVoucher = paymentVoucher;
    }

    public Integer getMergeNum() {
        return mergeNum;
    }

    public void setMergeNum(Integer mergeNum) {
        this.mergeNum = mergeNum;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getApplyIds() {
        return applyIds;
    }

    public void setApplyIds(String applyIds) {
        this.applyIds = applyIds;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }


}

