package com.ruoyi.modules.immune.service;

import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.common.constant.RedisConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.modules.certificates.dao.PetCertificatesDao;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.certificates.service.PetCertificatesService;
import com.ruoyi.modules.immune.dao.ImmuneRegisterDao;
import com.ruoyi.modules.immune.dao.ImmuneTransferDao;
import com.ruoyi.modules.immune.entity.ImmuneRegister;
import com.ruoyi.modules.immune.entity.ImmuneTransfer;
import com.ruoyi.modules.petRecord.dao.PetRecordDao;
import com.ruoyi.modules.petRecord.entity.PetRecord;
import com.ruoyi.modules.user.dao.SysUserDao;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.service.SysUploadFileService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.impl.SysUserServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;


/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 10:52
 * @version: 1.0
 **/
@Service
public class ImmuneTransferService extends BaseService<ImmuneTransferDao, ImmuneTransfer> {

    @Autowired
    PetCertificatesDao petCertificatesDao;
    @Autowired
    PetCertificatesService petCertificatesService;
    @Autowired
    SysUploadFileService uploadFileService;
    @Autowired
    ImmuneRegisterService immuneRegisterService;
    @Autowired
    SysUserServiceImpl sysUserService;
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    PetRecordDao petRecordDao;

    /**
     * 根据信息 查询犬只过户列表
     * @param petCertificates
     * @return
     */
    public PageInfo<PetCertificates> queryPageList(PetCertificates petCertificates){
        PageHelper.startPage(petCertificates.getPageNum(),petCertificates.getPageSize());
        //数据进行过滤
        List<String> roleList = SecurityUtils.getLoginUser().getUser().getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains("admin") || roleList.contains("dogZfj") || roleList.contains("dogAdmin") || roleList.contains("enforcePerson") || roleList.contains("common")) {
            petCertificates.setPetDept(null);
        }
        PageInfo<PetCertificates> pageInfo = new PageInfo<PetCertificates>(dao.queryPageList(petCertificates));
        return pageInfo;
    }

    public AjaxResult queryPageListH5(PetCertificates petCertificates) {
        Integer userType = sysUserService.selectUserByUserName(SecurityUtils.getUsername()).getUserType();
        String key = petCertificates.getTel()+"code";
        if(!userType.equals(2)){
            PageHelper.startPage(petCertificates.getPageNum(),petCertificates.getPageSize());
            PageInfo<PetCertificates> pageInfo = new PageInfo<PetCertificates>(dao.queryPageList(petCertificates));
            return AjaxResult.success(pageInfo);
        }else {
            String code = petCertificates.getCaptcha();
            // String codeinredis = (String) redisTemplate.boundValueOps(key).get();
            String codeinredis = (String) redisCache.getCacheObject(RedisConstants.DOG + ":" + petCertificates.getTel());
            if(codeinredis == null){
                return AjaxResult.error("请重新获取验证码");
            }
            else if(codeinredis.equals(code)){
                PageHelper.startPage(petCertificates.getPageNum(),petCertificates.getPageSize());
                PageInfo<PetCertificates> pageInfo = new PageInfo<PetCertificates>(dao.queryPageList(petCertificates));
                return AjaxResult.success(pageInfo);
            }else {
                return AjaxResult.error("验证码不正确");
            }
        }
    }

    public PetCertificates getByCardId(String id, String petIdCard) {//犬只ID
        PetCertificates petCertificates = petCertificatesDao.getById(id);
        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.setInstanceId(id);
        List<SysUploadFile> upList =  uploadFileService.getList(sysUploadFile);
        petCertificates.setUploadFiles(upList);
        petCertificates.setImmuneTransfer(dao.getPetTransfer(id,petCertificates.getTransferId()));
        return petCertificates;
    }

    /**
     * 免疫过户保存
     * @param immuneTransfer
     * @return
     */
    @Transactional
    public String saveOrUpdateRest(ImmuneTransfer immuneTransfer) {
//        String petNum = dao.fingPetNum(immuneTransfer.getNewIdCard());
//        if (StringUtils.isNotEmpty(petNum)) {
//            return "2";
//        }
        super.saveOrUpdate(immuneTransfer);
        PetCertificates petCertificates = new PetCertificates();
        petCertificates.setId(immuneTransfer.getPetId());
        petCertificates.setTransferId(immuneTransfer.getId());//存入犬只过户ID
        petCertificatesService.updateImmune(petCertificates);
        return "1";
    }
    /**
     * 免疫过户保存
     * @param immuneTransfer
     * @return
     */
    @Transactional
    public AjaxResult saveOrUpdateRestH5(ImmuneTransfer immuneTransfer) {

        String key = immuneTransfer.getNewTel()+"code";
        String code = immuneTransfer.getCaptcha();
        String codeinredis = (String) redisCache.getCacheObject(RedisConstants.DOG + ":" + immuneTransfer.getNewTel());
        // String codeinredis = (String) redisTemplate.boundValueOps(key).get();

        if (immuneTransfer.getStatus() != null && !immuneTransfer.getStatus().equals("4") && !immuneTransfer.getStatus().equals("3")) {
            if (codeinredis == null) {
                return AjaxResult.error("验证码过期请重新发送");
            } else if (!codeinredis.equals(code)) {
                return AjaxResult.error("验证码不正确或验证码为空");
            }
        }
        //判断犬只是否免疫过期，如果免疫过期则提示
        if (isImmuneExpired(immuneTransfer.getPetId())) {
            return AjaxResult.warn("该犬只免疫已过期，请重新免疫");
        }
        //判断名下是否已有犬只
        Long petCount = petCertificatesDao.findPetCountByIdCard(immuneTransfer.getNewIdCard(), null);
        if (petCount > 0){
            return AjaxResult.warn("该用户名下已有犬只");
        }



        super.saveOrUpdate(immuneTransfer);
        PetCertificates petCertificates = new PetCertificates();
        petCertificates.setId(immuneTransfer.getPetId());
        petCertificates.setTransferId(immuneTransfer.getId());//存入犬只过户ID
        petCertificatesService.updateImmune(petCertificates);
        redisTemplate.delete(key);
        return AjaxResult.success("已提交过户申请");
    }

    /**
     * 判断犬只是否免疫过期
     * @param petId
     * @return
     */
    private boolean isImmuneExpired(String petId) {
        String immuneStatus = petCertificatesDao.findImmuneStatusByPetId(petId);
        return "5".equals(immuneStatus);
    }

    /**
     * 免疫过户审核
     * @param immuneTransfer
     * @return
     */
    @Transactional
    public void updateStatus(ImmuneTransfer immuneTransfer) {
        dao.updateStatus(immuneTransfer);
        if ("3".equals(immuneTransfer.getStatus())) {
            PetCertificates petCertificates = new PetCertificates();
            petCertificates.setId(immuneTransfer.getPetId());
            petCertificates.setPetIdCard(immuneTransfer.getNewIdCard());
            petCertificates.setOwnerName(immuneTransfer.getNewName());
            petCertificates.setOwnerAddress(immuneTransfer.getNewAddress());
            petCertificates.setTel(immuneTransfer.getNewTel());
            petCertificatesService.updateImmune(petCertificates);
            uploadFileService.delByInstanceAndModel(petCertificates.getId(),"property");
            uploadFileService.delByInstanceAndModel(petCertificates.getId(),"residence");
            uploadFileService.saveAllListById(immuneTransfer.getImg(),petCertificates.getId());
            immuneRegisterService.createUser(immuneTransfer.getTel(),immuneTransfer.getRealName(),immuneTransfer.getPetIdCard());
        }
        //流程记录
        String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
        PetRecord petRecord = new PetRecord();
        petRecord.preInsert();
        petRecord.setCreateName(nickName);
        petRecord.setPetId(immuneTransfer.getPetId());//犬牌ID
        petRecord.setPetIdCard(immuneTransfer.getPetIdCard());
//        petRecord.setPetNum(immuneTransfer.getPetNum());
        petRecord.setNode(8);
        if ("3".equals(immuneTransfer.getStatus())) {//审核通过
            petRecord.setStatus(2);
        } else if ("4".equals(immuneTransfer.getStatus())) {//审核不通过
            petRecord.setStatus(3);
        }
        petRecord.setRemark(immuneTransfer.getReason());//原因
        petRecord.setStrDate(JSON.toJSONString(immuneTransfer));
        petRecordDao.insert(petRecord);
    }

    /**
     * 根据信息饲主身份证，验证是否有犬类
     * @param immuneTransfer
     * @return
     */
    public String verifIdCard(ImmuneTransfer immuneTransfer) {
        String petNum = dao.fingPetNum(immuneTransfer.getNewIdCard());
        if (StringUtils.isNotEmpty(petNum)) {
            return "2";
        }
        return "1";
    }


}
