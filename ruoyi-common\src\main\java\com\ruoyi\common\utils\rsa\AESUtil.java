package com.ruoyi.common.utils.rsa;


import com.ruoyi.common.constant.Constant;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;

public class AESUtil {

    private static final Logger log = LoggerFactory.getLogger(AESUtil.class);

    public static void main(String[] args) throws Exception {

        String name = "{\"username\":\"tzj\",\"password\":\"123456\"}";
        String password = "13566784873";
        //String encodeRule = genEncodeRule();
        String encodeRule = "ivqpsFQwQqxYUr7f";
        System.out.println(encodeRule);
        String enResult1 = aesEncrypt(password, encodeRule);
        //String enResult2 = aesEncrypt(password, encodeRule);
        System.out.println("加密后：----------" + enResult1);
        //String deResult1 = aesDecrypt(enResult1, encodeRule);
        String password2 = "NXeadMN8jEYV7G8nJINJlQ==";
        String deResult2 = aesDecrypt(password2, encodeRule);
        //System.out.println("解密后：=======" + deResult1);
        System.out.println("解密后：----------" + deResult2);
        //System.out.println("解密后：======="+aesDecrypt("aCjHUJESpDb6fND8+eb2e6fnwqs/xzqSi+LDQNqFYakvRyCKP0Si6CqfpYM6ov/Ttq8s5oJtaFkMUmn2AxM7r3h9HqklIhnbO0qIhR+5yBY=","Ggpp79CN80IlynMo"));
    }

    /**
     * 获取AES编码规则
     *
     * @return
     */
    public static String genEncodeRule() {
        StringBuilder chars = new StringBuilder("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789");
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < 16; i++) {
            int num = Integer.parseInt(String.valueOf(Math.round(Math.floor(Math.random() * chars.length()))));
            result.append(chars.charAt(num));
        }
        return result.toString();
    }

    public static String base64Encode(byte[] bytes) {
        return Base64.encodeBase64String(bytes);
    }

    public static byte[] base64Decode(String base64Code) throws Exception {
        return Base64.decodeBase64(base64Code);
    }

    public static byte[] aesEncryptToBytes(String content, String encryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(128);
        Cipher cipher = Cipher.getInstance(Constant.ALGORITHMSTR);
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), "AES"));
        return cipher.doFinal(content.getBytes("utf-8"));
    }

    public static String aesEncrypt(String content, String encryptKey) throws Exception {
        return base64Encode(aesEncryptToBytes(content, encryptKey));
    }

    public static String aesDecryptByBytes(byte[] encryptBytes, String decryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(128);
        Cipher cipher = Cipher.getInstance(Constant.ALGORITHMSTR);
        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), "AES"));
        byte[] decryptBytes = cipher.doFinal(encryptBytes);
        return new String(decryptBytes);
    }

    public static String aesDecrypt(String encryptStr, String decryptKey) throws Exception {
        String decrypt = "";
        try {
            decrypt = aesDecryptByBytes(base64Decode(encryptStr), decryptKey);
        } catch (Exception e) {
             return encryptStr;
        }
        return decrypt;
    }

}
