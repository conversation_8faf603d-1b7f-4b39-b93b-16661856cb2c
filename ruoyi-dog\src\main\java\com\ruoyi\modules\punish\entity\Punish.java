package com.ruoyi.modules.punish.entity;

import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.certificates.entity.PetCertificates;

import java.util.Date;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 违法处罚表(Punish)实体类
 *
 * <AUTHOR>
 * @since 2022-08-12 14:40:02
 */
public class Punish extends BaseEntity {


    /**
     * 类型：1.犬只无牌 2.犬只有牌
     */
    private String type;
    /**
     * 处罚状态：1.待处理（饲主未处理处罚）、2.已处理（饲主已完成处罚处理）、3.已撤销
     */
    private String status;
    /**
     * 犬牌编号
     */
    private String petNum;
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 联系电话
     */
    private String contactNumber;
    /**
     * 违章原因
     */
    private String illegalReason;
    /**
     * 违章地点
     */
    private String illegalAddress;
    /**
     * 违章时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date illegalTime;
    /**
     * 处罚条例key
     */
    private String regulationsKey;
    /**
     * 罚款金额
     */
    private Double penaltyAmount;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 处理记录
     * [{userId:'',status:'',time:'',remarks:''}]
     * 按照time排序
     */
    private String processingRecord;

    /**/
    private String keyword;//关键字
    private PetCertificates pet;//犬牌信息
    private JSONArray recordList;//处罚记录
    private String createName;//处罚人名称
    private String deptName;//所属机构名称
    private String deptId;//所属区域

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public JSONArray getRecordList() {
        return recordList;
    }

    public void setRecordList(JSONArray recordList) {
        this.recordList = recordList;
    }

    public String getProcessingRecord() {
        return processingRecord;
    }

    public void setProcessingRecord(String processingRecord) {
        this.processingRecord = processingRecord;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public PetCertificates getPet() {
        return pet;
    }

    public void setPet(PetCertificates pet) {
        this.pet = pet;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPetNum() {
        return petNum;
    }

    public void setPetNum(String petNum) {
        this.petNum = petNum;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getIllegalReason() {
        return illegalReason;
    }

    public void setIllegalReason(String illegalReason) {
        this.illegalReason = illegalReason;
    }

    public String getIllegalAddress() {
        return illegalAddress;
    }

    public void setIllegalAddress(String illegalAddress) {
        this.illegalAddress = illegalAddress;
    }

    public Date getIllegalTime() {
        return illegalTime;
    }

    public void setIllegalTime(Date illegalTime) {
        this.illegalTime = illegalTime;
    }

    public String getRegulationsKey() {
        return regulationsKey;
    }

    public void setRegulationsKey(String regulationsKey) {
        this.regulationsKey = regulationsKey;
    }

    public Double getPenaltyAmount() {
        return penaltyAmount;
    }

    public void setPenaltyAmount(Double penaltyAmount) {
        this.penaltyAmount = penaltyAmount;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }


    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
}
