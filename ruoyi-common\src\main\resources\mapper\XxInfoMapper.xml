<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.yw.mapper.XxInfoMapper">
    
    <resultMap type="com.ruoyi.common.yw.domain.XxInfo" id="XxInfoResult">
        <result property="id"    column="id"    />
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="fsr"    column="fsr"    />
        <result property="sex"    column="sex"    />
        <result property="jsr"    column="jsr"    />
        <result property="bt"    column="bt"    />
        <result property="phone"    column="phone"    />
        <result property="nr"    column="nr"    />
        <result property="secret"    column="secret"    />
        <result property="cTime"    column="c_time"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="status"    column="status"    />
        <result property="source"    column="source"    />
    </resultMap>

    <sql id="selectXxInfoVo">
        select id,   fsr, jsr, bt, nr, c_time,  status, source from xx_info
    </sql>

    <select id="selectXxInfoList" parameterType="com.ruoyi.common.yw.domain.XxInfo" resultMap="XxInfoResult">
        <include refid="selectXxInfoVo"/>
        <where>  
            <if test="fsr != null  and fsr != ''"> and fsr = #{fsr}</if>
            <if test="jsr != null  and jsr != ''"> and jsr = #{jsr}</if>
            <if test="bt != null  and bt != ''"> and bt like concat('%', #{bt}, '%')</if>
            <if test="nr != null  and nr != ''"> and nr  like concat('%', #{nr}, '%')</if>
            <if test="cTime != null and cEndTime !=null ">
                and date_format(c_time,"%Y-%m-%d") between date_format(#{cTime},"%Y-%m-%d") and
                date_format(#{cEndTime},"%Y-%m-%d")
            </if>
            <if test="status != null "> and status = #{status}</if>
            <if test="source != null "> and source = #{source}</if>
        </where>
    </select>
    
    <select id="selectXxInfoById" parameterType="String" resultMap="XxInfoResult">
        <include refid="selectXxInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertXxInfo" parameterType="com.ruoyi.common.yw.domain.XxInfo">
        insert into xx_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fsr != null">fsr,</if>
            <if test="sex != null">sex,</if>
            <if test="jsr != null">jsr,</if>
            <if test="bt != null">bt,</if>
            <if test="nr != null">nr,</if>
            <if test="cTime != null">c_time,</if>
            <if test="status != null">status,</if>
            <if test="source != null">source,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fsr != null">#{fsr},</if>
            <if test="sex != null">#{sex},</if>
            <if test="jsr != null">#{jsr},</if>
            <if test="bt != null">#{bt},</if>
            <if test="nr != null">#{nr},</if>
            <if test="cTime != null">#{cTime},</if>
            <if test="status != null">#{status},</if>
            <if test="source != null">#{source},</if>
         </trim>
    </insert>

    <update id="updateXxInfo" parameterType="com.ruoyi.common.yw.domain.XxInfo">
        update xx_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="fsr != null">fsr = #{fsr},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="jsr != null">jsr = #{jsr},</if>
            <if test="bt != null">bt = #{bt},</if>
            <if test="nr != null">nr = #{nr},</if>
            <if test="cTime != null">c_time = #{cTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="source != null">source = #{source},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteXxInfoById" parameterType="String">
        delete from xx_info where id = #{id}
    </delete>

    <delete id="deleteXxInfoByIds" parameterType="String">
        delete from xx_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>