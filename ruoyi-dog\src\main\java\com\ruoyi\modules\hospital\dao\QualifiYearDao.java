package com.ruoyi.modules.hospital.dao;


import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.hospital.entity.QualifiYear;
import com.ruoyi.modules.user.entity.SysUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 10:04
 * @version: 1.0
 **/
@Repository
public interface QualifiYearDao extends BaseDao<QualifiYear> {

    public void updateStatus(QualifiYear qualifi);

    public void updateAccount(QualifiYear qualifi);

    public QualifiYear getQualifiByAccount(QualifiYear qualifi);

    public List<QualifiYear> getAllList(QualifiYear qualifi);

    public String verifiUnitCode(@Param("id") String id , @Param("unifiedCode") String unifiedCode, @Param("tel") String tel);


    public List<SysUser> getApply(SysUser sysUser);


    /*复制医院资质信息到历史表*/
    public void copyQualifi(String id);

    public void deleteReduct(String id);

    /*历史表还原资质信息到医院资质表*/
    public void reductQualifi(String id);

    List<QualifiYear> getAlarmList(QualifiYear entity);

    QualifiYear checkExist(@Param("id") String id, @Param("year") Integer year);
}
