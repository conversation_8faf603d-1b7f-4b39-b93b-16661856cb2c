package com.ruoyi.modules.user.service;

import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.user.dao.SysDeptDao;
import com.ruoyi.modules.user.dao.SysUserDao;
import com.ruoyi.modules.user.entity.SysDept;
import com.ruoyi.modules.user.entity.SysUser;
import com.ruoyi.util.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @classname:
 * @description:
 * @author: 侯旭
 * @date: 2021/4/7 14:42
 * @version: 1.0
 **/
@Service
public class SysDeptService extends BaseService<SysDeptDao, SysDept> {

    @Autowired
    private SysUserDao sysUserDao;

    @Autowired
    private SysDeptDao deptDao;
    /**
     * 获取当前用户权限内的乡镇村部门数据
     * @return
     */
//    public List<SysDept> getRightSysDept(){
//        // 当前用户数据
//        SysUser user = ThreadLocalUtil.getCurrentUser();
//        SysDept userDept = dao.getById(user.getDeptId());
//        if(userDept.getIsMainFlag() != null && userDept.getIsMainFlag().intValue() == 1){
//            // 区信访部门统计：显示所有村相关数据  所有村数据
//            SysDept selectDept = new SysDept();
//            selectDept.setIsMainFlag(2);
//            List<SysDept> allList = dao.getList(selectDept);
//            // 所有菜单按照父级别进行分类
//            List<SysDept> resultList = new ArrayList<>();
//            Map<String,List<SysDept>> pidDeptMap = new HashMap<>();
//            for(SysDept dept : allList){
//                if(dept.getLevel() != null && dept.getLevel().intValue() == 2){
//                    // 一级菜单存入list中
//                    resultList.add(dept);
//                }else{
//                    // 非一级菜单  按照父级别进行分类
//                    List<SysDept> pidDeptList = pidDeptMap.get(dept.getParentId());
//                    if(pidDeptList == null){
//                        pidDeptList = new ArrayList<>();
//                    }
//                    pidDeptList.add(dept);
//                    pidDeptMap.put(dept.getParentId(),pidDeptList);
//                }
//            }
//            // 处理一级菜单和子数据
//            for(SysDept dept: resultList){
//                if(dept != null){
//                    dealMenu(dept,pidDeptMap);
//                }
//            }
//            return resultList;
//        } else {
//            List<SysDept> result = new ArrayList<>();
//            // 系统所有菜单数据
//            SysDept selectDept = new SysDept();
//            selectDept.setIsMainFlag(2);
//            List<SysDept> allList = dao.getList(selectDept);
//            // 所有菜单按照父级别进行分类
//            Map<String,List<SysDept>> pidDeptMap = new HashMap<>();
//            for(SysDept dept : allList){
//                // 非一级菜单  按照父级别进行分类
//                List<SysDept> pidDeptList = pidDeptMap.get(dept.getParentId());
//                if(pidDeptList == null){
//                    pidDeptList = new ArrayList<>();
//                }
//                pidDeptList.add(dept);
//                pidDeptMap.put(dept.getParentId(),pidDeptList);
//            }
//            dealSonDept(userDept,pidDeptMap);
//            result.add(userDept);
//            return result;
//        }
//    }

    /**
     * 递归处理子数据
     * @param dept
     * @param pidDeptMap
     */
    private void dealSonDept(SysDept dept,Map<String,List<SysDept>> pidDeptMap){
        if(pidDeptMap.get(dept) != null){
            List<SysDept> sonList = pidDeptMap.get(dept);
            dept.setSonList(sonList);
            for(SysDept sonDept : sonList){
                dealSonDept(sonDept,pidDeptMap);
            }
        }
    }

    /**
     * 获取（树形结构） 二级开始算
     * @param sysDept
     * @return
     */
    public List<SysDept> getSecondDeptTree(SysDept sysDept){
        // 定义返回结果
        List<SysDept> resultList = new ArrayList<>();
        // 系统所有菜单数据
        sysDept.setPageNum(null);
        List<SysDept> allList = dao.getList(sysDept);
        // 所有菜单按照父级别进行分类
        Map<String,List<SysDept>> pidDeptMap = new HashMap<>();
        for(SysDept dept : allList){
            if(dept.getLevel() != null && dept.getLevel().intValue() == 2){
                // 二级菜单存入list中
                resultList.add(dept);
            }else{
                // 非二级菜单  按照父级别进行分类
                List<SysDept> pidDeptList = pidDeptMap.get(dept.getParentId());
                if(pidDeptList == null){
                    pidDeptList = new ArrayList<>();
                }
                pidDeptList.add(dept);
                pidDeptMap.put(dept.getParentId(),pidDeptList);
            }
        }
        // 处理一级菜单和子数据
        for(SysDept dept: resultList){
            if(dept != null){
                dealMenu(dept,pidDeptMap);
            }
        }
        return resultList;
    }

    /**
     * 获取（树形结构）
     * @param sysDept
     * @return
     */
    public List<SysDept> getSysDeptTree(SysDept sysDept){
        // 定义返回结果
        List<SysDept> resultList = new ArrayList<>();
        // 系统所有菜单数据
        List<SysDept> allList = dao.getList(sysDept);
        // 所有菜单按照父级别进行分类
        Map<String,List<SysDept>> pidDeptMap = new HashMap<>();
        for(SysDept dept : allList){
            if(dept.getLevel() != null && dept.getLevel().intValue() == 1){
                // 一级菜单存入list中
                resultList.add(dept);
            }else{
                // 非一级菜单  按照父级别进行分类
                List<SysDept> pidDeptList = pidDeptMap.get(dept.getParentId());
                if(pidDeptList == null){
                    pidDeptList = new ArrayList<>();
                }
                pidDeptList.add(dept);
                pidDeptMap.put(dept.getParentId(),pidDeptList);
            }
        }
        // 处理一级菜单和子数据
        for(SysDept dept: resultList){
            if(dept != null){
                dealMenu(dept,pidDeptMap);
            }
        }
        return resultList;
    }

    /**
     * 递归处理子菜单数据
     * @param dept
     * @param pidMenuMap
     */
    private void dealMenu(SysDept dept,Map<String,List<SysDept>> pidMenuMap){
        List<SysDept> sonList = pidMenuMap.get(dept.getId());
        if(sonList != null && sonList.size() > 0){
            // 子菜单保存到数据中
            dept.setSonList(sonList);
            // 存在子菜单继续递归处理 3或4级菜单
            for(SysDept sonDept : sonList){
                // 每个子数据汇总到父数据
                dept.setUserCount(dept.getUserCount()+sonDept.getUserCount());
                dealMenu(sonDept,pidMenuMap);
            }
        }
    }

    /**
     * 递归处理子菜单数据
     * @param dept
     * @param pidMenuMap
     */
    private void dealMenu(SysDept dept,Map<String,List<SysDept>> pidMenuMap,List<String> list){
        List<SysDept> sonList = pidMenuMap.get(dept.getId());
        if(sonList != null && sonList.size() > 0){
            // 子菜单保存到数据中
            dept.setSonList(sonList);
            // 存在子菜单继续递归处理 3或4级菜单
            for(SysDept sonDept : sonList){
                list.add(sonDept.getId());
                dealMenu(sonDept,pidMenuMap,list);
            }
        }
    }

    /**
     * 递归处理子菜单数据
     * @param dept
     * @param pidMenuMap
     */
    private void dealMenuList(SysDept dept,Map<String,List<SysDept>> pidMenuMap,List<SysDept> list){
        List<SysDept> sonList = pidMenuMap.get(dept.getId());
        if(sonList != null && sonList.size() > 0){
            // 子菜单保存到数据中
            dept.setSonList(sonList);
            // 存在子菜单继续递归处理 3或4级菜单
            for(SysDept sonDept : sonList){
                list.add(sonDept);
                dealMenuList(sonDept,pidMenuMap,list);
            }
        }
    }

    /**
     * 获取所有管辖子部门id
     * @return
     */
    public List<String> getAllDeptIds(SysDept sysDept){
        // 定义返回结果
        List<String> list = new ArrayList<>();
        // 管辖的子部门处理
        SysDept resultDept = null;
        if(sysDept.getId() != null){
            // 当前部门获取
            list.add(sysDept.getId());
            resultDept = sysDept;
        }
        // 系统所有菜单数据
        List<SysDept> allList = dao.getList(new SysDept());
        // 所有菜单按照父级别进行分类
        Map<String,List<SysDept>> pidDeptMap = new HashMap<>();
        for(SysDept dept : allList){
            if(dept.getLevel() != null && dept.getLevel().intValue() == 1){
                // 一级菜单存入list中  需要查询的数据
//                if(dept.getId().equals(sysDept.getId())){
//                    resultDept = dept;
//                }
            }else{
                // 非一级菜单  按照父级别进行分类
                List<SysDept> pidDeptList = pidDeptMap.get(dept.getParentId());
                if(pidDeptList == null){
                    pidDeptList = new ArrayList<>();
                }
                pidDeptList.add(dept);
                pidDeptMap.put(dept.getParentId(),pidDeptList);
            }
        }
        if(resultDept != null){
            dealMenu(resultDept,pidDeptMap,list);
        }
        // 去掉最后一个字符
        return list;
    }

    /**
     * 获取所有管辖子部门id
     * @return
     */
    public List<SysDept> getAllSonDept(SysDept sysDept){
        // 定义返回结果
        List<SysDept> list = new ArrayList<>();
        // 系统所有菜单数据
        List<SysDept> allList = dao.getList(new SysDept());
        // 所有菜单按照父级别进行分类
        Map<String,List<SysDept>> pidDeptMap = new HashMap<>();
        for(SysDept dept : allList){
            if(dept.getLevel() != null && dept.getLevel().intValue() == 1){
                // 一级菜单存入list中  需要查询的数据
//                if(dept.getId().equals(sysDept.getId())){
//                    resultDept = dept;
//                }
            }else{
                // 非一级菜单  按照父级别进行分类
                List<SysDept> pidDeptList = pidDeptMap.get(dept.getParentId());
                if(pidDeptList == null){
                    pidDeptList = new ArrayList<>();
                }
                pidDeptList.add(dept);
                pidDeptMap.put(dept.getParentId(),pidDeptList);
            }
        }
        if(sysDept != null){
            dealMenuList(sysDept,pidDeptMap,list);
        }
        // 去掉最后一个字符
        return list;
    }

    /**
     * 统计使用
     * 获取所有乡村子部门对应一级部门数据
     * 一级部门父id仍是一级部门
     * @return
     */
    public Map<String,String> getSonToParentMap(){
        SysDept sysDept = new SysDept();
        sysDept.setIsMainFlag(2);
        List<SysDept> list = dao.getList(sysDept);
        Map<String,SysDept> deptMap = new HashMap<>();
        for(SysDept dept : list){
            deptMap.put(dept.getId(),dept);
        }
        Map<String,String> resultMap = new HashMap<>();
        for(SysDept dept : list){
            String parentId = getTwoLevelParentId(dept,deptMap);
            resultMap.put(dept.getId(),parentId);
        }
        return resultMap;
    }

    /**
     * 递归取父级数据
     * @param dept
     * @param deptMap
     * @return
     */
    private String getTwoLevelParentId(SysDept dept,Map<String,SysDept> deptMap){
        if(dept.getLevel() == 2){
            // 本身是2级部门数据
            return dept.getId();
        } else {
            SysDept parent = deptMap.get(dept.getParentId());
            if(parent != null){
                if(parent.getLevel().intValue() == 2){
                    return parent.getId();
                }else{
                    return getTwoLevelParentId(parent,deptMap);
                }
            } else{
                return dept.getId();
            }
        }
    }

    //查找对应id的上级id，level = 1时，查level级别为1的上级id；为level = 2时，查level级别为2的上级id
    //sonId为村级id，level级别为3
    public String getSeniorIdList(String sonId,Integer level){
        //先查全部数据
        SysDept sysDept = new SysDept();
        List<SysDept> allList = dao.getList(sysDept);
        String levelTwoId = "";
        String levelOneId = "";
        for(SysDept sysDeptGet : allList){
            if(sonId != null && sonId.equals(sysDeptGet.getId())){
                levelTwoId = sysDeptGet.getParentId();
            }
        }
        for(SysDept sysDeptGet : allList){
            if(levelTwoId != null && levelTwoId.equals(sysDeptGet.getId())){
                levelOneId = sysDeptGet.getParentId();
            }
        }
        if(level.intValue() == 1){
            return levelOneId;
        }else if(level.intValue() == 2){
            return levelTwoId;
        }
        return levelOneId;
    }

}
