package com.ruoyi.common.vo.ocr.req;

import lombok.Data;

@Data
public class OcrAtoCapExportFfileReqVO {

    /**
     * 调用高精版接口得到的返回值所组成的数据，数据结构类似[第一次调用接口高精版接口得到的返回值，第二次调用接口高精版接口得到的返回值，......]。注：调用高精版接口识别时必须包含单字(charInfo)，表格(table)，成行(row)参数
     */
    private String resultArrayStr;

    /**
     * 生成文件的类型，括号内表示需要传入的参数值，word文档(word)，excle表格(excel)
     */
    private String type = "word";


    /**
     * 固定值
     */
    private String method = "ocrExportFileService";
}
