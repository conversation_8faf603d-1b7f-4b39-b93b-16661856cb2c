package com.ruoyi.xzzfj.domain.zfjly.jdq;

/**
 * <AUTHOR>
 **/
public class JdqCamerasOnline {

    private String deviceType;

    private String regionIndexCode;

    private String collectTime;

    private String deviceIndexCode;

    private String ip;

    private String regionName;

    private String indexCode;

    private String cn;

    private String treatyType;

    private String manufacturer;

    private String port;

    private int online;

    public void setDeviceType(String deviceType){
        this.deviceType = deviceType;
    }
    public String getDeviceType(){
        return this.deviceType;
    }
    public void setRegionIndexCode(String regionIndexCode){
        this.regionIndexCode = regionIndexCode;
    }
    public String getRegionIndexCode(){
        return this.regionIndexCode;
    }
    public void setCollectTime(String collectTime){
        this.collectTime = collectTime;
    }
    public String getCollectTime(){
        return this.collectTime;
    }
    public void setDeviceIndexCode(String deviceIndexCode){
        this.deviceIndexCode = deviceIndexCode;
    }
    public String getDeviceIndexCode(){
        return this.deviceIndexCode;
    }
    public void setIp(String ip){
        this.ip = ip;
    }
    public String getIp(){
        return this.ip;
    }
    public void setRegionName(String regionName){
        this.regionName = regionName;
    }
    public String getRegionName(){
        return this.regionName;
    }
    public void setIndexCode(String indexCode){
        this.indexCode = indexCode;
    }
    public String getIndexCode(){
        return this.indexCode;
    }
    public void setCn(String cn){
        this.cn = cn;
    }
    public String getCn(){
        return this.cn;
    }
    public void setTreatyType(String treatyType){
        this.treatyType = treatyType;
    }
    public String getTreatyType(){
        return this.treatyType;
    }
    public void setManufacturer(String manufacturer){
        this.manufacturer = manufacturer;
    }
    public String getManufacturer(){
        return this.manufacturer;
    }
    public void setPort(String port){
        this.port = port;
    }
    public String getPort(){
        return this.port;
    }
    public void setOnline(int online){
        this.online = online;
    }
    public int getOnline(){
        return this.online;
    }
}
