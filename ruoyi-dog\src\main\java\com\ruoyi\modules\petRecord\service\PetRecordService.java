package com.ruoyi.modules.petRecord.service;

import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.petRecord.dao.PetRecordDao;
import com.ruoyi.modules.petRecord.entity.PetRecord;
import com.ruoyi.modules.petRecord.entity.PetRecord;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 犬只操作记录表(pet_record)表服务接口
 * <AUTHOR>
 *
 */
 @Service
public class PetRecordService  extends BaseService<PetRecordDao, PetRecord> {

   @Transactional
    public void saveOrUpdate(PetRecord entity) {
        if (entity.getId() != null && !entity.getId().equals("")) {
            // 更新操作
            entity.preUpdate();
            update(entity);
        } else {
            // 新增操作
            insert(entity);
        }
    }

}
