package com.ruoyi.instruction.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 提醒单-信息对象 t_instruction_remind
 * 
 * <AUTHOR>
 * @date 2023-05-18
 */
@Data
public class InstructionRemind extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  主键 */
    private Long id;

    /** 提醒标题 */
    @Excel(name = "提醒标题")
    private String remindTitle;

    /** 接收单位ids */
    @Excel(name = "接收单位ids")
    private String receiveUnitIds;

    /** 接收单位名称 */
    @Excel(name = "接收单位名称")
    private String receiveUnit;

    /** 是否接收 1：已接收  2：未接收 */
    @Excel(name = "是否接收 1：已接收  2：未接收")
    private String isReceive;

    /** 提醒内容 */
    @Excel(name = "提醒内容")
    private String remindContent;

    /** 编号信息 */
    @Excel(name = "编号信息")
    private String numberInfo;

    /** 提醒单位名称 */
    @Excel(name = "提醒单位名称")
    private String remindUnit;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /** 创建部门id */
    @Excel(name = "创建部门id")
    private Long createDeptId;

    /**
     * 接收单位 接收单位id
     */
    private List<Map<String,Object>> maps;

}
