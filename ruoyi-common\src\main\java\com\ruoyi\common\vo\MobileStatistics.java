package com.ruoyi.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/3 9:29
 */
@Data
public class MobileStatistics extends BaseEntity{

    /**
     * 关键词
     */
    private String keyWord;


    /** 开始时间 */
    private String startTime;

    /** 完成时间 */
    private String endTime;

    /**
     * 类型
     * 1:案件  2:专项整治  3:牛皮鲜 4:四位一体 5：监控抓拍 6：智能抓拍  7：大综合一体化案件 8：黄牛处置 9：日常巡查 10：违规处置 11：一般案件,12回访案件,13认领案件，14.联合执法
     */
    private Integer type;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 事件id
     */
    private Object eventId;

    /**
     * 任务内容
     */
    private String content;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    /**
     * 搜索类型 1:我的上报 2:我的处置 3:我的待办任务 4:我的待办案件 5:部门待办任务 6:部门待办案件
     */
    private Integer searchType;

    /**
     * 创建人id
     */
    private Long createUserId;


    /**
     * 是否展示 1:整治  2:不展示（普查问题）
     */
    private Integer isShow;


    /** 详细的案卷状态 */
    private Integer status;

    /**
     * 状态中文名称
     */
    private String statusDesc;

    /**
     * 开始页码
     */
    private Integer pageNum;

    /**
     * 每页显示条数
     */
    private Integer pageSize;


}
