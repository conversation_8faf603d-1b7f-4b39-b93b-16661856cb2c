package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.ThresholdInfo;

import java.util.List;

/**
 * 研判阈值信息表Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-05
 */
@DataSource(value = DataSourceType.SLAVE)
public interface ThresholdInfoMapper 
{
    /**
     * 查询研判阈值信息表
     * 
     * @param id 研判阈值信息表主键
     * @return 研判阈值信息表
     */
    public ThresholdInfo selectThresholdInfoById(Long id);

    /**
     * 查询研判阈值信息表列表
     * 
     * @param thresholdInfo 研判阈值信息表
     * @return 研判阈值信息表集合
     */
    public List<ThresholdInfo> selectThresholdInfoList(ThresholdInfo thresholdInfo);

    /**
     * 新增研判阈值信息表
     * 
     * @param thresholdInfo 研判阈值信息表
     * @return 结果
     */
    public int insertThresholdInfo(ThresholdInfo thresholdInfo);

    /**
     * 修改研判阈值信息表
     * 
     * @param thresholdInfo 研判阈值信息表
     * @return 结果
     */
    public int updateThresholdInfo(ThresholdInfo thresholdInfo);

    /**
     * 删除研判阈值信息表
     * 
     * @param id 研判阈值信息表主键
     * @return 结果
     */
    public int deleteThresholdInfoById(Long id);

    /**
     * 批量删除研判阈值信息表
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteThresholdInfoByIds(Long[] ids);
}
