package com.ruoyi.modules.punish.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.punish.dao.PunishDao;
import com.ruoyi.modules.punish.entity.Punish;
import com.ruoyi.modules.user.dao.SysUserDao;
import com.ruoyi.modules.user.entity.SysUser;
import com.ruoyi.util.AmapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 违法处罚表(punish)表服务接口
 *
 * <AUTHOR>
 */
@Service
public class PunishService extends BaseService<PunishDao, Punish> {
    @Autowired
    private SysUserDao sysUserDao;

    public Punish getById(String id) {
        Punish punish = dao.getById(id);
        JSONArray array = JSON.parseArray(punish.getProcessingRecord());
        if (array!=null && array.size() > 0) {
            for (int i = 0; i < array.size(); i++) {
                if (array.getJSONObject(i).getString("userId") != null) {
                    SysUser user = sysUserDao.getById(array.getJSONObject(i).getString("userId"));
                    if (user != null) {
                        array.getJSONObject(i).put("userName", user.getRealName());
                    }
                }
            }
            array.sort(Comparator.comparing(obj -> ((JSONObject) obj).getDate("time")).reversed());
            punish.setRecordList(array);
        }
        return punish;
    }

    @Transactional
    public void saveOrUpdate(Punish entity) {
        if (entity.getId() != null && !entity.getId().equals("")) {
            // 更新操作
            entity.preUpdate();
            update(entity);
        } else {
            // 新增操作
            insert(entity);
        }
    }

    public void updateByEntity(Punish punish) {
        punish.preUpdate();
        if (punish.getProcessingRecord() != null && !"".equals(punish.getProcessingRecord())) {
            Punish old = dao.getById(punish.getId());
            JSONArray array = JSON.parseArray(old.getProcessingRecord());
            if (array == null) {
                array = new JSONArray();
            }
            array.add(JSONObject.parseObject(punish.getProcessingRecord()));
            punish.setProcessingRecord(JSONArray.toJSONString(array));
        }
        dao.updateByEntity(punish);
    }

    /**
     * @author: tongsiyu
     * @date: 2022/08/15 14:26
     * @Description:根据经纬度获取地址
     */
    public String getAddress(Double lon, Double lat) {
        String result = AmapUtils.getAddressByLonLat(lon, lat);
        String address = "";
        if (result != null) {
            JSONObject obj = JSONObject.parseObject(result);
            if (obj != null) {
                JSONObject regeocode = (JSONObject) obj.get("regeocode");
                if (regeocode != null) {
                    address = (String) regeocode.get("formatted_address");
                }
            }
        }
        return address;
    }
}
