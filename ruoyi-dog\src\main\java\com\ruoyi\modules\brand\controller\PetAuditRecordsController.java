package com.ruoyi.modules.brand.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.brand.entity.PetAuditRecords;
import com.ruoyi.modules.brand.service.PetAuditRecordsService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * 犬牌申请审核记录(pet_audit_records)表控制层
 * <AUTHOR>
 */
@RestController
@RequestMapping("petAuditRecords")
public class PetAuditRecordsController {
    /**
     * 服务对象
     */
    @Resource
    private PetAuditRecordsService service;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("/getPageList")
    public AjaxResult getPageList(PetAuditRecords entity) {
        return AjaxResult.success(service.getPageList(entity));
    }
    @RequestMapping("/getList")
    public AjaxResult getList(PetAuditRecords entity) {
        return AjaxResult.success(service.getList(entity));
    }

    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(PetAuditRecords entity) {
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }

    @RequestMapping("/delete")
    public AjaxResult delete(PetAuditRecords entity) {
        entity.setDelFlag(2);
        service.delete(entity);
        return AjaxResult.success();
    }

}
