package com.ruoyi.common.dtalk.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> yx-0176
 * @description
 * @date : 2021/10/18
 */
@Data
@Configuration
public class DtalkProperties {

    @Value("${login.dtalk.appKey}")
    private String loginAppkey;

    @Value("${login.dtalk.appSecret}")
    private String loginAppsecret;

    @Value("${dept.dtalk.appKey}")
    private String deptAppkey;

    @Value("${dept.dtalk.appSecret}")
    private String deptAppsecret;

    @Value("${dtalk.domain:openplatform.dg-work.cn}")
    private String domain;

    @Value("${dtalk.protocal:https}")
    private String protocal;

    @Value("${dtalk.login.url}")
    private String loginUrl;

    @Value("${dept.dtalk.tenantId}")
    private Long tenantId;

}
