package com.ruoyi.modules.sysDict.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.sysDict.entity.SysDict;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by 29217 on 2019/2/27.
 */
@Repository
public interface SysDictDao extends BaseDao<SysDict> {

    public List<SysDict> getPetType(SysDict sysDict);

    String queryDictKey(SysDict sysDict);
}
