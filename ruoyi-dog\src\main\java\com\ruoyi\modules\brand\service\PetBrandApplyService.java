package com.ruoyi.modules.brand.service;


import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.modules.brand.dao.ApplyMergeDao;
import com.ruoyi.modules.brand.dao.PetAuditRecordsDao;
import com.ruoyi.modules.brand.dao.PetBrandApplyDao;
import com.ruoyi.modules.brand.dao.PetBrandDao;
import com.ruoyi.modules.brand.entity.ApplyMerge;
import com.ruoyi.modules.brand.entity.PetAuditRecords;
import com.ruoyi.modules.brand.entity.PetBrand;
import com.ruoyi.modules.brand.entity.PetBrandApply;
import com.ruoyi.modules.hospital.dao.QualifiDao;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.user.dao.SysUserDao;
import com.ruoyi.modules.user.entity.SysUser;
import com.ruoyi.util.ExcelUtil;
import com.ruoyi.util.PinYinUtil;
import com.ruoyi.util.QRCode;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PetBrandApplyService extends BaseService<PetBrandApplyDao, PetBrandApply> {

    @Value("${petBrandServiceUrl}")
    private String petBrandServiceUrl;
    @Value("${uploadFilePath}")
    private String uploadFilePath;

    @Resource
    PetBrandDao petBrandDao;
    @Autowired
    private PetAuditRecordsDao petAuditRecordsDao;
    @Autowired
    private ApplyMergeDao applyMergeDao;
@Autowired
private QualifiDao qualifiDao;
@Autowired
private SysUserDao sysUserDao;
    /**
     * 根据ID获取单条数据
     *
     * @param id
     * @return
     */
    public PetBrandApply getById(String id) {
        PetBrandApply res = dao.getById(id);
        if (res.getDogCodes() != null) {
            String[] ids = res.getDogCodes().split(",");
            List<PetBrand> list = new ArrayList<>();
            for (String i : ids) {
                list.add(petBrandDao.getById(i));
            }
            res.setBrandList(list);
        }
        return res;
    }

    @Override
    public PageInfo<PetBrandApply> getPageList(PetBrandApply entity) {
        PageHelper.startPage(entity.getPageNum(),entity.getPageSize());
        // 获取用户信息
        com.ruoyi.common.core.domain.entity.SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录或会话失效");
        }
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains("admin") || roleList.contains("dogZfj") || roleList.contains("dogAdmin") || roleList.contains("enforcePerson") ) {
                entity.setCounty(null);
        }
        List<PetBrandApply> list = getList(entity);

        PageInfo<PetBrandApply> pageInfo = new PageInfo<PetBrandApply>(list);




        if (CollectionUtils.isNotEmpty(list)) {
            for (PetBrandApply petBrandApply : list) {
                if (roleList.contains(InstructionRolesConstants.DOG_COUNTY_CONFIRM)){
                    String notUsedNum = dao.getUnUsedBrandByCounty(petBrandApply.getCounty());
                    petBrandApply.setNotUsedNum(notUsedNum);
                }else{
                    String notUsedNum = dao.getUnUsedBrandByQualifiId(petBrandApply.getQualifiId());
                    petBrandApply.setNotUsedNum(notUsedNum);
                }

//                if (petBrandApply.getType() == 1) {
//                    String notUsedNum = dao.getUnUsedBrandByQualifiId(petBrandApply.getQualifiId());
//
//                    petBrandApply.setNotUsedNum(notUsedNum);
//                } else {
//                    String notUsedNum = dao.getUnUsedBrandByCounty(petBrandApply.getQualifiId());
//
//                    petBrandApply.setNotUsedNum(notUsedNum);
//                }
            }
        }

        return pageInfo;
    }

    public List<PetBrandApply> getMergeList(ApplyMerge merge) {
        ApplyMerge applyMerge = applyMergeDao.getById(merge.getId());
        List<String> list = JSON.parseArray(applyMerge.getApplyIds(), String.class);
        List<PetBrandApply> petlist = new ArrayList<>();
        for (String id : list) {
            petlist.add(dao.getById(id));
        }
        return petlist;
    }

    public List<ApplyMerge> getPageListMerge(ApplyMerge entity) {
        //PageHelper.startPage(entity.getPageNum(), entity.getPageSize());
        List<ApplyMerge> mergeList = applyMergeDao.getList(entity);
        for (ApplyMerge merge : mergeList) {
            List<String> ids = JSON.parseArray(merge.getApplyIds(), String.class);
            Integer status = merge.getStatus();
            for (String id : ids) {
                PetBrandApply apply = dao.getById(id);
                if (merge.getStatus().intValue() == 7) {//运输中
                    if (status != null && status.intValue() == apply.getStatus()) continue;
                    status = apply.getStatus();
                }
            }
            merge.setStatus(status);
        }
        if (entity.getStatus() != null) {
            mergeList = mergeList.stream().filter(s -> s.getStatus().equals(entity.getStatus())).collect(Collectors.toList());
        }
        //PageInfo<ApplyMerge> pageInfo = new PageInfo<ApplyMerge>(mergeList);
        return mergeList;
    }

    @Transactional
    public void batchSubmit(String ids, String path) {
        List<String> list = JSON.parseArray(ids, String.class);
        Integer num = 0;
        String deptId = "";
        for (String id : list) {
            PetBrandApply apply = dao.getById(id);
            apply.setPaymentVoucher(path);
            apply.setStatus(30);//已提交制作
            dao.update(apply);
            deptId = apply.getCounty();
            num = num + apply.getBrandNum();
        }
        //新增合并数据
        ApplyMerge merge = new ApplyMerge();
        merge.preInsert();
        merge.setApplyIds(ids);
        merge.setStatus(30);
        merge.setDeptId(deptId);
        merge.setNum(num);
        merge.setPaymentVoucher(path);
        merge.setMergeNum(list.size());
        applyMergeDao.insert(merge);
    }

    public List<HashMap> countyStatistics(PetBrandApply apply) {
        return dao.countyStatistics(apply);
    }

    public List<HashMap> unitStatistics(PetBrandApply apply) {
        //PageHelper.startPage(apply.getPageNum(), apply.getPageSize());
        //PageInfo<HashMap> pageInfo = new PageInfo<HashMap>(dao.unitStatistics(apply));
        //return pageInfo;
    return dao.unitStatistics(apply);
    }

    public void unitStatisticsDown(PetBrandApply entity, HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<HashMap> list = dao.unitStatistics(entity);
        List<String> headList = new ArrayList();
        headList.add("单位名称");
        headList.add("所属地区");
        headList.add("剩余可用犬牌数");
        headList.add("已绑定犬牌数");
        headList.add("注销犬牌数");
        List<List<String>> content = new ArrayList<>();
        Integer notUse = 0, inUse = 0, yzx = 0;
        for (HashMap d : list) {
            List<String> row = new ArrayList();
            row.add(d.get("name").toString());
            row.add(d.get("countyName").toString());
            row.add(d.get("notUse").toString());
            row.add(d.get("inUse").toString());
            row.add(d.get("yzx").toString());
            if (d.get("notUse") != null) {
                notUse += Integer.parseInt(d.get("notUse").toString());
            }
            if (d.get("inUse") != null) {
                inUse += Integer.parseInt(d.get("inUse").toString());
            }
            if (d.get("yzx") != null) {
                yzx += Integer.parseInt(d.get("yzx").toString());
            }
            content.add(row);
        }
        List<String> row = new ArrayList();
        row.add("合计");
        row.add("");
        row.add(notUse.toString());
        row.add(inUse.toString());
        row.add(yzx.toString());
        content.add(row);
        ExcelUtil.downloadExecl("", headList, content, response, request);
    }

    //public PageInfo<HashMap> getManufacturerPageList(PetBrandApply entity) {
    //    PageHelper.startPage(entity.getPageNum(), entity.getPageSize());
    //    PageInfo<HashMap> pageInfo = new PageInfo<HashMap>(dao.getManufacturerPageList(entity));
    //    return pageInfo;
    //}
    public List<ApplyMerge> getManufacturerPageList(ApplyMerge entity) {
        //PageHelper.startPage(entity.getPageNum(), entity.getPageSize());
        List<ApplyMerge> list = applyMergeDao.getListCJ(entity);
        for (ApplyMerge merge : list) {
            List<String> ids = JSON.parseArray(merge.getApplyIds(), String.class);
            String danhao = "";
            Integer status = merge.getStatus();
            for (String id : ids) {
                PetBrandApply apply = dao.getById(id);
                if (apply != null) {
                    danhao = apply.getCourierNumber() != null ? apply.getCourierNumber() : "";
                }
                if (merge.getStatus().intValue() == 7) {//运输中
                    if (status != null && status.intValue() == apply.getStatus()) continue;
                    status = apply.getStatus();
                }
            }
            merge.setStatus(status);
            merge.setCourierNumber(danhao);
        }
        if (entity.getStatus() != null) {
            list = list.stream().filter(s -> s.getStatus().equals(entity.getStatus())).collect(Collectors.toList());
        }//list.stream().filter(s=> s.)
        //PageInfo<ApplyMerge> pageInfo = new PageInfo<ApplyMerge>(list);
        return list;
    }

    public Integer getNotDistributionNum(String area) {
        return dao.getNotDistributionNum(area);
    }

    public List<PetBrand> getPetBrand(String id) {
        PetBrandApply apply = dao.getById(id);
        if (apply.getDogCodes() != null) {
            List<PetBrand> list = petBrandDao.getPetBrand(Arrays.asList(Arrays.stream(apply.getDogCodes().split(",")).toArray()));
            return list;
        }
        return null;
    }

    public void exeportData(PetBrandApply entity, HttpServletRequest request, HttpServletResponse response) throws Exception {
        PetBrandApply apply = dao.getById(entity.getId());
        if (apply.getDogCodes() != null) {
            List<PetBrand> data = petBrandDao.getPetBrand(Arrays.asList(Arrays.stream(apply.getDogCodes().split(",")).toArray()));
            Map<String, Object> objMap = new HashMap<>();
            List<Map<String, Object>> list = new ArrayList<>();
            if (data.size() > 0) {
                for (PetBrand brand : data) {
                    PetBrand b = new PetBrand();
                    b.setBrandNum(brand.getBrandNum());
                    String imgFile = ExcelUtil.saveFile(brand.getQrCode(), uploadFilePath);
                    if (imgFile != null) {
                        b.setImgFile(new File(imgFile));
                    } else {
                        b.setImgFile(null);
                    }
                    b.setCreateDate(brand.getCreateDate());
                    objMap = ExcelUtil.javaBean2Map(b);
                    list.add(objMap);
                }
            }
            //String[] objects =new String ["brandNum","imgFile","createDate"];
            String[] titles = new String[]{"犬牌编号", "生成时间", "电子犬牌"};
            //Map<String, String> titleMap = new HashMap<>();
            //titleMap.put("brandNum", "犬牌编号");
            //titleMap.put("imgFile", "电子犬牌");
            //titleMap.put("createDate", "创建时间");
            //Map<String, String> map = titleMap;
            //for (int i = 0; i < titles.length; i++) {
            //    String key = objects[i].toString();
            //    titles[i] = map.get(key);
            //}
            String fileName = "xxx";
            response.setContentType("application/vnd.ms-excel");
            response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
            ExcelUtil.excelOut(titles, list.size(), list, fileName, response, uploadFilePath);
        }
    }

    @Transactional
    public void updateStatusMerge(PetBrandApply petBrandApply) {
        ApplyMerge merge = applyMergeDao.getById(petBrandApply.getId());
        if (merge != null) {
            //更改合并信息状态
            merge.setStatus(petBrandApply.getStatus());
            merge.setRemarks(petBrandApply.getRemarks());
            applyMergeDao.update(merge);

            List<String> list = JSON.parseArray(merge.getApplyIds(), String.class);
            for (String id : list) {
                PetBrandApply a = dao.getById(id);
                a.setStatus(petBrandApply.getStatus());
                a.setRemarks(petBrandApply.getRemarks());
                if (petBrandApply.getStatus().intValue() == 7) {//运输中
                    a.setCourierName(petBrandApply.getCourierName());
                    a.setCourierNumber(petBrandApply.getCourierNumber());
                }
                if (petBrandApply.getStatus().intValue() == 5) {//管理公司驳回
                    a.setPaymentVoucher("");
                }
                dao.update(a);
                //    审核记录
                PetAuditRecords record = new PetAuditRecords();
                record.setApplyId(a.getId());
                record.setStatus(a.getStatus());
                record.setRemarks(a.getRemarks());
                record.preInsert();
                petAuditRecordsDao.insert(record);
            }
        }
        //PetBrandApply apply = new PetBrandApply();
        //apply.setCounty(petBrandApply.getCounty());
        //apply.setPaymentVoucher(petBrandApply.getPaymentVoucher());
        //List<PetBrandApply> list = dao.getList(apply);
        //for (PetBrandApply a : list) {
        //    a.setStatus(petBrandApply.getStatus());
        //    a.setRemarks(petBrandApply.getRemarks());
        //    if (petBrandApply.getStatus().intValue() == 7) {//运输中
        //        a.setCourierName(petBrandApply.getCourierName());
        //        a.setCourierNumber(petBrandApply.getCourierNumber());
        //    }
        //    if (petBrandApply.getStatus().intValue() == 5) {//管理公司驳回
        //        a.setPaymentVoucher("");
        //    }
        //    dao.update(a);
        //    //    审核记录
        //    PetAuditRecords record = new PetAuditRecords();
        //    record.setApplyId(a.getId());
        //    record.setStatus(a.getStatus());
        //    record.setRemarks(a.getRemarks());
        //    record.preInsert();
        //    petAuditRecordsDao.insert(record);
        //}

    }

    @Transactional
    public AjaxResult updateByEntity(PetBrandApply entity) {
        if (entity.getStatus().intValue() == 3 && entity.getDogCodes() != null) {
            List<String> idList = JSON.parseArray(entity.getDogCodes(), String.class);
            String dogCodes = "";
            for (String id : idList) {
                PetBrand brand = petBrandDao.getById(id);
                if (brand.getApplyId() != null && !"".equals(brand.getApplyId())) {
                    return AjaxResult.error(brand.getBrandNum() + "已被其他单位锁定，请重新选择");
                }
                brand.setApplyId(entity.getId());
                petBrandDao.updateByEntity(brand);

                dogCodes += id + ",";
            }
            if (dogCodes != "") {
                entity.setDogCodes(dogCodes.substring(0, dogCodes.lastIndexOf(",")));
            }
            entity.setStatus(8);
        }
        if (entity.getStatus().intValue() == 9) {
            //将状态修改成10
            entity.setStatus(10);
            PetBrandApply apply = dao.getById(entity.getId());
            String[] ids = apply.getDogCodes().split(",");
            for (String id : ids) {
                PetBrand brand = petBrandDao.getById(id);
//判断号牌是否使用中
                if (brand.getBrandCom() != null || brand.getIsUse().intValue() == 1) {
                    return AjaxResult.error("号码集合中存在已使用的号码，请重新获取获取号段");
                }
                String name = "";
                if (apply.getType().intValue() == 1) {
                    brand.setBrandCom(apply.getQualifiId());
                    Qualifi qualifi=qualifiDao.getById(apply.getQualifiId());
                    if(qualifi!=null)name=qualifi.getName();
                }
                if (apply.getType().intValue() == 3) {
                    brand.setBrandCom(apply.getUserId());
                    SysUser user=sysUserDao.getById(apply.getUserId());
                    if(user!=null)name=user.getRealName();
                }
                petBrandDao.updateByEntity(brand);
//                分配记录
                PetAuditRecords petAuditRecords = new PetAuditRecords();
                petAuditRecords.setApplyId(brand.getId());
                petAuditRecords.setRemarks(name);
                petAuditRecords.preInsert();
                petAuditRecordsDao.insert(petAuditRecords);
            }
        }
        dao.update(entity);
        //    审核记录
        PetAuditRecords record = new PetAuditRecords();
        record.setApplyId(entity.getId());
        record.setStatus(entity.getStatus());
        record.setRemarks(entity.getRemarks());
        record.preInsert();
        petAuditRecordsDao.insert(record);
        return AjaxResult.success();
    }

    @Transactional
    public void updateStatus(PetBrandApply petBrandApply) {
        //List<String> dogCodes = new ArrayList<>();
        String dogCodes = "";
        //生成犬牌
        if (petBrandApply.getStatus() == 3) {
            PetBrandApply apply = dao.getById(petBrandApply.getId());
            String prefix = "NO " + new PinYinUtil().StringAlpha(apply.getCountyName());
            //获取犬牌末尾号
            String lastBrandNum = petBrandDao.getLastBrandNum(prefix);
            Integer num = 0;
            List<PetBrand> list = new ArrayList<>();
            if (lastBrandNum != null) {
                num = Integer.parseInt(lastBrandNum);
            }
            for (int i = 0; i < apply.getBrandNum(); i++) {
                num++;
                PetBrand brand = new PetBrand();
                brand.preInsert();
                brand.setIsRecovery(1);          //未使用
                brand.setIsCancellation(1);      //未注销
                brand.setIsUse(2);               //未使用
                brand.setArea(apply.getCounty());
                //当医院申请时
                if (apply.getType() != null && apply.getType().intValue() == 1) {
                    brand.setBrandCom(apply.getQualifiId());//归属医院
                }
                //执法局或执法队申请时
                if (apply.getType() != null && (apply.getType().intValue() == 2 || apply.getType().intValue() == 3)) {
                    brand.setBrandCom(apply.getUserId());//归属人
                }
                String dogCode = prefix + String.format("%06d", num);
                //dogCodes.add(brand.getId());
                dogCodes += brand.getId() + ",";
                brand.setBrandNum(dogCode);
                brand.setQrCode(QRCode.createQrImg(petBrandServiceUrl + dogCode));
                list.add(brand);
            }
            petBrandDao.saveList(list);
            System.out.println(lastBrandNum);
        }
        //    编辑状态
        //生成的犬牌号
        if (dogCodes != "") {
            petBrandApply.setDogCodes(dogCodes.substring(0, dogCodes.lastIndexOf(",")));
        }
        dao.update(petBrandApply);
        //    审核记录
        PetAuditRecords record = new PetAuditRecords();
        record.setApplyId(petBrandApply.getId());
        record.setStatus(petBrandApply.getStatus());
        record.setRemarks(petBrandApply.getRemarks());
        record.preInsert();
        petAuditRecordsDao.insert(record);
    }

    @Transactional
    public void saveOrUpdate(PetBrandApply petBrandApply) {
        com.ruoyi.common.core.domain.entity.SysUser user = SecurityUtils.getLoginUser().getUser();

        if (user.getDogUserId() != null){
            Qualifi qualifi = new Qualifi();
            qualifi.setAccount(user.getDogUserId());



            Qualifi qualifiByAccount = null;
            qualifiByAccount = qualifiDao.getQualifiByAccount(qualifi);


            if (Objects.nonNull(qualifiByAccount)) {
                petBrandApply.setQualifiId(qualifiByAccount.getId());
            }

            if (petBrandApply.getId() != null && !"".equals(petBrandApply.getId())) {
                petBrandApply.preUpdate();
                dao.update(petBrandApply);
            } else {
                petBrandApply.preInsert();
                petBrandApply.setUserId(user.getDogUserId());
                dao.insert(petBrandApply);
            }
        }
    }




//        Qualifi qualifiByAccount = qualifiDao.getQualifiByAccount(qualifi);
//        if (Objects.nonNull(qualifiByAccount)) {
//            petBrandApply.setQualifiId(qualifiByAccount.getId());
//        }
//
//        if (petBrandApply.getId() != null && !"".equals(petBrandApply.getId())) {
//            petBrandApply.preUpdate();
//            //状态等于3 自动发放犬牌
//            //if (petBrandApply.getStatus().intValue() == 2) {
//            //    PetBrand pet =new PetBrand();
//            //   pet.setNum(petBrandApply.getBrandNum());
//            //    //犬牌申请表的所在县==犬牌管理表的所在区
//            //    pet.setArea(petBrandApply.getCounty());
//            //    List<PetBrand> list = petBrandDao.getListByNum(pet);
//            //    for (PetBrand petBrand : list) {
//            //        petBrand.setBrandCom(petBrandApply.getQualifiId());
//            //        petBrand.setQrCode(QRCode.createQrImg(petBrandServiceUrl + petBrand.getId()));
//            //        petBrandDao.assignDogCard(petBrand);
//            //    }
//            //
//            //}
//            dao.update(petBrandApply);
//        } else {
//            petBrandApply.preInsert();
//            dao.insert(petBrandApply);
//        }
////        //执法局申请,默认审核通过的
////        if (petBrandApply.getType() != null && petBrandApply.getType().intValue() == 2) {
////            //if (petBrandApply.getStatus() != null && petBrandApply.getStatus().intValue() == 1) {
////            PetBrandApply a = new PetBrandApply();
////            a.setId(petBrandApply.getId());
////            a.setStatus(3);
////            updateStatus(a);
////            //}
////        }
//    }


}
