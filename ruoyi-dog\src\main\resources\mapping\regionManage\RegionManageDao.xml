<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.regionManage.dao.RegionManageDao">

    <sql id="columns">
     <trim suffixOverrides=",">
            a.id as id,
            a.name as name,
            a.type as type,
            a.marks as marks,
            a.create_date as createDate,
            a.create_by as createBy,
            a.update_date as updateDate,
            a.update_by as updateBy,
            a.del_flag as delFlag,
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.regionManage.entity.RegionManage" resultType="com.ruoyi.modules.regionManage.entity.RegionManage">
        select <include refid="columns"/>

        from region_manage a
        where a.id =#{id}
    </select>



    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.regionManage.entity.RegionManage" resultType="com.ruoyi.modules.regionManage.entity.RegionManage">
        select <include refid="columns"/>
        from region_manage a
        where a.del_flag =1
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="name != null and name != ''">
                and a.name like concat('%',#{name},'%')
            </if>
            <if test="type != null and type != ''">
                and a.type = #{type}
            </if>
            <if test="marks != null and marks != ''">
                and a.marks = #{marks}
            </if>
            <if test="createDate != null">
                and a.create_date = #{createDate}
            </if>
            <if test="createBy != null and createBy != ''">
                and a.create_by = #{createBy}
            </if>
            <if test="updateDate != null">
                and a.update_date = #{updateDate}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and a.update_by = #{updateBy}
            </if>
            <if test="delFlag != null">
                and a.del_flag = #{delFlag}
            </if>
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.regionManage.entity.RegionManage">
        insert into region_manage(id,name, type, marks, create_date, create_by, update_date, update_by, del_flag)
        values (#{id},#{name}, #{type}, #{marks}, #{createDate}, #{createBy}, #{updateDate}, #{updateBy}, #{delFlag})
    </insert>
    <update id="updateByEntity" parameterType="com.ruoyi.modules.regionManage.entity.RegionManage">
        update region_manage set
        <trim suffixOverrides=",">
    <if test="name != null and name != ''">
    name = #{name},
        </if>
    <if test="type != null">
    type = #{type},
        </if>
    <if test="marks != null and marks != ''">
    marks = #{marks},
        </if>
    <if test="createDate != null">
    create_date = #{createDate},
        </if>
    <if test="createBy != null and createBy != ''">
    create_by = #{createBy},
        </if>
    <if test="updateDate != null">
    update_date = #{updateDate},
        </if>
    <if test="updateBy != null and updateBy != ''">
    update_by = #{updateBy},
        </if>
    <if test="delFlag != null">
    del_flag = #{delFlag},
        </if>
        </trim>
        where id = #{id}
    </update>
    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.regionManage.entity.RegionManage">
        update region_manage set
        <trim suffixOverrides=",">
               name = #{name},
               type = #{type},
               marks = #{marks},
               create_date = #{createDate},
               create_by = #{createBy},
               update_date = #{updateDate},
               update_by = #{updateBy},
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.regionManage.entity.RegionManage">
        UPDATE  region_manage
        SET
        del_flag=#{delFlag},
        update_date = #{updateDate},
        update_by = #{updateBy}
        where id = #{id}
    </delete>

</mapper>

