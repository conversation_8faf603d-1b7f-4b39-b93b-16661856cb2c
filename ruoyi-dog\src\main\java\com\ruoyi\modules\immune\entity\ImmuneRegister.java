package com.ruoyi.modules.immune.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.user.entity.SysUploadFile;

import java.util.Date;
import java.util.List;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 9:52
 * @version: 1.0
 **/
public class ImmuneRegister extends BaseEntity {

    private Integer type;                            //类型区分1正常免疫，2年审免疫
    private String hospital;                         //注射医院
    private String doctor;                           //注射医生
    private String vaccineBrand;                     //疫苗品牌
    private String vaccineBatch;                     //疫苗批次
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date injectionDate;                       //注射日期
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date injectionEnddate;                     //疫苗到期日期
    private String petIdCard;                          //饲主身份证号
    private String status;                          //审核状态：1待审批，2已通过，3未通过，4已注销
    private String reason;                          //审核意见
    private String immuneCard;                          //免疫证
    private Integer crossType;                          //是否是跨区办理 1:否 2：是

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date aboutDate;                         //预约时间
    private String aboutMake;                       //预约方式
    private String hospitalId;                      //疫苗医院ID
    private String vaccineBrandOld;                          //原来的批次品牌

    private String petId; //犬只主键ID
    private String petNum; //犬只牌
    private String areaCode; //犬只主键ID
    private String tel; //饲主手机号
    private String realName; //饲主真是姓名
    private String handle;//是否是执法窗口直接办理的 1：是 其余：否
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date bTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date eTime;
    private String keyword;//关键字
    private String queryType;//首页统计-查询状态
    private String deptId;//首页统计-区域id
    private String petType;//首页统计-宠物类别
private String qualifiAddress;//注射医院地址

    public String getQualifiAddress() {
        return qualifiAddress;
    }

    public void setQualifiAddress(String qualifiAddress) {
        this.qualifiAddress = qualifiAddress;
    }

    public String getPetType() {
        return petType;
    }

    public void setPetType(String petType) {
        this.petType = petType;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Date getbTime() {
        return bTime;
    }

    public void setbTime(Date bTime) {
        this.bTime = bTime;
    }

    public Date geteTime() {
        return eTime;
    }

    public void seteTime(Date eTime) {
        this.eTime = eTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getPetId() {
        return petId;
    }

    public void setPetId(String petId) {
        this.petId = petId;
    }

    public String getHospital() {
        return hospital;
    }

    public void setHospital(String hospital) {
        this.hospital = hospital;
    }

    public String getDoctor() {
        return doctor;
    }

    public void setDoctor(String doctor) {
        this.doctor = doctor;
    }

    public String getVaccineBrand() {
        return vaccineBrand;
    }

    public void setVaccineBrand(String vaccineBrand) {
        this.vaccineBrand = vaccineBrand;
    }

    public String getVaccineBatch() {
        return vaccineBatch;
    }

    public void setVaccineBatch(String vaccineBatch) {
        this.vaccineBatch = vaccineBatch;
    }

    public Date getInjectionDate() {
        return injectionDate;
    }

    public void setInjectionDate(Date injectionDate) {
        this.injectionDate = injectionDate;
    }

    public Date getInjectionEnddate() {
        return injectionEnddate;
    }

    public void setInjectionEnddate(Date injectionEnddate) {
        this.injectionEnddate = injectionEnddate;
    }

    public String getPetIdCard() {
        return petIdCard;
    }

    public void setPetIdCard(String petIdCard) {
        this.petIdCard = petIdCard;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getImmuneCard() {
        return immuneCard;
    }

    public void setImmuneCard(String immuneCard) {
        this.immuneCard = immuneCard;
    }

    public String getVaccineBrandOld() {
        return vaccineBrandOld;
    }

    public void setVaccineBrandOld(String vaccineBrandOld) {
        this.vaccineBrandOld = vaccineBrandOld;
    }

    public String getPetNum() {
        return petNum;
    }

    public void setPetNum(String petNum) {
        this.petNum = petNum;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public Date getAboutDate() {
        return aboutDate;
    }

    public void setAboutDate(Date aboutDate) {
        this.aboutDate = aboutDate;
    }

    public String getAboutMake() {
        return aboutMake;
    }

    public void setAboutMake(String aboutMake) {
        this.aboutMake = aboutMake;
    }

    public String getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(String hospitalId) {
        this.hospitalId = hospitalId;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getHandle() {
        return handle;
    }

    public void setHandle(String handle) {
        this.handle = handle;
    }

    public Integer getCrossType() {
        return crossType;
    }

    public void setCrossType(Integer crossType) {
        this.crossType = crossType;
    }
}
