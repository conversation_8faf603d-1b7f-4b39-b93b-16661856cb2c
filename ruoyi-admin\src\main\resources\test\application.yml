# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.8
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /home/<USER>/ruoyi/uploadPath/ygf
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math
  fileUr : https://csdn.dsjj.jinhua.gov.cn:8303/file

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 9000
  servlet:
    # 应用的访问路径
    context-path: /prod-api
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  main:
    allow-circular-references: true
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  data:
    # redis 配置
    redis:
      # 地址
      host: localhost
      # 端口，默认为6379
      port: 6379
      # 数据库索引
      database: 0
      # 密码
      password: Szjh2025!@#
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml,classpath*:mapping/**/*.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Springdoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: false
  swagger-ui:
    enabled: false
    path: /swagger-ui.html
    tags-sorter: alpha
  group-configs:
    - group: 'default'
      display-name: '测试模块'
      paths-to-match: '/**'
      packages-to-scan: com.ruoyi.web.controller.tool

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*



#访问二维码前缀 （犬只）
qrServiceUrl: http://localhost:8777/petCertificates/getById?id=
petBrandServiceUrl: https://www.jinhuadog.com/pet?num=

uploadFilePath: D:/wjl/图书//book/
zipFilePath: D:/dogCode/dog


localUploadPrefix: D://emis/
uploadFilePrefix: http://localhost:8888/sysUploadFile/getFileById?id=



#行政执法局配置
dx:
  siid: JHSZHXZZFJ
  user: jhzhcg
  sPSecret: Jhzhcg7*
  url: http://************:12000/sms/smsservice/httpservices/capService
  jumpUrl: https://jazz.swzfw.jinhua.gov.cn:9443/ajhf/#/form

#图片存储地址
image:
  url: http://************:8000/file/uploadPath/

ajhfSsyj:
  url: http://*************/

environment: test

wrj:
  url: http://3rdapi.shunxvision.com:18080/api/v1

aes:
  key: jazz!@#

#三色预警信息加密
ssyj:
  appSecret: jh*374bf2l4mh2m5m^uvdtyd5!9u86#8998t754%qnjkyiniugu

api:
  ywhdjjUrl: https://************:4482/station/mobile/serverapi.action

#接口应用
port-server:
  url: http://127.0.0.1:9000/prod-api/indexPort


#浙政钉登录验证相关信息
zzd:
  #应用名称
  clientName: 金华市城市大脑驾驶舱
  #生产应用标识
  clientId: jhscsdn_dingoa
#  appKey: jhscsdn_dingoa-yGwFOY9j1faENzi
#  appSecret: 8sdq550Evl1o7i6y62I16M6Se1zH08191ekNQRFp
  appKey: ygf-test-uc49nqxxSPlj25urQFRza
  appSecret: Ld85Z65O0B2fWt6fH7SaSZi3KktS06sc5O08E2xJ
  #环境域名
  domainName: open.on-premises.dingtalk.com
  authorization-timeout: 600000
  tenantId: 50415073

#浙政钉扫码登录
zzd1:
  #应用名称
  clientName: 金华执法
  #生产应用标识
  clientId: jhzf_dingoa
  appKey: jhzf_dingoa-1tn22hJj3tdUquMBVr
  appSecret: c9xAn9999d84tZ89757gIf9aAXwm9D6A4188wt7l
  #环境域名
  domainName: openplatform-pro.ding.zj.gov.cn
  authorization-timeout: 600000
  tenantId: 196729


upload:
  filePath: /home/<USER>/ruoyi/uploadPath/ygf
  urlPath:  http://************:8000/file/uploadPath/
  jumpLink: https://csdn.dsjj.jinhua.gov.cn:8806


env: test

getui:
  appid: NcmHDks6S78ZF6BwE6Sdt3
  appkey: d9L0ULox6K9ha7X16y8RC1
  mastersecret: FucMF7aKeT6BZHhAXsZea1

mqtt:
  username: artemis_26251915_GEVSXV5I
  password: IV1X0GQ3
  hostUrl: tcp://************:1883
  clientId: 26251915
  timeout: 30
  keepalive: 100
  isOpen: 0 #135开启 是否开启：0 不开启，1开启

alartMqtt:
  username: artemis_29977596_RQE9ZYHW
  password: S681V8QX
  hostUrl: tcp://************:1883
  clientId: 29977596
  timeout: 30
  keepalive: 100
  isOpen: 0 #134开启 是否开启：0 不开启，1开启

task:
  isOpen: 1

zlb:
  zlburl: https://appapi.zjzwfw.gov.cn/sso/servlet/simpleauth
  servicecode: wlbzyfw
  servicepwd: wlbzyfwpwd
  # 令牌自定义标识
  header: ZlbAuthorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 1200
  # aes的key
  key: ivqpsFQwQqxYUr7f

qrcode:
  url: http://zqzf.xzzfj.jinhua.gov.cn/download/exam.html

sidewalk:
  url: http://************:8090/jhcg-open-api/api/core
  uploadUrl: http://************:9080/jhcgwt-spzp-service/api/upload
  serviceUrl: http://************:9080/jhcgwt-spzp-service/api/service
  salt: 8e32b40651ae11ec99b400163e08fa00
  user: hczqglzx
  pwd: 000000

# 专有钉钉登录配置
login:
  dtalk:
    appKey: ygf-saoma-test_dingoa-7TbyQJvi
    appSecret: 4168B6Mp7dg2KLbvom9cN0cm56I0gZxS3iyO3EHL

# 专有钉钉租户部门配置
dept:
  dtalk:
    appKey: jhzf-y5Zq95O4mYyBH7BzG3suIvMVz
    appSecret: W9U99DZZ7JA680AZo0J4196gBAly8bijiM0QjH8R
    tenantId: 196729

# 专有钉钉登录地址配置
dtalk:
  domain: openplatform-pro.ding.zj.gov.cn
  login:
    url: https://login.dg-work.cn/oauth2/auth.htm?response_type=code&client_id=ygf-saoma-test_dingoa&scope=get_user_info&authType=QRCODE&redirect_uri=http://************/ygf/home
  protocal: https

single:
  url: http://************/ygf/home
  pcUrl: http://************/ygf/home

irs:
  component:
    accessKey: ca348e35b0b349c1b786fe3129b8aba5
    secretKey: 8bf9e0970c744cde920a189aa4b9bf45

pic:
  prefix-url: https://ygf.xzzfj.jinhua.gov.cn/prod-api/sysUploadFile/downloadLocalFile?path=


aly:
  accessKeyId: p9obSmvBKnQD5jOq
  accessKeySecret: vFJ6G5B7eu9hsy0dqFTlPeogiDTFaW
  url: http://oss1e52-cn-jinhua-jhzwy-d01-a.inner.jhszwy.net
ajhProxyPath: https://ww:aijinhua:cn/ajhApplet

zyy:
  url: https://iccs.pointlinkprox.com:10008/

#微信小程序
wx:
  appid: wx4db99eceebb39b63
  appSecret: 8e0b5c9ea47edb3fd2e2e4a05971da28
  #获取接口调用凭据
  getAccessTokenUrl: https://api.weixin.qq.com/cgi-bin/token
  accessTokenFromCodeUrl: https://api.weixin.qq.com/sns/oauth2/access_token
  userInfoFromAccessTokenUrl: https://api.weixin.qq.com/sns/userinfo

invoke:
  url: http://localhost:9000/prod-api
