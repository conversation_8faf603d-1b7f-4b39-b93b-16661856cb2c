package com.ruoyi.common.constant;

import java.util.Locale;
import io.jsonwebtoken.Claims;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants
{
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * 系统语言
     */
    public static final Locale DEFAULT_LOCALE = Locale.SIMPLIFIED_CHINESE;

    /**
     * www主域
     */
    public static final String WWW = "www.";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 所有权限标识
     */
    public static final String ALL_PERMISSION = "*:*:*";

    /**
     * 管理员角色权限标识
     */
    public static final String SUPER_ADMIN = "admin";

    /**
     * 角色权限分隔符
     */
    public static final String ROLE_DELIMETER = ",";

    /**
     * 权限标识分隔符
     */
    public static final String PERMISSION_DELIMETER = ",";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = Claims.SUBJECT;

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * RMI 远程方法调用
     */
    public static final String LOOKUP_RMI = "rmi:";

    /**
     * LDAP 远程方法调用
     */
    public static final String LOOKUP_LDAP = "ldap:";

    /**
     * LDAPS 远程方法调用
     */
    public static final String LOOKUP_LDAPS = "ldaps:";

    /**
     * 自动识别json对象白名单配置（仅允许解析的包名，范围越小越安全）
     */
    public static final String[] JSON_WHITELIST_STR = { "org.springframework", "com.ruoyi" };

    /**
     * 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
     */
    public static final String[] JOB_WHITELIST_STR = { "com.ruoyi.quartz.task" };

    /**
     * 定时任务违规的字符
     */
    public static final String[] JOB_ERROR_STR = { "java.net.URL", "javax.naming.InitialContext", "org.yaml.snakeyaml",
            "org.springframework", "org.apache", "com.ruoyi.common.utils.file", "com.ruoyi.common.config", "com.ruoyi.generator" };

    /**
     * 金华市政法委部门id
     */
    public static final Long JINHUA_CITY_DEPT_ID = 202L;

    /**
     * 磐安县政法委部门id
     */
    public static final Long PANAN_CITY_DEPT_ID = 213L;

    /**
     * 兰溪市政法委部门id
     */
    public static final Long LANXI_CITY_DEPT_ID = 214L;

    /**
     * 东阳市政法委部门id
     */
    public static final Long DONGYANG_CITY_DEPT_ID = 215L;

    /**
     * 义乌市政法委部门id
     */
    public static final Long YIWU_CITY_DEPT_ID = 216L;

    /**
     * 浦江市政法委部门id
     */
    public static final Long PUJIANG_CITY_DEPT_ID = 217L;

    /**
     * 永康市政法委部门id
     */
    public static final Long YONGKAN_CITY_DEPT_ID = 218L;

    /**
     * 金东政法委部门id
     */
    public static final Long JINDON_CITY_DEPT_ID = 219L;

    /**
     * 婺城政法委部门id
     */
    public static final Long WUCHENG_CITY_DEPT_ID = 220L;

    /**
     * 开发区政法委部门id
     */
    public static final Long KAIFA_CITY_DEPT_ID = 221L;

    /**
     * 武义政法委部门id
     */
    public static final Long WUYI_CITY_DEPT_ID = 262L;

    /**
     * 配置文件zzd1 应用的访问accessToken
     */
    public static final String DING_ACCESS_TOKEN = "ding_zzd:access_token";

    /**
     * 启用标识
     */
    public static final String START = "0";

    /**
     * 禁用标识
     */
    public static final String STOP = "1";

    /**
     * 浙里办令牌前缀
     */
    public static final String LOGIN_ZLB_USER_KEY = "login_zlb_user_key";

    /**
     * 浙里办登录用户 redis key
     */
    public static final String LOGIN_ZLB_TOKEN_KEY = "login_zlb_tokens:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    //浙政钉扫码登录
    public static final String DING_SDN_DINGOA = "jhscsdn_dingoa";
    public static final String ZYY_TOKEN = "zyy_token";

    //zzd-浙政钉应用免登登录
    public static final String DING_JINHUACSDN = "jinhuacsdn";

}
