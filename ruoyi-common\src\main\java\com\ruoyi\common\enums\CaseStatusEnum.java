package com.ruoyi.common.enums;

/**
 * 案件状态
 */
public enum CaseStatusEnum {

    STAY_DISTRIBUTE("0", "待分配"),
    STAY_HANDLE("1", "待处理"),
    HANDLE_ING("2", "处理中"),
    SQUADRON_LEADER("3", "下发组长"),
    MEMBER("4", "下发队员"),
    OUR_FOR_CASE("5", "队员出警"),
    ARRIVE("6", "到达现场"),
    FEEDBACK("7", "警情反馈"),
    FINISH("9", "已办结");

    private String type;
    private String desc;

    CaseStatusEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
