package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.InstructionPersonControl;

import java.util.List;

/**
 * 重点人员-管控人员信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-21
 */
@DataSource(value = DataSourceType.SLAVE)
public interface InstructionPersonControlMapper 
{
    /**
     * 查询重点人员-管控人员信息
     * 
     * @param id 重点人员-管控人员信息主键
     * @return 重点人员-管控人员信息
     */
    public InstructionPersonControl selectInstructionPersonControlById(Long id);

    /**
     * 查询重点人员-管控人员信息列表
     * 
     * @param instructionPersonControl 重点人员-管控人员信息
     * @return 重点人员-管控人员信息集合
     */
    public List<InstructionPersonControl> selectInstructionPersonControlList(InstructionPersonControl instructionPersonControl);

    /**
     * 新增重点人员-管控人员信息
     * 
     * @param instructionPersonControl 重点人员-管控人员信息
     * @return 结果
     */
    public int insertInstructionPersonControl(InstructionPersonControl instructionPersonControl);

    /**
     * 修改重点人员-管控人员信息
     * 
     * @param instructionPersonControl 重点人员-管控人员信息
     * @return 结果
     */
    public int updateInstructionPersonControl(InstructionPersonControl instructionPersonControl);

    /**
     * 删除重点人员-管控人员信息
     * 
     * @param id 重点人员-管控人员信息主键
     * @return 结果
     */
    public int deleteInstructionPersonControlById(Long id);

    /**
     * 批量删除重点人员-管控人员信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionPersonControlByIds(Long[] ids);
}
