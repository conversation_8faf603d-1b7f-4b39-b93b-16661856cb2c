package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.UndercoverInspection;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 暗访督察Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-23
 */
@Mapper
@DataSource(value = DataSourceType.SLAVE)
public interface UndercoverInspectionMapper 
{
    /**
     * 查询暗访督察
     * 
     * @param id 暗访督察主键
     * @return 暗访督察
     */
    public UndercoverInspection selectUndercoverInspectionById(Long id);

    /**
     * 查询暗访督察列表
     * 
     * @param undercoverInspection 暗访督察
     * @return 暗访督察集合
     */
    public List<UndercoverInspection> selectUndercoverInspectionList(UndercoverInspection undercoverInspection);

    /**
     * 新增暗访督察
     * 
     * @param undercoverInspection 暗访督察
     * @return 结果
     */
    public int insertUndercoverInspection(UndercoverInspection undercoverInspection);

    /**
     * 修改暗访督察
     * 
     * @param undercoverInspection 暗访督察
     * @return 结果
     */
    public int updateUndercoverInspection(UndercoverInspection undercoverInspection);

    /**
     * 删除暗访督察
     * 
     * @param id 暗访督察主键
     * @return 结果
     */
    public int deleteUndercoverInspectionById(Long id);

    /**
     * 批量删除暗访督察
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUndercoverInspectionByIds(Long[] ids);
}
