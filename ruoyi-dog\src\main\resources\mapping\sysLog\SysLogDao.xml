<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.sysLog.dao.SysLogDao">


    <sql id="columns">
        <trim suffixOverrides=",">
            a.id as id ,
            a.type as type ,a.ip as ip,
            a.platform as platform ,
            a.create_date as createDate ,
            a.create_by as createBy ,
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.sysLog.entity.SysLog"
            resultType="com.ruoyi.modules.sysLog.entity.SysLog">
        select
        <include refid="columns"/>
        from sys_log a
        where a.id =#{id}
    </select>
    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.sysLog.entity.SysLog"
            resultType="com.ruoyi.modules.sysLog.entity.SysLog">
        select
        <include refid="columns"/>
        from sys_log a
        where 1 =1
        <if test="bDate!=null and eDate!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bDate},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eDate},'%Y-%m-%d')
        </if>
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="platform != null and platform != ''">
            and a.platform = #{platform}
        </if>
        <if test="createDate != null">
            and a.create_date = #{createDate}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by in (select id from sys_user where user_name like concat('%',#{createBy},'%'))
        </if>

        order by a.create_date desc
    </select>


    <!--新增所有列-->
    <insert id="insert">
        insert into sys_log(id, type, platform, ip, create_date, create_by)
        values (#{id}, #{type}, #{platform}, #{ip}, #{createDate}, #{createBy})
    </insert>


    <!--通过主键修改数据-->
    <update id="updateByEntity">
        update sys_log
        <set>
            <trim suffixOverrides=",">
                <if test="type != null and type != ''">
                    type = #{type},
                </if>
                <if test="platform != null and platform != ''">
                    platform = #{platform},
                </if>
                <if test="createDate != null">
                    create_date = #{createDate},
                </if>
                <if test="createBy != null and createBy != ''">
                    create_by = #{createBy},
                </if>
            </trim>
        </set>
        where id = #{id}
    </update>
    <update id="update">
        update sys_log
        set
        <trim suffixOverrides=",">

            type = #{type},

            platform = #{platform},

            create_date = #{createDate},

            create_by = #{createBy},
        </trim>
        where id = #{id}
    </update>
    <select id="getVisitsNum" parameterType="com.ruoyi.modules.sysLog.entity.SysLog" resultType="java.lang.Integer">
        select count(id)
        from sys_log
        where DATE_FORMAT(create_date, '%Y-%m-%d') = DATE_FORMAT(now(), '%Y-%m-%d') and type=1
    </select>
    <select id="getVisitsList" parameterType="com.ruoyi.modules.sysLog.entity.SysLog" resultType="java.util.HashMap">
        select DATE_FORMAT(create_date, '%Y-%m-%d') date,
(select count(id) from sys_log where DATE_FORMAT(create_date, '%Y-%m-%d')=DATE_FORMAT(l.create_date, '%Y-%m-%d') and platform=1) as pc,
(select count(id) from sys_log where DATE_FORMAT(create_date, '%Y-%m-%d')=DATE_FORMAT(l.create_date, '%Y-%m-%d') and platform=2) as zlb,
(select count(id) from sys_log where DATE_FORMAT(create_date, '%Y-%m-%d')=DATE_FORMAT(l.create_date, '%Y-%m-%d') and platform=3) as zzd
        from sys_log l
        where type =1
        <if test="bDate!=null and eDate!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bDate},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eDate},'%Y-%m-%d')
        </if>
        group by date
        order by date desc
    </select>
</mapper>

