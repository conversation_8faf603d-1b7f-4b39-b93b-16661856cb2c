package com.ruoyi.zhcg.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.zhcg.domain.Notice;
import com.ruoyi.zhcg.service.INoticePublishService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 通知发布Controller
 */
@RestController
@RequestMapping("/zhcg/notice")
public class NoticePublishController extends BaseController {
    @Autowired
    private INoticePublishService noticeService;

    /**
     * 查询通知列表
     */
//    @PreAuthorize("@ss.hasPermi('zhcg:notice:list')")
    @GetMapping("/list")
    public TableDataInfo list(Notice notice) {
        startPage();
        List<Notice> list = noticeService.selectNoticeList(notice);
        return getDataTable(list);
    }

    /**
     * 导出通知列表
     */
//    @PreAuthorize("@ss.hasPermi('zhcg:notice:export')")
    @Log(title = "通知发布", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Notice notice) {
        List<Notice> list = noticeService.selectNoticeList(notice);
        ExcelUtil<Notice> util = new ExcelUtil<Notice>(Notice.class);
        util.exportExcel(response, list, "通知发布数据");
    }

    /**
     * 获取通知详细信息
     */
//    @PreAuthorize("@ss.hasPermi('zhcg:notice:query')")
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable("noticeId") Long noticeId) {
        return success(noticeService.selectNoticeById(noticeId));
    }

    /**
     * 新增通知
     */
//    @PreAuthorize("@ss.hasPermi('zhcg:notice:add')")
    @Log(title = "通知发布", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Notice notice) {
        return toAjax(noticeService.insertNotice(notice));
    }

    /**
     * 修改通知
     */
//    @PreAuthorize("@ss.hasPermi('zhcg:notice:edit')")
    @Log(title = "通知发布", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Notice notice) {
        return toAjax(noticeService.updateNotice(notice));
    }

    /**
     * 删除通知
     */
//    @PreAuthorize("@ss.hasPermi('zhcg:notice:remove')")
    @Log(title = "通知发布", businessType = BusinessType.DELETE)
    @PutMapping("/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds) {
        return toAjax(noticeService.deleteNoticeByIds(noticeIds));
    }

    /**
     * 审核通知
     */
//    @PreAuthorize("@ss.hasPermi('zhcg:notice:audit')")
    @Log(title = "通知发布", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody Notice notice) {
        return toAjax(noticeService.auditNotice(notice));
    }
}
