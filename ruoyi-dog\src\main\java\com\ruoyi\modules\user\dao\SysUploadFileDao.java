package com.ruoyi.modules.user.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.user.entity.SysUploadFile;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by Administrator on 2021-3-11.
 */
@Repository
public interface SysUploadFileDao extends BaseDao<SysUploadFile> {

    /**
     * 批量保存方法
     * @param list
     */
    public void saveAllList(List<SysUploadFile> list);

    /**
     * 根据业务id删除相关附件
     * @param
     */
    public void delByInstanceAndModel(SysUploadFile sysUploadFile);

    List<SysUploadFile> listByInstanceId(@Param("instanceIdList") List<String> instanceIdList);

    void deleteByInstanceIdAndTypeList(@Param("id") String id, @Param("typeList") List<String> typeList);
}
