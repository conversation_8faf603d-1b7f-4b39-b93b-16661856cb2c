package com.ruoyi.modules.user.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.user.entity.SysUser;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 * Created by Administrator on 2020/1/3/003.
 */
@Repository
public interface SysUserDao extends BaseDao<SysUser>{

    public SysUser getByUserName(String userName);

    void editPass(SysUser sysUser);

    public void saveList(List<SysUser> list);

    public void updatePassWord(SysUser sysUser);

    public SysUser getUserInfo(String petIdCard);

    public List<SysUser> queryUserAddres();


    public void updateUserPassword(SysUser user);

    /**
     * @author: tongsiyu
     * @date: 2022/12/13 15:51
     * @Description:根据区县获取饲主数量
     */
    public List<HashMap> getEchatByDept();

}
