package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 指令接收对象 t_instruction_receive
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
@EqualsAndHashCode(callSuper = true)
public class JazzInstructionReceive extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 接收表主键id */
    private Long id;

    /** 接收部门 */
    @Excel(name = "接收部门")
    private String receiveDept;

    /** 接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "接收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    /** 是否接收 1:是 2：否 */
    @Excel(name = "是否接收 1:是 2：否")
    private Integer isReceive;

    /** 接收人 */
    @Excel(name = "接收人")
    private String receiveBy;

    /** 指令id */
    @Excel(name = "指令id")
    private Long instrucationId;

    /** 1:正常 9：删除 */
    @Excel(name = "1:正常 9：删除")
    private Integer status;

    /**
     * 转交部门实体类
     */
    private List<JazzInstructionTransfer> transferList;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setReceiveDept(String receiveDept) 
    {
        this.receiveDept = receiveDept;
    }

    public String getReceiveDept() 
    {
        return receiveDept;
    }
    public void setReceiveTime(Date receiveTime) 
    {
        this.receiveTime = receiveTime;
    }

    public Date getReceiveTime() 
    {
        return receiveTime;
    }
    public void setIsReceive(Integer isReceive) 
    {
        this.isReceive = isReceive;
    }

    public Integer getIsReceive() 
    {
        return isReceive;
    }
    public void setReceiveBy(String receiveBy) 
    {
        this.receiveBy = receiveBy;
    }

    public String getReceiveBy() 
    {
        return receiveBy;
    }
    public void setInstrucationId(Long instrucationId) 
    {
        this.instrucationId = instrucationId;
    }

    public Long getInstrucationId() 
    {
        return instrucationId;
    }

    public List<JazzInstructionTransfer> getTransferList() {
        return transferList;
    }

    public void setTransferList(List<JazzInstructionTransfer> transferList) {
        this.transferList = transferList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(final Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("receiveDept", getReceiveDept())
            .append("receiveTime", getReceiveTime())
            .append("isReceive", getIsReceive())
            .append("receiveBy", getReceiveBy())
            .append("instrucationId", getInstrucationId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .toString();
    }
}
