package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 督办单对象 t_instruction_handle
 * 
 * <AUTHOR>
 * @date 2023-05-17
 */
@Data
public class InstructionHandle extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 督办id */
    private Long id;

    /** 督办名称 */
    @Excel(name = "督办名称")
    private String handle;

    /** 接收单位ids */
    @Excel(name = "接收单位ids")
    private String receiveUnitIds;

    /** 接收单位名称 */
    @Excel(name = "接收单位名称")
    private String receuveUnit;

    /** 督办单位 */
    @Excel(name = "督办单位")
    private String handleUnit;

    /** 是否接收 1：已接收 2：未接收 */
    @Excel(name = "是否接收 1：已接收 2：未接收")
    private String isReceive;

    /** 反馈状态 1：已反馈 2：未反馈 */
    @Excel(name = "反馈状态 1：已反馈 2：未反馈")
    private String feedbackStatus;

    /** 督办内容 */
    @Excel(name = "督办内容")
    private String handleContent;

    /** 反馈内容 */
    @Excel(name = "反馈内容")
    private String feedbackContent;

    /** 状态1：正常 9：删除 */
    @Excel(name = "状态1：正常 9：删除")
    private String status;

    /** 依据信息 */
    @Excel(name = "依据信息")
    private String info;

    /** 编号信息 */
    @Excel(name = "编号信息")
    private String numberInfo;

    /** 下发单位 */
    @Excel(name = "下发单位")
    private String issueUnit;

    /** 指令id */
    @Excel(name = "指令id")
    private Long instructionId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "反馈期限", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deadline;

    /** 创建部门id */
    @Excel(name = "创建部门id")
    private Long createDeptId;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactTel;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /**
     * 接收单位 接收单位id
     */
    private List<Map<String,Object>> maps;

}
