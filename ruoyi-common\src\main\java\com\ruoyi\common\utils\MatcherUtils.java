package com.ruoyi.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description 正则工具
 * @date 2021-08-23 10:45
 */
public class MatcherUtils {
    /**
     * 验证输入手机号码
     */
    public static boolean isMobile(String str) {
        String regex = "^[1]+\\d{10}$";
        return match(regex, str);
    }

    /**
     * 验证输入身份证号
     */
    public static boolean isIDcard(String str) {
        String regex = "(^\\d{18}$)|(^\\d{15}$)";
        return match(regex, str);
    }

    private static boolean match(String regex, String str) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }
}
