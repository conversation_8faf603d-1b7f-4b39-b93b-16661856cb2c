<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.immune.dao.ImmuneTransferDao">
    <sql id="ImmuneTransferSql">
        a.id as "id",
        a.pet_id as "petId",
        a.new_id_card as "newIdCard",
        a.new_name as "newName",
        a.new_address as "newAddress",
        a.new_tel as "newTel",
        a.old_id_card as "oldIdCard",
        a.old_name as "oldName",
        a.old_address as "oldAddress",
        a.old_tel as "oldTel",
        a.status as "status",
        a.reason as "reason",
        a.img as "img"
    </sql>

    <select id="getList" parameterType="com.ruoyi.modules.immune.entity.ImmuneTransfer" resultType="com.ruoyi.modules.immune.entity.ImmuneTransfer">
        select
        <include refid="ImmuneTransferSql" />
        from immune_Transfer a
        where del_flag = 1
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        order by  a.create_date desc
    </select>

    <select id="getById" resultType="com.ruoyi.modules.immune.entity.ImmuneTransfer">
        SELECT
        <include refid="ImmuneTransferSql"/>
        FROM  immune_transfer a
        where a.pet_id = #{id}
    </select>

    <select id="getPetTransfer" resultType="com.ruoyi.modules.immune.entity.ImmuneTransfer">
        SELECT
        <include refid="ImmuneTransferSql"/>
        FROM immune_transfer a where id =  #{transferId} and a.pet_id = #{petId}
    </select>

    <insert id="insert" parameterType="com.ruoyi.modules.immune.entity.ImmuneTransfer">
        insert into immune_Transfer(
            id,
            pet_id,
            new_id_card,
            new_name,
            new_address,
            new_tel,
            old_id_card,
            old_name,
            old_address,
            old_tel,
            status,
            reason,
            create_date,
            create_by,
            del_flag,
            img
        )values (
                    #{id},
                    #{petId},
                    #{newIdCard},
                    #{newName},
                    #{newAddress},
                    #{newTel},
                    #{oldIdCard},
                    #{oldName},
                    #{oldAddress},
                    #{oldTel},
                    #{status},
                    #{reason},
                    #{createDate},
                    #{createBy},
                    #{delFlag},
                    #{img}
                )
    </insert>

    <update id="update" parameterType="com.ruoyi.modules.immune.entity.ImmuneTransfer">
        update immune_Transfer set
                                   pet_id = #{petId},
                                   new_id_card = #{newIdCard},
                                   new_name = #{newName},
                                   new_address = #{newAddress},
                                   new_tel = #{newTel},
                                   old_id_card = #{oldIdCard},
                                   old_name = #{oldName},
                                   old_address = #{oldAddress},
                                   old_tel = #{oldTel},
                                   status = #{status},
                                   reason = #{reason},
                                   update_date = #{updateDate},
                                   update_by = #{updateBy},
                                   img = #{img}
        where id = #{id}
    </update>

    <update id="delete" parameterType="com.ruoyi.modules.immune.entity.ImmuneTransfer">
        update immune_Transfer set  del_flag = #{delFlag}  where id = #{id}
    </update>


    <update id="updateStatus" parameterType="com.ruoyi.modules.immune.entity.ImmuneTransfer">
        update immune_Transfer set status = #{status},reason = #{reason} where id = #{id}
    </update>


    <resultMap id="petMap" type="com.ruoyi.modules.certificates.entity.PetCertificates">
        <id property="id" column="id"/>
        <result property="ownerName" column="owner_name"/>
        <result property="petIdCard" column="pet_id_card"/>
        <result property="tel" column="tel"/>
        <result property="ownerAddress" column="owner_address"/>
        <result property="petName" column="pet_name"/>
        <result property="petNum" column="pet_num"/>
        <result property="petType" column="pet_type"/>
        <result property="petSex" column="pet_sex"/>
        <result property="petVarieties" column="pet_varieties"/>
        <result property="petHair" column="pet_hair"/>
        <result property="petAge" column="pet_age"/>
        <result property="raiseDate" column="raise_date"/>
        <result property="petAddress" column="pet_address"/>
        <result property="petDept" column="pet_dept"/>
        <result property="isAgency" column="is_agency"/>
        <result property="agencyCom" column="agency_com"/>
        <result property="source" column="source"/>
        <result property="status" column="status"/>
        <result property="immuneId" column="immune_id"/>
        <result property="transferId" column="transfer_id"/>
        <result property="cancelId" column="cancel_id"/>
        <result property="endDate" column="end_date"/>
        <result property="aboutMake" column="about_make"/>
        <result property="aboutDate" column="about_date"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="petCount" column="petCount"/>
        <result property="petImg" column="pet_img"/>
        <collection property="immuneTransfer" ofType="com.ruoyi.modules.immune.entity.ImmuneTransfer">
            <id property="id" column="transferId"/>
            <result property="newIdCard" column="new_id_card"/>
            <result property="newName" column="new_name"/>
            <result property="newAddress" column="new_address"/>
            <result property="newTel" column="new_tel"/>
            <result property="oldIdCard" column="old_id_card"/>
            <result property="oldName" column="old_name"/>
            <result property="oldAddress" column="old_address"/>
            <result property="oldTel" column="old_tel"/>
            <result property="status" column="transferStatus"/>
            <result property="reason" column="reason"/>
            <result property="petId" column="pet_id"/>
        </collection>
    </resultMap>

    <select id="queryPageList" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates"  resultMap="petMap">
        select  a.*,
        fa.new_id_card, fa.new_name, fa.new_address, fa.new_tel, fa.old_id_card, fa.old_name,
        fa.old_address, fa.old_tel, fa.status as transferStatus,fa.id as transferId
        from pet_certificates a
        LEFT JOIN
        immune_transfer fa ON fa.id = a.transfer_id
        where a.del_flag = 1
        <if test="petIdCard != null and petIdCard != ''">
            and a.pet_id_card = #{petIdCard}
        </if>
        <if test="petIds != null and petIds.size()>0">
            AND a.id in
            <foreach item="id" collection="petIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="transferStatus != null and transferStatus != ''">
            and IFNULL(fa.status,0) = #{transferStatus}
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            and a.hospital_id = #{hospitalId}
        </if>
        <if test="petDept != null and petDept != ''">
            and a.pet_dept =  #{petDept}
        </if>
        <if test="tel != null and tel != ''">
            and a.tel like  concat('%',#{tel,jdbcType=VARCHAR},'%')
        </if>
        <if test="petName != null and petName != ''">
            and a.pet_name LIKE concat("%", #{petName}, "%")
        </if>
        <if test="ownerName != null and ownerName != ''">
            and a.owner_name LIKE concat("%", #{ownerName}, "%")
        </if>
        <if test="petNum != null and petNum != ''">
            and a.pet_num like concat('%',#{petNum},'%')
        </if>
        <if test='searchType != null and searchType == "1"'>
            and fa.id is not null
        </if>
        <if test='searchType != null and searchType == "2"'>
            and fa.id is null
        </if>
        <if test='searchType != null and searchType == "3"'>
            and fa.status != 2
        </if>
        order by a.create_date desc
    </select>

    <select id="fingPetNum" parameterType="java.lang.String" resultType="java.lang.String">
        select max(pet_num) from  pet_certificates  where del_flag = 1 and pet_id_card = #{petIdCard}
    </select>
</mapper>
