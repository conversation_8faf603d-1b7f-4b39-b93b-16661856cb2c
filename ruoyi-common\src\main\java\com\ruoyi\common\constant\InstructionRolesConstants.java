package com.ruoyi.common.constant;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/19 16:41
 * 指令角色类
 */
public class InstructionRolesConstants {

    /**
     * 指令-交办员
     */
    public static final String ASSIGN = "assign";

    /**
     * 指令-接收员
     */
    public static final String RECEIVE = "receive";

    /**
     * 指令-转交员
     */
    public static final String TRANSFER = "transfer";

    /**
     * 指令-处置员
     */
    public static final String DISPOSE = "dispose";

    /**
     * 指令-反馈员
     */
    public static final String FEEDBACK = "feedback";

    /**
     * 指令-销号员
     */
    public static final String END = "end";

    /**
     * 超级管理员
     */
    public static final String ADMIN = "admin";

    /**
     * 市值班长
     */
    public static final String SZBZ = "szbz";

    /**
     * 区值班长
     */
    public static final String ZHCG_QZBZ = "zhcg-qzbz";

    /**
     * 市受理员
     */
    public static final String ZHCG_SLY = "zhcg-sly";

    /**
     * 区受理员
     */
    public static final String ZHCG_QSLY = "zhcg-qsly";

    /**
     * 专业部门
     */
    public static final String ZYBM = "zhcg-zybm";

    /**
     * 专业部门id
     */
    public static final Long ZYBM_ID = 114L;
    /**
     * 牛皮藓上报人员
     */
    public static final String NPX_SBRY = "npx_sbry";
    /**
     * 牛皮藓县市区执法人员
     */
    public static final String NPX_XSQ = "npx_xsq";
    /**
     * 牛皮藓市执法人员
     */
    public static final String NPX_SZFRY = "npx_szfry";
    /**
     * 牛皮藓运营商
     */
    public static final String NPX_YYS = "npx_yys";
    /**
     * 市牛皮藓员
     */
    public static final String SNPXY = "snpxy";

    /**
     * 智慧城管-市受理员部门id
     */
    public static final Long ZHCG_SLY_DEPT_ID = 202L;

    /**
     * 智慧城管监督员角色
     */
    public static final String ZHCG_JDY = "zhcg-jdy";

    /**
     * 市/区 监督员展示菜单
     */
    public static final String ZHCG_JDY_MENU = "问题上报,待办任务,已办任务,工作统计,草稿箱,专项整治";

    /**
     * 智慧城管-专业部门展示菜单
     */
    public static final String ZHCG_ZYBM_MENU = "待处置任务,已办任务,工作统计,牛皮癣";

    /**
     * 协同工作——管理员
     */
    public static final String XTGZ_GLY = "collaborativeAdmin";

    /**
     * 协同工作——处置人员
     */
    public static final String XTGZ_CZRY = "collaborativePerson";
    /**
     * 案件回访人员
     */
    public static final String AJHFRY = "ajhfry";

    /**
     * 站前app
     */
    public static final String ZQZFJ_ROLE = "11";

    /**
     * 全权限
     */
    public static final String ALL_ROLE = "all";

    /**
     * 超级管理员
     */
    public static final String ADMIN_ROLE = "ADMIN_ROLE";

    /**
     * 管理员权限列表
     */
    public static final List<String> ADMIN_ROLE_LIST = Lists.newArrayList("admin", "all");

    /**
     * 犬类角色
     */
    public static final List<String> DOG_ROLE_LIST = Lists.newArrayList("enforcePerson", "dogFactory", "dogCompany",
            "dogAdmin", "enforceBureau", "enforceWindow",
            "dogConfirm", "petHospital");

    /**
     * 执法局角色
     */
    public static final List<String> ENFORCE_ROLE_LIST = Lists.newArrayList("enforcePerson", "dogAdmin",
            "enforceBureau", "enforceWindow");

    /**
     * 宠物医院角色
     */
    public static final String HOSPITAL_ROLE = "petHospital";

    /**
     * 路段检查员
     */
    public static final String ROAD_CHECK_ROLE = "roadcheck";

    /**
     * 路段审查员
     */
    public static final String ROAD_CENSOR_ROLE = "roadcensor";

    /**
     * 犬类区县执法局
     */
    public static final String DOG_COUNTY_CONFIRM = "dogConfirm";

    /**
     * 犬类区县执法局
     */
    public static final String DOG_CITY_CONFIRM = "dogZfj";
    /**
     * 收容所
     */
    public static final String DOG_QLXT_SRS= "qlxt_srs";



}
