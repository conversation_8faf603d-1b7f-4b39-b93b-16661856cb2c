package com.ruoyi.modules.hospital.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.modules.hospital.dao.QualifiDao;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.hospital.service.QualifiService;
import com.ruoyi.modules.vaccine.entity.Vaccine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @version: 1.0
 **/
@RestController
@RequestMapping("hospital")
public class QualifiController {
    @Autowired
    private QualifiService service;

    @Autowired
    private QualifiDao qualifiDao;

    /**
     * 校验资质
     * @return
     */
    @GetMapping("checkQualifi")
    public AjaxResult checkQualifi() {
        return AjaxResult.success(service.checkQualifi());
    }

    /**
     * 获取当前资质
     * @return
     */
    @GetMapping("getCurrentQualifi")
    public AjaxResult getCurrentQualifi() {
        Qualifi currentQualifi = service.getCurrentQualifi();

        return AjaxResult.success(currentQualifi);
    }

    /**
     * 分页
     * @param qualifi
     * @return
     */
    @RequestMapping("getPageList")
    public AjaxResult getPageList(Qualifi qualifi) {
        return AjaxResult.success(service.getPageList(qualifi));
    }

    @RequestMapping("getList")
    public AjaxResult getList(Qualifi qualifi) {
        qualifi.setPageNum(null);
        qualifi.setPageSize(null);
        return AjaxResult.success(qualifiDao.getList(qualifi));
    }

    /**
     * 新增/更新
     * @param qualifi
     * @return
     */
    @PostMapping("saveOrUpdate")
    public AjaxResult saveOrUpdate(@RequestBody Qualifi qualifi){
        return AjaxResult.success(service.save(qualifi));
    }

    @RequestMapping("delete")
    public AjaxResult delete(Qualifi qualifi){
        service.delete(qualifi);
        return AjaxResult.success();
    }

    /**
     * 详情
     * @param id
     * @return
     */
    @RequestMapping("getById")
    public AjaxResult getById(String id){
        return  AjaxResult.success(service.getById(id));
    }

    /**
     * 审核
     * @param qualifi
     * @return
     */
    @PostMapping("updateStatus")
    public AjaxResult updateStatus(@RequestBody Qualifi qualifi){
        service.updateStatus(qualifi);
        return AjaxResult.success();
    }

    @PostMapping("createAccount")
    public AjaxResult createAccount(@RequestBody Qualifi qualifi){
        service.createAccount(qualifi);
        return AjaxResult.success();
    }

    @RequestMapping("getQualifiByAccount")
    public AjaxResult getQualifiByAccount(Qualifi qualifi){
        return AjaxResult.success(service.getQualifiByAccount(qualifi));
    }

    @RequestMapping("/getAllList")
    public AjaxResult getAllList(Qualifi qualifi) {
        qualifi.setPageNum(null);
        qualifi.setPageSize(null);
        return AjaxResult.success(service.getAllList(qualifi));
    }

    @RequestMapping("/getApply")
    public AjaxResult getApply(SysUser sysUser) {
        return AjaxResult.success(service.getApply(sysUser));
    }

    /**
     * 获取所有疫苗
     * @return
     */
    @GetMapping("/getAllVaccine")
    public AjaxResult getAllVaccine() {
        return AjaxResult.success(service.getAllVaccine());
    }
}
