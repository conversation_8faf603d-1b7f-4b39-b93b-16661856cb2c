/**
 *
 */
package com.ruoyi.util;

import com.google.zxing.*;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.common.HybridBinarizer;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Map;

/**
 * 利用zxing开源工具生成二维码QRCode
 *
 * @date 2012-10-26
 * <AUTHOR>
 *
 */
public class QRCode {
    private static final int BLACK = 0xff000000;
    private static final int WHITE = 0xFFFFFFFF;

    /**
     * @param urlContent  根据二维码内容（带参数的URL） 生成二维码图片并生成七牛云图片
     * @return 返回七牛云图片地址
     */
    public static String createQrImg(String urlContent) {
        try {
            //设置二维码边框
            Map<EncodeHintType, Object> hints = new HashMap<EncodeHintType, Object>();
            hints.put(EncodeHintType.MARGIN, 1); //二维码边框 1
            BitMatrix bitMatrix = new MultiFormatWriter().encode(urlContent, BarcodeFormat.QR_CODE, 300, 300, hints);
            BufferedImage image = toBufferedImage(bitMatrix);
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(image, "png", os);
            InputStream input = new ByteArrayInputStream(os.toByteArray());
            String key = "dog/qrcode/" + IdGen.uuid();
            MultipartFile multipartFile = new MockMultipartFile(key, key + ".png", "text/plain", input);

            //QiniuFileUtils.uploadByBytes(os.toByteArray(), key, true);
            //String filePath = QiniuFileUtils.generatePublicURL(key);
            String filePath = UploadLocalUtil.uploadFile(multipartFile);
            return filePath;
        } catch (WriterException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 犬牌二维码输出到流
     * @param urlContent
     */
    public static void initQrImgToOs(String urlContent,OutputStream os){
        try {
            //设置二维码边框
            Map<EncodeHintType, Object> hints = new HashMap<EncodeHintType, Object>();
            hints.put(EncodeHintType.MARGIN, 1); //二维码边框 1
            BitMatrix bitMatrix = new MultiFormatWriter().encode(urlContent, BarcodeFormat.QR_CODE, 300, 300, hints);
            BufferedImage image = toBufferedImage(bitMatrix);
            ImageIO.write(image, "png", os);
        } catch (WriterException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * @param args
     */
    public static void main(String[] args) {
        try {
                String filePath = "D://问题修改.txt";
                FileInputStream fileInputStream = new FileInputStream(filePath);
             InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream, "UTF-8");
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

            String line = null;
            Integer i = 0;
            StringBuffer sb = new StringBuffer();
            while ((line = bufferedReader.readLine()) != null) {
                String[] t = line.split("\t");
                String id = t[0];
                String path = createQrImg("https://www.jinhuadog.com/pet?num="+t[1]);
                sb.append(id + "   "+ path + "\r\n");
                i++;
                System.out.println(i);
            }

            File f=new File("D:\\fileresult.txt");
            FileOutputStream fos=null;
            try {
                if(!f.exists()){
                    f.createNewFile();
                }
                fos=new FileOutputStream(f);
                String content=sb.toString();
                fos.write(content.getBytes());
            } catch (IOException e) {
                e.printStackTrace();
            }finally{
                if(fos!=null){
                    try {
                        fos.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();

        }

//        System.out.println(createQrImg("https://www.jinhuadog.com/pet?num=NO WC005227"));


//		QRCode test = new QRCode();
//		String filePostfix="png";
//		File file = new File("D://test_QR_CODE1."+filePostfix);
//		test.encode("http://admin.chinau8.com", file,filePostfix, BarcodeFormat.QR_CODE, 300, 300, null);
//		test.decode(file);
    }

    /**
     *  生成QRCode二维码<br>
     *  在编码时需要将com.google.zxing.qrcode.encoder.Encoder.java中的<br>
     *  static final String DEFAULT_BYTE_MODE_ENCODING = "ISO8859-1";<br>
     *  修改为UTF-8，否则中文编译后解析不了<br>
     * @param contents 二维码的内容
     * @param file 二维码保存的路径，如：C://test_QR_CODE.png
     * @param filePostfix 生成二维码图片的格式：png,jpeg,gif等格式
     * @param format qrcode码的生成格式
     * @param width 图片宽度
     * @param height 图片高度
     * @param hints
     */
    public void encode(String contents, File file, String filePostfix, BarcodeFormat format, int width, int height, Map<EncodeHintType, ?> hints) {
        try {
            BitMatrix bitMatrix = new MultiFormatWriter().encode(contents, format, width, height);
            writeToFile(bitMatrix, filePostfix, file);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 生成二维码图片<br>
     *
     * @param matrix
     * @param format
     *            图片格式
     * @param file
     *            生成二维码图片位置
     * @throws IOException
     */
    public static void writeToFile(BitMatrix matrix, String format, File file) throws IOException {
        BufferedImage image = toBufferedImage(matrix);
        ImageIO.write(image, format, file);
    }

    /**
     * 生成二维码内容<br>
     *
     * @param matrix
     * @return
     */
    public static BufferedImage toBufferedImage(BitMatrix matrix) {
        int width = matrix.getWidth();
        int height = matrix.getHeight();
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, matrix.get(x, y) == true ? BLACK : WHITE);
            }
        }
        return image;
    }

    /**
     * 解析QRCode二维码
     */
    //@SuppressWarnings("unchecked")
    public static String  decode(File file) {
        try {
            BufferedImage image;
            try {
                image = ImageIO.read(file);
                if (image == null) {
                    System.out.println("Could not decode image");
                }
                LuminanceSource source = new BufferedImageLuminanceSource(image);
                BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));
                Result result;
                @SuppressWarnings("rawtypes")
                Hashtable hints = new Hashtable();
                //解码设置编码方式为：utf-8
                hints.put(DecodeHintType.CHARACTER_SET, "utf-8");
                result = new MultiFormatReader().decode(bitmap, hints);
                String resultStr = result.getText();
                System.out.println("解析后内容：" + resultStr);
                return resultStr;
            } catch (IOException ioe) {
                System.out.println(ioe.toString());
            } catch (ReaderException re) {
                System.out.println(re.toString());
            }
        } catch (Exception ex) {
            System.out.println(ex.toString());
        }
        return null;
    }
}
