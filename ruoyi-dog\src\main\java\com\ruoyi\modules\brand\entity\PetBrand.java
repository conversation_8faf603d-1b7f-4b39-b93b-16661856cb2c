package com.ruoyi.modules.brand.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;

import java.io.File;
import java.util.Date;

public class PetBrand extends BaseEntity {
private String applyId;//当审批角色分配犬牌时，绑定申请id，避免重复选择
    private String brandNum;            //犬牌编号
    private String area;                //所在区
    private String street;              //所在街道
    private String county;              //所在县
    private String brandCity;           //犬牌归属区域
    private String brandCom;            //犬牌归属区域单位
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private Date offDate;               //注销时间
    private String remark;              //注销原因
    private String offCom;              //注销单位
    private Integer isCancellation;      //是否注销: 1未注销，2已注销,3已走失
    private Integer isRecovery;         //是否回收：1未回收，2已回收
    private Integer isUse;              //使用情况：1使用期，2未使用
    private String qrCode;//二维码（已使用状态才会生成）
    private Integer num;                //申请犬牌数量
private Integer isReceipt; //是否收货 1未收货 2.已收货
    private String sNum;                //新增开始号段
    private String eNum;                //新增结束号段


    /**/
    private String brandComStr;//犬牌归属区域单位名称
    private File imgFile;

    public Integer getIsReceipt() {
        return isReceipt;
    }

    public void setIsReceipt(Integer isReceipt) {
        this.isReceipt = isReceipt;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public File getImgFile() {
        return imgFile;
    }

    public void setImgFile(File imgFile) {
        this.imgFile = imgFile;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public String getBrandComStr() {
        return brandComStr;
    }

    public void setBrandComStr(String brandComStr) {
        this.brandComStr = brandComStr;
    }

    public String getBrandNum() {
        return brandNum;
    }

    public void setBrandNum(String brandNum) {
        this.brandNum = brandNum;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getBrandCity() {
        return brandCity;
    }

    public void setBrandCity(String brandCity) {
        this.brandCity = brandCity;
    }

    public String getBrandCom() {
        return brandCom;
    }

    public void setBrandCom(String brandCom) {
        this.brandCom = brandCom;
    }

    public Date getOffDate() {
        return offDate;
    }

    public void setOffDate(Date offDate) {
        this.offDate = offDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOffCom() {
        return offCom;
    }

    public void setOffCom(String offCom) {
        this.offCom = offCom;
    }

    public Integer getIsCancellation() {
        return isCancellation;
    }

    public void setIsCancellation(Integer isCancellation) {
        this.isCancellation = isCancellation;
    }

    public Integer getIsRecovery() {
        return isRecovery;
    }

    public void setIsRecovery(Integer isRecovery) {
        this.isRecovery = isRecovery;
    }

    public Integer getIsUse() {
        return isUse;
    }

    public void setIsUse(Integer isUse) {
        this.isUse = isUse;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getsNum() {
        return sNum;
    }

    public void setsNum(String sNum) {
        this.sNum = sNum;
    }

    public String geteNum() {
        return eNum;
    }

    public void seteNum(String eNum) {
        this.eNum = eNum;
    }
}
