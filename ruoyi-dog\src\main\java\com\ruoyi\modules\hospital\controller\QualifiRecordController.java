package com.ruoyi.modules.hospital.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.hospital.entity.QualifiRecord;
import com.ruoyi.modules.hospital.service.QualifiRecordService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * 资质审批记录(qualifi_record)表控制层
 * <AUTHOR>
 */
@RestController
@RequestMapping("qualifiRecord")
public class QualifiRecordController {
    /**
     * 服务对象
     */
    @Resource
    private QualifiRecordService service;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("/getPageList")
    public AjaxResult getPageList(QualifiRecord entity) {
        return AjaxResult.success(service.getPageList(entity));
    }

    @RequestMapping("/getList")
    public AjaxResult getList(QualifiRecord entity) {
        return AjaxResult.success(service.getList(entity));
    }

    @PostMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(@RequestBody QualifiRecord entity) {
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }

    @PostMapping("/delete")
    public AjaxResult delete(@RequestBody QualifiRecord entity) {
        entity.setDelFlag(2);
        service.delete(entity);
        return AjaxResult.success();
    }

}
