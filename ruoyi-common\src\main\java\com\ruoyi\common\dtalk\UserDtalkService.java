package com.ruoyi.common.dtalk;

import com.ruoyi.common.dtalk.vo.*;

import java.util.List;

/**
 * <AUTHOR> yx-0176
 * @description 浙政钉对接工具类
 * @date : 2021/10/18
 */
public interface UserDtalkService {

    /**
     * 获取accesstoken
     *
     * @return
     */
    String getAccessToken();

    /**
     * 通过临时授权码获取用户信息
     *
     * @param code 临时授权码
     * @return
     */
    UserResp getDingtalkAppUser(String code);

    /**
     * 根据手机号获取浙政钉用户信息
     */
    MobileUserResp getDingtalkAppUserByMobile(String phone) throws Exception;

    /**
     * 根据accountId获取employeeCode
     */
    String getUserByAccountId(String accountId);

    /**
     * 根据employeeCode获取用户信息
     * @param employeeCode
     */
    UserEmployeeDTO getUserByEmployeeCode(String employeeCode);

    /**
     * 发起工作通知
     */
    boolean workNotification(String receiverIds, String bizMsgId,
                             String title, String content, String singleUrl, String singlePcUrl);

    /**
     * 分页查询部门下用户列表
     *
     * @return
     */
    List<EmployeeInfo> getOrganizationUserList(String organizationCode, Integer pageSize, Integer pageNum);

    /**
     * 查询当前部门人员列表
     *
     * @param pageNum          页码数
     * @param pageSize         每页数量
     * @param organizationCode 机构id
     * @param nameKeyword      姓名关键字
     * @return
     */
    List<DtalkEmployeeVo> pageSearchEmployee(Integer pageNum, Integer pageSize, String organizationCode, String nameKeyword);
}
