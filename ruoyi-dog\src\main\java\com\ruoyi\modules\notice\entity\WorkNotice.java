package com.ruoyi.modules.notice.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.user.entity.SysUploadFile;

import java.util.Date;
import java.util.List;

/**
 * 违法处罚表(Punish)实体类
 *
 * <AUTHOR>
 * @since 2022-08-12 14:40:02
 */
public class WorkNotice extends BaseEntity {

    private String title;//公告标题
    private String content;//公告内容
    private String status;//状态：1待发送通知，2已发送通知
    private String summary;//公告摘要
    private String noticeOrg;//通知机构名称
    private String noticeOrgid;//通知机构ID
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;//创建日期
    private String createBy;//创建人

    private String messageDept;//机构ID
    private List<SysUploadFile> uploadFileList;  //附件
    private String uploadFileStr;//附件字符串
    private String createName;

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getNoticeOrg() {
        return noticeOrg;
    }

    public void setNoticeOrg(String noticeOrg) {
        this.noticeOrg = noticeOrg;
    }

    public String getNoticeOrgid() {
        return noticeOrgid;
    }

    public void setNoticeOrgid(String noticeOrgid) {
        this.noticeOrgid = noticeOrgid;
    }

    @Override
    public Date getCreateDate() {
        return createDate;
    }

    @Override
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Override
    public String getCreateBy() {
        return createBy;
    }

    @Override
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getMessageDept() {
        return messageDept;
    }

    public void setMessageDept(String messageDept) {
        this.messageDept = messageDept;
    }

    public List<SysUploadFile> getUploadFileList() {
        return uploadFileList;
    }

    public void setUploadFileList(List<SysUploadFile> uploadFileList) {
        this.uploadFileList = uploadFileList;
    }

    public String getUploadFileStr() {
        return uploadFileStr;
    }

    public void setUploadFileStr(String uploadFileStr) {
        this.uploadFileStr = uploadFileStr;
    }
}
