package com.ruoyi.framework.web.service;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.component.DingtalkHelper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.SecurityUtils;
import jakarta.annotation.Resource;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.exception.user.BlackListException;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.exception.user.UserNotExistsException;
import com.ruoyi.common.exception.user.UserPasswordNotMatchException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.framework.security.context.AuthenticationContextHolder;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.web.bind.annotation.GetMapping;

import javax.security.sasl.SaslServer;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService
{
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private DingtalkHelper dingtalkHelper;

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid)
    {
        // 验证码校验
        validateCaptcha(username, code, uuid);
        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        try
        {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        }
        catch (Exception e)
        {
            if (e instanceof BadCredentialsException)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }
        finally
        {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public AjaxResult loginjsc(String username, String password, String code, String uuid) {
        AjaxResult dataMap = AjaxResult.success();
//        boolean captchaEnabled = configService.selectCaptchaEnabled();
//        // 验证码开关
//        if (captchaEnabled) {
//            validateCaptcha(username, code, uuid);
//        }
        // 用户验证
        Authentication authentication = null;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        String token = tokenService.createToken(loginUser);
        dataMap.put(Constants.TOKEN, token);
        Long deptId = loginUser.getDeptId();
        if (deptId == 103L || deptId == 202L || deptId == 213L || deptId == 214L || deptId == 215L || deptId == 216L || deptId == 217L || deptId == 218L || deptId == 219L || deptId == 220L || deptId == 221L || deptId == 262L) {
            dataMap.put("bigScreen", 1);
        } else {
            dataMap.put("bigScreen", 2);
        }

        return dataMap;
    }

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public AjaxResult loginMd(String username, String password, String code, String uuid) {
        AjaxResult dataMap = AjaxResult.success();
        String cacheObject =(String)redisCache.getCacheObject(username);
        if (!password.equals(cacheObject)){
            throw  new GlobalException("登录失败");
        }
        username=username.replace("@@@@$$$$XXXXXXXXX_","");
        SysUser sysUser=userService.selectUserByUserName(username);
        LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser.getDeptId(), sysUser, permissionService.getMenuPermission(sysUser));
        // 生成token
        String token = tokenService.createToken(loginUser);
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        dataMap.put(Constants.TOKEN, token);
        return dataMap;
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid)
    {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled)
        {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisCache.getCacheObject(verifyKey);
            if (captcha == null)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new CaptchaExpireException();
            }
            redisCache.deleteObject(verifyKey);
            if (!code.equalsIgnoreCase(captcha))
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new CaptchaException();
            }
        }
    }

    /**
     * 登录前置校验
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId)
    {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }

    /**
     * 获得一个指令板块管理员权限的用户的Token
     * <AUTHOR>
     */
    public String login2(String username) {
        SysUser sysUser = null;
        sysUser=userService.selectUserByUserName(username);
        LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser.getDeptId(), sysUser, permissionService.getMenuPermission(sysUser));
        // 生成token
        String token = tokenService.createToken(loginUser);
        return token;
    }


    public String loginV2() {
        String username = "test1";
        String password = "Aa123456!";

        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        try
        {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        }
        catch (Exception e)
        {
            if (e instanceof BadCredentialsException)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }
        finally
        {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }
    public String loginV3(String username,String password) {
//        String username = "ssyj";
//        String password = "Ssyj#2%#";

        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        try
        {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        }
        catch (Exception e)
        {
            if (e instanceof BadCredentialsException)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }
        finally
        {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 浙政钉扫码登录
     *
     * @param username
     * @param code     浙政钉扫码登录临时授权码
     * @return
     */
    public AjaxResult loginZzdQRCode(String username, String code) {

        String accessToken = Strings.EMPTY;
        String zzdUserName = Strings.EMPTY;
        Map<String, Object> zzdUserInfo = new HashMap();

        AjaxResult dataMap = AjaxResult.success();
        try {
            Map<String, Object> tokenMap = dingtalkHelper.gettoken(Constants.DING_SDN_DINGOA);
            if (Boolean.parseBoolean(String.valueOf(tokenMap.get("success")))) {
                if (Boolean.parseBoolean(String.valueOf(((Map<String, Object>) tokenMap.get("content")).get("success")))) {
                    // 浙政钉token
                    accessToken = (String) ((Map<String, Object>) ((Map<String, Object>) tokenMap.get("content")).get("data")).get("accessToken");
                    // 获取浙政钉扫码用户信息
                    Map<String, Object> userInfoMap = dingtalkHelper.getuserinfo_bycode(accessToken, code, Constants.DING_SDN_DINGOA);
                    if (Boolean.parseBoolean(String.valueOf(userInfoMap.get("success")))) {
                        if (Boolean.parseBoolean(String.valueOf(((Map<String, Object>) userInfoMap.get("content")).get("success")))) {
                            // 用户信息
                            zzdUserInfo = (Map<String, Object>) ((Map<String, Object>) userInfoMap.get("content")).get("data");
                            System.out.println("浙政钉扫码用户信息：" + JSON.toJSONString(zzdUserInfo));
                            zzdUserInfo.put("zzdToken", accessToken);
                        } else {
                            throw new GlobalException("Internal Server Error");
                        }
                    } else {
                        throw new GlobalException("Internal Server Error");
                    }
                } else {
                    throw new GlobalException("Internal Server Error");
                }
            } else {
                throw new GlobalException("Internal Server Error");
            }
            SysUser sysUser = userService.selectUserByEmployeeCode(zzdUserInfo.get("employeeCode").toString());
            Boolean flag = sysUser != null;
            dataMap.put("flag", flag);
            if (flag) {
                username = sysUser.getUserName();
            } else {
                throw  new GlobalException("用户不存在！");
//                dataMap = AjaxResult.error("该用户没有权限！");
//                return dataMap;
            }

            dataMap.put("zzdUserInfo", zzdUserInfo);
            dataMap.put("username", username);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(StringUtils.isNotEmpty(zzdUserName) ? zzdUserName : username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(StringUtils.isNotEmpty(zzdUserName) ? zzdUserName : username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new GlobalException(e.getMessage());
            }
        }
        return dataMap;
    }

    public String loginV5(String username, String password) {
        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        try
        {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        }
        catch (Exception e)
        {
            if (e instanceof BadCredentialsException)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }
        finally
        {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }


    /**
     * 浙政钉应用免登登录
     * @param username
     * @param authCode 免登授权码
     * @return
     */
    public AjaxResult loginZzd( String username, final String authCode) {
        String accessToken = Strings.EMPTY;
        String zzdUserName = Strings.EMPTY;
        Map<String, Object> zzdUserInfo = new HashMap();
        System.out.println("authCode：" + authCode);
        AjaxResult dataMap = AjaxResult.success();
        try {
            Map<String, Object> tokenMap = dingtalkHelper.gettoken(Constants.DING_JINHUACSDN);
            if (Boolean.parseBoolean(String.valueOf(tokenMap.get("success")))) {
                if (Boolean.parseBoolean(String.valueOf(((Map<String, Object>) tokenMap.get("content")).get("success")))) {
                    // 浙政钉token
                    accessToken = (String) ((Map<String, Object>) ((Map<String, Object>) tokenMap.get("content")).get("data")).get("accessToken");
                    // 获取浙政钉扫码用户信息
                    Map<String, Object> userInfoMap = dingtalkHelper.getZzdUserInfo(accessToken, authCode, Constants.DING_JINHUACSDN);
                    if (Boolean.parseBoolean(String.valueOf(userInfoMap.get("success")))) {
                        if (Boolean.parseBoolean(String.valueOf(((Map<String, Object>) userInfoMap.get("content")).get("success")))) {
                            // 用户信息
                            zzdUserInfo = (Map<String, Object>) ((Map<String, Object>) userInfoMap.get("content")).get("data");
                            System.out.println("浙政钉扫码用户信息：" + com.alibaba.fastjson.JSON.toJSONString(zzdUserInfo));
                            zzdUserInfo.put("zzdToken", accessToken);
                        } else {
                            throw new GlobalException("Internal Server Error");
                        }
                    } else {
                        throw new GlobalException("Internal Server Error");
                    }
                } else {
                    throw new GlobalException("Internal Server Error");
                }
            } else {
                throw new GlobalException("Internal Server Error");
            }
            SysUser sysUser = userService.selectUserByEmployeeCode(zzdUserInfo.get("employeeCode").toString());
            Boolean flag = sysUser != null;
            dataMap.put("flag", flag);
            if (flag) {
                username = sysUser.getUserName();
            } else {
                dataMap = AjaxResult.error("该用户没有权限！");
                return dataMap;
            }

            dataMap.put("zzdUserInfo", zzdUserInfo);
            dataMap.put("username", username);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(org.apache.commons.lang3.StringUtils.isNotEmpty(zzdUserName) ? zzdUserName : username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(org.apache.commons.lang3.StringUtils.isNotEmpty(zzdUserName) ? zzdUserName : username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new GlobalException("Internal Server Error");
            }
        }
        System.out.println("登录成功:"+dataMap);
        return dataMap;
    }

    /**
     * 微信登录
     * @param openid
     * @return
     */
    public String wxLogin(String openid) {
        SysUser userInfoByOpenId = userService.getUserInfoByOpenId(openid,null);
        if (userInfoByOpenId == null){
            userInfoByOpenId=new SysUser();
            userInfoByOpenId.setOpenId(openid);
            userInfoByOpenId.setUserType(8);
            boolean b=true;
            while (b){
                String s="微信用户_"+  RandomUtil.randomString(8);
                SysUser userInfoByOpenId1 = userService.getUserInfoByOpenId(null, s);
                if (userInfoByOpenId1==null){
                   b=false;
                    userInfoByOpenId.setUserName(s);
                    userInfoByOpenId.setNickName(s);
                }
            }
            userService.insertUser(userInfoByOpenId);
        }
        LoginUser loginUser = new LoginUser(userInfoByOpenId.getUserId(), userInfoByOpenId.getDeptId(), userInfoByOpenId, permissionService.getMenuPermission(userInfoByOpenId));
        String token = tokenService.createToken(loginUser);
        return token;
    }
}
