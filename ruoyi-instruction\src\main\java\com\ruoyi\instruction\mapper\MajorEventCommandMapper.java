package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.MajorEventCommand;

import java.util.List;

/**
 * 重大事件-跟踪指挥Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-02
 */
@DataSource(value = DataSourceType.SLAVE)
public interface MajorEventCommandMapper 
{
    /**
     * 查询重大事件-跟踪指挥
     * 
     * @param id 重大事件-跟踪指挥主键
     * @return 重大事件-跟踪指挥
     */
    public MajorEventCommand selectMajorEventCommandById(Long id);

    /**
     * 查询重大事件-跟踪指挥列表
     * 
     * @param majorEventCommand 重大事件-跟踪指挥
     * @return 重大事件-跟踪指挥集合
     */
    public List<MajorEventCommand> selectMajorEventCommandList(MajorEventCommand majorEventCommand);

    /**
     * 新增重大事件-跟踪指挥
     * 
     * @param majorEventCommand 重大事件-跟踪指挥
     * @return 结果
     */
    public int insertMajorEventCommand(MajorEventCommand majorEventCommand);

    /**
     * 修改重大事件-跟踪指挥
     * 
     * @param majorEventCommand 重大事件-跟踪指挥
     * @return 结果
     */
    public int updateMajorEventCommand(MajorEventCommand majorEventCommand);

    /**
     * 删除重大事件-跟踪指挥
     * 
     * @param id 重大事件-跟踪指挥主键
     * @return 结果
     */
    public int deleteMajorEventCommandById(Long id);

    /**
     * 批量删除重大事件-跟踪指挥
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMajorEventCommandByIds(Long[] ids);
}
