package com.ruoyi.common.enums;

/**
 * 来源
 */
public enum VolApproveEnum {

    USER("1", "志愿者申请"),
    NEWS("2", "站前动态"),
    PUNCH("3" , "补卡申请"),
    TASK("4" , "志愿者任务"),
    APPOINTMENT("5", "服务预约"),
    BOOKRECORD("6", "借书审核");


    private String type;
    private String desc;

    VolApproveEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
