package com.ruoyi.modules.vaccine.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.sysDict.entity.SysDict;
import com.ruoyi.modules.vaccine.entity.Vaccine;
import com.ruoyi.modules.vaccine.service.VaccineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @version: 1.0
 **/
@RestController
@RequestMapping("vaccine")
public class VaccineController {
    @Autowired
    private VaccineService service;

    @RequestMapping("getPageList")
    public AjaxResult getPageList(Vaccine vaccine){
        return AjaxResult.success(service.getPageList(vaccine));
    }

    @RequestMapping("saveOrUpdate")
    public AjaxResult saveOrUpdate(Vaccine vaccine){
        service.saveOrUpdate(vaccine);
        return AjaxResult.success();
    }

    @RequestMapping("delete")
    public AjaxResult delete(Vaccine vaccine){
        service.delete(vaccine);
        return AjaxResult.success();
    }

    @RequestMapping("getById")
    public AjaxResult getById(String id){
        return  AjaxResult.success(service.getById(id));
    }

    @RequestMapping("updateStatus")
    public AjaxResult updateStatus(Vaccine vaccine){
        Date date = new Date();
        vaccine.setAuditTime(date);
        service.updateStatus(vaccine);
        return AjaxResult.success();
    }

    @RequestMapping("/getAllList")
    public AjaxResult getAllList(Vaccine vaccine) {
        return AjaxResult.success(service.getAllList(vaccine));
    }

    @RequestMapping("/queryUserVaccine")
    public AjaxResult queryUserVaccine(Vaccine vaccine) {
        return AjaxResult.success(service.queryUserVaccine(vaccine));
    }


}
