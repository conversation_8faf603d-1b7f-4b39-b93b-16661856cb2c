package com.ruoyi.modules.hospital.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.brand.entity.PetBrandApply;
import com.ruoyi.modules.hospital.service.QualifiService;
import com.ruoyi.modules.hospital.vo.HospitalRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 申请记录
 */
@RestController
@RequestMapping("/hospital/record")
public class HospitalApplyController {
    @Autowired
    private QualifiService service;


    /**
     * 分页
     * @return
     */
    @GetMapping("getApplyList")
    public AjaxResult getApplyList(HospitalRecordVO recordVO) {
        return AjaxResult.success(service.getApplyList(recordVO));
    }

    /**
     * 根据医院获取申请记录
     * @return
     */
    @GetMapping("getHospitalApplyList")
    public AjaxResult getHospitalApplyList(HospitalRecordVO recordVO) {
        return AjaxResult.success(service.getHospitalApplyList(recordVO));
    }
}
