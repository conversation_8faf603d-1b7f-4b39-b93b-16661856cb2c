package com.ruoyi.modules.notice.service;


import com.alibaba.fastjson2.JSON;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.notice.dao.NoticeDao;
import com.ruoyi.modules.notice.entity.Notice;
import com.ruoyi.modules.user.dao.SysUploadFileDao;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.service.SysUploadFileService;
import io.netty.util.internal.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 违法处罚表(punish)表服务接口
 *
 * <AUTHOR>
 */
@Service
public class NoticeService extends BaseService<NoticeDao, Notice> {

    @Autowired
    SysUploadFileService uploadFileService;
    @Autowired
    SysUploadFileDao uploadFileDao;

    @Override
    public void saveOrUpdate(Notice entity) {
        super.saveOrUpdate(entity);
        uploadFileService.delByInstanceAndModel(entity.getId(), "");
        if (!ObjectUtils.isEmpty(entity.getUploadFileStr())) {
            List<SysUploadFile> list = JSON.parseArray(entity.getUploadFileStr(), SysUploadFile.class);
            List<SysUploadFile> result = new ArrayList<>();
            for (SysUploadFile s : list) {
                s.preInsert();
                s.setInstanceId(entity.getId());
                result.add(s);
            }
            uploadFileDao.saveAllList(result);
        }
    }

    public Notice getById(String id){
        Notice policyNotice = dao.getById(id);
        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.setInstanceId(id);
        sysUploadFile.setModelType("notice_id");
        policyNotice.setUploadFileList(uploadFileService.getList(sysUploadFile));
        return policyNotice;
    }

    public void updateStatus(Notice entity){
        dao.updateStatus(entity);
    }

}
