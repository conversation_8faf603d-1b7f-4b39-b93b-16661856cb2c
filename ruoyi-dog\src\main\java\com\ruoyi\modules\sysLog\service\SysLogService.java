package com.ruoyi.modules.sysLog.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.config.log.NetUtils;
import com.ruoyi.modules.sysLog.dao.SysLogDao;
import com.ruoyi.modules.sysLog.entity.SysLog;
import com.ruoyi.modules.takeIn.dao.TakeInDao;
import com.ruoyi.modules.takeIn.entity.TakeIn;
import com.ruoyi.modules.user.dao.SysUserDao;
import com.ruoyi.modules.user.entity.SysUser;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;

/**
 * 系统日志(SysLog)表服务接口
 *
 * <AUTHOR>
 * @since 2022-12-26 10:58:08
 */
@Service
public class SysLogService extends BaseService<SysLogDao, SysLog> {
    @Autowired
    private SysUserDao sysUserDao;
    @Transactional
    public void saveOrUpdate(SysLog entity, HttpServletRequest request) {
        entity.setIp(NetUtils.getClientIpAddress(request));
        if (entity.getId() != null && !entity.getId().equals("")) {
            // 更新操作
            entity.preUpdate();
            update(entity);
        } else {
            // 新增操作
            insert(entity);
        }
    }
    public PageInfo<SysLog> getPageList(SysLog entity){
        PageHelper.startPage(entity.getPageNum(),entity.getPageSize());
        List<SysLog> list=getList(entity);
        for(SysLog log:list){
            log.setUser(sysUserDao.getById(log.getCreateBy()));
        }
        PageInfo<SysLog> pageInfo = new PageInfo<SysLog>(list);
        return pageInfo;
    }
    public Integer getVisitsNum(SysLog sysLog){
        return dao.getVisitsNum(sysLog);
    }

    public PageInfo<HashMap> getVisitsList(SysLog entity){
        PageHelper.startPage(entity.getPageNum(),entity.getPageSize());
        List<HashMap> list=dao.getVisitsList(entity);
        PageInfo<HashMap> pageInfo = new PageInfo<HashMap>(list);
        return pageInfo;
    }
}
