package com.ruoyi.modules.user.service;

import com.alibaba.fastjson2.JSONArray;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.user.dao.SysRoleDao;
import com.ruoyi.modules.user.dao.SysRoleMenuDao;
import com.ruoyi.modules.user.entity.SysRole;
import com.ruoyi.modules.user.entity.SysRoleMenu;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Administrator on 2021-3-13.
 */
@Service
public class SysRoleService extends BaseService<SysRoleDao, SysRole> {

    @Autowired
    private SysRoleMenuDao sysRoleMenuDao;

    /**
     * 保存角色数据
     */
    @Transactional
    @Override
    public void saveOrUpdate(SysRole sysRole){
        if(sysRole.getId() != null){
            // 更新操作
            sysRole.preUpdate();
            update(sysRole);
        }else{
            // 新增操作
            insert(sysRole);
        }
    }

    /**
     * 角色菜单权限更新
     * @param sysRole
     */
    @Transactional
    public void updateAllSysRoleMenu(SysRole sysRole){
        List<SysRoleMenu> list = new ArrayList<>();
        if(sysRole.getMenuListStr() != null && !sysRole.getMenuListStr().equals("")){
//            String[] menuIds = sysRole.getMenuListStr().split(",");
            List<String> menuIds = JSONArray.parseArray(sysRole.getMenuListStr(), String.class);
            for(String menuId : menuIds){
                SysRoleMenu roleMenu = new SysRoleMenu();
                roleMenu.preInsert();
                roleMenu.setRoleId(sysRole.getId());
                roleMenu.setMenuId(menuId);
                list.add(roleMenu);
            }
        }
        sysRoleMenuDao.deleteByRoleId(sysRole.getId());
        if(list.size() > 0){
            sysRoleMenuDao.saveList(list);
        }
    }

    /**
     * 根据角色ID获取菜单授权数据
     * @param
     * @return
     */
    public List<SysRoleMenu> getSysRoleMenuList(SysRoleMenu sysRoleMenu){
        return sysRoleMenuDao.getList(sysRoleMenu);
    }
}
