package com.ruoyi.common.core.domain;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.domain.entity.*;

/**
 * Treeselect树结构实体类
 *
 * <AUTHOR>
 */
public class TreeSelect implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private Long id;

    /**
     * 节点名称
     */
    private String label;

    /**
     * 类型
     */
    private String type;

    /**
     * 分值
     */
    private Integer score;

    /**
     * 父节点
     */
    private Long parentId;

    /**
     * 子节点
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelect> children;

    public TreeSelect() {

    }

    public TreeSelect(SysDept dept) {
        this.id = dept.getDeptId();
        this.label = dept.getDeptName();
        this.children = dept.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(SysMenu menu) {
        this.id = menu.getMenuId();
        this.label = menu.getMenuName();
        this.children = menu.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(CasePersuasionType type) {
        this.id = type.getTypeId();
        this.label = type.getTitle();
        this.children = type.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(CaseSummary summary) {
        this.id = summary.getSummaryId();
        this.label = summary.getTitle();
        this.type = summary.getType();
        this.children = summary.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(SupervisionCheckStandard supervisionCheckStandard) {
        this.id = supervisionCheckStandard.getCheckStandardId();
        this.label = supervisionCheckStandard.getTitle();
        this.type = supervisionCheckStandard.getType();
        this.score = supervisionCheckStandard.getScore();
        this.children = supervisionCheckStandard.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(SysDeptUser deptUser) {
        this.id = deptUser.getDuid();
        this.label = deptUser.getName();
        this.type = deptUser.getType();
        this.parentId = deptUser.getParentId();
        this.children = deptUser.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public List<TreeSelect> getChildren() {
        return children;
    }

    public void setChildren(List<TreeSelect> children) {
        this.children = children;
    }
}
