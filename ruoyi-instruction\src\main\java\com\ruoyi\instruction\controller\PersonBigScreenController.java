package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.domain.InstructionPersonControl;
import com.ruoyi.instruction.service.IInstrucationPersonService;
import com.ruoyi.instruction.service.IInstructionPersonControlService;
import com.ruoyi.instruction.service.LargeScreenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/8 13:52
 * 大屏驾驶舱接口
 */
@RestController
@RequestMapping("/bigScreen/person")
public class PersonBigScreenController extends BaseController {


    @Autowired
    private LargeScreenService largeScreenService;

    @Autowired
    private IInstrucationPersonService instrucationPersonService;

    @Autowired
    private IInstructionPersonControlService instructionPersonControlService;

    /**
     * 获取人员类别
     *
     * @return
     */
    @GetMapping("/getPersonType")
    public AjaxResult getPersonType() {
        return largeScreenService.getPersonType();
    }

    /**
     * 获取人员类型数量
     * @return
     */
    @GetMapping("/getPersonTypeCount")
    public AjaxResult getPersonTypeCount(){
        return largeScreenService.getPersonTypeCount();
    }

    /**
     * 获取活跃重点人员分布
     *
     * @return
     */
    @GetMapping("/getPersonnelDistribution")
    public AjaxResult getPersonnelDistribution() {
        return largeScreenService.getPersonnelDistribution();
    }

    /**
     * 获取重点人员总数
     *
     * @return
     */
    @GetMapping("/getPersonCount")
    public AjaxResult getPersonCount() {
        return largeScreenService.getPersonCount();
    }

    /**
     * 获取活跃人员列表
     * @param instrucationPerson
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo list(InstrucationPerson instrucationPerson) {

        startPage();
        List<InstrucationPerson> list = instrucationPersonService.selectInstrucationPersonListForBigScreen(instrucationPerson);
        return getDataTable(list);
    }

    /**
     * 根据人员id获取人员五包一数据
     *
     * @param id
     * @return
     */
    @GetMapping("/getPersonController/{id}")
    public AjaxResult getPersonController(@PathVariable("id") Long id) {
        HashMap<String, Object> map = new HashMap<>();
        InstructionPersonControl instructionPersonControl = new InstructionPersonControl();
        instructionPersonControl.setPersonId(id);
        List<InstructionPersonControl> list = instructionPersonControlService.selectInstructionPersonControlList(instructionPersonControl);
        map.put("list",list);
        //查询该人员关联群体
        String groupName = instrucationPersonService.findGroupNameByPersonId(id);
        map.put("groupName",groupName);
        return AjaxResult.success(map);
    }
}
