package com.ruoyi.config;


import org.springframework.stereotype.Component;

import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: MyWebSocket
 * @Description: webSocket工具类  uid为区分用户使用，建立websocket时需指定可变参数
 */

@ServerEndpoint(value = "/websocket/{userId}")
@Component
public class MyWebSocket {
    //静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
    private static int onlineCount = 0;

    //concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。若要实现服务端与单一客户端通信的话，可以使用Map来存放，其中Key可以为用户标识
    public static ConcurrentHashMap<String,MyWebSocket> webSocketMap = new ConcurrentHashMap<String,MyWebSocket>();

    //与某个客户端的连接会话，需要通过它来给客户端发送数据
    private Session session;

    public Session getSession(){
        return this.session;
    }

    /**
     * 连接建立成功调用的方法
     *
     * @param session 可选的参数。session为与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    @OnOpen
    public void onOpen(@PathParam("userId") String userId, Session session) {
        this.session = session;
        addOnlineCount();           //在线数加1
        webSocketMap.put(userId,this);
        System.out.println("有新连接加入:"+userId+" 当前在线人数为" + getOnlineCount());
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(@PathParam("userId") String userId) {
        webSocketMap.remove(userId);  //从map中删除
        subOnlineCount();           //在线数减1
        System.out.println("有一连接关闭！当前在线人数为" + getOnlineCount());
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     * @param session 可选的参数
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        System.out.println("来自客户端的消息:" + message);
        //调用群发
//        sendAllMessage("{\"msgType\":\"1\",\"content\":\"服务器返回小程序心跳\"}");
    }

    /**
     * 发生错误时调用
     *
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        System.out.println("发生错误");
        error.printStackTrace();
    }

    /**
     * 给某一个客户发消息
     * @param message
     * @throws IOException
     */
    public void sendMessage(String message,Session session) throws IOException {
        session.getBasicRemote().sendText(message);
    }

    public static synchronized int getOnlineCount() {
        return onlineCount;
    }

    public static synchronized void addOnlineCount() {
        MyWebSocket.onlineCount++;
    }

    public static synchronized void subOnlineCount() {
        MyWebSocket.onlineCount--;
    }

    /**
     * 对外开放接口，针对websocket 某一个客户端发送消息  使用userId区分客户端
     * @param msg
     * @param userId
     */
    public static void sendMsg(String msg,String userId) {
        MyWebSocket item = webSocketMap.get(userId);
        try {
            if(item != null){
                //用户在线 发送消息
                item.sendMessage(msg,item.getSession());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 对外开放接口 群发消息 给所有在线客户端发送消息
     * @param message
     */
    public void sendAllMessage(String message){
        Set<String> keySet = webSocketMap.keySet();
        if(keySet != null && keySet.size() > 0){
            //群发消息
            for (String key : keySet) {
                try {
                    MyWebSocket item = webSocketMap.get(key);
                    item.sendMessage(message,item.getSession());
                } catch (IOException e) {
                    e.printStackTrace();
                    continue;
                }
            }
        }
    }
}
