package com.ruoyi.modules.msg.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 短信发送记录对象 send_msg_info
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
public class SendMsgInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Long id;

    private Date cTime;

    private Date uTime;

    /** 短信类型，1预警 */
    @Excel(name = "短信类型，1预警")
    private Integer type;

    /** 数据id */
    @Excel(name = "数据id")
    private String dataId;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;
    /**
     * 超期时间
     */
    private String cqsj;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setcTime(Date cTime) 
    {
        this.cTime = cTime;
    }

    public Date getcTime() 
    {
        return cTime;
    }
    public void setuTime(Date uTime) 
    {
        this.uTime = uTime;
    }

    public Date getuTime() 
    {
        return uTime;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }
    public void setDataId(String dataId) 
    {
        this.dataId = dataId;
    }

    public String getDataId() 
    {
        return dataId;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }

    public String getCqsj() {
        return cqsj;
    }

    public void setCqsj(String cqsj) {
        this.cqsj = cqsj;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("cTime", getcTime())
            .append("uTime", getuTime())
            .append("type", getType())
            .append("dataId", getDataId())
            .append("phone", getPhone())
            .toString();
    }
}
