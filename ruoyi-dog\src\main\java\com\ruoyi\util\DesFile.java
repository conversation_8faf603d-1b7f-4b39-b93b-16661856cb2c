package com.ruoyi.util;




import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.io.*;

/**
 * Created by Administrator on 2022-1-15.
 */
public class DesFile {
//
//     public static void downLoadFile(String fullPath, OutputStream out, int key, int length) throws FileNotFoundException {
//         FileInputStream inp = new FileInputStream(fullPath);
//         int num = 0;
//         int indexNum = 0;
//         try {
//             StringBuilder newSB = new StringBuilder();
//             while ((num = inp.read()) != -1){
//                 indexNum = indexNum + 1;
//                 if (indexNum > length) {
//                     out.write(num);
//                 } else {
//                     newSB.append(num + "-");
//                     if (indexNum == length){
//                         String strValue = getString(newSB.toString(), key);
//                         String[] charArray = strValue.split("-");
//                         for (String value : charArray){
//                             if (value != null && !"".equals(value)){
//                                 out.write(JSType.toInt32(value));
//                             }
//                         }
//                     }
//                 }
//             }
//         } catch (IOException e) {
//             e.printStackTrace();
//         } finally {
//             if(inp != null){
//                 try {
//                     inp.close();
//                 } catch (IOException e) {
//                     e.printStackTrace();
//                 }
//             }
//             if(out != null){
//                 try {
//                     out.close();
//                 } catch (IOException e) {
//                     e.printStackTrace();
//                 }
//             }
//         }
//     }
//
//     public static String createNewFile(String sourceFileName, String destFileName, int key, int length) throws FileNotFoundException {
//         FileInputStream inp = new FileInputStream("D:\\wjl\\图书\\book\\hb_1002_6.pdf");
//         //把读出的字符串写入到in.txt
//         FileOutputStream out = new FileOutputStream("D:\\wjl\\图书\\book\\hb_1002_6_out.pdf");
//
//         int num = 0;
//         int indexNum = 0;
//         try {
//             StringBuilder newSB = new StringBuilder();
//             while ((num = inp.read()) != -1){
//                 indexNum = indexNum + 1;
//                 if (indexNum > length) {
//                     out.write(num);
//                 } else {
//                     newSB.append(num + "-");
//                     if (indexNum == length){
//                         String strValue = getString(newSB.toString(), key);
//                         String[] charArray = strValue.split("-");
//                         for (String value : charArray){
//                             if (value != null && !"".equals(value)){
//                                 out.write(JSType.toInt32(value));
//                             }
//                         }
//                     }
//                 }
//             }
//         } catch (IOException e) {
//             e.printStackTrace();
//         } finally {
//             if(inp != null){
//                 try {
//                     inp.close();
//                 } catch (IOException e) {
//                     e.printStackTrace();
//                 }
//             }
//             if(out != null){
//                 try {
//                     out.close();
//                 } catch (IOException e) {
//                     e.printStackTrace();
//                 }
//             }
//         }
//         return "done";
//     }
//
//     public static String getString(String value, int key){
//         StringBuilder newSB = new StringBuilder();
//         String[] charArray = value.split("-");
//         for (int i = 0; i < charArray.length; i++) {
//             if (charArray[i] != null && !"".equals(charArray[i])){
//                 newSB.append((JSType.toInt32(charArray[i]) ^ key) + "-");
//             }
//         }
//         return newSB.toString();
//     }
//
//     public static String strDecrypt(String deStr) throws Exception {
//         byte[] key = { 0, 1, 5, 1, 3, 8, 9, 1 };
//         byte[] iv1 = { 3, 1, 0, 5, 4, 8, 9, 1 };
//         //分解解密字符
//         String[] str = deStr.split("\\.");
//         //声明解密字符数组的长度
//         int length = str.length;
//         //定义解密字节数组
//         byte[] data = new byte[length];
//         //把字符串转换为字节
//         for (int i = 0; i < length; i++)
//         {
//             int num=Integer.parseInt(str[i]);
//             byte x = (byte) num;
//             data[i] = x;
//         }
//         Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
//         DESKeySpec desKeySpec = new DESKeySpec(key);
//         SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
//         SecretKey secretKey = keyFactory.generateSecret(desKeySpec);
//         IvParameterSpec iv = new IvParameterSpec(iv1);
//         cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);
//         //byte[] data=cipher.doFinal(message.getBytes("UTF-8"));
//         byte[] out = cipher.doFinal(data);
//         String str_source = new String(out);
//         return str_source;
//     }
//
//     public static void main(String[] args) {
// //        try {
// //            createNewFile("","",7,3969);
// //        } catch (FileNotFoundException e) {
// //            e.printStackTrace();
// //        }
//         try {
//             System.out.println(strDecrypt("*************.************.***************.************.**************.***************.************.*************"));
//         } catch (Exception e) {
//             e.printStackTrace();
//         }
//     }

}
