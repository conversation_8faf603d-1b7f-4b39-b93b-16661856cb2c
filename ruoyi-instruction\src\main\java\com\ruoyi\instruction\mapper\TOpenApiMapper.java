package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.TOpenApi;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 第三方对接配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-20
 */
@Mapper
@DataSource(value = DataSourceType.SLAVE)
public interface TOpenApiMapper
{
    /**
     * 查询第三方对接配置
     * 
     * @param id 第三方对接配置ID
     * @return 第三方对接配置
     */
    public TOpenApi selectTOpenApiById(Long id);

    /**
     * 查询第三方对接配置列表
     * 
     * @param tOpenApi 第三方对接配置
     * @return 第三方对接配置集合
     */
    public List<TOpenApi> selectTOpenApiList(TOpenApi tOpenApi);

    /**
     * 新增第三方对接配置
     * 
     * @param tOpenApi 第三方对接配置
     * @return 结果
     */
    public int insertTOpenApi(TOpenApi tOpenApi);

    /**
     * 修改第三方对接配置
     * 
     * @param tOpenApi 第三方对接配置
     * @return 结果
     */
    public int updateTOpenApi(TOpenApi tOpenApi);

    /**
     * 删除第三方对接配置
     * 
     * @param id 第三方对接配置ID
     * @return 结果
     */
    public int deleteTOpenApiById(Long id);

    /**
     * 批量删除第三方对接配置
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteTOpenApiByIds(Long[] ids);
}
