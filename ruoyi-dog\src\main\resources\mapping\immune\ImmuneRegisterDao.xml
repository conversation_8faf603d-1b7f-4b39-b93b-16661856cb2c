<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.immune.dao.ImmuneRegisterDao">
    <sql id="ImmuneRegisterSql">
        a.id as "id",
        a.hospital as "hospital",
        a.doctor as "doctor",
        a.type as "type",
        a.vaccine_brand as "vaccineBrand",
        a.vaccine_batch as "vaccineBatch",
        a.injection_date as "injectionDate",
        a.injection_enddate as "injectionEnddate",
        a.status as "status",
        a.reason as "reason",
        a.pet_id as "petId",
        a.hospital_id as "hospitalId",
        a.about_make as "aboutMake",
        a.about_date as "aboutDate",
        a.immune_card as "immuneCard",
        a.cross_type as "crossType"
    </sql>
    <sql id="certificatesSql">a.id as "id",
        a.owner_name as "ownerName",
        a.pet_id_card as "petIdCard",
        a.tel as "tel",
        a.pet_num as "petNum",
        a.pet_name as "petName",
        a.pet_type as "petType",
        a.pet_sex as "petSex",
        a.pet_varieties as "petVarieties",
        a.pet_varieties_one as "petVarietiesOne",
        a.pet_hair as "petHair",
        a.pet_age as "petAge",
        a.raise_date as "raiseDate",
        a.pet_address as "petAddress",
        a.pet_dept as "petDept",
        a.is_agency as "isAgency",
        a.agency_com as "agencyCom",
        a.source as "source",
        a.is_reissue as "isReissue",
        a.year_status as "yearStatus",
        a.status as "status",
        a.create_date as "createDate",
        a.immune_id as immuneId,
        a.transfer_id as transferId,
        a.cancel_id as cancelId,
        a.end_date as endDate,
        a.tel as tel,
        a.owner_name as "ownerName",
        a.owner_address as "ownerAddress",
        a.expres_type as "expresType",
        a.expres_address as "expresAddress",
        a.hospital_id as "hospitalId",
        a.about_make as "aboutMake",
        a.about_date as "aboutDate",
        a.pet_img as "petImg",
        a.activation as "activation",
        a.about_status as "aboutStatus",
        a.about_reason as "aboutReason",
        a.about_hospital as "aboutHospital"</sql>
    <select id="getPetPageList" parameterType="com.ruoyi.modules.immune.entity.ImmuneRegister"
            resultType="com.ruoyi.modules.certificates.entity.PetCertificates">
        select
        <include refid="certificatesSql"/>
        <if test="queryType!=null and queryType!='myzl'">
            ,(select immune_card from immune_register where del_flag=1 and id=a.immune_id) as "immuneRegister.immuneCard"
            ,(select injection_enddate from immune_register where del_flag=1 and id=a.immune_id) as
            "immuneRegister.injectionEnddate"
            ,(select status from immune_register where del_flag=1 and id=a.immune_id) as "immuneRegister.status"
        </if>

        <if test="queryType!=null and queryType=='myzl'">
            ,r.immune_card  as "immuneRegister.immuneCard"
            ,r.injection_enddate as  "immuneRegister.injectionEnddate"
            ,r.status as "immuneRegister.status"
        </if>
        from pet_certificates a
        <if test="queryType!=null and queryType=='myzl'">
            RIGHT join immune_register r ON r.pet_id = a.id
        </if>
        where 1 = 1
        <if test="queryType!=null and queryType=='djzl'">
            AND (
            (a.del_flag = 1 and a.immune_id in (select r.id from immune_register r where r.del_flag=1 and
            r.injection_enddate >= now()
            <if test="hospitalId!=null and hospitalId!=''">
                and hospital_id=#{hospitalId}
            </if>))
            or(a.del_flag = 1 and a.immune_id in (select r.id from immune_register r where r.del_flag=1 and
            r.injection_enddate &lt; now()
            <if test="hospitalId!=null and hospitalId!=''">
                and hospital_id=#{hospitalId}
            </if>))
            or(a.del_flag = 1 and (a.immune_id is null or a.immune_id = ''))
            or( a.del_flag = 2)
            )
        </if>
        <if test="queryType!=null and queryType=='myzl'">
            and  r.del_flag=1
            <if test="hospitalId!=null and hospitalId!=''">
                and r.hospital_id=#{hospitalId}
            </if>
        </if>
        <if test="queryType!=null and queryType=='myyx'">
            and  a.del_flag = 1 and a.immune_id in (select r.id from immune_register r where r.del_flag=1 and r.injection_enddate >= now())
            <if test="hospitalId!=null and hospitalId!=''">
                and hospital_id=#{hospitalId}
            </if>
        </if>
        <if test="queryType!=null and queryType=='mygq'">
            and  a.del_flag = 1 and a.immune_id in (select r.id from immune_register r where r.del_flag=1 and r.injection_enddate&lt; now())
            <if test="hospitalId!=null and hospitalId!=''">
                and hospital_id=#{hospitalId}
            </if>
        </if>
        <if test="queryType!=null and queryType=='myjjgq'">
            and  a.del_flag = 1 and a.immune_id in (select r.id from immune_register r where r.del_flag=1 and datediff(r.injection_enddate, now()) &lt;= 30  and datediff(r.injection_enddate, now()) > 0)
            <if test="hospitalId!=null and hospitalId!=''">
                and hospital_id=#{hospitalId}
            </if>
        </if>
        <if test="queryType!=null and queryType=='wmy'">
            and a.del_flag = 1 and (a.immune_id is null or a.immune_id = '')
            <if test="hospitalId!=null and hospitalId!=''">
                and hospital_id=#{hospitalId}
            </if>
        </if>
        <if test="queryType!=null and queryType=='qzzx'">
            and a.del_flag = 2
            <if test="hospitalId!=null and hospitalId!=''">
                and a.hospital_id=#{hospitalId}
            </if>
        </if>
        <if test="queryType!=null and queryType=='ybp'">
            and  a.del_flag = 1 and a.immune_id is not null and a.immune_id !='' and a.pet_num is not null and a.pet_num !=''
            <if test="hospitalId!=null and hospitalId!=''">
                and a.hospital_id=#{hospitalId}
            </if>
        </if>
        <if test="queryType!=null and queryType=='wbp'">
            and  a.del_flag = 1 and a.immune_id is not null and a.immune_id!=''  and (a.pet_num is null or a.pet_num ='')
            <if test="hospitalId!=null and hospitalId!=''">
                and a.hospital_id=#{hospitalId}
            </if>
        </if>
        <if test="deptId!=null and deptId!=''">
            and a.pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="keyword!=null and keyword!=''">
            and (a.owner_name like concat('%',#{keyword},'%') or
            a.pet_id_card like concat('%',#{keyword},'%') or
            a.tel like concat('%',#{keyword},'%') or
            a.owner_address like concat('%',#{keyword},'%') or
            a.pet_name like concat('%',#{keyword},'%') or
            a.pet_num like concat('%',#{keyword},'%') )
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(a.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="petType!=null and petType!=''">
            and a.pet_type=#{petType}
        </if>
    </select>
    <!-- left join immune_register r on r.pet_id = a.id -->
  <!--  <select id="getPetPageList" parameterType="com.ruoyi.modules.immune.entity.ImmuneRegister"
            resultType="com.ruoyi.modules.certificates.entity.PetCertificates">
        select a.id as "id",
        a.owner_name as "ownerName",
        a.pet_id_card as "petIdCard",
        a.tel as "tel",
        a.pet_num as "petNum",
        a.pet_name as "petName",
        a.pet_type as "petType",
        a.pet_sex as "petSex",
        a.pet_varieties as "petVarieties",
        a.pet_varieties_one as "petVarietiesOne",
        a.pet_hair as "petHair",
        a.pet_age as "petAge",
        a.raise_date as "raiseDate",
        a.pet_address as "petAddress",
        a.pet_dept as "petDept",
        a.is_agency as "isAgency",
        a.agency_com as "agencyCom",
        a.source as "source",
        a.is_reissue as "isReissue",
        a.year_status as "yearStatus",
        a.status as "status",
        a.create_date as "createDate",
        a.immune_id as immuneId,
        a.transfer_id as transferId,
        a.cancel_id as cancelId,
        a.end_date as endDate,
        a.tel as tel,
        a.owner_name as "ownerName",
        a.owner_address as "ownerAddress",
        a.expres_type as "expresType",
        a.expres_address as "expresAddress",
        a.hospital_id as "hospitalId",
        a.about_make as "aboutMake",
        a.about_date as "aboutDate",
        a.pet_img as "petImg",
        a.activation as "activation",
        a.about_status as "aboutStatus",
        a.about_reason as "aboutReason",
        a.about_hospital as "aboutHospital"
        <if test="queryType==null or queryType=='' or queryType=='djzl' or queryType=='myzx' or queryType=='wmy'or queryType=='qzzx'or queryType=='ybp'or queryType=='wbp'">
            ,(select immune_card from immune_register where del_flag=1 and pet_id=a.id order by create_date desc LIMIT
            1) as "immuneRegister.immuneCard"
            ,(select injection_enddate from immune_register where del_flag=1 and pet_id=a.id order by create_date desc
            LIMIT 1) as "immuneRegister.injectionEnddate"
            ,(select status from immune_register where del_flag=1 and pet_id=a.id order by create_date desc LIMIT 1) as
            "immuneRegister.status"
        </if>
        <if test="queryType!=null and queryType!=''   and queryType!='myzx' and queryType!='wmy'and queryType!='qzzx'and queryType!='ybp'and queryType!='wbp'">
            ,r.immune_card as "immuneRegister.immuneCard"
            ,r.injection_enddate as "immuneRegister.injectionEnddate"
            ,r.status as "immuneRegister.status"
        </if>
        from pet_certificates a
        <if test="queryType!=null and queryType!=''   and queryType!='myzx' and queryType!='wmy'and queryType!='qzzx'and queryType!='ybp'and queryType!='wbp'">
            left join immune_register r on r.pet_id = a.id
        </if>
        where 1 = 1
        <if test="queryType!='djzl' and queryType!='wmy'and queryType!='qzzx'and queryType!='ybp'and queryType!='wbp'">
            <if test="hospitalId!=null and hospitalId!=''">
                and r.hospital_id = #{hospitalId}
            </if>
        </if>
        <if test="queryType=='djzl'">
            AND (r.injection_enddate >= now()
            or r.injection_enddate &lt; now()
            or (immune_id is null or immune_id = '')

            )
        </if>
        <if test="queryType=='djzl' or queryType=='wmy'or queryType=='qzzx'or queryType=='ybp'or queryType=='wbp'">
            <if test="hospitalId!=null and hospitalId!=''">
                and a.hospital_id = #{hospitalId}
            </if>
        </if>
        <if test="queryType!=null and queryType=='myyx'">
            and r.injection_enddate >= now()
            and  a.del_flag = 1
        </if>



        <if test="queryType!=null and queryType=='myzx'">
            and a.id in(select pet_id from immune_cancel where del_flag = 1 and status = 2)
        </if>
        <if test="queryType!=null and queryType=='ybp'">
            and a.immune_id is not null and a.immune_id !=''
            and a.pet_num is not null and a.pet_num !=''
            and  a.del_flag = 1
        </if>
        <if test="queryType!=null and queryType=='wbp'">
            and a.immune_id is not null and a.immune_id!=''
            and (a.pet_num is null or a.pet_num ='')
            and  a.del_flag = 1
        </if>

        <if test="deptId!=null and deptId!=''">
            and a.pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="keyword!=null and keyword!=''">
            and (a.owner_name like concat('%',#{keyword},'%') or
            a.pet_id_card like concat('%',#{keyword},'%') or
            a.tel like concat('%',#{keyword},'%') or
            a.owner_address like concat('%',#{keyword},'%') or
            a.pet_name like concat('%',#{keyword},'%') or
            a.pet_num like concat('%',#{keyword},'%') )
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(a.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="petType!=null and petType!=''">
            and a.pet_type=#{petType}
        </if>
    </select>-->
  <!--  <select id="getTotalNum" resultType="java.util.HashMap"
            parameterType="com.ruoyi.modules.immune.entity.ImmuneRegister">
        select (select count(id) from pet_certificates where 1=1 and del_flag=1
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="deptId!=null and deptId!=''">
            and pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and hospital_id=#{hospitalId}
        </if>
        ) as "djzl"
        , (
        SELECT count(a.id)
        FROM pet_certificates a
        LEFT JOIN immune_register r ON r.pet_id = a.id
        WHERE a.del_flag = 1 AND r.injection_enddate >= now()
        <if test="deptId!=null and deptId!=''">
            and a.pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(a.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and r.hospital_id=#{hospitalId}
        </if>
        ) as "myyx"
        , (
        SELECT count(a.id)
        FROM pet_certificates a
        JOIN immune_register r ON r.pet_id = a.id
        where r.injection_enddate &lt; now() and r.del_flag=1 and a.del_flag=1
        <if test="deptId!=null and deptId!=''">
            and a.pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(a.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and r.hospital_id=#{hospitalId}
        </if>
        ) as "mygq"
        , (select count(id)
        from immune_register
        where del_flag = 1
        and pet_id in (select id from pet_certificates where del_flag = 1
        <if test="deptId!=null and deptId!=''">
            and pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
            )
        and datediff(injection_enddate, now()) &lt;= 30
        and datediff(injection_enddate, now()) > 0
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and hospital_id=#{hospitalId}
        </if>
        ) as "myjjgq"
        , (select count(id)
        from pet_certificates
        where del_flag = 1
        and (immune_id is null or immune_id = '')
        <if test="deptId!=null and deptId!=''">
            and pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and hospital_id=#{hospitalId}
        </if>
        ) as "wmy"
        , (select count(id) from pet_certificates where del_flag = 2
        <if test="deptId!=null and deptId!=''">
            and pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and hospital_id=#{hospitalId}
        </if>
        ) as "qzzx"
        , (
        select count(id) from pet_certificates where cancel_id in (select id from immune_cancel where del_flag=1 and status=2)
        and del_flag=1
        <if test="deptId!=null and deptId!=''">
            and pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and pet_id in (select id from pet_certificates where hospital_id=#{hospitalId})
        </if>
        ) as "myzx"
        , (select count(c.id)
        from pet_certificates c
        where c.del_flag = 1
        and c.immune_id is not null
        and c.immune_id  !='' and c.pet_num is not null and c.pet_num !=''
        <if test="deptId!=null and deptId!=''">
            and c.pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(c.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(c.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and c.hospital_id=#{hospitalId}
        </if>
        ) as "ybp"
        , (select count(c.id) from pet_certificates c
        where c.del_flag=1 and c.immune_id is not null and c.immune_id!='' and (c.pet_num is null or c.pet_num ='')
        <if test="deptId!=null and deptId!=''">
            and c.pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(c.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(c.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and c.hospital_id=#{hospitalId}
        </if>
        ) as "wbp"
        from dual

    </select>-->
    <select id="getTotalNum" resultType="java.util.HashMap"
            parameterType="com.ruoyi.modules.immune.entity.ImmuneRegister">
        select (select count(id) from pet_certificates where 1=1 and del_flag=1
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="deptId!=null and deptId!=''">
            and pet_dept in (select dept_id from sys_dept where dept_id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and hospital_id=#{hospitalId}
        </if>
        ) as "djzl"
        ,(select count(id) from immune_register where del_flag=1
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and hospital_id=#{hospitalId}
        </if>
        ) as myzl
        , (
        SELECT count(a.id)
        FROM pet_certificates a
        WHERE a.del_flag = 1
        <if test="deptId!=null and deptId!=''">
            and a.pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(a.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        AND a.immune_id in (select r.id from immune_register r where r.del_flag=1 AND r.injection_enddate >= now()
        <if test="hospitalId!=null and hospitalId!=''">
            and r.hospital_id=#{hospitalId}
        </if>)
        ) as "myyx"
        , (
        SELECT count(a.id)
        FROM pet_certificates a
        where a.del_flag=1
        <if test="deptId!=null and deptId!=''">
            and a.pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(a.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        AND a.immune_id in (select r.id from immune_register r where r.del_flag=1 AND r.injection_enddate &lt; now()
        <if test="hospitalId!=null and hospitalId!=''">
            and r.hospital_id=#{hospitalId}
        </if>)
        ) as "mygq"
        , ( SELECT count(id) FROM pet_certificates a WHERE a.del_flag = 1
        AND a.immune_id in (select r.id from  immune_register r where r.del_flag=1
            and datediff(r.injection_enddate, now()) &lt;= 30
            and datediff(r.injection_enddate, now()) > 0
            <if test="bTime!=null and eTime!=null">
                and DATE_FORMAT(r.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
                AND DATE_FORMAT(r.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
            </if>
            <if test="hospitalId!=null and hospitalId!=''">
                and r.hospital_id=#{hospitalId}
            </if>)
        <if test="deptId!=null and deptId!=''">
            and a.pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        ) as "myjjgq"
        , (select count(a.id)
        from pet_certificates a
        where a.del_flag = 1  and (a.immune_id is null or a.immune_id = '')
        <if test="deptId!=null and deptId!=''">
            and a.pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and a.hospital_id=#{hospitalId}
        </if>
        ) as "wmy"
        , (select count(id) from pet_certificates where del_flag = 2
        <if test="deptId!=null and deptId!=''">
            and pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and hospital_id=#{hospitalId}
        </if>
        ) as "qzzx"
        , (select count(c.id)
        from pet_certificates c
        where c.del_flag = 1
        and c.immune_id is not null
        and c.immune_id  !='' and c.pet_num is not null and c.pet_num !=''
        <if test="deptId!=null and deptId!=''">
            and c.pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(c.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(c.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and c.hospital_id=#{hospitalId}
        </if>
        ) as "ybp"
        , (select count(c.id) from pet_certificates c
        where c.del_flag=1 and c.immune_id is not null and c.immune_id!='' and (c.pet_num is null or c.pet_num ='')
        <if test="deptId!=null and deptId!=''">
            and c.pet_dept in (select id from sys_dept where id=#{deptId} or parent_id=#{deptId})
        </if>
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(c.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(c.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="hospitalId!=null and hospitalId!=''">
            and c.hospital_id=#{hospitalId}
        </if>
        ) as "wbp"
        from dual
    </select>
    <select id="getDeptSort" resultType="java.util.HashMap"
            parameterType="com.ruoyi.modules.immune.entity.ImmuneRegister">
        select res.*, ifnull(res.yx / res.djbz, 0) as afterNum
        from (
        select d.dept_id
        , d.dept_name as deptName
,(select count(r.id) from immune_register r
        join pet_certificates c on c.id=r.pet_id
        where r.del_flag=1 and c.del_flag=1 	AND c.pet_dept in (select dept_id from sys_dept where dept_id=d.dept_id or parent_id=d.dept_id)
    ) as djbz
        , ifnull((
        SELECT count(a.id)
        FROM pet_certificates a
        LEFT JOIN immune_register r ON r.pet_id = a.id
        WHERE a.del_flag = 1 AND r.injection_enddate >= now() AND a.pet_dept in (select dept_id from sys_dept where dept_id=d.dept_id or parent_id=d.dept_id)
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ), 0) as "yx"
        , ifnull((
        SELECT count(a.id)
        FROM pet_certificates a
        LEFT JOIN immune_register r ON r.pet_id = a.id
        WHERE a.del_flag = 1 AND r.injection_enddate &lt; now() AND a.pet_dept in (select dept_id from sys_dept where dept_id=d.dept_id or parent_id=d.dept_id)
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(a.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ), 0) as "mygq"
        , (
        SELECT count(a.id)
        FROM pet_certificates a
        LEFT JOIN immune_cancel r ON r.pet_id = a.id
        WHERE a.del_flag = 1 AND r.status=2 AND a.pet_dept in (select dept_id from sys_dept where dept_id=d.dept_id or parent_id=d.dept_id)
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(a.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ) as "myzx"
        , (select count(id)
        from pet_certificates
        where del_flag = 1
        and (immune_id is null or immune_id = '')
        and pet_dept in (select id from sys_dept where id=d.dept_id or parent_id=d.dept_id )
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ) as "wmy"
        , (
        SELECT count(a.id)
        FROM pet_certificates a
        WHERE a.del_flag = 1 AND a.pet_dept in (select dept_id from sys_dept where dept_id=d.dept_id or parent_id=d.dept_id)
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ) as "dj"
        from sys_dept d
        where d.del_flag = 1
        and (d.parent_id=#{deptId} or d.dept_id=#{deptId})
        group by d.dept_id, d.dept_name) res
        order by afterNum desc
    </select>
<!--    street-->

    <select id="getDeptSortJD" resultType="java.util.HashMap"
            parameterType="com.ruoyi.modules.immune.entity.ImmuneRegister">
        select res.*, ifnull(res.yx / res.djbz, 0) as afterNum
        from (
        select d.id
             ,d.level
        , d.dept_name as deptName
        ,(select count(r.id) from immune_register r
        join pet_certificates c on c.id=r.pet_id
        where r.del_flag=1 and c.del_flag=1 	AND c.street in (select id from sys_dept where id=d.id or parent_id=d.id)
        ) as djbz
        , ifnull((
        SELECT count(a.id)
        FROM pet_certificates a
        LEFT JOIN immune_register r ON r.pet_id = a.id
        WHERE a.del_flag = 1 AND r.injection_enddate >= now() AND a.street in (select id from sys_dept where id=d.id or parent_id=d.id )
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ), 0) as "yx"
        , ifnull((
        SELECT count(a.id)
        FROM pet_certificates a
        LEFT JOIN immune_register r ON r.pet_id = a.id
        WHERE a.del_flag = 1 AND r.injection_enddate &lt; now() AND a.street in (select id from sys_dept where id=d.id or parent_id=d.id )
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(a.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ), 0) as "mygq"
        , (
        SELECT count(a.id)
        FROM pet_certificates a
        LEFT JOIN immune_cancel r ON r.pet_id = a.id
        WHERE a.del_flag = 1 AND r.status=2 AND a.street in (select id from sys_dept where id=d.id or parent_id=d.id )
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(a.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ) as "myzx"
        , (select count(id)
        from pet_certificates
        where del_flag = 1
        and (immune_id is null or immune_id = '')
        and pet_dept in (select id from sys_dept where id=d.id or parent_id=d.id )
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ) as "wmy"
        , (
        SELECT count(a.id)
        FROM pet_certificates a
        WHERE a.del_flag = 1 AND a.street in (select id from sys_dept where id=d.id or parent_id=d.id )
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ) as "dj"
        from sys_dept d
        where d.del_flag = 1
        and (d.parent_id=#{deptId} or d.id=#{deptId})
        group by d.id, d.dept_name) res
        order by level, afterNum desc
    </select>
    <select id="getList" parameterType="com.ruoyi.modules.immune.entity.ImmuneRegister"
            resultType="com.ruoyi.modules.immune.entity.ImmuneRegister">
        select
        <include refid="ImmuneRegisterSql"/>
        from immune_register a
        where del_flag = 1
        <if test="vaccineBatch != null and vaccineBatch != ''">
            and a.vaccine_batch = #{vaccineBatch}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="petId != null and petId != ''">
            and a.pet_id = #{petId}
        </if>
        order by a.create_date desc
    </select>

    <select id="getById" resultType="com.ruoyi.modules.immune.entity.ImmuneRegister">
        SELECT
        <include refid="ImmuneRegisterSql"/>
        ,(select address from qualifi where del_flag=1 and id=a.hospital_id)as "qualifiAddress"
        FROM immune_register a
        where a.id = #{id}
    </select>

    <select id="getByPetId" resultType="com.ruoyi.modules.immune.entity.ImmuneRegister">
        SELECT
        <include refid="ImmuneRegisterSql"/>
        FROM immune_register a
        where a.pet_id = #{petId}
        and a.type = 2
        and a.id= #{immuneId}
        order by a.create_date desc
    </select>

    <select id="getPetImmune" resultType="com.ruoyi.modules.immune.entity.ImmuneRegister">
        SELECT
        <include refid="ImmuneRegisterSql"/>
        FROM immune_register a
        where id = #{immuneId}
        and a.pet_id = #{petId}
    </select>

    <insert id="insert" parameterType="com.ruoyi.modules.immune.entity.ImmuneRegister">
        insert into immune_register(id,
                                    hospital,
                                    doctor,
                                    type,
                                    vaccine_brand,
                                    vaccine_batch,
                                    injection_date,
                                    injection_enddate,
                                    status,
                                    reason,
                                    pet_id,
                                    create_date,
                                    create_by,
                                    del_flag,
                                    hospital_id,
                                    about_make,
                                    about_date,
                                    immune_card,cross_type)
        values (#{id},
                #{hospital},
                #{doctor},
                #{type},
                #{vaccineBrand},
                #{vaccineBatch},
                #{injectionDate},
                #{injectionEnddate},
                #{status},
                #{reason},
                #{petId},
                #{createDate},
                #{createBy},
                #{delFlag},
                #{hospitalId},
                #{aboutMake},
                #{aboutDate},
                #{immuneCard},
                #{crossType})
    </insert>

    <update id="update" parameterType="com.ruoyi.modules.immune.entity.ImmuneRegister">
        update immune_register
        set doctor            = #{doctor},
            hospital          = #{hospital},
            type              = #{type},
            vaccine_brand     = #{vaccineBrand},
            vaccine_batch     = #{vaccineBatch},
            injection_date    = #{injectionDate},
            injection_enddate = #{injectionEnddate},
            update_date       = #{updateDate},
            update_by         = #{updateBy},
            hospital_id       = #{hospitalId},
            about_make        = #{aboutMake},
            about_date        = #{aboutDate},
            immune_card       = #{immuneCard}
        where id = #{id}
    </update>

    <update id="delete" parameterType="com.ruoyi.modules.immune.entity.ImmuneRegister">
        update immune_register
        set del_flag = #{delFlag}
        where id = #{id}
    </update>


    <select id="getImmuneInfo" parameterType="java.lang.String"
            resultType="com.ruoyi.modules.immune.entity.ImmuneRegister">
        select
        <include refid="ImmuneRegisterSql"/>
        from immune_register a
        where del_flag = 1 and a.pet_id = #{petId}
        order by a.create_date desc
    </select>

    <update id="updateStatus" parameterType="com.ruoyi.modules.immune.entity.ImmuneRegister">
        update immune_register
        set status      = #{status},
            reason      = #{reason},
            update_date = #{updateDate},
            update_by   = #{updateBy}
        where id = #{id}
    </update>

    <update id="addVaccine" parameterType="java.lang.String">
        update vaccine_record
        set batch_surplus = (batch_surplus + 1)
        where id = #{batchId} ORDER by create_date LIMIT 1
    </update>

    <update id="reduceVaccine" parameterType="java.lang.String">
        update vaccine_record
        set batch_surplus = (batch_surplus - 1)
        where id = #{batchId} ORDER by create_date LIMIT 1
    </update>

    <update id="updateApply" parameterType="java.lang.String">
        update immune_register
        set status = '2'
        where id = #{id}
    </update>


    <select id="findCard" parameterType="java.lang.String" resultType="java.lang.String" >
        select immune_card  from immune_register  where  del_flag = 1 and immune_card = #{str}
    </select>

    <select id="currval" parameterType="java.lang.String" resultType="java.lang.String" >
        SELECT   CONCAT(seq_code,LPAD(current_val,6,0) ) FROM sequence where seq_name = #{seqName}
    </select>

    <update id="nextval" parameterType="java.lang.String"  >
        UPDATE sequence
        SET current_val = current_val + increment_val
        WHERE seq_name = #{seqName}
    </update>

    <update id="updateval" parameterType="java.lang.String"  >
        UPDATE sequence
        SET current_val = 1,seq_code = #{seqCode}
        WHERE seq_name = #{seqName}
    </update>


    <select id="getHospital" resultType="java.util.HashMap">
        SELECT d.dept_name as NAME,
               count(t.id) AS YYZS,
               (SELECT count(pc.id) from pet_certificates pc where pc.del_flag = 1 and pc.immune_id is not NULL and pc.pet_dept = d.id) AS MYZS,
               (SELECT count(pc.pet_num) from pet_certificates pc where pc.del_flag = 1 and pc.pet_dept = d.id  and pet_num != '' ) AS BZZS,
               (SELECT count(pc.id) from pet_certificates pc join immune_register ir on ir.id=pc.immune_id
                where  pc.del_flag = 1 and pc.pet_dept = d.id  and TO_DAYS( ir.create_date ) = TO_DAYS( now())) AS MYSL,
               (SELECT count(pc.pet_num) from pet_certificates pc where pc.del_flag = 1 and pc.pet_dept = d.id  and pet_num != ''
                and TO_DAYS( pc.cert_date ) = TO_DAYS( now())) AS BZSL
        FROM sys_dept d
            LEFT JOIN qualifi t ON county = d.id
        WHERE t.type = 0
        GROUP BY d.dept_name
        order by YYZS desc
    </select>

    <select id="getImmune" resultType="java.util.HashMap">
        SELECT
            IFNULL( count( i.immune_id ), 0 ) AS MYSL,
            (IFNULL( count( i.id ), 0 ) - IFNULL( count( i.immune_id ), 0 )) AS WMYSL,
            substr( CONVERT ( t2.year_month_str, CHAR ), 6 ) MM
        FROM
            (SELECT @rownum := @rownum + 1 AS num,
		        date_format( DATE_SUB( now(), INTERVAL @rownum MONTH ), '%Y/%m' ) AS year_month_str
                FROM
                    ( SELECT @rownum := - 1 ) AS r_init,
                    ( SELECT c.id FROM sys_dict c LIMIT 6 ) AS c_init
            ) t2
                LEFT JOIN ( SELECT t.id, t.create_date, t.del_flag, t.immune_id FROM pet_certificates t ) AS i ON (
                        CONCAT( DATE_FORMAT( i.create_date, '%Y' ), '/', DATE_FORMAT( i.create_date, '%m' ) ) = t2.year_month_str
                    AND i.del_flag = 1
                )
        GROUP BY
            t2.year_month_str
    </select>

    <select id="getPetNum" resultType="java.util.HashMap">
        SELECT
            IFNULL(count(case when i.pet_num='' then null else i.pet_num end ), 0 ) AS BLSL,
            substr( CONVERT ( t2.year_month_str, CHAR ), 6 ) MM
        FROM
            ( SELECT
                    @rownum := @rownum + 1 AS num,
		            date_format( DATE_SUB( now(), INTERVAL @rownum MONTH ), '%Y/%m' ) AS year_month_str
                FROM
                    ( SELECT @rownum := - 1 ) AS r_init,
                    ( SELECT c.id FROM sys_dict c LIMIT 6 ) AS c_init
            ) t2
                LEFT JOIN pet_certificates AS i ON (
                        CONCAT( DATE_FORMAT( i.create_date, '%Y' ), '/', DATE_FORMAT( i.create_date, '%m' ) ) = t2.year_month_str
                    AND i.del_flag = 1
                )
        GROUP BY
            t2.year_month_str
    </select>

    <select id="getImmuneWarn" resultType="java.lang.String">
        SELECT
            DISTINCT a.tel
        FROM
            pet_certificates a
        LEFT JOIN immune_register ua ON ua.id = a.immune_id
        WHERE
            a.del_flag = 1
            AND injection_enddate >= DATE_SUB( CURDATE(), INTERVAL 1 MONTH )
            AND injection_enddate &lt; CURDATE();
    </select>
    <select id="getDogJscStatistics" resultType="java.util.Map">
        SELECT
        (
        SELECT
        count( id )
        FROM
        pet_certificates
        WHERE
        1 = 1
        AND del_flag = 1
        <if test="startTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{startTime},'%Y-%m-%d')
        </if>
        ) AS "djzl",
        (
        SELECT
        count( a.id )
        FROM
        pet_certificates a
        WHERE
        a.del_flag = 1
        AND a.immune_id IN ( SELECT r.id FROM immune_register r WHERE r.del_flag = 1 AND r.injection_enddate &lt; now() )
        <if test="startTime!=null">
            and DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{startTime},'%Y-%m-%d')
        </if>
        ) AS "mygq",
        (
        SELECT
        count( c.id )
        FROM
        pet_certificates c
        WHERE
        c.del_flag = 1
        AND c.immune_id IS NOT NULL
        AND c.immune_id != ''
        AND c.pet_num IS NOT NULL
        AND c.pet_num != ''
        <if test="startTime!=null">
            and DATE_FORMAT(c.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{startTime},'%Y-%m-%d')
        </if>
        ) AS "ybp",
        (SELECT count(id) from take_in where del_flag = 1
        <if test="startTime!=null">
            and DATE_FORMAT(time,'%Y-%m-%d') >=DATE_FORMAT(#{startTime},'%Y-%m-%d')
        </if>
         ) as srs
        FROM
        DUAL
    </select>
</mapper>
