package com.ruoyi.common.dtalk;


import com.ruoyi.common.dtalk.vo.OrganizationNodeInfo;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @Date 2022/3/8
 * @Description 查询浙政钉部门机构信息
 */
public interface DeptDtalkService {

    /**
     * 根据EmployeeCode获取organizationCode
     *
     * @param employeeCode
     * @return
     */
    String getOrganizationCode(String employeeCode) throws ExecutionException;


    /**
     * 根据organizationCode获取organizationName
     *
     * @param organizationCode
     * @return
     */
    String getOrganizationName(String organizationCode) throws ExecutionException;

    /**
     * 获取当前组织结构树
     *
     * @return
     */
    List<OrganizationNodeInfo> getOrganization() throws ExecutionException;

    /**
     * 查询子集部门
     *
     * @param organizationCode
     * @return
     */
    List<OrganizationNodeInfo> getPageOrganization(String organizationCode) throws ExecutionException;

}
