package com.ruoyi.modules.immune.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.brand.dao.PetBrandDao;
import com.ruoyi.modules.brand.entity.PetBrand;
import com.ruoyi.modules.certificates.dao.PetCertificatesDao;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.certificates.service.PetCertificatesService;
import com.ruoyi.modules.immune.dao.ImmuneCancelDao;
import com.ruoyi.modules.immune.entity.ImmuneCancel;
import com.ruoyi.modules.user.dao.SysUserDao;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.service.SysUploadFileService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.Date;
import java.util.List;


/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 10:52
 * @version: 1.0
 **/
@Service
public class ImmuneCancelService extends BaseService<ImmuneCancelDao, ImmuneCancel> {

    @Autowired
    PetCertificatesDao petCertificatesDao;
    @Autowired
    PetCertificatesService petCertificatesService;
    @Autowired
    SysUploadFileService uploadFileService;
    @Resource
    PetBrandDao petBrandDao;

    /**
     * 根据信息 查询犬只注销列表
     * @param petCertificates
     * @return
     */
    public PageInfo<PetCertificates> queryPageList(PetCertificates petCertificates){
        PageHelper.startPage(petCertificates.getPageNum(),petCertificates.getPageSize());
        PageInfo<PetCertificates> pageInfo = new PageInfo<PetCertificates>(dao.queryPageList(petCertificates));
        return pageInfo;
    }

    public PetCertificates getByCardId(String id,String petIdCard) {//犬只ID
        PetCertificates petCertificates =petCertificatesDao.getById(id);
        petCertificates.setImmuneCancel(dao.getById(id));
        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.setInstanceId(id);
        List<SysUploadFile> upList =  uploadFileService.getList(sysUploadFile);
        petCertificates.setUploadFiles(upList);
        return petCertificates;
    }

    /**
     * 免疫注销保存
     * @param immuneCancel
     * @return
     */
    @Override
    public void saveOrUpdate(ImmuneCancel immuneCancel) {
        super.saveOrUpdate(immuneCancel);
//        PetCertificates petCertificates=new PetCertificates();
//        petCertificates.setId(immuneCancel.getPetId());
//        petCertificates.setCancelId(immuneCancel.getId());//存入犬只过户ID
//        petCertificatesService.updateImmune(petCertificates);

        if ("2".equals(immuneCancel.getStatus())) {
            PetCertificates pet = new PetCertificates();
            pet.setCancelId(immuneCancel.getId());//存入犬只注销ID
            pet.setCancelStatus(immuneCancel.getStatus());
            pet.setId(immuneCancel.getPetId());
            pet.setPetNum("");
            pet.setDelFlag(2);
            petCertificatesService.updateImmune(pet);

            //犬牌注销
            PetBrand petBrand = new PetBrand();
            petBrand.setBrandNum(immuneCancel.getBrandNum());
            // 犬只走失注销 犬牌状态改为走失  犬只死亡注销 犬牌状态改为注销
            if(immuneCancel.getType()== 1){//走失注销
                petBrand.setIsCancellation(3);
            } else if(immuneCancel.getType() == 2){//死亡注销
                petBrand.setIsCancellation(2);
            } else if(immuneCancel.getType() == 3){//重复注销
                petBrand.setIsCancellation(4);
            } else if(immuneCancel.getType() == 4){//其他
                petBrand.setIsCancellation(5);
            }
            petBrand.setOffDate(new Date());
            petBrand.setOffCom(immuneCancel.getCancelId());
            petBrandDao.updateIsCancellation(petBrand);
        }
    }

    /**
     * 免疫注销审核
     * @param immuneCancel
     * @return
     */
    public void updateStatus(ImmuneCancel immuneCancel){
        dao.updateStatus(immuneCancel);
    }

    public ImmuneCancel getByPet(ImmuneCancel immuneCancel){
        return dao.getByPet(immuneCancel);
    }


}
