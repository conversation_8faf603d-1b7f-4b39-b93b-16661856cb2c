package com.ruoyi.modules.immune.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.immune.entity.ImmuneRegister;
import com.ruoyi.modules.immune.entity.ImmuneTransfer;
import com.ruoyi.modules.immune.service.ImmuneRegisterService;
import com.ruoyi.modules.immune.service.ImmuneTransferService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @version: 1.0
 **/
@RestController
@RequestMapping("immuneTransfer")
public class ImmuneTransferController {
    @Autowired
    private ImmuneTransferService service;

    @RequestMapping("getPageList")
    public AjaxResult getPageList(ImmuneTransfer immuneTransfer){
        return AjaxResult.success(service.getPageList(immuneTransfer));
    }

    @RequestMapping("saveOrUpdate")
    public AjaxResult saveOrUpdate(ImmuneTransfer immuneTransfer){
        return AjaxResult.success(service.saveOrUpdateRest(immuneTransfer));
    }
    @RequestMapping("saveOrUpdateH5")
    public AjaxResult saveOrUpdateH5(ImmuneTransfer immuneTransfer){
        return service.saveOrUpdateRestH5(immuneTransfer);
    }

    @RequestMapping("delete")
    public AjaxResult delete(ImmuneTransfer immuneTransfer){
        service.delete(immuneTransfer);
        return AjaxResult.success();
    }

    @RequestMapping("getById")
    public AjaxResult getById(String id){
        return AjaxResult.success(service.getById(id));
    }


    /**
     * 免疫管理审核
     * @param immuneTransfer
     * @return
     */
    @RequestMapping("updateStatus")
    public AjaxResult updateStatus(ImmuneTransfer immuneTransfer){
        service.updateStatus(immuneTransfer);
        return AjaxResult.success();
    }

    @RequestMapping("getByCardId")
    public AjaxResult getByCardId(String id, String petIdCard) {
        return AjaxResult.success(service.getByCardId(id, petIdCard));
    }


    /**
     * 根据信息 查询犬只过户
     * @param petCertificates
     * @return
     */
    @RequestMapping("queryPageList")
    public AjaxResult queryPageList(PetCertificates petCertificates){
        return AjaxResult.success(service.queryPageList(petCertificates));
    }

    /**
     * 根据信息 查询犬只过户 移动端
     * @param petCertificates
     * @return
     */
    @RequestMapping("queryPageListH5")
    public AjaxResult queryPageListH5(PetCertificates petCertificates){
        return service.queryPageListH5(petCertificates);
    }

    /**
     * 根据信息饲主身份证，验证是否有犬类
     * @param immuneTransfer
     * @return
     */
    @RequestMapping("verifIdCard")
    public AjaxResult verifIdCard(ImmuneTransfer immuneTransfer){
        return AjaxResult.success(service.verifIdCard(immuneTransfer));
    }

}
