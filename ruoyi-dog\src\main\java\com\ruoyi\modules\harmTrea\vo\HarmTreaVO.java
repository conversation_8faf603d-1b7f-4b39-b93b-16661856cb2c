package com.ruoyi.modules.harmTrea.vo;

import cn.hutool.db.DaoTemplate;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.modules.harmTrea.entity.HarmTrea;
import com.ruoyi.modules.takeIn.entity.TakeIn;
import com.ruoyi.modules.user.entity.SysUploadFile;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class HarmTreaVO {

    private String id;

    private String harmTreaId;

    private String imgUrl;

    /**
     * 移交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signDate;

    /**
     * 收容时长
     */
    private String takeInDuration;

    /**
     * 收容时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date takeInDate;

    /**
     * 收容类型
     */
    private String type;

    /**
     * 犬牌号
     */
    private String petNum;

    /**
     * 附件
     */
    private List<SysUploadFile> uploadFileList;

    private HarmTrea harmTrea;

    /**
     * 处理状态 0未处理 1已处理
     */
    private Integer status;

    private Integer pageNum = 1;

    private Integer pageSize = 10;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
}
