package com.ruoyi.modules.user.controller;

import com.alibaba.fastjson2.JSON;
import com.qiniu.sms.SmsManager;
import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.yw.mapper.XxInfoMapper;
import com.ruoyi.modules.hospital.dao.QualifiDao;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.user.dao.SysUserRoleDao;
import com.ruoyi.modules.user.entity.SysUser;
import com.ruoyi.modules.user.entity.SysUserRole;
import com.ruoyi.modules.user.service.SysUserService;
import com.ruoyi.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2020/1/3/003
 */
@RequestMapping("/sysUser")
@RestController
public class SysUserControllerDog {

    @Autowired
    public SysUserService sysUserService ;

    @Autowired
    public QualifiDao qualifiDao;
    @Autowired
    public SysUserRoleDao sysUserRoleDao;
    @Autowired
    private XxInfoMapper xxInfoMapper;

    /**
     * 根据id获取用户信息
     * @param id
     * @return
     */
    @RequestMapping("/getById")
    public AjaxResult getById(String id){
        return AjaxResult.success(sysUserService.getById(id));
    }

    /**
     * 分页获取用户列表数据
     * @param sysUser
     * @return
     */
    @RequestMapping("/getPageList")
    public AjaxResult getPageList(SysUser sysUser){
        return AjaxResult.success(sysUserService.getPageList(sysUser));
    }


    @RequestMapping("/getList")
    public AjaxResult getList(SysUser sysUser){
        return AjaxResult.success(sysUserService.getList(sysUser));
    }


    /**
     * 检查用户是否登录
     * @param request
     * @return
     */
    @RequestMapping("/checkLogin")
    public AjaxResult checkLogin(HttpServletRequest request){
        SysUser user = (SysUser) request.getSession().getAttribute("sysUser");
        if(user != null){
            return AjaxResult.success();
        }else{
            return AjaxResult.error();
        }
    }

    /**
     * 登录接口
     * @param sysUser
     * @param request
     * @return
     */
    @RequestMapping("/login")
    public AjaxResult login(SysUser sysUser, HttpServletRequest request){
        UserMapCache.clearOldUser();
        //判断密码是否已经错误超过5次
        UserCache userCacheLogin = UserMapCache.getByToken(sysUser.getUserName());
        if (userCacheLogin != null && userCacheLogin.getErrorNum() >= 5) {
            System.out.println(userCacheLogin.getEndTime()+"---"+userCacheLogin.getId());
            return AjaxResult.error("密码错误次数过多，请五分钟后重试！");
        }
        SysUser result = sysUserService.checkLoginUser(sysUser);
        if(result != null){
            // 登录成功  用户信息存储到session中
//            request.getSession().setAttribute("sysUser",result);
            // 随机令牌
            String token = IdGen.uuid();
            // 用户信息存入缓存中
            UserCache userCache = new UserCache();
            userCache.setId(result.getId()+"");
            UserMapCache.login(token,userCache);
            // 用户令牌返回页面
            result.setToken(token);
            //关联返回资质信息
            Qualifi qualifi = new Qualifi();
            qualifi.setAccount(result.getId());
            result.setUserQualifi(qualifiDao.getQualifiByAccount(qualifi));
            if(result.getUserType().intValue() != 2){
                SysUserRole role=new SysUserRole();
                role.setUserId(result.getId());
                List<SysUserRole> roleList=sysUserRoleDao.getList(role);
                result.setSysUserRoleList(roleList);
            }
            return AjaxResult.success(result);
        }else{
            // 登录失败
            if (userCacheLogin != null){
                int errorNum = userCacheLogin.getErrorNum();
                userCacheLogin.setErrorNum(errorNum + 1);
                return AjaxResult.error("密码错误！");
            }else {
                userCacheLogin = new UserCache();
                userCacheLogin.setId(sysUser.getUserName());
                userCacheLogin.setErrorNum(1);
                UserMapCache.loginError(sysUser.getUserName(), userCacheLogin);
            }
            return AjaxResult.error("账号密码错误！");
        }
    }
    @RequestMapping("/editPass")
    public AjaxResult editPass(SysUser sysUser, HttpServletRequest request){
        SysUser result = sysUserService.checkLoginUser(sysUser);
        if(result != null){
            sysUser.setId(result.getId());
            sysUserService.editPass(sysUser);
            return AjaxResult.success(result);
        }else{
            // 登录失败
            return AjaxResult.error("账号密码错误！");
        }
    }

    /**
     * 保存或更新用户基本信息
     * @param sysUser
     * @return
     */
    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(SysUser sysUser){
        sysUserService.saveOrUpdate(sysUser);
        return AjaxResult.success(sysUser);
    }

    /**
     * 保存或更新用户基本信息
     * @param sysUser
     * @return
     */
    @RequestMapping("/delete")
    public AjaxResult delete(SysUser sysUser){
        sysUserService.delete(sysUser);
        return AjaxResult.success();
    }

    /**
     * 退出登录
     * @return
     */
    @RequestMapping("/loginOut")
    public AjaxResult loginOut(HttpServletRequest request){
        // 后端移除用户session
        request.getSession().removeAttribute("sysUser");
        return AjaxResult.success();
    }

    /**
     * 检查用户名是否存在
     * @param userName
     * @return
     */
    @RequestMapping("/checkUserName")
    public AjaxResult checkUserName(String userName){
        SysUser sysUser = sysUserService.getByUserName(userName);
        if(sysUser != null && !sysUser.getId().equals("")){
            // 存在用户  不能添加
            return AjaxResult.success();
        } else{
            return AjaxResult.error();
        }
    }

    /**
     * 修改用户角色信息
     *
     * @return
     */
    @RequestMapping("/changeUserRole")
    public AjaxResult changeUserRole(SysUser user) {
        sysUserService.changeUserRole(user);
        return AjaxResult.success();
    }

    @RequestMapping("updatePassWord")
    public AjaxResult updatePassWord(SysUser sysUser){
        return AjaxResult.success(sysUserService.updatePassWord(sysUser));
    }

    @RequestMapping("resetPwd")
    public AjaxResult resetPwd(SysUser sysUser){
        sysUserService.resetPwd(sysUser);
        return AjaxResult.success();
    }

    /**
     * 查询执法窗口地址
     *
     * @return
     */
    @RequestMapping("/queryUserAddres")
    public AjaxResult queryUserAddres() {
        return AjaxResult.success(sysUserService.queryUserAddres());
    }


    /**
     * 修改密码
     *
     * @return
     */
    @RequestMapping("/changePassword")
    public AjaxResult changePassword(String oldPassword, SysUser user) {
        String result = sysUserService.changePassword(oldPassword, user);
        if (result != null) {
            return AjaxResult.error(result);
        } else {
            return AjaxResult.success();
        }
    }
    @RequestMapping("/getEchatByDept")
    public AjaxResult getEchatByDept(){
        return AjaxResult.success(sysUserService.getEchatByDept());
    }

    /**
     * 登录发送短信验证码
     * @return
     */
    @RequestMapping("/sendLoginMessage")
    public AjaxResult sendLoginMessage(String phone){
        // 随机验证码
        String code = ((int) ((Math.random() * 9 + 1) * 100000)) + "";
        System.out.println(code);
        //比较用户过期时间
        UserMapCache.clearOldUser();
        UserCache userCache = UserMapCache.getByToken(phone);
        if (userCache != null && userCache.getErrorNum() >= 5) {
            return AjaxResult.error("验证码错误次数过多，请五分钟后重试！");
        } else {
            // 用户验证码信息存入缓存中
            userCache = new UserCache();
            userCache.setId(phone);
            userCache.setCode(code);
            userCache.setErrorNum(0);
            UserMapCache.messageYzm(phone, userCache);
            // 调用短信发送验证码
            DianXinSMS.sendMessage(phone, "登录验证码：" + code, xxInfoMapper);
            return AjaxResult.success();
        }

    }

    /**
     * 校验短信验证码
      */
    @RequestMapping("/checkMessageCode")
    public AjaxResult checkMessageCode(String phone,String code){
        UserCache userCache = UserMapCache.getByToken(phone);
        if(userCache != null){
            if(userCache.getEndTime() < System.currentTimeMillis()){
                return AjaxResult.error("验证码已过期！");
            }else{
                if(userCache.getErrorNum() >= 5){
                    return AjaxResult.error("验证码错误次数过多，请五分钟后重试！");
                }
                if(userCache.getCode().equals(code)){
                    return AjaxResult.success();
                }else{
                    int errorNum = userCache.getErrorNum();
                    userCache.setErrorNum(errorNum + 1);
                    return AjaxResult.error("验证码错误！");
                }
            }
        }
        return AjaxResult.error("验证码错误！");
    }
}
