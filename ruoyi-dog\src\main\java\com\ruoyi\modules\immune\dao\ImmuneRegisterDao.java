package com.ruoyi.modules.immune.dao;


import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.immune.entity.ImmuneRegister;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 10:04
 * @version: 1.0
 **/
@Repository
public interface ImmuneRegisterDao extends BaseDao<ImmuneRegister> {
    public List<HashMap> getDeptSort(ImmuneRegister immuneRegister);

    public List<HashMap>  getDeptSortJD(ImmuneRegister immuneRegister);
    public HashMap getTotalNum(ImmuneRegister immuneRegister);

    public List<ImmuneRegister> getImmuneInfo(String petId);

    public ImmuneRegister getByPetId(@Param("petId") String petId, @Param("immuneId") String immuneId);

    public void updateStatus(ImmuneRegister immuneRegister);

    public ImmuneRegister getPetImmune(@Param("petId") String petId, @Param("immuneId") String immuneId);


    public void addVaccine(@Param("batchId") String batchId);

    public void reduceVaccine(@Param("batchId") String batchId);

    public void updateApply(String id);
    public List<PetCertificates> getPetPageList(ImmuneRegister immuneRegister);

    public String findCard(@Param("str") String str);

    public String currval(@Param("seqName") String seqName);

    public void nextval(@Param("seqName") String seqName);

    public void updateval(@Param("seqName") String seqName,@Param("seqCode") String seqCode);

    public List<HashMap> getHospital();
    public List<HashMap> getImmune();
    public List<HashMap> getPetNum();

    /**
     * 查询近一个月内免疫过期的用户手机号
     * @return
     */
    List<String> getImmuneWarn();

    /**
     * 犬类驾驶舱统计接口
     * @param startTime
     * @return
     */
    Map<String, Object> getDogJscStatistics(@Param("startTime") Date startTime);
}
