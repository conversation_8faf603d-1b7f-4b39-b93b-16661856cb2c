package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.UndercoverPerson;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 暗访督察人员Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-23
 */
@Mapper
@DataSource(value = DataSourceType.SLAVE)
public interface UndercoverPersonMapper 
{
    /**
     * 查询暗访督察人员
     * 
     * @param id 暗访督察人员主键
     * @return 暗访督察人员
     */
    public UndercoverPerson selectUndercoverPersonById(Long id);

    /**
     * 查询暗访督察人员列表
     * 
     * @param undercoverPerson 暗访督察人员
     * @return 暗访督察人员集合
     */
    public List<UndercoverPerson> selectUndercoverPersonList(UndercoverPerson undercoverPerson);

    /**
     * 新增暗访督察人员
     * 
     * @param undercoverPerson 暗访督察人员
     * @return 结果
     */
    public int insertUndercoverPerson(UndercoverPerson undercoverPerson);

    /**
     * 修改暗访督察人员
     * 
     * @param undercoverPerson 暗访督察人员
     * @return 结果
     */
    public int updateUndercoverPerson(UndercoverPerson undercoverPerson);

    /**
     * 删除暗访督察人员
     * 
     * @param id 暗访督察人员主键
     * @return 结果
     */
    public int deleteUndercoverPersonById(Long id);

    /**
     * 批量删除暗访督察人员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUndercoverPersonByIds(Long[] ids);

    List<UndercoverPerson> selectInstrucationPersonByIds(String[] longs, String personName);
}
