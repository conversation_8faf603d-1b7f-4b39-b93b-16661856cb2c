package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 暗访督察问题对象 t_undercover_problem
 * 
 * <AUTHOR>
 * @date 2023-05-23
 */
public class UndercoverProblem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 问题内容 */
    @Excel(name = "问题内容")
    private String content;

    /** 问题区域 */
    @Excel(name = "问题区域")
    private String region;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String detailAddress;

    /** 牵头部门 */
    @Excel(name = "牵头部门")
    private String leadingDepartment;

    /** 责任人 */
    @Excel(name = "责任人")
    private String personResponsible;

    /** 反馈情况 */
    @Excel(name = "反馈情况")
    private String feedbackSituation;

    /** 附件 */
    @Excel(name = "附件")
    private String fileUrl;

    /** 暗访督察id */
    @Excel(name = "暗访督察id")
    private Long undercoverInspectionId;

    /** 问题类型id */
    @Excel(name = "问题类型id")
    private Long type;

    /** 检查单位类别 */
    @Excel(name = "检查单位类别")
    private String checkType;

    /** 状态，1正常，9删除 */
    @Excel(name = "状态，1正常，9删除")
    private Long status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cTime;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setRegion(String region) 
    {
        this.region = region;
    }

    public String getRegion() 
    {
        return region;
    }
    public void setDetailAddress(String detailAddress) 
    {
        this.detailAddress = detailAddress;
    }

    public String getDetailAddress() 
    {
        return detailAddress;
    }
    public void setLeadingDepartment(String leadingDepartment) 
    {
        this.leadingDepartment = leadingDepartment;
    }

    public String getLeadingDepartment() 
    {
        return leadingDepartment;
    }
    public void setPersonResponsible(String personResponsible) 
    {
        this.personResponsible = personResponsible;
    }

    public String getPersonResponsible() 
    {
        return personResponsible;
    }
    public void setFeedbackSituation(String feedbackSituation) 
    {
        this.feedbackSituation = feedbackSituation;
    }

    public String getFeedbackSituation() 
    {
        return feedbackSituation;
    }
    public void setFileUrl(String fileUrl) 
    {
        this.fileUrl = fileUrl;
    }

    public String getFileUrl() 
    {
        return fileUrl;
    }
    public void setUndercoverInspectionId(Long undercoverInspectionId) 
    {
        this.undercoverInspectionId = undercoverInspectionId;
    }

    public Long getUndercoverInspectionId() 
    {
        return undercoverInspectionId;
    }
    public void setType(Long type) 
    {
        this.type = type;
    }

    public Long getType() 
    {
        return type;
    }
    public void setCheckType(String checkType) 
    {
        this.checkType = checkType;
    }

    public String getCheckType() 
    {
        return checkType;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }
    public void setcTime(Date cTime) 
    {
        this.cTime = cTime;
    }

    public Date getcTime() 
    {
        return cTime;
    }
    public void setuTime(Date uTime) 
    {
        this.uTime = uTime;
    }

    public Date getuTime() 
    {
        return uTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("content", getContent())
            .append("region", getRegion())
            .append("detailAddress", getDetailAddress())
            .append("leadingDepartment", getLeadingDepartment())
            .append("personResponsible", getPersonResponsible())
            .append("feedbackSituation", getFeedbackSituation())
            .append("fileUrl", getFileUrl())
            .append("undercoverInspectionId", getUndercoverInspectionId())
            .append("type", getType())
            .append("checkType", getCheckType())
            .append("status", getStatus())
            .append("cTime", getcTime())
            .append("uTime", getuTime())
            .toString();
    }
}
