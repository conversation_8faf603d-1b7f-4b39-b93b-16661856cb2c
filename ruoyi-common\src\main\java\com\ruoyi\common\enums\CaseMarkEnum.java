package com.ruoyi.common.enums;

/**
 * 角标(透传消息)
 */
public enum CaseMarkEnum {

    NOTICE("notice","通知公告"),
    FENCE("fence","电子围栏"),
    FOUR("four", "四位一体"),
    CAPTURE("capture", "监控抓拍"),
    AUTOCAPTURE("autoCapture", "智能抓拍"),
    UNION("union", "任务发布"),
    LEADERTASK("leadertask", "领导交办"),
    MESSAGE("message" , "消息"),
    APPEAL("appeal", "督查申述"),
    SUPERVISION("supervision", "督查考核"),
    SUBSTITUTE("substitute", "代班");


    private String type;
    private String desc;

    CaseMarkEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
