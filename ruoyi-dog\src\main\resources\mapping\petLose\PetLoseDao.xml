<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.petLose.dao.PetLoseDao">

    <sql id="columns">
     <trim suffixOverrides=",">
            a.id as id,
            a.pet_imgz as petImgz,
            a.pet_imgc as petImgc,
            a.pet_name as petName,
            a.pet_varieties_one as petVarietiesOne,
            a.pet_varieties as petVarieties,
            a.pet_hair as petHair,
            a.lose_date as loseDate,
            a.lose_dept as loseDept,
            a.address as address,
            a.owner_name as ownerName,
            a.pet_id_card as petIdCard,
            a.tel as tel,
            a.status as status,
            a.del_flag as delFlag,
            a.create_date as createDate,
            a.create_by as createBy,
            a.update_date as updateDate,
            a.update_by as updateBy,
            a.pet_clue as petClue,

        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.petLose.entity.PetLose" resultType="com.ruoyi.modules.petLose.entity.PetLose">
        select <include refid="columns"/>
        from pet_lose a
        where a.id =#{id}
    </select>



    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.petLose.entity.PetLose" resultType="com.ruoyi.modules.petLose.entity.PetLose">
        select <include refid="columns"/>
        from pet_lose a
        where a.del_flag =1
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="petImgz != null and petImgz != ''">
                and a.pet_imgz = #{petImgz}
            </if>
            <if test="petImgc != null and petImgc != ''">
                and a.pet_imgc = #{petImgc}
            </if>
            <if test="petName != null and petName != ''">
                and a.pet_name = #{petName}
            </if>
            <if test="petVarietiesOne != null and petVarietiesOne != ''">
                and a.pet_varieties_one = #{petVarietiesOne}
            </if>
            <if test="petVarieties != null and petVarieties != ''">
                and a.pet_varieties = #{petVarieties}
            </if>
            <if test="petHair != null and petHair != ''">
                and a.pet_hair = #{petHair}
            </if>
            <if test="loseDate != null">
                and a.lose_date = #{loseDate}
            </if>
            <if test="loseDept != null and loseDept != ''">
                and a.lose_dept = #{loseDept}
            </if>
            <if test="address != null and address != ''">
                and a.address = #{address}
            </if>
            <if test="ownerName != null and ownerName != ''">
                and a.owner_name = #{ownerName}
            </if>
            <if test="petIdCard != null and petIdCard != ''">
                and a.pet_id_card = #{petIdCard}
            </if>
            <if test="tel != null and tel != ''">
                and a.tel = #{tel}
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status}
            </if>
            <if test="delFlag != null">
                and a.del_flag = #{delFlag}
            </if>
            <if test="createDate != null">
                and a.create_date = #{createDate}
            </if>
            <if test="createBy != null and createBy != ''">
                and a.create_by = #{createBy}
            </if>
            <if test="updateDate != null">
                and a.update_date = #{updateDate}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and a.update_by = #{updateBy}
            </if>
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.petLose.entity.PetLose">
        insert into pet_lose(id, pet_imgz, pet_imgc, pet_name, pet_varieties_one, pet_varieties,
                             pet_hair, lose_date, lose_dept, address, owner_name, pet_id_card, tel,
                             status, del_flag, create_date, create_by, update_date, update_by, pet_clue)
        values (#{id}, #{petImgz}, #{petImgc}, #{petName}, #{petVarietiesOne}, #{petVarieties},
                #{petHair}, #{loseDate}, #{loseDept}, #{address}, #{ownerName}, #{petIdCard},
                #{tel}, #{status}, #{delFlag}, #{createDate}, #{createBy}, #{updateDate},
                #{updateBy}, #{petClue})
    </insert>

    <update id="updateByEntity" parameterType="com.ruoyi.modules.petLose.entity.PetLose">
        update pet_lose set
        <trim suffixOverrides=",">
            <if test="petImgz != null and petImgz != ''">
                pet_imgz = #{petImgz},
            </if>
            <if test="petImgc != null and petImgc != ''">
                pet_imgc = #{petImgc},
            </if>
            <if test="petName != null and petName != ''">
                pet_name = #{petName},
            </if>
            <if test="petVarietiesOne != null and petVarietiesOne != ''">
                pet_varieties_one = #{petVarietiesOne},
            </if>
            <if test="petVarieties != null and petVarieties != ''">
                pet_varieties = #{petVarieties},
            </if>
            <if test="petHair != null and petHair != ''">
                pet_hair = #{petHair},
            </if>
            <if test="loseDate != null">
                lose_date = #{loseDate},
            </if>
            <if test="loseDept != null and loseDept != ''">
                lose_dept = #{loseDept},
            </if>
            <if test="address != null and address != ''">
                address = #{address},
            </if>
            <if test="ownerName != null and ownerName != ''">
                owner_name = #{ownerName},
            </if>
            <if test="petIdCard != null and petIdCard != ''">
                pet_id_card = #{petIdCard},
            </if>
            <if test="tel != null and tel != ''">
                tel = #{tel},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="petClue != null and petClue != ''">
                pet_clue = #{petClue},
            </if>
        </trim>
        where id = #{id}
    </update>
    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.petLose.entity.PetLose">
        update pet_lose set
        <trim suffixOverrides=",">
               pet_imgz = #{petImgz},
               pet_imgc = #{petImgc},
               pet_name = #{petName},
               pet_varieties_one = #{petVarietiesOne},
               pet_varieties = #{petVarieties},
               pet_hair = #{petHair},
               lose_date = #{loseDate},
               lose_dept = #{loseDept},
               address = #{address},
               owner_name = #{ownerName},
               pet_id_card = #{petIdCard},
               tel = #{tel},
               status = #{status},
               del_flag = #{delFlag},
               create_date = #{createDate},
               create_by = #{createBy},
               update_date = #{updateDate},
               update_by = #{updateBy},
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.petLose.entity.PetLose">
        UPDATE  pet_lose
        SET
        del_flag=#{delFlag},
        update_date = #{updateDate},
        update_by = #{updateBy}
        where id = #{id}
    </delete>

</mapper>
