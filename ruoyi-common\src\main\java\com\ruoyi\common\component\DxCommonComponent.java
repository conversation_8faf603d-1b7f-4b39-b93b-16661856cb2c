package com.ruoyi.common.component;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.vo.DuanxingReq;
import com.ruoyi.common.yw.domain.XxInfo;
import com.ruoyi.common.yw.mapper.XxInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.util.Base64;
import java.util.Date;

/**
 * 短信发送工具
 */
@Component
@Slf4j
public class DxCommonComponent {

    @Value("${dx.siid}")
    private  String siid;

    @Value("${dx.user}")
    private  String user;

    @Value("${dx.sPSecret}")
    private  String sPSecret;
    @Value("${dx.url}")
    private  String url;

    @Autowired
    XxInfoMapper xxInfoMapper;

    /**
     * 发送短信
     * @param mobile  手机号,多人用,隔开
     * @param content
     */
    public  void  send(String mobile,String content){
        String timeStamp = DateUtils.format(new Date(),"yyyyMMddHHmmss");
        timeStamp=timeStamp+"671";
        String transactionID= RandomUtil.randomString(24);
        String streamingNo=  RandomUtil.randomString(24);
        DuanxingReq d=new DuanxingReq();
        d.setSiid(siid);
        d.setUser(user);
        d.setStreamingNo(streamingNo);
        d.setTimeStamp(timeStamp);
        d.setTransactionID(transactionID);
        String s1 = encoderByMd5(timeStamp +  transactionID + streamingNo + sPSecret);
        System.out.println(timeStamp +  transactionID + streamingNo + sPSecret);
        System.out.println(s1);
        d.setAuthenticator(s1);
        d.setMobile(mobile);
        d.setContent(content);
        d.setExtcode("001");
        String s = JSON.toJSONString(d);
        String body1 = HttpUtil.createPost(url).body( s).execute().body();
        log.info(body1);
        String[] split = mobile.split(",");
        for (int i = 0; i < split.length; i++){
            String phone = split[i];
            XxInfo xxInfo=new XxInfo();
            xxInfo.setFsr("金华市综合行政执法局");
            xxInfo.setJsr(phone);
            xxInfo.setNr(content);
            xxInfo.setBt("金华市综合行政执法消息通知");
            xxInfo.setSource(1);
            xxInfoMapper.insertXxInfo(xxInfo);
        }


    }

    /**
     * 加密方法
     * @param str
     * @return
     */
    private static String encoderByMd5(String str) {
        try {
            // 确定计算方法
            MessageDigest md5 = MessageDigest.getInstance("MD5");
//            BASE64Encoder base64en = new BASE64Encoder();
            // 加密后的字符串
            return Base64.getEncoder().encodeToString(md5.digest(str.getBytes("UTF-8")));
        } catch (Exception e) {
            return "";
        }
    }
}
