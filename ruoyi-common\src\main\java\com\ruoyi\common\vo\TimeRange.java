package com.ruoyi.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 自定义类表示时间范围信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TimeRange {
    private String label;
    private Date startTime;
    private Date endTime;

    public TimeRange(String label, Date startTime) {
        this.label = label;
        this.startTime = startTime;
    }
}
