<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.pushMessageDept.dao.PushMessageDeptDao">
    <sql id="columns">
        <trim suffixOverrides=",">
            a.id as id,
            a.message_id as messageId,
            a.detp_id as detpId,
            a.status as status,
            a.read_time as readTime
        </trim>
    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.pushMessageDept.entity.PushMessageDept" resultType="com.ruoyi.modules.pushMessageDept.entity.PushMessageDept">
        select
        <include refid="columns"/>
        from push_message_dept a
        where a.id =#{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.pushMessageDept.entity.PushMessageDept" resultType="com.ruoyi.modules.pushMessageDept.entity.PushMessageDept">
        select
        <include refid="columns"/>
        from push_message_dept a
        where a.del_flag =1
        <if test="messageId != null and messageId != ''">
            and  a.message_id =#{messageId}
        </if>
        order by a.create_date desc
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.pushMessageDept.entity.PushMessageDept">
        insert into push_message_dept
              (id,
               message_id,
               detp_id,
               status,
               read_time)
        values (#{id},
                #{messageId},
                #{detpId},
                #{status},
                #{readTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.pushMessageDept.entity.PushMessageDept">
        update push_message_dept set
        <trim suffixOverrides=",">
            message_id = #{messageId},
            detp_id = #{detpId},
            status = #{status},
            read_time = #{readTime},
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.pushMessageDept.entity.PushMessageDept">
        UPDATE push_message_dept
        SET del_flag=#{delFlag}, update_date = #{updateDate}, update_by = #{updateBy}
        where id = #{id}
    </delete>

    <insert id="saveAllList" parameterType="java.util.List">
        INSERT INTO push_message_dept (
        id,
        message_id,
        detp_id,
        status,
        read_time,
        create_date,
        del_flag
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.messageId},
            #{item.detpId},
            #{item.status},
            #{item.readTime},
            #{item.createDate},
            #{item.delFlag}
            )
        </foreach>
    </insert>

    <!--通过主键删除-->
    <delete id="deleteMessageId" parameterType="java.lang.String">
        UPDATE push_message_dept SET del_flag='2'  where message_id = #{messageId}
    </delete>


</mapper>
