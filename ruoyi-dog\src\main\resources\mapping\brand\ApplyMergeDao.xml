<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.brand.dao.ApplyMergeDao">


    <sql id="columns">
        <trim suffixOverrides=",">
            a.id as id ,
            a.apply_ids as applyIds ,
            a.status as status ,
            a.remarks as remarks ,
            a.create_date as createDate ,
            a.create_by as createBy ,
            a.update_date as updateDate ,
            a.update_by as updateBy ,
            a.del_flag as delFlag ,a.num as num,a.dept_id as deptId,a.payment_voucher as paymentVoucher,a.merge_num as
            mergeNum
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.brand.entity.ApplyMerge"
            resultType="com.ruoyi.modules.brand.entity.ApplyMerge">
        select
        <include refid="columns"/>
        from apply_merge a
        where a.id =#{id}
    </select>
    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.brand.entity.ApplyMerge"
            resultType="com.ruoyi.modules.brand.entity.ApplyMerge">
        select
        <include refid="columns"/>
        from apply_merge a
        where a.del_flag =1
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="num != null ">
            and a.num = #{num}
        </if>
        <if test="applyIds != null and applyIds != ''">
            and a.apply_ids = #{applyIds}
        </if>
        <if test="remarks != null and remarks != ''">
            and a.remarks = #{remarks}
        </if>
        <if test="createDate != null">
            and a.create_date = #{createDate}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by = #{createBy}
        </if>
        <if test="updateDate != null">
            and a.update_date = #{updateDate}
        </if>
        <if test="updateBy != null and updateBy != ''">
            and a.update_by = #{updateBy}
        </if>
        <if test="delFlag != null">
            and a.del_flag = #{delFlag}
        </if>
        <if test="userRealName!=null and userRealName!=''">
            and a.dept_id in (select id from sys_dept where del_flag=1 and dept_name like  concat('%',#{userRealName},'%'))
        </if>
        order by a.create_date desc
    </select>

    <select id="getListCJ" parameterType="com.ruoyi.modules.brand.entity.ApplyMerge"
            resultType="com.ruoyi.modules.brand.entity.ApplyMerge">
        select
        <include refid="columns"/>
        from apply_merge a
        where a.del_flag =1 and a.status in (4,6,7)
        <if test="userRealName!=null and userRealName!=''">
            and a.dept_id in (select id from sys_dept where del_flag=1 and dept_name like  concat('%',#{userRealName},'%'))
        </if>
        <if test="num != null ">
            and a.num = #{num}
        </if>
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="applyIds != null and applyIds != ''">
            and a.apply_ids = #{applyIds}
        </if>

        <if test="remarks != null and remarks != ''">
            and a.remarks = #{remarks}
        </if>
        <if test="createDate != null">
            and a.create_date = #{createDate}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by = #{createBy}
        </if>
        <if test="updateDate != null">
            and a.update_date = #{updateDate}
        </if>
        <if test="updateBy != null and updateBy != ''">
            and a.update_by = #{updateBy}
        </if>
        <if test="delFlag != null">
            and a.del_flag = #{delFlag}
        </if>

        order by a.create_date desc
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into apply_merge(id, apply_ids, status, remarks, create_date, create_by, update_date, update_by,
                                del_flag, num, dept_id, payment_voucher, merge_num)
        values (#{id}, #{applyIds}, #{status}, #{remarks}, #{createDate}, #{createBy}, #{updateDate}, #{updateBy},
                #{delFlag}, #{num}, #{deptId}, #{paymentVoucher}, #{mergeNum})
    </insert>


    <!--通过主键修改数据-->
    <update id="updateByEntity">
        update apply_merge
        <set>
            <trim suffixOverrides=",">
                <if test="deptId != null and deptId != ''">
                    dept_id = #{deptId},
                </if>
                <if test="applyIds != null and applyIds != ''">
                    apply_ids = #{applyIds},
                </if>
                <if test="status != null">
                    status = #{status},
                </if>
                <if test="num != null">
                    num = #{num},
                </if>
                <if test="remarks != null and remarks != ''">
                    remarks = #{remarks},
                </if>
                <if test="createDate != null">
                    create_date = #{createDate},
                </if>
                <if test="createBy != null and createBy != ''">
                    create_by = #{createBy},
                </if>
                <if test="updateDate != null">
                    update_date = #{updateDate},
                </if>
                <if test="updateBy != null and updateBy != ''">
                    update_by = #{updateBy},
                </if>
                <if test="delFlag != null">
                    del_flag = #{delFlag},
                </if>
            </trim>
        </set>
        where id = #{id}
    </update>
    <update id="update">
        update apply_merge
        set
        <trim suffixOverrides=",">
            dept_id = #{deptId},
            apply_ids = #{applyIds},

            status = #{status},

            remarks = #{remarks},

            create_date = #{createDate},

            create_by = #{createBy},

            update_date = #{updateDate},

            update_by = #{updateBy},

            del_flag = #{delFlag}, num = #{num},
        </trim>
        where id = #{id}
    </update>
    <!--通过主键删除-->
    <delete id="delete">
        UPDATE apply_merge
        SET del_flag=#{delFlag},
            update_date = #{updateDate},
            update_by = #{updateBy}
        where id = #{id}
    </delete>

</mapper>

