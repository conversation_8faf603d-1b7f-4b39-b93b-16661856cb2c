package com.ruoyi.common.utils.rsa;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;

import java.nio.charset.StandardCharsets;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Base64;

public class RSAv2Util {
    public static void main(String[] args) {
        String text = "人最宝贵的是生命.生命对每个人只有一次.人的一生应当这样度过：当他回首往事的时候,不会因为虚度年华而悔恨,也不会因为碌碌无为而羞耻.这样,在临死的时候,他能够说：“我已把自己的整个的生命和全部的精力献给了世界上最壮丽的事业---------为人类的解放而斗争.”";
        System.out.println("原文：" + text);

        //生成公私钥对
        KeyPair pair = SecureUtil.generateKeyPair("RSA");
        PrivateKey privateKey = pair.getPrivate();
        PublicKey publicKey = pair.getPublic();
        //获得私钥
        String privateKeyStr = bytesToBase64(privateKey.getEncoded());
        System.out.println("私钥：" + privateKeyStr);
        //获得公钥
        String publicKeyStr = bytesToBase64(publicKey.getEncoded());
        System.out.println("公钥：" + publicKeyStr);

        RSA rsa = new RSA(privateKeyStr, publicKeyStr);
        System.out.println(rsa);

        //公钥加密，私钥解密
        byte[] encrypt = rsa.encrypt(StrUtil.bytes(text, CharsetUtil.CHARSET_UTF_8), KeyType.PublicKey);
        System.out.println("公钥加密：" + bytesToBase64(encrypt));

        byte[] decrypt = rsa.decrypt(encrypt, KeyType.PrivateKey);
        System.out.println("私钥解密：" + new String(decrypt,StandardCharsets.UTF_8));
    }

    private static String PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIyBl5YO6QMCVDaJOtDNXrE61IaK4h8jHaZXuJhE0fP3r6k5aESJorF+t3LZSWPSXk68RQNtOLeRUYu/NduVAMLtYx3+L6iwmNX9ddDVgS756I/b+8kiAkYkshs3dEb54hkD16QX6zmI3WhVb9qR9tmwwdro3nURJcmjODgSMAJxAgMBAAECgYA84G04M++bQSgRlPWpwEF10oKAZK5CRWm0R6+QMckxxjASxu+5OlUGSFvdY+2bVWtM+99+85SuXEUTol4IbI+uOvhKXrRCgOARd6joJnbMXdZrNYe2Pp3jZ6kCMvIb71dxhpMbgQ4VhERpEemeA89Jong8QmtF4OXXUYScf9gQfQJBAPf6DhZ7PsVlD/amfWkZ3R6F5tMAgoJ7bvXl5OjyfvcRsFM5cGEtdLAbepH/HM4sgU4JyZmOadFqdogeie0dNpMCQQCRDWDyb8139XHy6vzXtWWhsi/mpoxyUilrN43SfA6y5VFGq0rD8/T8uxCWi5BEk5kcf0MmzOrWCNbkymDU0eFrAkEA4r4Fpa7ZzbrEJv9DSxf4JRHA5wzfSRo+cp/ceNhLGfVVqjOqO2FKAkGOCPMhTrBJJ6cxbLRYs8DKU1rBOUvt+wJADk3ydJYf/id/YERaQO+LoUGQRb/A8/k0SnWFD+/aBzxkIoL78Y0aaN4NybrjtE+V9uB5/9aPhiIDBzcKC5LTFwJAVh5hmBLsLp1Isd7q0LJoTLZ/Z1w4L3Mp4gPFJBPwwvKddA66mOg1aXf9pne8ZSahgew5MbbTgIiNwLi/fdPcCg==;";
    private static String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCMgZeWDukDAlQ2iTrQzV6xOtSGiuIfIx2mV7iYRNHz96+pOWhEiaKxfrdy2Ulj0l5OvEUDbTi3kVGLvzXblQDC7WMd/i+osJjV/XXQ1YEu+eiP2/vJIgJGJLIbN3RG+eIZA9ekF+s5iN1oVW/akfbZsMHa6N51ESXJozg4EjACcQIDAQAB";

    public static void test(String text) {
        System.out.println("原文：" + text);

        RSA rsa = new RSA(PRIVATE_KEY, PUBLIC_KEY);
        System.out.println(rsa);

        //公钥加密，私钥解密
        byte[] encrypt = rsa.encrypt(StrUtil.bytes(text, CharsetUtil.CHARSET_UTF_8), KeyType.PublicKey);
        System.out.println("公钥加密：" + bytesToBase64(encrypt));

        byte[] decrypt = rsa.decrypt(encrypt, KeyType.PrivateKey);
        System.out.println("私钥解密：" + new String(decrypt,StandardCharsets.UTF_8));
    }

    public static String encode(String text) {
        RSA rsa = new RSA(PRIVATE_KEY, PUBLIC_KEY);
        //公钥加密，私钥解密
        byte[] encrypt = rsa.encrypt(StrUtil.bytes(text, CharsetUtil.CHARSET_UTF_8), KeyType.PublicKey);

        return bytesToBase64(encrypt);
    }

    public static String decode(String text) {
        RSA rsa = new RSA(PRIVATE_KEY, null);

        byte[] decrypt = rsa.decrypt(text, KeyType.PrivateKey);

        return new String(decrypt,StandardCharsets.UTF_8);
    }

    /**
     * 字节数组转Base64编码
     *
     * @param bytes 字节数组
     * @return Base64编码
     */
    private static String bytesToBase64(byte[] bytes) {
        byte[] encodedBytes = Base64.getEncoder().encode(bytes);
        return new String(encodedBytes, StandardCharsets.UTF_8);
    }

    /**
     * Base64编码转字节数组
     *
     * @param base64Str Base64编码
     * @return 字节数组
     */
    private static byte[] base64ToBytes(String base64Str) {
        byte[] bytes = base64Str.getBytes(StandardCharsets.UTF_8);
        return Base64.getDecoder().decode(bytes);
    }
}
