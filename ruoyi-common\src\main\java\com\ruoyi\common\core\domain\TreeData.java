package com.ruoyi.common.core.domain;

import java.io.Serializable;
import java.util.List;

/**
 * 树状结构显示实体.
 */
public class TreeData implements Serializable {


    private static final long serialVersionUID = 3640299655414720932L;
    /**
     * 业务数据主键id
     */
    private Integer id;
    /**
     * 树节点必须属性
     * 该节点所表示的唯一主键
     * 业务主键id+表名
     */
    private String nodeKey;

    /**
     * 业务keyCode
     */
    private String keyCode;
    /**
     * 树节点必须属性
     * 该树节点在界面上显示的名称
     */
    private String name;
    /**
     * 树节点必须属性
     * 该树节点所属的根节点
     * 业务主键id+表名
     */
    private String pId;
    /**
     * 业务数据分组id
     */
    private Integer groupId;
    /**
     * 树节点必须属性
     * * 该树节点所属的分组节点
     * * 业务主键id+表名
     */
    private String groupNodeId;
    /**
     * 业务数据父级id
     */
    private Integer rootId;
    /**
     * 树节点样式，比如树节点图标 等
     * 例如：ext-icon-house
     */
    private String iconSkin;
    /**
     * 图标名
     */
    private String iconName;
    /**
     * 是否是初始数据（1，是  0，不是）
     */
    private String initialData;
    /**
     * 可选值：open   closed
     * 树节点状态是否展开
     */
    private String state = "open";
    /**
     * 默认是否选中树中的节点，用于带有checkbo选择框的EasyUI Tree
     * 默认值：false（不选中）
     */
    private Boolean checked = false;

    /**
     * 默认是否是组元素
     * 默认值：false（不是）
     */
    private Boolean isGroup = false;

    /**
     * 树节点扩展属性，表示树节点的勾选状态<br>
     * 1、全选  2、半选   3、空选
     */
    private Integer checkStatus;

    /**
     * 该节点的子节点
     */
    private List<TreeData> children;

    /**
     * 树节点扩展属性
     * key:value形式
     * 存放url，description等信息
     * 该对象可以是一个：Map、JSON、Java对象
     */
    private Object attributes;

    /**
     * 树节点层级
     */
    private String rootpath;
    /**
     * 树节点类型（表名）
     */
    private String type;
    /**
     * 业务数据基础类型
     */
    private Integer baseType;

    /**
     * 路径
     */
    private String url;

    /**
     * 子集个数
     */
    private Integer childCount;
    /**
     * 设计组名称
     */
    private String userDesignerName;
    private Integer userDesignerId;

    /**
     * 对应selectTree组件
     */
    private String label;

    /**
     * 岗位
     */
    private String position;

    /**
     * 路径名称
     */
    private String pathName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNodeKey() {
        return nodeKey;
    }

    public void setNodeKey(String nodeKey) {
        this.nodeKey = nodeKey;
    }

    public String getKeyCode() {
        return keyCode;
    }

    public void setKeyCode(String keyCode) {
        this.keyCode = keyCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPId() {
        return pId;
    }

    public void setPId(String pId) {
        this.pId = pId;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupNodeId() {
        return groupNodeId;
    }

    public void setGroupNodeId(String groupNodeId) {
        this.groupNodeId = groupNodeId;
    }

    public Integer getRootId() {
        return rootId;
    }

    public void setRootId(Integer rootId) {
        this.rootId = rootId;
    }

    public String getIconSkin() {
        return iconSkin;
    }

    public void setIconSkin(String iconSkin) {
        this.iconSkin = iconSkin;
    }

    public String getIconName() {
        return iconName;
    }

    public void setIconName(String iconName) {
        this.iconName = iconName;
    }

    public String getInitialData() {
        return initialData;
    }

    public void setInitialData(String initialData) {
        this.initialData = initialData;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Boolean getChecked() {
        return checked;
    }

    public void setChecked(Boolean checked) {
        this.checked = checked;
    }

    public Boolean getGroup() {
        return isGroup;
    }

    public void setGroup(Boolean group) {
        isGroup = group;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public List<TreeData> getChildren() {
        return children;
    }

    public void setChildren(List<TreeData> children) {
        this.children = children;
    }

    public Object getAttributes() {
        return attributes;
    }

    public void setAttributes(Object attributes) {
        this.attributes = attributes;
    }

    public String getRootpath() {
        return rootpath;
    }

    public void setRootpath(String rootpath) {
        this.rootpath = rootpath;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getBaseType() {
        return baseType;
    }

    public void setBaseType(Integer baseType) {
        this.baseType = baseType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getChildCount() {
        return childCount;
    }

    public void setChildCount(Integer childCount) {
        this.childCount = childCount;
    }

    public String getUserDesignerName() {
        return userDesignerName;
    }

    public void setUserDesignerName(String userDesignerName) {
        this.userDesignerName = userDesignerName;
    }

    public Integer getUserDesignerId() {
        return userDesignerId;
    }

    public void setUserDesignerId(Integer userDesignerId) {
        this.userDesignerId = userDesignerId;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getPathName() {
        return pathName;
    }

    public void setPathName(String pathName) {
        this.pathName = pathName;
    }
}
