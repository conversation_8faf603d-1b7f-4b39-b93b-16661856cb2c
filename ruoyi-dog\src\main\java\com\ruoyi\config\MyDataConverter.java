package com.ruoyi.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ProjectName: apiservice
 * @Package: com.youba.config
 * @ClassName: MyDataConverter
 * @Description: 日期参数对象转换类
 * @Author: 一时
 */

@Configuration
public class MyDataConverter {
    //参数日期格式自动转换
    private static final List<String> formarts = new ArrayList<String>();
    static{
        formarts.add("yyyy-MM");
        formarts.add("yyyy-MM-dd");
        formarts.add("yyyy-MM-dd HH:mm");
        formarts.add("yyyy-MM-dd HH:mm:ss");
        formarts.add("yyyy/MM/dd");
    }

    /**
     * 参数绑定时 追加日期转换格式
     * 支持类字符串直接转换为日期
     * @return
     */
    @Bean
    public Converter<String, Date> addNewConvert() {
        return new Converter<String, Date>() {
            @Override
            public Date convert(String source) {
                SimpleDateFormat sdf = null;
                if(source != null && !source.equals("")){
                    if(source.matches("^\\d{4}-\\d{1,2}$")){
                        sdf = new SimpleDateFormat(formarts.get(0));
                    }else if(source.matches("^\\d{4}-\\d{1,2}-\\d{1,2}$")){
                        sdf = new SimpleDateFormat(formarts.get(1));
                    }else if(source.matches("^\\d{4}-\\d{1,2}-\\d{1,2} {1}\\d{1,2}:\\d{1,2}$")){
                        sdf = new SimpleDateFormat(formarts.get(2));
                    }else if(source.matches("^\\d{4}-\\d{1,2}-\\d{1,2} {1}\\d{1,2}:\\d{1,2}:\\d{1,2}$")){
                        sdf = new SimpleDateFormat(formarts.get(3));
                    }else {
//                        throw new IllegalArgumentException("Invalid boolean value '" + source + "'");
                        sdf = new SimpleDateFormat(formarts.get(4));
                    }
                }
                Date date = null;
                try {
                    if(source != null && !source.equals("")){
                        date = sdf.parse((String) source);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException("日期转换错误！");
                }
                return date;
            }
        };
    }
}
