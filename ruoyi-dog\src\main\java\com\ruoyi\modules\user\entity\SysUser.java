package com.ruoyi.modules.user.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.hospital.entity.Qualifi;

import java.util.List;

/**
 * Created by Administrator on 2020/1/3/003.
 * 系统用户对象
 */
public class SysUser extends BaseEntity{

    private String userName ;   //用户名
    @JsonIgnore
    private String password ;   //密码
    private String newPass1 ;   //修改密码
    private String realName ;     //真是姓名
    private Integer userType ;    //1管理员用户  2企业用户  3乡镇管理用户
    private Integer userSex ;     //性别 1男 2女
    private String userEmail ;    //邮件
    private String deptId ;       //机构id
    private String deptName ;     //机构名称
    private String orgType ;      //机构类型
    private String headimg ;      //头像地址
    private Integer readnum ;      //阅读量
    private Integer downnum ;      //下载量
    private String mobile;         //手机号
    private String idCard;         //身份证号
    private String address;        //地址
    private String userRoleStr ;    // 用户角色字符串: 用户对应得角色ID
    private String managerOrgs ;    // 分管领导角色 管辖得科室
    private String userRoleNames ;  // 用户角色名称
    private List<SysUserRole> sysUserRoleList;
    private String userId;
    private String nickName;
    private String customerNo;

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    private String token;           // 访问令牌

    private Qualifi userQualifi;        //userType == 2时候传到前端的医院信息

    private String qualifi;//医院具有资质

    private String showUserType;//用户权限不展示某些用户

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getManagerOrgs() {
        return managerOrgs;
    }

    public void setManagerOrgs(String managerOrgs) {
        this.managerOrgs = managerOrgs;
    }

    public String getUserRoleNames() {
        return userRoleNames;
    }

    public void setUserRoleNames(String userRoleNames) {
        this.userRoleNames = userRoleNames;
    }

    public List<SysUserRole> getSysUserRoleList() {
        return sysUserRoleList;
    }

    public void setSysUserRoleList(List<SysUserRole> sysUserRoleList) {
        this.sysUserRoleList = sysUserRoleList;
    }

    public String getNewPass1() {
        return newPass1;
    }

    public void setNewPass1(String newPass1) {
        this.newPass1 = newPass1;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Integer getUserSex() {
        return userSex;
    }

    public void setUserSex(Integer userSex) {
        this.userSex = userSex;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getHeadimg() {
        return headimg;
    }

    public void setHeadimg(String headimg) {
        this.headimg = headimg;
    }

    public Integer getReadnum() {
        return readnum;
    }

    public void setReadnum(Integer readnum) {
        this.readnum = readnum;
    }

    public Integer getDownnum() {
        return downnum;
    }

    public void setDownnum(Integer downnum) {
        this.downnum = downnum;
    }

    public String getUserRoleStr() {
        return userRoleStr;
    }

    public void setUserRoleStr(String userRoleStr) {
        this.userRoleStr = userRoleStr;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getQualifi() {
        return qualifi;
    }

    public void setQualifi(String qualifi) {
        this.qualifi = qualifi;
    }

    public Qualifi getUserQualifi() {
        return userQualifi;
    }

    public void setUserQualifi(Qualifi userQualifi) {
        this.userQualifi = userQualifi;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getShowUserType() {
        return showUserType;
    }

    public void setShowUserType(String showUserType) {
        this.showUserType = showUserType;
    }
}
