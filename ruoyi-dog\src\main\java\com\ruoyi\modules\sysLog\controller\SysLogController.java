package com.ruoyi.modules.sysLog.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.sysLog.entity.SysLog;
import com.ruoyi.modules.sysLog.service.SysLogService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;

/**
 * 系统日志(SysLog)表控制层
 *
 * <AUTHOR>
 * @since 2022-12-26 10:58:01
 */
@RestController
@RequestMapping("sysLog")
public class SysLogController {
    /**
     * 服务对象
     */
    @Resource
    private SysLogService service;


    @RequestMapping("getPageList")
    public AjaxResult getPageList(SysLog entity) {
        return AjaxResult.success(service.getPageList(entity));
    }

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(SysLog entity, HttpServletRequest request) {
        service.saveOrUpdate(entity, request);
        return AjaxResult.success();
    }

    /**
     * @author: tongsiyu
     * @date: 2022/12/26 11:14
     * @Description:获取今日访问量
     */
    @RequestMapping("/getVisitsNum")
    public AjaxResult getVisitsNum(SysLog sysLog) {
        return AjaxResult.success(service.getVisitsNum(sysLog));
    }

    @RequestMapping("/getVisitsList")
    public AjaxResult getVisitsList(SysLog log) {
        return AjaxResult.success(service.getVisitsList(log));
    }
}

