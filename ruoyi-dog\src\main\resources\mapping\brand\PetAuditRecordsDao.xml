<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.brand.dao.PetAuditRecordsDao">

    <sql id="columns">
        <trim suffixOverrides=",">
            a.id as id,
            a.apply_id as applyId,
            a.status as status,
            a.remarks as remarks,
            a.create_date as createDate,
            a.create_by as createBy,
            a.update_date as updateDate,
            a.update_by as updateBy,
            a.del_flag as delFlag,
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.brand.entity.PetAuditRecords"
            resultType="com.ruoyi.modules.brand.entity.PetAuditRecords">
        select
        <include refid="columns"/>

        from pet_audit_records a
        where a.id =#{id}
    </select>


    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.brand.entity.PetAuditRecords"
            resultType="com.ruoyi.modules.brand.entity.PetAuditRecords">
        select
        a.id as id,
        a.apply_id as applyId,
        a.status as status,
        a.remarks as remarks,
        a.create_date as createDate,
        IFNULL((select nick_name from sys_user where  dog_user_id= a.create_by) ,(select nick_name from sys_user where  user_id= a.create_by) )as createBy ,
        a.update_date as updateDate,
        a.update_by as updateBy,
        a.del_flag as delFlag
        from pet_audit_records a
        where a.del_flag =1
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="applyId != null and applyId != ''">
            and a.apply_id = #{applyId}
        </if>
        <if test="status != null">
            and a.status = #{status}
        </if>
        <if test="remarks != null and remarks != ''">
            and a.remarks = #{remarks}
        </if>
        <if test="createDate != null">
            and a.create_date = #{createDate}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by = #{createBy}
        </if>
        <if test="updateDate != null">
            and a.update_date = #{updateDate}
        </if>
        <if test="updateBy != null and updateBy != ''">
            and a.update_by = #{updateBy}
        </if>
        <if test="delFlag != null">
            and a.del_flag = #{delFlag}
        </if>
        order by a.create_date desc
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.brand.entity.PetAuditRecords">
        insert into pet_audit_records(id, apply_id, status, remarks, create_date, create_by, update_date, update_by,
                                      del_flag)
        values (#{id}, #{applyId}, #{status}, #{remarks}, #{createDate}, #{createBy}, #{updateDate}, #{updateBy},
                #{delFlag})
    </insert>
    <update id="updateByEntity" parameterType="com.ruoyi.modules.brand.entity.PetAuditRecords">
        update pet_audit_records set
        <trim suffixOverrides=",">
            <if test="applyId != null and applyId != ''">
                apply_id = #{applyId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
        </trim>
        where id = #{id}
    </update>
    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.brand.entity.PetAuditRecords">
        update pet_audit_records set
        <trim suffixOverrides=",">
            apply_id = #{applyId},
            status = #{status},
            remarks = #{remarks},
            create_date = #{createDate},
            create_by = #{createBy},
            update_date = #{updateDate},
            update_by = #{updateBy},
            del_flag = #{delFlag},
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.brand.entity.PetAuditRecords">
        UPDATE pet_audit_records
        SET del_flag=#{delFlag},
            update_date = #{updateDate},
            update_by = #{updateBy}
        where id = #{id}
    </delete>

</mapper>
