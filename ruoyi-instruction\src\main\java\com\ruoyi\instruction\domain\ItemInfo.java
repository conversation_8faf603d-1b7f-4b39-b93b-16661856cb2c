package com.ruoyi.instruction.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 事项基本信息对象 t_item_info
 * 
 * <AUTHOR>
 * @date 2023-05-09
 */
@Data
public class ItemInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 事项类型名称 */
    @Excel(name = "事项类型名称")
    private String itemTypeName;

    /** 事项名称 */
    @Excel(name = "事项名称")
    private String itemName;

    /** 事件类型 */
    @Excel(name = "事件类型")
    private String type;

    /** 简要情况 */
    @Excel(name = "简要情况")
    private String situation;

    /** 责任单位 */
    @Excel(name = "责任单位")
    private String dutyUnit;

    /** 责任人 */
    @Excel(name = "责任人")
    private String dutyPerson;

    /** 化解时限 */
    @Excel(name = "化解时限")
    private String dissolveTime;

    /** 属地部门 */
    @Excel(name = "属地部门")
    private String department;

    /** 目前治理进度 */
    @Excel(name = "目前治理进度")
    private String progress;

    /** 是否发布 1：发布 2：未发布 */
    @Excel(name = "是否发布 1：发布 2：未发布")
    private String isRelease;

    /** 状态 1：正常 9：删除 */
//    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /** 化解时限（格式时间） */
    @Excel(name = "化解时限", readConverterExp = "格=式时间")
    private Date dissolveDatetime;

    /** 化解时限，0短期，1长期 */
    @Excel(name = "化解时限，0短期，1长期")
    private Integer dissolveCycle;

    /** 上访类型，1来市上访，2教育稳控，3市挂牌，4多次扬言 */
    @Excel(name = "上访类型，1来市上访，2教育稳控，3市挂牌，4多次扬言")
    private Long petitionType;

    /** 是否化解，0否，1是 */
    @Excel(name = "是否化解，0否，1是")
    private Long isDissolve;

    /** 人员id集合 */
//    @Excel(name = "人员id集合")
    private String personIds;
    /**
     * 所有人员id
     */
    private  String allPersonIds;

    /**
     * 群体关联人数
     */
    private Integer personCount;

    /**
     * 事件关联人员
     */
    private List<InstrucationPerson> personList;

    /** 挂牌级别 */
    private String listLevel;

    private  Integer pageNum;

}
