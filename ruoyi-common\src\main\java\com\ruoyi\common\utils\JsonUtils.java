package com.ruoyi.common.utils;

import com.google.gson.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/13 10:50
 */
public class JsonUtils {
    private static final Logger logger = LoggerFactory.getLogger(JsonUtils.class);
    private static Gson gson = null;

    static {
        gson = new GsonBuilder()
                .enableComplexMapKeySerialization() //当Map的key为复杂对象时,需要开启该方法
                .serializeNulls() //当字段值为空或null时，依然对该字段进行转换
                .setDateFormat("yyyy-MM-dd HH:mm:ss:SSS") //时间转化为特定格式
                .setPrettyPrinting() //对结果进行格式化，增加换行
                .disableHtmlEscaping() //防止特殊字符出现乱码
                .create();
    }

    /**
     * 将Json数据解析成相应的映射对象
     *
     * @param jsonData
     * @param type
     * @param <T>
     * @return
     */
    public static <T> T parseJson(String jsonData, Class<T> type) {
        T result = null;
        if (!StringUtils.isEmpty(jsonData)) {
            try {
                result = gson.fromJson(jsonData, type);
            } catch (Exception e) {
                logger.error("将Json数据解析成相应的映射对象出错，字符串{}", jsonData);
            }
        }
        return result;
    }

    /**
     * 将Json数组解析成相应的映射对象List
     *
     * @param jsonData
     * @param type
     * @param <T>
     * @return
     */
    public static <T> List<T> parseJsonArray(String jsonData, Class<T> type) {
        List<T> result = null;
        if (!StringUtils.isEmpty(jsonData)) {
            try {
                com.google.gson.JsonParser parser = new com.google.gson.JsonParser();
                com.google.gson.JsonArray Jarray = parser.parse(jsonData).getAsJsonArray();
                if (Jarray != null) {
                    result = new ArrayList<>();
                    for (JsonElement obj : Jarray) {
                        try {
                            T cse = gson.fromJson(obj, type);
                            result.add(cse);
                        } catch (Exception e) {
                            logger.error("将Json数组解析成相应的映射对象List出错", e);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("将Json数组解析成相应的映射对象List出错", e);
            }
        }
        return result;
    }

    /**
     * 将对象转换成Json
     *
     * @param entity
     * @param <T>
     * @return
     */
    public static <T> String toJsonWithSerializeNulls(T entity) {
        entity.getClass();
        //Gson gson = new GsonBuilder().serializeNulls().create();
        String result = "";
        try {
            result = gson.toJson(entity);
        } catch (Exception e) {
            logger.error("将对象转换成Json出错", e);
        }
        return result;
    }

    /**
     * 将list排除值为null的字段转换成Json数组
     *
     * @param list
     * @param <T>
     * @return
     */
    public static <T> String toJsonArrayWithSerializeNulls(List<T> list) {
        String result = "";
        try {
            result = gson.toJson(list);
        } catch (Exception e) {
            logger.error("将list排除值为null的字段转换成Json数组出错", e);
        }
        return result;
    }

    /**
     * 将list中将Expose注解的字段转换成Json数组
     *
     * @param list
     * @param <T>
     * @return
     */
    public static <T> String toJsonArrayWithExpose(List<T> list) {
        Gson gson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().create();
        String result = "";
        try {
            result = gson.toJson(list);
        } catch (Exception e) {
            logger.error("将list中将Expose注解的字段转换成Json数组出错", e);
        }
        return result;
    }

}
