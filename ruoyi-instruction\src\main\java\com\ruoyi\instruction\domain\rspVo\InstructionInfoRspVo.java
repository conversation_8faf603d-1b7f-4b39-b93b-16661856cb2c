package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/21 19:39
 */
@Data
public class InstructionInfoRspVo {
    /**
     * 指令id
     */
    @Excel(name = "指令id")
    private Long id;

    /**
     * 指令标题
     */
    @Excel(name = "指令标题")
    private String instructionTitle;

    /**
     * 当前办理区县
     */
    @Excel(name = "当前办理区县")
    private String currentCounty;

    /**
     * 关联人数
     */
    private int personCount;

    /**
     * 交办时间
     */
    @Excel(name = "交办时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    /**
     * 接收时间
     */
    @Excel(name = "接收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reveiveTime;

    /**
     * 处置时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处置时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date disposeTime;

    /**
     * 反馈时间
     */
    @Excel(name = "反馈时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTime;

    /**
     * 销号时间
     */
    @Excel(name = "销号时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 指令创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 县市区反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date countyFeedbackTime;

    /**
     * 县市区已反馈部门
     */
    private Integer countDeptNum;

    /**
     * 接收部门个数
     */
    private Integer receiveUnitCount;

    /**
     * 该条记录是否可以处理 0：可以处理 1：不可处理
     */
    private Integer isDeal;

    /** 指令创建者部门id */
    private Long createDeptId;

    /** 事件类型Id */
    private String type;

    /** 事件类型名称 */
    private String typeName;

    /** 信息来源 */
    private String sourceInfo;

    /** 接收单位 */
    private String receiveUnit;


    /** 办理期限 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /** 反馈要求 */
    private String feedback;

    /** 人员ids */
    private String personIds;

    /** 指令内容 */
    private String instructionContent;

    /** 基本情况 */
    private String baseInfo;

    /**
     * 牵头人员ids
     */
    private String leadPersonIds;

    /**
     * 添加字段，用于驾驶舱标记状态
     */
    private String statusName;

    /** 是否发布 1：发布 2：未发布 */
    private String isRelease;
}
