package com.ruoyi;

import lombok.Data;
import java.util.Date;

/**
 * 采集员考核实体类
 */
@Data
public class CollectorsAttendance {
    private Long id; // 主键id
    private Long userId; // 用户id
    private String userName; // 用户名称
    private String nickName; // 用户昵称
    private String phonenumber; // 电话号码
    private String dept; // 组织
    private String nowLongitude; // 现在经度
    private String nowLatitude; // 现在纬度
    private Date signTime; // 签到时间
    private String signLongitude; // 签到经度
    private String signLatitude; // 签到纬度
    private String signRemark; // 签到备注
    private String signFilepath; // 签到文件路径
    private Date outTime; // 签退时间
    private String outLongitude; // 签退经度
    private String outLatitude; // 签退纬度
    private String outRemark; // 签退备注
    private String outFilepath; // 签退文件路径
    private Integer status; // 状态 1是上班 2是下班
    private String type; // 考勤状态类型 1是正常 2是迟到 3是早退 4是部门未配置规则
}