package com.ruoyi.util;

import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by Administrator on 2022-9-5.
 */
public class UserMapCache {

    // 当前登录用户map管理
    private static Map<String,UserCache> map = new ConcurrentHashMap<>();

    public static UserCache getByToken(String token){
        return map.get(token);
    }

    public static void login(String token,UserCache userCache){
        userCache.setEndTime(DateUtil.getTimeAfterHour(new Date(),2).getTime());
        map.put(token,userCache);
    }

    public static void flushEndTime(UserCache userCache){
        userCache.setEndTime(DateUtil.getTimeAfterHour(new Date(),2).getTime());
    }

    //短信验证码 有效期5分钟
    public static void messageYzm(String token,UserCache userCache){
        userCache.setEndTime(DateUtil.getTimeAfterMinute(new Date(),5).getTime());
        map.put(token,userCache);
    }

    /**
     * 密码错误 有限期5分钟
     *
     * @param token
     * @param userCache
     */
    public static void loginError(String token, UserCache userCache) {
        userCache.setEndTime(DateUtil.getTimeAfterMinute(new Date(), 5).getTime());
        map.put(token, userCache);
    }


    public static void clearOldUser(){
        // 清理已过期得用户
        Set<String> keySet = map.keySet();
        if(keySet != null && keySet.size() > 0){
            for(String key: keySet){
                UserCache cache = map.get(key);
                if(cache.getEndTime() < System.currentTimeMillis()){
                    // 已过期，移除缓存数据
                    map.remove(key);
                }
            }
        }
    }
}
