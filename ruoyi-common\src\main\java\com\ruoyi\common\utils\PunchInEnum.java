package com.ruoyi.common.utils;

/**
 * 打卡类型
 */
public enum PunchInEnum {
    //0=未打卡 1=正常 2=迟到 3=早退 4=定位异常 5=迟到及定位异常 6=早退及定位异常 7=请假

    NOPUNCHIN("0", "未打卡"),
    NORMAL("1", "正常"),
    LATE("2", "迟到"),
    EARLY("3", "早退"),
    ABNORMAL("4", "定位异常"),
    LATEANDABNORMAL("5","迟到及定位异常"),
    EARLYANDABNORMAL("6","早退及定位异常"),
    LEAVE("7", "请假");

    private String type;
    private String desc;

    PunchInEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
