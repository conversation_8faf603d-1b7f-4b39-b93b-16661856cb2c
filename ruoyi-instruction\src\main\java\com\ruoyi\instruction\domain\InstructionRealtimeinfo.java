package com.ruoyi.instruction.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【实时信息】对象 t_instruction_realtimeinfo
 *
 * <AUTHOR>
 * @date 2023-05-12
 */
public class InstructionRealtimeinfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 实时信息表自增id */
    private Long id;

    /** 信息类型 */
    @Excel(name = "信息类型")
    private String infoTitle;

    /** 信息内容 */
    @Excel(name = "信息内容")
    private String infoContent;

    /** 1：发布  2：未发布 */
    @Excel(name = "1：发布  2：未发布")
    private String isRelease;

    /** 1:正常  9：删除 */
    @Excel(name = "1:正常  9：删除")
    private String status;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setInfoTitle(String infoTitle)
    {
        this.infoTitle = infoTitle;
    }

    public String getInfoTitle()
    {
        return infoTitle;
    }
    public void setInfoContent(String infoContent)
    {
        this.infoContent = infoContent;
    }

    public String getInfoContent()
    {
        return infoContent;
    }
    public void setIsRelease(String isRelease)
    {
        this.isRelease = isRelease;
    }

    public String getIsRelease()
    {
        return isRelease;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("infoTitle", getInfoTitle())
            .append("infoContent", getInfoContent())
            .append("isRelease", getIsRelease())
            .append("createTime", getCreateTime())
            .append("status", getStatus())
            .toString();
    }
}
