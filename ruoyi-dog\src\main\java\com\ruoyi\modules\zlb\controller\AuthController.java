package com.ruoyi.modules.zlb.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.rsa.AESUtil;
import com.ruoyi.cykj.business.domain.vol.VolUser;
import com.ruoyi.cykj.business.mapper.vol.VolUserMapper;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.modules.sysLog.entity.SysLog;
import com.ruoyi.modules.sysLog.service.SysLogService;
import com.ruoyi.modules.user.service.SysUserService;
import com.ruoyi.modules.zlb.service.AuthService;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.ISysLogininforService;
import com.ruoyi.system.service.ISysOperLogService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.util.IdGen;
import com.ruoyi.util.UserCache;
import com.ruoyi.util.UserMapCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

import java.util.*;

import static com.ruoyi.util.zlb.ZLBConstants.TOKEN_SESSION_KEY;

@Slf4j
@RestController
@RequestMapping("/ZLB")
public class AuthController {

    @Autowired
    private AuthService authService;
    @Autowired
    public SysUserMapper sysUserMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysPermissionService permissionService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private VolUserMapper volUserMapper;
    @Autowired
    private ISysLogininforService sysLogininforService;

    @Value("${zlb.key}")
    private String key;

    /**
     * 登录
     *
     * @param ticketId
     * @param request
     * @return
     */
    @GetMapping("/wxlogin")
    public AjaxResult wxlogin(@RequestParam("ticketId") String ticketId, HttpServletRequest request) {
        HttpSession session = request.getSession();
        //1. 通过ticketId 换取 accessToken
        String token = authService.getTokenByTicketId(ticketId);
        //2. 保存accessToken
        session.setAttribute(TOKEN_SESSION_KEY, token);
        //3. 通过accessToken 获取用户信息
        JSONObject userInfo = authService.getUserInfoByToken(token);
//        System.out.println(userInfo.toString());
        JSONObject user = userInfo.getJSONObject("personInfo");
        log.info(user.toString());
        SysUser sysUser = new SysUser();
        //4. 生成登录态
        Integer sex = null;
        if (user.getString("gender") != null) {
            sex = user.getString("gender").equals("MALE") ? 1 : 2;
        }
        sysUser = userLogin(userInfo);

        AsyncFactory.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_SUCCESS, "浙里办登录成功");

        return AjaxResult.success(sysUser);
    }

    /**
     * 浙里办登录
     *
     * @param ticketId
     * @param request
     * @return
     */
    @GetMapping("/zlblogin")
    public AjaxResult zlblogin(@RequestParam("ticketId") String ticketId, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();

        HttpSession session = request.getSession();
        //1. 通过ticketId 换取 accessToken
        String token = authService.getZlbTokenByTicketId(ticketId);
        //2. 保存accessToken
        session.setAttribute(TOKEN_SESSION_KEY, token);
        //3. 通过accessToken 获取用户信息
        JSONObject userInfo = authService.getZlbUserInfoByToken(token);
        log.info("zlbLogin, user:{}", userInfo.toString());
//        JSONObject userInfo = JSON.parseObject("{\"orgcoding\":\"001006\",\"country\":\"\",\"officeaddress\":\"\",\"isFace\":\"0\",\"nation\":\"\",\"telephone2\":\"\",\"createdate\":\"2019-01-12 08:42:56\",\"official\":\"\",\"userid\":\"8afac9aa683c6c2801683f82ff5728c5\",\"officefax\":\"\",\"officialtype\":\"\",\"province\":\"\",\"idnumendtime\":\"\",\"authlevel\":\"3\",\"workaddr\":\"\",\"officenum\":\"\",\"companyalias\":\"\",\"postcode\":\"\",\"errmsg\":\"成功\",\"orderby\":\"17811806\",\"telephone\":\"\",\"firmname\":\"\",\"virtualnum\":\"\",\"idtype\":\"1\",\"companyname\":\"\",\"companydesc\":\"\",\"companysize\":\"\",\"driverlicense\":\"\",\"birthday\":\"\",\"aliuserid\":\"2088602333243022\",\"homephone\":\"\",\"city\":\"\",\"headpicture\":\"\",\"mid\":\"njqoHjw65/Gd0b1UdJnpekx8NjzMYmXGoJST2W3RKKJuD5teFZPP9GIFb2V0FnwJtcXayoc8r2Y=\",\"result\":\"0\",\"loginaddr\":\"\",\"useable\":\"1\",\"permitlicense\":\"\",\"mobile2\":\"\",\"servicecontent\":\"\",\"email\":\"\",\"loginname\":\"zjzw35144744\",\"sex\":\"1\",\"homeaddress\":\"\",\"mobile\":\"13923235656\",\"companypro\":\"\",\"idnumstarttime\":\"\",\"companytype\":\"\",\"idnum\":\"330100197001011234\",\"username\":\"debug_user_name\"}");
//        JSONObject userInfo = JSON.parseObject("{\"orgcoding\":\"001\",\"result\":\"0\",\"idtype\":\"1\",\"nation\":\"HA\",\"sex\":\"1\",\"mobile\":\"13923235656\",\"errmsg\":\"成功\",\"idnum\":\"330\n" +
//                "100197001011234\",\"userid\":\"debug_8e7f790ce5c8462da37976b52b72b1a7\",\"email\":\"<EMAIL>\",\"username\":\"debug_user_name\"}");
        //{"orgcoding":"001006","country":"","officeaddress":"","isFace":"0","nation":"","telephone2":"","createdate":"2019-01-12 08:42:56","official":"","userid":"8afac9aa683c6c2801683f82ff5728c5","officefax":"","officialtype":"","province":"","idnumendtime":"","authlevel":"3","workaddr":"","officenum":"","companyalias":"","postcode":"","errmsg":"成功","orderby":"17811806","telephone":"","firmname":"","virtualnum":"","idtype":"1","companyname":"","companydesc":"","companysize":"","driverlicense":"","birthday":"","aliuserid":"2088602333243022","homephone":"","city":"","headpicture":"","mid":"njqoHjw65/Gd0b1UdJnpekx8NjzMYmXGoJST2W3RKKJuD5teFZPP9GIFb2V0FnwJtcXayoc8r2Y=","result":"0","loginaddr":"","useable":"1","permitlicense":"","mobile2":"","servicecontent":"","email":"","loginname":"zjzw35144744","sex":"1","homeaddress":"","mobile":"15268151174","companypro":"","idnumstarttime":"","companytype":"","idnum":"330721199409041914","username":"施振强"}
        //4. 生成登录态
        SysUser sysUser = userLogin(userInfo);
        sysUser.setZlbUserId(userInfo.getString("userid"));
        sysUser.setNickName(userInfo.getString("username"));

        LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser.getDeptId(),
                sysUser, permissionService.getMenuPermission(sysUser));
        loginUser.setUser(sysUser);
        loginUser.setUserId(sysUser.getUserId());

        AsyncFactory.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_SUCCESS, "浙里办登录成功");

        // 返回参数
        map.put(Constants.TOKEN, tokenService.createToken(loginUser));
        map.put("sysUser", sysUser);
        map.put("volUserId", sysUser.getVolUserId());

        return AjaxResult.success(map);
    }

    //浙里办用户登录/注册登录
    private SysUser userLogin(JSONObject userInfo) {
        VolUser currentZlbUser = JSON.parseObject(JSON.toJSONString(userInfo), VolUser.class);
        String mobile = currentZlbUser.getMobile();
        String idCard = currentZlbUser.getIdnum();

        SysUser sysUser = sysUserMapper.getByMobile(currentZlbUser.getMobile());
        if (Objects.isNull(sysUser)) {
            sysUser = new SysUser();
            sysUser.setDeptId(623L);
            sysUser.setUserName(mobile);
            sysUser.setNickName(currentZlbUser.getUsername());
            sysUser.setRealName(currentZlbUser.getUsername());
            sysUser.setIdCard(idCard);
            sysUser.setUserType(1);
            sysUser.setSex(currentZlbUser.getSex());
            sysUser.setPhonenumber(mobile);
            sysUser.setZlbUserId(currentZlbUser.getUserid());
            if (mobile != null && mobile.length() > 10) {
                String pass = mobile.substring(mobile.length() - 6);
                sysUser.setPassword(SecurityUtils.encryptPassword(pass));
            } else {
                sysUser.setPassword(SecurityUtils.encryptPassword("123456"));//密码
            }
            sysUserMapper.insertUser(sysUser);

            List<SysUserRole> sysUserRoleList = Lists.newArrayList();
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(sysUser.getUserId());
            sysUserRole.setRoleId(171L);
            sysUserRoleList.add(sysUserRole);
            sysUserRoleMapper.batchUserRole(sysUserRoleList);
        }

        //拿到志愿者信息
        try {
            String mobileSecret = AESUtil.aesEncrypt(mobile, key);
            String idCardSecret = "";
            if (StringUtils.isNotBlank(idCard)) {
                idCardSecret = AESUtil.aesEncrypt(idCard, key);
            }
            VolUser volUser = volUserMapper.getByMobileOrIdCard(mobileSecret, idCardSecret);
            if (Objects.isNull(volUser)) {
                //塞入新的volUser
                volUser = new VolUser();
                currentZlbUser.setRole("0");
                String username = AESUtil.aesEncrypt(currentZlbUser.getUsername(), key);
                volUser.setUserName(username);
                volUser.setRealName(username);
                volUser.setPhone(mobileSecret);
                volUser.setIdCard(idCardSecret);
                volUser.setActualName(currentZlbUser.getUsername());
                volUser.setUserid(currentZlbUser.getUserid());
                volUser.setCreateTime(new Date());

                volUserMapper.insertVolUser(volUser);
            }

            sysUser.setVolUserId(volUser.getUserId());
        } catch (Exception e) {
            log.info("zlbLogin, error:{}", e);
        }

        return sysUser;
    }


}
