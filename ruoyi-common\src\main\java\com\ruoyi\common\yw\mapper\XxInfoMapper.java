package com.ruoyi.common.yw.mapper;

import java.util.List;
import com.ruoyi.common.yw.domain.XxInfo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 消息记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-19
 */
@Mapper
public interface XxInfoMapper 
{
    /**
     * 查询消息记录
     * 
     * @param id 消息记录主键
     * @return 消息记录
     */
    public XxInfo selectXxInfoById(String id);

    /**
     * 查询消息记录列表
     * 
     * @param xxInfo 消息记录
     * @return 消息记录集合
     */
    public List<XxInfo> selectXxInfoList(XxInfo xxInfo);

    /**
     * 新增消息记录
     * 
     * @param xxInfo 消息记录
     * @return 结果
     */
    public int insertXxInfo(XxInfo xxInfo);

    /**
     * 修改消息记录
     * 
     * @param xxInfo 消息记录
     * @return 结果
     */
    public int updateXxInfo(XxInfo xxInfo);

    /**
     * 删除消息记录
     * 
     * @param id 消息记录主键
     * @return 结果
     */
    public int deleteXxInfoById(String id);

    /**
     * 批量删除消息记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteXxInfoByIds(String[] ids);
}
