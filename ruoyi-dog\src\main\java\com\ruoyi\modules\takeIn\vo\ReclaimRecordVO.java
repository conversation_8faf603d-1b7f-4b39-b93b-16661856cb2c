package com.ruoyi.modules.takeIn.vo;

import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.modules.takeIn.entity.TakeIn;
import com.ruoyi.modules.user.entity.SysUploadFile;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ReclaimRecordVO {

    private String id;

    private String bizCode;
    /**
     * 收容表id
     */
    private String takeInId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 联系电话
     */
    private String contactNumber;

    private String idCard;

    private String street;

    private String county;

    private String address;

    private List<SysUploadFile> uploadFileList;

    private String imgStr;
}
