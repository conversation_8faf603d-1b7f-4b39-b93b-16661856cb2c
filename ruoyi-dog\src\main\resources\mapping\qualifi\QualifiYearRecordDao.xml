<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.hospital.dao.QualifiYearRecordDao">

    <sql id="columns">
     <trim suffixOverrides=",">
            a.id as id,
            a.qualifi_id as qualifiId,
            a.node as node,
            a.record_type as recordType,
            a.remark as remark,
            a.status as status,
            a.create_name as createName,
            a.create_date as createDate,
            a.create_by as createBy,
            a.update_date as updateDate,
            a.update_by as updateBy,
            a.del_flag as delFlag,
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.hospital.entity.QualifiYearRecord" resultType="com.ruoyi.modules.hospital.entity.QualifiYearRecord">
        select <include refid="columns"/>
        from qualifi_year_record a
        where a.id =#{id}
    </select>



    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.hospital.entity.QualifiYearRecord" resultType="com.ruoyi.modules.hospital.entity.QualifiYearRecord">
        select <include refid="columns"/>
        from qualifi_year_record a
        where a.del_flag =1
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="qualifiId != null and qualifiId != ''">
                and a.qualifi_id = #{qualifiId}
            </if>
            <if test="node != null">
                and a.node = #{node}
            </if>
            <if test="recordType != null">
                and a.record_type = #{recordType}
            </if>
            <if test="remark != null and remark != ''">
                and a.remark = #{remark}
            </if>
            <if test="status != null">
                and a.status = #{status}
            </if>
            <if test="createName != null and createName != ''">
                and a.create_name = #{createName}
            </if>
            <if test="createDate != null">
                and a.create_date = #{createDate}
            </if>
            <if test="createBy != null and createBy != ''">
                and a.create_by = #{createBy}
            </if>
            <if test="updateDate != null">
                and a.update_date = #{updateDate}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and a.update_by = #{updateBy}
            </if>
            <if test="delFlag != null">
                and a.del_flag = #{delFlag}
            </if>
            order by a.create_date desc
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.hospital.entity.QualifiYearRecord">
        insert into qualifi_year_record(id,qualifi_id, node, record_type, remark, status, create_name, create_date, create_by, update_date, update_by, del_flag)
        values (#{id},#{qualifiId}, #{node}, #{recordType}, #{remark}, #{status}, #{createName}, #{createDate}, #{createBy}, #{updateDate}, #{updateBy}, #{delFlag})
    </insert>

    <update id="updateByEntity" parameterType="com.ruoyi.modules.hospital.entity.QualifiYearRecord">
        update qualifi_year_record set
        <trim suffixOverrides=",">
            <if test="qualifiId != null and qualifiId != ''">
                qualifi_id = #{qualifiId},
            </if>
            <if test="node != null">
                node = #{node},
            </if>
            <if test="recordType != null">
                record_type = #{recordType},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="createName != null and createName != ''">
                create_name = #{createName},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
        </trim>
        where id = #{id}
    </update>
    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.hospital.entity.QualifiYearRecord">
        update qualifi_year_record set
        <trim suffixOverrides=",">
               qualifi_id = #{qualifiId},
               node = #{node},
               record_type = #{recordType},
               remark = #{remark},
               status = #{status},
               create_name = #{createName},
               create_date = #{createDate},
               create_by = #{createBy},
               update_date = #{updateDate},
               update_by = #{updateBy},
               del_flag = #{delFlag},
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.hospital.entity.QualifiYearRecord">
        UPDATE  qualifi_year_record
        SET
        del_flag=#{delFlag},
        update_date = #{updateDate},
        update_by = #{updateBy}
        where id = #{id}
    </delete>

</mapper>

