package com.ruoyi.common.vo.ocr.res;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class OcrAtoCapMixCardResVO {

    /**
     * 识别卡片数量
     */
    private String count;

    /**
     * 返回数据
     *
     */
    private List<Map<String, Object>> subMsgs;

    /**
     * sid
     */
    private String sid;

    /**
     * 高度
     */
    private String height;

    /**
     * 宽度
     */
    private String width;

    /**
     * 原始高度
     */
    private String orgHeight;

    /**
     * 原始宽度
     */
    private String orgWidth;
}
