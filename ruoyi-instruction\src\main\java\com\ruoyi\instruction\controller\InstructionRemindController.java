package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.InstructionRemind;
import com.ruoyi.instruction.service.IInstructionRemindService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 提醒单-信息Controller
 * 
 * <AUTHOR>
 * @date 2023-05-18
 */
@RestController
@RequestMapping("/instruction/remind")
public class InstructionRemindController extends BaseController
{
    @Autowired
    private IInstructionRemindService instructionRemindService;

    /**
     * 查询提醒单-信息列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:remind:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionRemind instructionRemind)
    {
        Long deptId = SecurityUtils.getDeptId();
        instructionRemind.setCreateDeptId(deptId);
        startPage();
        List<InstructionRemind> list = instructionRemindService.selectInstructionRemindList(instructionRemind);
        return getDataTable(list);
    }

    /**
     * 导出提醒单-信息列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:remind:export')")
    @Log(title = "提醒单-信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionRemind instructionRemind)
    {
        List<InstructionRemind> list = instructionRemindService.selectInstructionRemindList(instructionRemind);
        ExcelUtil<InstructionRemind> util = new ExcelUtil<InstructionRemind>(InstructionRemind.class);
        util.exportExcel(response, list, "提醒单-信息数据");
    }

    /**
     * 获取提醒单-信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:remind:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(instructionRemindService.selectInstructionRemindById(id));
    }

    /**
     * 新增提醒单-信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:remind:add')")
    @Log(title = "提醒单-信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionRemind instructionRemind)
    {
        return toAjax(instructionRemindService.insertInstructionRemind(instructionRemind));
    }

    /**
     * 修改提醒单-信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:remind:edit')")
    @Log(title = "提醒单-信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionRemind instructionRemind)
    {
        return toAjax(instructionRemindService.updateInstructionRemind(instructionRemind));
    }

    /**
     * 删除提醒单-信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:remind:remove')")
    @Log(title = "提醒单-信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(instructionRemindService.deleteInstructionRemindByIds(ids));
    }
}
