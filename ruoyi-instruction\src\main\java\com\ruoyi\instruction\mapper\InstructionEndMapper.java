package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.InstructionEnd;
import com.ruoyi.instruction.domain.rspVo.InstructionEndVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 指令销号Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
@DataSource(value = DataSourceType.SLAVE)
public interface InstructionEndMapper 
{
    /**
     * 查询指令销号
     * 
     * @param id 指令销号主键
     * @return 指令销号
     */
    public InstructionEnd selectInstructionEndById(Long id);

    /**
     * 查询指令销号列表
     * 
     * @param instructionEnd 指令销号
     * @return 指令销号集合
     */
    public List<InstructionEnd> selectInstructionEndList(InstructionEnd instructionEnd);

    /**
     * 新增指令销号
     * 
     * @param instructionEnd 指令销号
     * @return 结果
     */
    public int insertInstructionEnd(InstructionEnd instructionEnd);

    /**
     * 修改指令销号
     * 
     * @param instructionEnd 指令销号
     * @return 结果
     */
    public int updateInstructionEnd(InstructionEnd instructionEnd);

    /**
     * 删除指令销号
     * 
     * @param id 指令销号主键
     * @return 结果
     */
    public int deleteInstructionEndById(Long id);

    /**
     * 批量删除指令销号
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionEndByIds(Long[] ids);

    /**
     * 根据指令id查询销号信息
     * @param id
     * @return
     */
    InstructionEnd selectInstructionEndByInstructionId(Long id);

    /**
     * 查询群组下各指令的销号状态
     * @param instructionIds
     * @return
     */
    List<Integer> selectIsEndByGroupId(@Param("list")  List<Long> instructionIds);

    /**
     * 根据时间查询
     * @param date
     * @return
     */
    List<InstructionEndVo> selectList(@Param("date") String date,@Param("receiveUnit")String receiveUnit);
}
