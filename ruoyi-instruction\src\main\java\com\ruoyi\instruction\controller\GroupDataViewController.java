package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.instruction.domain.InstructionGroup;
import com.ruoyi.instruction.domain.rspVo.GroupDataRspVo;
import com.ruoyi.instruction.service.IInstrucationPersonService;
import com.ruoyi.instruction.service.IInstructionGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/17 14:48
 * 群体工作台controller
 */
@RestController
@RequestMapping("/group/dateView")
public class GroupDataViewController {

    @Autowired
    private IInstructionGroupService groupService;

    @Autowired
    private IInstrucationPersonService personService;

    /**
     * 获取群体总数、高、中、低管控级别数据
     *
     * @return
     */
    @PostMapping("/getGroupData")
    public AjaxResult getGroup(@RequestBody(required = false) Map<String,Object> map) {
        GroupDataRspVo groupDataRspVo = groupService.getGroupData(map);
        return AjaxResult.success(groupDataRspVo);
    }

    /**
     * 获取群体关联人员排行
     *
     * @return
     */
    @GetMapping("/getPersonCount")
    public AjaxResult getPersonCount() {
        InstructionGroup group1 = new InstructionGroup();
        List<InstructionGroup> list = groupService.selectInstructionGroupList(group1);
        if (list != null && list.size() > 0) {
            for (InstructionGroup group : list) {
                //所有人员ids
                group.setPersonCount(0);
                if (group.getAllPersonIds().length() > 0) {
                    List<String> collect = Arrays.stream(group.getAllPersonIds().split(",")).collect(Collectors.toList());
                    //查询人员数量
                    int count = personService.findPeronCountById(collect);
                    group.setPersonCount(count);
                }

            }
        }
        List<InstructionGroup> collect = list.stream()
                .sorted(Comparator.comparing(InstructionGroup::getPersonCount)
                .reversed())
                .limit(3)
                .collect(Collectors.toList());

        return AjaxResult.success(collect);
    }

    /**
     * 获取群体关联事件排名
     *
     * @return
     */
    @GetMapping("/getEventCount")
    public AjaxResult getEventCount() {
        List<Map<String, Integer>> maps = groupService.getEventCount();
        return AjaxResult.success(maps);
    }

    /**
     * 获取群体类型数量
     *
     * @return
     */
    @GetMapping("/getType")
    public AjaxResult getType() {
        List<Map<String, Integer>> maps = groupService.getType();
        return AjaxResult.success(maps);
    }

    /**
     * 获取群体高频人员
     *
     * @return
     */
    @GetMapping("/getGroupPerson")
    public AjaxResult getGroupPerson() {
        List<Map<String, Integer>> maps = groupService.getGroupPerson();
        return AjaxResult.success(maps);
    }

    /**
     * 根据群体id获取群体高频人员(新)
     * @return
     */
    @GetMapping("/getGroupPersonNew/{id}")
    public AjaxResult getGroupPersonNew(@PathVariable("id")Long id){
        List<Map<String,Object>> maps =  groupService.getGroupPersonNew(id);
        return AjaxResult.success(maps);
    }

    /**
     * 获取群体列表
     *
     * @return
     */
    @GetMapping("/getGroupList")
    public AjaxResult getGroupList(@RequestParam Map<String, Object> params) {
        InstructionGroup group = new InstructionGroup();
        group.setParams(params);
        List<InstructionGroup> list = groupService.selectInstructionGroupList(group);
        return AjaxResult.success(list);
    }

}
