<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.user.dao.SysUserRoleDao" >

    <sql id="sysUserRoleColumns">
        a.id AS "id",
        a.user_id AS "userId",
        a.role_id AS "roleId",
        a.manager_orgs AS "managerOrgs"
    </sql>

    <select id="getList" resultType="com.ruoyi.modules.user.entity.SysUserRole">
        select
          <include refid="sysUserRoleColumns"/>
        from sys_user_role a
        where 1=1
        <if test='userId != null and userId != ""'>
            and a.user_id = #{userId}
        </if>
    </select>

    <insert id="insert" parameterType="com.ruoyi.modules.user.entity.SysUserRole">
        INSERT INTO sys_user_role(
        id,
        user_id,
        role_id
        )VALUES (
        #{id},
        #{userId},
        #{roleId}
        )
    </insert>

    <update id="update" parameterType="com.ruoyi.modules.user.entity.SysUserRole">
        UPDATE sys_user_role SET
        <if test='userId != null and userId != ""'>
            user_id = #{userId},
        </if>
        <if test='roleId != null and roleId != ""'>
            role_id = #{roleId},
        </if>
        id = #{id}
        WHERE id = #{id}
    </update>


    <update id="deleteByUserId">
        delete from sys_user_role where user_id = #{userId}
    </update>

    <insert id="saveList" parameterType="java.util.List">
        INSERT INTO sys_user_role (
            id,
            user_id,
            role_id,
            manager_orgs
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
                #{item.id},
                #{item.userId},
                #{item.roleId},
                #{item.managerOrgs}
            )
        </foreach>
    </insert>
</mapper>
