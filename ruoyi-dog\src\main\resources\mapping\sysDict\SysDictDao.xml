<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.sysDict.dao.SysDictDao">
    <sql id="sysDictColumns">
        a.id as "id",
        a.parent_id AS "parentId",
        a.dict_type AS "dictType",
        a.dict_key AS "dictKey",
        a.name AS "name",
        a.value1 AS "value1",
        a.value2 AS "value2",
        a.value3 AS "value3",
        a.value4 AS "value4",
        a.description AS "description",
        a.remarks AS "remarks",
        a.status AS "status",
        a.create_by AS "createBy",
        a.create_date AS "createDate",
        a.del_flag AS "delFlag",
        a.sort_num AS "sortNum"
    </sql>

    <select id="getById" resultType="com.ruoyi.modules.sysDict.entity.SysDict">
        SELECT
        <include refid="sysDictColumns"/>
        FROM sys_dict a WHERE id=#{id}
    </select>

    <select id="getByEntity" resultType="com.ruoyi.modules.sysDict.entity.SysDict" parameterType="com.ruoyi.modules.sysDict.entity.SysDict">
        SELECT
        <include refid="sysDictColumns"/>
        FROM sys_dict a
        WHERE a.del_flag = 1
        <if test="dictKey != null and dictKey != ''">
            AND a.dict_key = #{dictKey}
        </if>
        <if test="name != null and name != ''">
            AND a.name LIKE concat("%", #{name}, "%")
        </if>
        <if test="dictType != null and dictType != ''">
            AND a.dict_type = #{dictType}
        </if>
        <if test="value1 != null and value1 != ''">
            AND a.value1 = #{value1}
        </if>
        <if test="value2 != null and value2 != ''">
            and a.value2 = #{value2}
        </if>
        <if test="value3 != null and value3 != ''">
            and a.value3 = #{value3}
        </if>
        <if test="value4 != null and value4 != ''">
            and a.value4 = #{value4}
        </if>
        <if test="status != null">
            AND a.status = #{status}
        </if>
    </select>

    <insert id="insert" parameterType="com.ruoyi.modules.sysDict.entity.SysDict">
        INSERT INTO sys_dict(
          id,
          parent_id,
          dict_type,
          dict_key,
          name,
          value1,
          value2,
          value3,
          description,
          remarks,
          status,
          create_by,
          create_date,
          del_flag,
          sort_num
        )VALUES (
          #{id},
          #{parentId},
          #{dictType},
          #{dictKey},
          #{name},
          #{value1},
          #{value2},
          #{value3},
          #{description},
          #{remarks},
          #{status},
          #{createBy},
          #{createDate},
          #{delFlag},
          #{sortNum}
        )
    </insert>

    <update id="update" parameterType="com.ruoyi.modules.sysDict.entity.SysDict">
        UPDATE sys_dict SET
        <if test='parentId != null and parentId != ""'>
            parent_id = #{parentId},
        </if>
        <if test='dictType != null and dictType != ""'>
            dict_type = #{dictType},
        </if>
        <if test='dictKey != null and dictKey != ""'>
            dict_key = #{dictKey},
        </if>
        <if test='name != null and name != ""'>
            name = #{name},
        </if>
        <if test='value1 != null and value1 != ""'>
            value1 = #{value1},
        </if>
        <if test='value2 != null and value2 != ""'>
            value2 = #{value2},
        </if>
        <if test='value3 != null and value3 != ""'>
            value3 = #{value3},
        </if>
        <if test='value4 != null and value4 != ""'>
            value4 = #{value4},
        </if>
        <if test='description != null and description != ""'>
            description = #{description},
        </if>
        <if test='remarks != null and remarks != ""'>
            remarks = #{remarks},
        </if>
        <if test='status != null'>
            status = #{status},
        </if>
        <if test='createBy != null and createBy != ""'>
            create_by = #{createBy},
        </if>
        <if test='createDate != null'>
            create_date = #{createDate},
        </if>
        <if test='delFlag != null'>
            del_flag = #{delFlag},
        </if>
        <if test='sortNum != null'>
            sort_num = #{sortNum},
        </if>
        <if test='updateBy != null and updateBy != ""'>
            update_by = #{updateBy},
        </if>
        <if test='updateDate != null'>
            update_date = #{updateDate},
        </if>
        id = #{id}
        WHERE id=#{id}
    </update>

    <update id="delete">
        UPDATE sys_dict SET del_flag = 2 WHERE id=#{id}
    </update>

    <select id="getList" parameterType="com.ruoyi.modules.sysDict.entity.SysDict" resultType="com.ruoyi.modules.sysDict.entity.SysDict">
        SELECT
            <include refid="sysDictColumns"/>
        FROM sys_dict a
        WHERE del_flag = 1
        <if test="dictKey != null and dictKey != ''">
            AND a.dict_key = #{dictKey}
        </if>
        <if test="name != null and name != ''">
            AND a.name LIKE concat("%", #{name}, "%")
        </if>
        <if test="dictType != null and dictType != ''">
            AND a.dict_type = #{dictType}
        </if>
        <if test="value1 != null and value1 != ''">
            AND a.value1 = #{value1}
        </if>
        <if test="value2 != null and value2 != ''">
            and a.value2 = #{value2}
        </if>
        <if test="value3 != null and value3 != ''">
            and a.value3 = #{value3}
        </if>
        <if test="value4 != null and value4 != ''">
            and a.value4 = #{value4}
        </if>
        <if test="status != null">
            AND a.status = #{status}
        </if>
        order by a.sort_num
    </select>


    <select id="getPetType" parameterType="com.ruoyi.modules.sysDict.entity.SysDict" resultType="com.ruoyi.modules.sysDict.entity.SysDict">
        SELECT
            <include refid="sysDictColumns"/>
        FROM sys_dict a
        WHERE del_flag = 1
        and (a.dict_type = 'varieties_type' or a.dict_type = 'varieties')
        <if test="dictKey != null and dictKey != ''">
            AND a.dict_key =  #{dictKey}
        </if>
        <if test="name != null and name != ''">
            AND a.name LIKE concat("%", #{name}, "%")
        </if>
        <if test="value1 != null and value1 != ''">
            AND a.value1 = #{value1}
        </if>
        <if test="value2 != null and value2 != ''">
            and a.value2 = #{value2}
        </if>
        <if test="value3 != null and value3 != ''">
            and a.value3 = #{value3}
        </if>
        <if test="value4 != null and value4 != ''">
            and a.value4 = #{value4}
        </if>
        <if test="status != null">
            AND a.status = #{status}
        </if>
        order by a.sort_num
    </select>

    <select id="queryDictKey" resultType="java.lang.String">
        select ifnull(max(dict_key),0) + 1 as dictKey
        from sys_dict
        WHERE dict_type = #{dictType}
        <if test="parentId != null and parentId != ''">
            AND parent_id = #{parentId}
        </if>
    </select>

</mapper>
