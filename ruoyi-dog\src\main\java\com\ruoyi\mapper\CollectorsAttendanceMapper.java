package com.ruoyi.mapper;

import com.ruoyi.CollectorsAttendance;
import java.util.List;

/**
 * 采集员考核Mapper接口
 */
public interface CollectorsAttendanceMapper {
    /**
     * 查询采集员考核
     *
     * @param id 采集员考核主键
     * @return 采集员考核
     */
    public CollectorsAttendance selectCollectorsAttendanceById(Long id);

    /**
     * 查询采集员考核列表
     *
     * @param collectorsAttendance 采集员考核
     * @return 采集员考核集合
     */
    public List<CollectorsAttendance> selectCollectorsAttendanceList(CollectorsAttendance collectorsAttendance);

    /**
     * 新增采集员考核
     *
     * @param collectorsAttendance 采集员考核
     * @return 结果
     */
    public int insertCollectorsAttendance(CollectorsAttendance collectorsAttendance);

    /**
     * 修改采集员考核
     *
     * @param collectorsAttendance 采集员考核
     * @return 结果
     */
    public int updateCollectorsAttendance(CollectorsAttendance collectorsAttendance);

    /**
     * 删除采集员考核
     *
     * @param id 采集员考核主键
     * @return 结果
     */
    public int deleteCollectorsAttendanceById(Long id);
}