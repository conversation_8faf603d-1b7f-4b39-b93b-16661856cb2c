package com.ruoyi.util;

import com.google.gson.Gson;
import com.qiniu.common.QiniuException;
import com.qiniu.common.Zone;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import com.qiniu.util.Auth;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.URLEncoder;

/**
 * @classname:
 * @description:
 * @author: 侯旭
 * @date: 2021/3/10 11:19
 * @version: 1.0
 **/
public class QiniuFileUtils {

    /**
     * 上传文件至七牛云
     *
     * @param key      文件在七牛云存储的标记，带目录，不可重复，不加文件类型，如：u8/userHead/72356eed6af840f2afa12c20921eddbe
     * @param isPublic 是否存放在公有空间
     * @return 返回文件地址
     */
    public static void upload(MultipartFile multipartFile,
                              HttpServletRequest request,
                              String key,
                              Boolean isPublic) throws IOException {
        //文件信息
        String fileType = multipartFile.getOriginalFilename();
//
        //构造一个带指定Zone对象的配置类
        Configuration cfg = new Configuration(Zone.zone1());
        //...其他参数参考类注释
        UploadManager uploadManager = new UploadManager(cfg);
        //生成上传凭证
        Auth auth = Auth.create(U8Constant.QINIU_ACCESS_KEY, U8Constant.QINIU_SECRET_KEY);
        //生成token，注意这里加上key参数才可以覆盖上传
        String upToken;
        if (isPublic)
            //upToken = auth.uploadToken("u8file-public", key);  //中海
            upToken = auth.uploadToken("chinau8-pub", key);
        else
//            upToken = auth.uploadToken("u8file", key);    //中海
            upToken = auth.uploadToken("chinau8-pri", key);
        //上传
        Response response = uploadManager.put(multipartFile.getInputStream(), key, upToken,null,null);
        //解析上传成功的结果
        DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
        //删除临时文件
//        FileUtils.deleteDirectory(absoluteDirectory);
    }

    /**
     * 生成公开链接，主要为头像
     *
     * @param key 即存储的文件地址
     */
    public static String generatePublicURL(String key) throws UnsupportedEncodingException {
        if (key == null || key.isEmpty())
            return null;
        //bucket u8file地址
        String domainOfBucket = U8Constant.QINIU_PUBLIC_BUCKET_DOMAIN;
        String encodedFileName;
        encodedFileName = URLEncoder.encode(key, "utf-8");
        return String.format("%s/%s?v=%d", domainOfBucket, encodedFileName, (int) (Math.random() * 899999 + 100000));
    }

    /**
     * 生成临时公开链接
     *
     * @param key 即存储的文件地址
     */
    public static String generatePrivateURL(String key) throws UnsupportedEncodingException {
        if (key == null || key.isEmpty())
            return null;
        //bucket u8file地址
        String domainOfBucket = U8Constant.QINIU_SECRET_BUCKET_DOMAIN;
        String encodedFileName;
        encodedFileName = URLEncoder.encode(key, "utf-8");
        String publicUrl = String.format("%s/%s?v=%d", domainOfBucket, encodedFileName, (int) (Math.random() * 899999 + 100000));
        long expireInSeconds = 3600;//1小时，可以自定义链接过期时间
        //生成上传凭证
        Auth auth = Auth.create(U8Constant.QINIU_ACCESS_KEY, U8Constant.QINIU_SECRET_KEY);
        return auth.privateDownloadUrl(publicUrl, expireInSeconds);
    }

    /**
     * 按照字节上传七牛云
     *
     * @param byteData
     * @param key
     * @param isPublic
     * @throws QiniuException
     */
    public static void uploadByBytes(byte[] byteData, String key, Boolean isPublic) throws QiniuException {
        //构造一个带指定Zone对象的配置类
        Configuration cfg = new Configuration(Zone.zone1());
        //...其他参数参考类注释
        UploadManager uploadManager = new UploadManager(cfg);
        //生成上传凭证
        Auth auth = Auth.create(U8Constant.QINIU_ACCESS_KEY, U8Constant.QINIU_SECRET_KEY);
        //生成token，注意这里加上key参数才可以覆盖上传
        String upToken;
        if (isPublic) {
            upToken = auth.uploadToken("chinau8-pub", key);
        } else {
            upToken = auth.uploadToken("chinau8-pri", key);
        }
        //上传
        Response response = uploadManager.put(byteData, key, upToken);
    }

    public static void deleteFile(String publicURL, String bucket) {
        try {
            //生成凭证
            String[] path = publicURL.split("/");
            String key = path[3];
            Auth auth = Auth.create(U8Constant.QINIU_ACCESS_KEY, U8Constant.QINIU_SECRET_KEY);
            Configuration configuration = new Configuration(Zone.autoZone());
            BucketManager bucketManager = new BucketManager(auth, configuration);
            bucketManager.delete(bucket, key);
        } catch (QiniuException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        File file = new File("D:\\123456.jpg");
        ByteArrayOutputStream bos = new ByteArrayOutputStream((int) file.length());
        BufferedInputStream in = null;
        try {
            in = new BufferedInputStream(new FileInputStream(file));
            int buf_size = 1024;
            byte[] buffer = new byte[buf_size];
            int len = 0;
            while (-1 != (len = in.read(buffer, 0, buf_size))) {
                bos.write(buffer, 0, len);
            }
            byte[] t = bos.toByteArray();
            String key = "u8/baochepai/" + IdGen.uuid();
            QiniuFileUtils.uploadByBytes(t, key, true);
            String filePath = QiniuFileUtils.generatePublicURL(key);
            System.out.println(filePath);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                in.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                bos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
