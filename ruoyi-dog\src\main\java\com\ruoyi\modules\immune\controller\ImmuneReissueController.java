package com.ruoyi.modules.immune.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.immune.entity.ImmuneReissue;
import com.ruoyi.modules.immune.service.ImmuneReissueService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



@RestController
@RequestMapping("reissue")
public class ImmuneReissueController {

    @Resource
    private ImmuneReissueService service;

    @RequestMapping("getPageList")
    public AjaxResult getPageList(ImmuneReissue entity){
        return AjaxResult.success(service.getPageList(entity));
    }

    @RequestMapping("getById")
    public AjaxResult getById(String id){
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("getList")
    public AjaxResult getList(ImmuneReissue entity){
        return AjaxResult.success(service.getList(entity));
    }

    @RequestMapping("saveOrUpdate")
    public AjaxResult saveOrUpdate(ImmuneReissue entity){
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }
    @RequestMapping("saveOrUpdateGr")
    public AjaxResult saveOrUpdateGr(ImmuneReissue entity){
        service.saveOrUpdateGr(entity);
        return AjaxResult.success();
    }

    @RequestMapping("updateStatus")
    public AjaxResult updateStatus(ImmuneReissue entity){
        return AjaxResult.success().put("data",service.updateStatus(entity));
    }

    @RequestMapping("delete")
    public AjaxResult delete(ImmuneReissue entity){
        service.delete(entity);
        return AjaxResult.success();
    }

    @RequestMapping("getByPetId")
    public AjaxResult getByPetId(ImmuneReissue immuneReissue){
        return AjaxResult.success(service.getByPetId(immuneReissue));
    }
}
