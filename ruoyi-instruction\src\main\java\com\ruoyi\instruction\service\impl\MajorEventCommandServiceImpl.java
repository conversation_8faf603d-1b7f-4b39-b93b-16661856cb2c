package com.ruoyi.instruction.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.instruction.domain.MajorEventCommand;
import com.ruoyi.instruction.mapper.MajorEventCommandMapper;
import com.ruoyi.instruction.service.IMajorEventCommandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 重大事件-跟踪指挥Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-02
 */
@Service
public class MajorEventCommandServiceImpl implements IMajorEventCommandService 
{
    @Autowired
    private MajorEventCommandMapper majorEventCommandMapper;

    /**
     * 查询重大事件-跟踪指挥
     * 
     * @param id 重大事件-跟踪指挥主键
     * @return 重大事件-跟踪指挥
     */
    @Override
    public MajorEventCommand selectMajorEventCommandById(Long id)
    {
        return majorEventCommandMapper.selectMajorEventCommandById(id);
    }

    /**
     * 查询重大事件-跟踪指挥列表
     * 
     * @param majorEventCommand 重大事件-跟踪指挥
     * @return 重大事件-跟踪指挥
     */
    @Override
    public List<MajorEventCommand> selectMajorEventCommandList(MajorEventCommand majorEventCommand)
    {
        return majorEventCommandMapper.selectMajorEventCommandList(majorEventCommand);
    }

    /**
     * 新增重大事件-跟踪指挥
     * 
     * @param majorEventCommand 重大事件-跟踪指挥
     * @return 结果
     */
    @Override
    public int insertMajorEventCommand(MajorEventCommand majorEventCommand)
    {
        majorEventCommand.setCreateTime(DateUtils.getNowDate());
        return majorEventCommandMapper.insertMajorEventCommand(majorEventCommand);
    }

    /**
     * 修改重大事件-跟踪指挥
     * 
     * @param majorEventCommand 重大事件-跟踪指挥
     * @return 结果
     */
    @Override
    public int updateMajorEventCommand(MajorEventCommand majorEventCommand)
    {
        majorEventCommand.setUpdateTime(DateUtils.getNowDate());
        return majorEventCommandMapper.updateMajorEventCommand(majorEventCommand);
    }

    /**
     * 批量删除重大事件-跟踪指挥
     * 
     * @param ids 需要删除的重大事件-跟踪指挥主键
     * @return 结果
     */
    @Override
    public int deleteMajorEventCommandByIds(Long[] ids)
    {
        return majorEventCommandMapper.deleteMajorEventCommandByIds(ids);
    }

    /**
     * 删除重大事件-跟踪指挥信息
     * 
     * @param id 重大事件-跟踪指挥主键
     * @return 结果
     */
    @Override
    public int deleteMajorEventCommandById(Long id)
    {
        return majorEventCommandMapper.deleteMajorEventCommandById(id);
    }
}
