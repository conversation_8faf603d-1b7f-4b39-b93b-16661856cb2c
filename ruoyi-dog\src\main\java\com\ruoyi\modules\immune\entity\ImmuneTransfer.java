package com.ruoyi.modules.immune.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.user.entity.SysUser;

import java.util.Date;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 9:52
 * @version: 1.0
 **/
public class ImmuneTransfer extends BaseEntity {

    private String petId;                             //犬只ID
    private String newIdCard;                         //新饲主身份证
    private String newName;                           //新饲主姓名
    private String newAddress;                        //新饲主身份证地址
    private String newTel;                            //新饲主手机号码
    private String oldIdCard;                         //原饲主身份证
    private String oldName;                           //原饲主姓名
    private String oldAddress;                        //原饲主身份证地址
    private String oldTel;                            //原饲主手机号码
    private String status;                            //审核状态：1待审批，2已通过，3未通过
    private String reason;                            //审核意见
    private String img;                              //新户主图片证件
    private String newArea;                        //新饲主户籍地址
    private String oldArea;                        //原饲主户籍地址
    private String tel; //饲主手机号
    private String realName; //饲主真是姓名
    private String petIdCard; //饲主身份证号
    private String captcha; //验证码

    public String getNewArea() {
        return newArea;
    }

    public void setNewArea(String newArea) {
        this.newArea = newArea;
    }

    public String getOldArea() {
        return oldArea;
    }

    public void setOldArea(String oldArea) {
        this.oldArea = oldArea;
    }

    public String getPetId() {
        return petId;
    }

    public void setPetId(String petId) {
        this.petId = petId;
    }

    public String getNewIdCard() {
        return newIdCard;
    }

    public void setNewIdCard(String newIdCard) {
        this.newIdCard = newIdCard;
    }

    public String getNewName() {
        return newName;
    }

    public void setNewName(String newName) {
        this.newName = newName;
    }

    public String getNewAddress() {
        return newAddress;
    }

    public void setNewAddress(String newAddress) {
        this.newAddress = newAddress;
    }

    public String getNewTel() {
        return newTel;
    }

    public void setNewTel(String newTel) {
        this.newTel = newTel;
    }

    public String getOldIdCard() {
        return oldIdCard;
    }

    public void setOldIdCard(String oldIdCard) {
        this.oldIdCard = oldIdCard;
    }

    public String getOldName() {
        return oldName;
    }

    public void setOldName(String oldName) {
        this.oldName = oldName;
    }

    public String getOldAddress() {
        return oldAddress;
    }

    public void setOldAddress(String oldAddress) {
        this.oldAddress = oldAddress;
    }

    public String getOldTel() {
        return oldTel;
    }

    public void setOldTel(String oldTel) {
        this.oldTel = oldTel;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }


    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPetIdCard() {
        return petIdCard;
    }

    public void setPetIdCard(String petIdCard) {
        this.petIdCard = petIdCard;
    }

    public String getCaptcha() {
        return captcha;
    }

    public void setCaptcha(String captcha) {
        this.captcha = captcha;
    }
}
