package com.ruoyi.modules.user.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.user.entity.SysRoleMenu;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by Administrator on 2021-3-13.
 */
@Repository
public interface SysRoleMenuDao extends BaseDao<SysRoleMenu> {

    /**
     * 根据角色ID批量删除菜单权限
     * @param roleId
     */
    public void deleteByRoleId(String roleId);

    /**
     * 批量保存菜单权限
     * @param list
     */
    public void saveList(List<SysRoleMenu> list);

}
