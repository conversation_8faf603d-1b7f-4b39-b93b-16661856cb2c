package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.InstructionEnd;
import com.ruoyi.instruction.service.IInstructionEndService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 指令销号Controller
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
@RestController
@RequestMapping("/instruction/end")
public class InstructionEndController extends BaseController
{
    @Autowired
    private IInstructionEndService instructionEndService;

    /**
     * 查询指令销号列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:end:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionEnd instructionEnd)
    {
        startPage();
        List<InstructionEnd> list = instructionEndService.selectInstructionEndList(instructionEnd);
        return getDataTable(list);
    }

    /**
     * 导出指令销号列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:end:export')")
    @Log(title = "指令销号", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionEnd instructionEnd)
    {
        List<InstructionEnd> list = instructionEndService.selectInstructionEndList(instructionEnd);
        ExcelUtil<InstructionEnd> util = new ExcelUtil<InstructionEnd>(InstructionEnd.class);
        util.exportExcel(response, list, "指令销号数据");
    }

    /**
     * 获取指令销号详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:end:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(instructionEndService.selectInstructionEndById(id));
    }

    /**
     * 新增指令销号
     */
    @PreAuthorize("@ss.hasPermi('instruction:end:add')")
    @Log(title = "指令销号", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionEnd instructionEnd)
    {
        return toAjax(instructionEndService.insertInstructionEnd(instructionEnd));
    }

    /**
     * 修改指令销号
     */
    @PreAuthorize("@ss.hasPermi('instruction:end:edit')")
    @Log(title = "指令销号", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionEnd instructionEnd)
    {
        return toAjax(instructionEndService.updateInstructionEnd(instructionEnd));
    }

    /**
     * 删除指令销号
     */
    @PreAuthorize("@ss.hasPermi('instruction:end:remove')")
    @Log(title = "指令销号", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(instructionEndService.deleteInstructionEndByIds(ids));
    }
}
