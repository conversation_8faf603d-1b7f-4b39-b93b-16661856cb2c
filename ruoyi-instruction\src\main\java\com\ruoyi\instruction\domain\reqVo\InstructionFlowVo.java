package com.ruoyi.instruction.domain.reqVo;

import com.ruoyi.instruction.domain.InstructionAssign;
import com.ruoyi.instruction.domain.InstructionEnd;
import com.ruoyi.instruction.domain.InstructionReceive;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/19 15:40
 * 指令流程请求类
 */
@Data
public class InstructionFlowVo {

    /**
     * 指令id
     */
    private Long infoId;

    /**
     * 交办实体类
     */
    private InstructionAssign assign;

    /**
     * 接收实体类集合
     */
    private List<InstructionReceive> receiveList;


    /**
     * 销号实体类
     */
    private InstructionEnd instructionEnd;


}
