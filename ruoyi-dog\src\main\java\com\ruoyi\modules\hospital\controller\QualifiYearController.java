package com.ruoyi.modules.hospital.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.hospital.entity.QualifiYear;
import com.ruoyi.modules.hospital.service.QualifiService;
import com.ruoyi.modules.hospital.service.QualifiYearService;
import com.ruoyi.modules.user.entity.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 资质年审/年审提醒
 */
@RestController
@RequestMapping("/hospital/year")
public class QualifiYearController {
    @Autowired
    private QualifiYearService service;

    /**
     * 分页
     * @param qualifi
     * @return
     */
    @RequestMapping("getPageList")
    public AjaxResult getPageList(QualifiYear qualifi) {
        return AjaxResult.success(service.getPageList(qualifi));
    }

    @RequestMapping("getList")
    public AjaxResult getList(QualifiYear qualifi) {
        return AjaxResult.success(service.getList(qualifi));
    }

    /**
     * 新增/更新
     * @param qualifi
     * @return
     */
    @PostMapping("saveOrUpdate")
    public AjaxResult saveOrUpdate(@RequestBody QualifiYear qualifi){
        return AjaxResult.success(service.save(qualifi));
    }

    @RequestMapping("delete")
    public AjaxResult delete(QualifiYear qualifi){
        service.delete(qualifi);
        return AjaxResult.success();
    }

    /**
     * 详情
     * @param id
     * @return
     */
    @RequestMapping("getById")
    public AjaxResult getById(String id){
        return  AjaxResult.success(service.getById(id));
    }

    /**
     * 审核
     * @param qualifi
     * @return
     */
    @PostMapping("updateStatus")
    public AjaxResult updateStatus(@RequestBody QualifiYear qualifi){
        service.updateStatus(qualifi);
        return AjaxResult.success();
    }

    @PostMapping("createAccount")
    public AjaxResult createAccount(@RequestBody QualifiYear qualifi){
        service.createAccount(qualifi);
        return AjaxResult.success();
    }

    @RequestMapping("getQualifiByAccount")
    public AjaxResult getQualifiByAccount(QualifiYear qualifi){
        return AjaxResult.success(service.getQualifiByAccount(qualifi));
    }

    @RequestMapping("/getAllList")
    public AjaxResult getAllList(QualifiYear qualifi) {
        return AjaxResult.success(service.getAllList(qualifi));
    }

    @RequestMapping("/getApply")
    public AjaxResult getApply(SysUser sysUser) {
        return AjaxResult.success(service.getApply(sysUser));
    }

    /**
     * 年审提醒通知
     * @param qualifi
     * @return
     */
    @GetMapping("/alarmList")
    public AjaxResult getAlarmList(QualifiYear qualifi) {
        return AjaxResult.success(service.getAlarmList(qualifi));
    }

    /**
     * 短信提醒
     * @return
     */
    @GetMapping("/sendMobile/{id}")
    public AjaxResult sendMobile(@PathVariable String id) {
        service.sendMobile(id);
        return AjaxResult.success();
    }
}
