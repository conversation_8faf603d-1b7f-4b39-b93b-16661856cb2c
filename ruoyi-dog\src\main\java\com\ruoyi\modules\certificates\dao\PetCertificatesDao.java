package com.ruoyi.modules.certificates.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.common.vo.BusinessCountVO;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.certificates.entity.ShowMapData;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface PetCertificatesDao extends BaseDao<PetCertificates> {

    public void upStatus(PetCertificates petCertificates);

    public void updateIsReissue(PetCertificates petCertificates);

    public void updateImmune(PetCertificates petCertificates);

    public PetCertificates getPet(String petId);

    public Integer selectHuKouNumberIsOnly(PetCertificates petCertificates);

    public List<PetCertificates> queryPageList(PetCertificates petCertificates);

    public PetCertificates queryPetCount(PetCertificates petCertificates);

    //获取犬只绑定免疫信息
    public List<PetCertificates> getPetAndRegister(PetCertificates petCertificates);

    // 修改犬只犬牌(修改时间，修改人)
    public void updatePetNum(PetCertificates petCertificates);
    /**
     * @author: tongsiyu
     * @date: 2022/10/11 16:11
     * @Description:根据犬牌编号获取犬只信息
     */
    public HashMap getByPetNum(String brandId);

    public void updateAbout(PetCertificates petCertificates);
    public HashMap getQZDJ();
    public HashMap getZFSR();

    /**
     * 获取结果数据
     * @return
     */
    public List<ShowMapData> getShowMapData();


    public void getpetAge();

    List<Map<String, Object>> findViewData(@Param("formattedDate") String formattedDate);

    HashMap getByBrandNum(String brandNum);

    List<PetCertificates> listByPetNumList(@Param("list") List<String> petNumList);


    List<PetCertificates> listByDate(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    Long countByDate(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<BusinessCountVO> groupByPetDept();

    /**
     * 根据身份证查询其下犬只数量
     * @param petIdCard
     * @param excludeId 需要排除的犬只ID（可选）
     * @return
     */
    Long findPetCountByIdCard(@Param("petIdCard") String petIdCard, @Param("excludeId") String excludeId);

    /**
     * 根据犬只id查询免疫状态
     * @param petId
     * @return
     */
    String findImmuneStatusByPetId(@Param("petId") String petId);

    /**
     * 禁养
     * @param otherVarieties
     * @return
     */
    Integer jy(@Param("otherVarieties")String otherVarieties);
}
