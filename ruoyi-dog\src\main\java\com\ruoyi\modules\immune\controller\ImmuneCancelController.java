package com.ruoyi.modules.immune.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.immune.entity.ImmuneCancel;
import com.ruoyi.modules.immune.entity.ImmuneTransfer;
import com.ruoyi.modules.immune.service.ImmuneCancelService;
import com.ruoyi.modules.immune.service.ImmuneTransferService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @version: 1.0
 **/
@RestController
@RequestMapping("immuneCancel")
public class ImmuneCancelController {
    @Autowired
    private ImmuneCancelService service;

    @RequestMapping("getPageList")
    public AjaxResult getPageList(ImmuneCancel immuneCancel){
        return AjaxResult.success(service.getPageList(immuneCancel));
    }

    @RequestMapping("saveOrUpdate")
    public AjaxResult saveOrUpdate(ImmuneCancel immuneCancel){
        service.saveOrUpdate(immuneCancel);
        return AjaxResult.success();
    }

    @RequestMapping("delete")
    public AjaxResult delete(ImmuneCancel immuneCancel){
        service.delete(immuneCancel);
        return AjaxResult.success();
    }

    @RequestMapping("getById")
    public AjaxResult getById(String id){
        return AjaxResult.success(service.getById(id));
    }


    /**
     * 免疫注销审核
     * @param immuneCancel
     * @return
     */
    @RequestMapping("updateStatus")
    public AjaxResult updateStatus(ImmuneCancel immuneCancel){
        service.updateStatus(immuneCancel);
        return AjaxResult.success();
    }

    @RequestMapping("getByCardId")
    public AjaxResult getByCardId(String id, String petIdCard) {
        return AjaxResult.success(service.getByCardId(id, petIdCard));
    }

    @RequestMapping("getByPet")
    public AjaxResult getByPet(ImmuneCancel immuneCancel){
        return AjaxResult.success(service.getByPet(immuneCancel));
    }

    /**
     * 根据信息 查询犬只注销
     * @param petCertificates
     * @return
     */
    @RequestMapping("queryPageList")
    public AjaxResult queryPageList(PetCertificates petCertificates){
        return AjaxResult.success(service.queryPageList(petCertificates));
    }

}
