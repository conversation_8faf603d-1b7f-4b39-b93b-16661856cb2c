package com.ruoyi.modules.user.service;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.user.dao.SysUploadFileDao;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.util.QRCode;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by Administrator on 2021-3-11.
 */
@Service
public class SysUploadFileService extends BaseService<SysUploadFileDao, SysUploadFile> {
    public String analysisQr(File file) {
        String url = QRCode.decode(file);

        return getUrlparameter(url, "id");
    }

    public static String getUrlparameter(String url, String name) {
        url += "&";
        String pattern = "(\\?|&){1}#{0,1}" + name + "=[a-zA-Z0-9]*(&{1})";
        Pattern r = Pattern.compile(pattern);
        Matcher matcher = r.matcher(url);
        if (matcher.find()) {
            return matcher.group(0).split("=")[1].replace("&", "");
        } else {
            return null;
        }
    }

    /**
     * 根据附件列表字符串批量保存数据
     *
     */
    @Transactional
    public void saveAllList(List<SysUploadFile> list) {
        if (list != null) {
            for (SysUploadFile sysUploadFile : list) {
                sysUploadFile.preInsert();
            }

            dao.saveAllList(list);
        }
    }

    /**
     * 根据附件列表字符串批量保存数据
     *
     * @param jsonFileStr
     */
    @Transactional
    public void saveAllList(String jsonFileStr) {
        List<SysUploadFile> list = JSON.parseArray(jsonFileStr, SysUploadFile.class);
        if (list != null) {
            for (SysUploadFile sysUploadFile : list) {
                sysUploadFile.preInsert();
            }

            dao.saveAllList(list);
        }
    }

    /**
     * 根据附件列表字符串批量保存数据
     *
     * @param jsonFileStr
     */
    @Transactional
    public void saveAllListById(String jsonFileStr, String id) {
        List<SysUploadFile> list = JSON.parseArray(jsonFileStr, SysUploadFile.class);
        if (list != null && list.size() > 0) {
            for (SysUploadFile sysUploadFile : list) {
                sysUploadFile.preInsert();
                sysUploadFile.setInstanceId(id);
            }
            dao.saveAllList(list);
        }
    }

    /**
     * 根据类型和实例删除附件
     *
     * @param instanceId
     * @param modelType
     */
    @Transactional
    public void delByInstanceAndModel(String instanceId, String modelType) {
        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.setInstanceId(instanceId);
        sysUploadFile.setModelType(modelType);
        dao.delByInstanceAndModel(sysUploadFile);
    }


    public List<SysUploadFile> listByInstanceId(List<String> instanceIdList) {
        return dao.listByInstanceId(instanceIdList);
    }

    public void deleteByInstanceIdAndTypeList(String id, List<String> typeList) {
        dao.deleteByInstanceIdAndTypeList(id, typeList);
    }
}
