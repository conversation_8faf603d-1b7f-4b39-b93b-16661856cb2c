package com.ruoyi.modules.brand.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.brand.entity.ApplyMerge;
import com.ruoyi.modules.brand.entity.PetBrandApply;
import com.ruoyi.modules.brand.service.PetBrandApplyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 犬牌领用审核
 */
@RestController
@RequestMapping("brandApply")
public class PetBrandApplyController {

    @Resource
    PetBrandApplyService service;

    /**
     * 分页
     * @param petBrandApply
     * @return
     */
    @RequestMapping("getPageList")
    public AjaxResult getPageList(PetBrandApply petBrandApply) {
        return AjaxResult.success(service.getPageList(petBrandApply));
    }

    /**
     * @author: tongsiyu
     * @date: 2022/11/22 10:26
     * @Description:犬牌制作数据查询（包含合并数据）
     */
    @RequestMapping("getPageListMerge")
    public AjaxResult getPageListMerge(ApplyMerge applyMerge) {
        return AjaxResult.success(service.getPageListMerge(applyMerge));
    }

    /**
     * @author: tongsiyu
     * @date: 2022/11/18 10:06
     * @Description:批量提交制作
     */
    @RequestMapping("batchSubmit")
    public AjaxResult batchSubmit(String ids, String path) {
        service.batchSubmit(ids, path);
        return AjaxResult.success();
    }

    @RequestMapping("getManufacturerPageList")
    public AjaxResult getManufacturerPageList(ApplyMerge petBrandApply) {
        return AjaxResult.success(service.getManufacturerPageList(petBrandApply));
    }

    @RequestMapping("getPetBrand")
    public AjaxResult getPetBrand(String id) {
        return AjaxResult.success(service.getPetBrand(id));
    }

    @RequestMapping("unitStatistics")
    public AjaxResult unitStatistics(PetBrandApply petBrandApply) {
        return AjaxResult.success(service.unitStatistics(petBrandApply));
    }

    /*
     *
     * @title 导出犬牌使用统计
     * <AUTHOR>
     * @date 2022/12/28 18:42
     */
    @RequestMapping("unitStatisticsDown")
    public void unitStatisticsDown(PetBrandApply petBrandApply, HttpServletRequest request, HttpServletResponse response) throws Exception {
        service.unitStatisticsDown(petBrandApply,request,response);
    }

    @RequestMapping("exeportData")
    public void exeportData(PetBrandApply petBrandApply, HttpServletRequest request, HttpServletResponse response) throws Exception {
        service.exeportData(petBrandApply, request, response);
    }

    @RequestMapping("countyStatistics")
    public AjaxResult countyStatistics(PetBrandApply petBrandApply) {
        return AjaxResult.success(service.countyStatistics(petBrandApply));
    }

    @RequestMapping("getList")
    public AjaxResult getList(PetBrandApply petBrandApply) {
        return AjaxResult.success(service.getList(petBrandApply));
    }

    /**
     * @author: tongsiyu
     * @date: 2022/11/22 20:48
     * @Description:获取合并申请数据列表
     */
    @RequestMapping("getMergeList")
    public AjaxResult getMergeList(ApplyMerge merge) {
        return AjaxResult.success(service.getMergeList(merge));
    }

    @RequestMapping("getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    /**
     * @author: tongsiyu
     * @date: 2022/10/09 10:25
     * @Description:获取未分配得到犬牌数量
     */
    @RequestMapping("getNotDistributionNum")
    public AjaxResult getNotDistributionNum(String area) {
        return AjaxResult.success(service.getNotDistributionNum(area));
    }

    /**
     * 新增/编辑
     * @param petBrandApply
     * @return
     */
    @PostMapping("saveOrUpdate")
    public AjaxResult saveOrUpdate(@RequestBody PetBrandApply petBrandApply) {
            service.saveOrUpdate(petBrandApply);
            return AjaxResult.success();
    }

    /**
     * @author: tongsiyu
     * @date: 2022/10/26 14:25
     * @Description:编辑审核状态
     */
    @PostMapping("updateStatus")
    public AjaxResult updateStatus(@RequestBody PetBrandApply petBrandApply) {
        service.updateStatus(petBrandApply);
        return AjaxResult.success();
    }
    @PostMapping("updateByEntity")
    public AjaxResult updateByEntity(@RequestBody PetBrandApply petBrandApply) {
        return AjaxResult.success(service.updateByEntity(petBrandApply));
    }

    @PostMapping("updateStatusMerge")
    public AjaxResult updateStatusMerge(@RequestBody PetBrandApply petBrandApply) {
        service.updateStatusMerge(petBrandApply);
        return AjaxResult.success();
    }

    @PostMapping("delete")
    public AjaxResult delete(@RequestBody PetBrandApply petBrandApply) {
        service.delete(petBrandApply);
        return AjaxResult.success();
    }
}
