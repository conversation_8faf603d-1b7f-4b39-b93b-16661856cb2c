package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.UndercoverProblem;
import com.ruoyi.instruction.service.IUndercoverProblemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 暗访督察问题Controller
 * 
 * <AUTHOR>
 * @date 2023-05-23
 */
@RestController
@RequestMapping("/instruction/undercoverProblem")
public class UndercoverProblemController extends BaseController
{
    @Autowired
    private IUndercoverProblemService undercoverProblemService;

    /**
     * 查询暗访督察问题列表
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverProblem:list')")
    @GetMapping("/list")
    public TableDataInfo list(UndercoverProblem undercoverProblem)
    {
        startPage();
        List<UndercoverProblem> list = undercoverProblemService.selectUndercoverProblemList(undercoverProblem);
        return getDataTable(list);
    }

    /**
     * 导出暗访督察问题列表
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverProblem:export')")
    @Log(title = "暗访督察问题", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UndercoverProblem undercoverProblem)
    {
        List<UndercoverProblem> list = undercoverProblemService.selectUndercoverProblemList(undercoverProblem);
        ExcelUtil<UndercoverProblem> util = new ExcelUtil<UndercoverProblem>(UndercoverProblem.class);
        util.exportExcel(response, list, "暗访督察问题数据");
    }

    /**
     * 获取暗访督察问题详细信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverProblem:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(undercoverProblemService.selectUndercoverProblemById(id));
    }

    /**
     * 新增暗访督察问题
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverProblem:add')")
    @Log(title = "暗访督察问题", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UndercoverProblem undercoverProblem)
    {
        return toAjax(undercoverProblemService.insertUndercoverProblem(undercoverProblem));
    }

    /**
     * 修改暗访督察问题
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverProblem:edit')")
    @Log(title = "暗访督察问题", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UndercoverProblem undercoverProblem)
    {
        return toAjax(undercoverProblemService.updateUndercoverProblem(undercoverProblem));
    }

    /**
     * 删除暗访督察问题
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverProblem:remove')")
    @Log(title = "暗访督察问题", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(undercoverProblemService.deleteUndercoverProblemByIds(ids));
    }

    /**
     * 根据暗访督察id获取问题列表
     * @param id
     * @return
     */
    @GetMapping(value = "selectInspection/{id}")
    public AjaxResult selectInspection(@PathVariable("id") Long id)
    {
        UndercoverProblem undercoverProblem=new UndercoverProblem();
        undercoverProblem.setUndercoverInspectionId(id);
        return success(undercoverProblemService.selectUndercoverProblemList(undercoverProblem));
    }
}
