package com.ruoyi.common.utils;

import java.awt.geom.Point2D;
import java.util.ArrayList;
import java.util.List;

/**
 *  <span style="font-family: Arial; font-size: 14px; line-height: 26px;">点和线的一些公用方法</span><br/>
 *
 * <AUTHOR>
 * 2016年8月6日 下午3:48:38
 */
public class PointUtil {

    public static void main(String[] args) {
        // 被检测的经纬度点
        //double X= 119.635915;
        //double Y= 29.108943;
        //范围内
        //119.637836,29.109539,119.635733,29.109182
        //范围外
        //119.635867,29.108526,119.635915,29.108943
        // 商业区域（百度多边形区域经纬度集合）
        //String partitionLocation = "119.63426,29.109886;119.633857,29.108536;119.638391,29.109453;119.637207,29.110487;119.635452,29.110392;119.634764,29.110267";
        //String partitionLocation = "119.63256,29.11648;119.63179,29.11599;119.631,29.11553;119.62992,29.11481;119.62925,29.11425;119.62877,29.11384;119.62829,29.11309;119.62694,29.1116;119.62619,29.11014;119.62646,29.10986;119.62672,29.10982;119.62798,29.11003;119.62762,29.11165;119.62866,29.11181;119.62922,29.11141;119.62968,29.11143;119.63159,29.1119;119.63344,29.11231;119.63346,29.11239;119.63354,29.11255;119.63258,29.11324;119.63243,29.11354;119.63183,29.11469;119.63346,29.11513;119.6332,29.11576";
        String partitionLocation = "119.6313,29.11317;119.63152,29.11238;119.63344,29.1128;119.6324,29.1134;119.63189,29.1133";

        double X= 119.631645;
        double Y= 29.113041;

        double X1 = 119.630988;
        double Y1 = 29.11181;
        System.out.println(isInPolygon(X,Y,partitionLocation));

        System.out.println(isInPolygon(X1,Y1,partitionLocation));
    }

    /**
     * 判断当前位置是否在多边形区域内
     * @param X 当前点
     * @param Y 当前点
     * @param partitionLocation 区域顶点
     * @return
     */
    public static boolean isInPolygon(double X,double Y,String partitionLocation){

        double p_x = X;
        double p_y = Y;
        Point2D.Double point = new Point2D.Double(p_x, p_y);

        List<Point2D.Double> pointList= new ArrayList<Point2D.Double>();
        String[] strList = partitionLocation.split(";");

        for (String str : strList){
            String[] points = str.split(",");
            double polygonPoint_x=Double.parseDouble(points[0]);
            double polygonPoint_y=Double.parseDouble(points[1]);
            Point2D.Double polygonPoint = new Point2D.Double(polygonPoint_x,polygonPoint_y);
            pointList.add(polygonPoint);
        }
        return IsPtInPoly(point,pointList);
    }
    /**
     * 判断点是否在多边形内，如果点位于多边形的顶点或边上，也算做点在多边形内，直接返回true
     * @param point 检测点
     * @param pts   多边形的顶点
     * @return      点在多边形内返回true,否则返回false
     */
    public static boolean IsPtInPoly(Point2D.Double point, List<Point2D.Double> pts){

        int N = pts.size();
        boolean boundOrVertex = true; //如果点位于多边形的顶点或边上，也算做点在多边形内，直接返回true
        int intersectCount = 0;//cross points count of x
        double precision = 2e-10; //浮点类型计算时候与0比较时候的容差
        Point2D.Double p1, p2;//neighbour bound vertices
        Point2D.Double p = point; //当前点

        p1 = pts.get(0);//left vertex
        for(int i = 1; i <= N; ++i){//check all rays
            if(p.equals(p1)){
                return boundOrVertex;//p is an vertex
            }

            p2 = pts.get(i % N);
            if(p.x < Math.min(p1.x, p2.x) || p.x > Math.max(p1.x, p2.x)){
                p1 = p2;
                continue;
            }

            if(p.x > Math.min(p1.x, p2.x) && p.x < Math.max(p1.x, p2.x)){
                if(p.y <= Math.max(p1.y, p2.y)){
                    if(p1.x == p2.x && p.y >= Math.min(p1.y, p2.y)){
                        return boundOrVertex;
                    }

                    if(p1.y == p2.y){
                        if(p1.y == p.y){
                            return boundOrVertex;
                        }else{//before ray
                            ++intersectCount;
                        }
                    }else{
                        double xinters = (p.x - p1.x) * (p2.y - p1.y) / (p2.x - p1.x) + p1.y;
                        if(Math.abs(p.y - xinters) < precision){
                            return boundOrVertex;
                        }

                        if(p.y < xinters){
                            ++intersectCount;
                        }
                    }
                }
            }else{
                if(p.x == p2.x && p.y <= p2.y){
                    Point2D.Double p3 = pts.get((i+1) % N);
                    if(p.x >= Math.min(p1.x, p3.x) && p.x <= Math.max(p1.x, p3.x)){
                        ++intersectCount;
                    }else{
                        intersectCount += 2;
                    }
                }
            }
            p1 = p2;
        }

        if(intersectCount % 2 == 0){//偶数在多边形外
            return false;
        } else { //奇数在多边形内
            return true;
        }
    }

}
