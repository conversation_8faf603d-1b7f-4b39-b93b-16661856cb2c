package com.ruoyi.instruction.controller;


import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.instruction.domain.InstructionExperience;
import com.ruoyi.instruction.service.IInstructionExperienceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 金安智治驾驶舱金安大数据基层安全
 * <AUTHOR>
 * @version 1.0
 * @data 2023.05.19
 *
 */
@RestController
@RequestMapping("/bigScreen/jazz/Grassroots")
public class BigScreenJazzGrassrootsSafeController extends BaseController{
    @Autowired
    private IInstructionExperienceService instructionExperienceService;
    @GetMapping("/Experience")
    public TableDataInfo getInstructionExperience(){
        List<InstructionExperience> list=instructionExperienceService.getInstructionExperience();
        return getDataTable(list);
    }
}
