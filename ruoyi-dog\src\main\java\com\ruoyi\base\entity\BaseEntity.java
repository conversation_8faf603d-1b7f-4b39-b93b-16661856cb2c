package com.ruoyi.base.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.util.IdGen;
import com.ruoyi.util.ThreadLocalUtil;
import com.ruoyi.util.UserCache;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * Created by Administrator on 2020/1/2/002.
 */
public class BaseEntity implements Serializable {

    /**
     * 数据ID
     */
    private String id ;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8" ,pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate ;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8" ,pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate ;

    /**
     * 创建人
     */
    private String createBy ;

    /**
     * 更新人
     */
    private String updateBy ;

    /**
     * 删除标记 1正常  2删除
     */
    private Integer delFlag ;

    /**
     * 分页页数  查询使用
     */
    @JsonIgnore
    private Integer pageNum = 1;   //默认值第一页
    /**
     * 分页数量  查询使用
     */
    @JsonIgnore
    private Integer pageSize = 10;  //默认10条数据分页

    @JsonIgnore
    private String managerOrgName;  // 管理乡镇

    @JsonFormat(timezone = "GMT+8" ,pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;                       //审核时间
    @JsonFormat(timezone = "GMT+8" ,pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private String keyword;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    // public int getPageNum() {
    //     return pageNum;
    // }
    //
    // public void setPageNum(int pageNum) {
    //     this.pageNum = pageNum;
    // }
    //
    // public int getPageSize() {
    //     return pageSize;
    // }
    //
    // public void setPageSize(int pageSize) {
    //     this.pageSize = pageSize;
    // }


    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(final Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(final Integer pageSize) {
        this.pageSize = pageSize;
    }

    public void preInsert(){
        //添加之前执行
        this.setId(IdGen.uuid());
        this.setDelFlag(1);
        // UserCache user = ThreadLocalUtil.getCurrentUser();

        LoginUser loginUser = SecurityUtils.getLoginUserWithOutException();
        if (Objects.nonNull(loginUser)) {
            SysUser user = loginUser.getUser();
            if (user != null) {
                this.setCreateBy(user.getUserId() + "");
            }
        }
        this.setCreateDate(new Date());
        this.setUpdateDate(new Date());
    }
    public void preUpdate(){
        //更新之前执行
        // UserCache user = ThreadLocalUtil.getCurrentUser();
        LoginUser loginUser = SecurityUtils.getLoginUserWithOutException();
        if (Objects.nonNull(loginUser)) {
            SysUser user = loginUser.getUser();
            if (user != null) {
                this.setUpdateBy(user.getUserId() + "");
            }
        }
        this.setUpdateDate(new Date());
    }

    public String getManagerOrgName() {
        return managerOrgName;
    }

    public void setManagerOrgName(String managerOrgName) {
        this.managerOrgName = managerOrgName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
}
