package com.ruoyi.common.enums;

import com.ruoyi.common.utils.StringUtils;

/**
 * 操作状态
 * 
 * <AUTHOR>
 *
 */
public enum AreaDept
{
    LANXI("兰溪市","兰溪市行政执法局"),
    YIWU("义乌市","义乌市行政执法局"),
    PUJIANG("浦江县","浦江县行政执法局"),
    WUCHENG("婺城区","婺城区行政执法局"),
    JIDONG("金东区","金东区行政执法局"),
    WUYI("武义县","武义县行政执法局"),
    YONGKANG("永康市","永康市行政执法局"),
    DONGYANG("东阳市","东阳市行政执法局"),
    PANAN("磐安县","磐安县行政执法局"),
    KAIFAQU("开发区","开发区行政执法局"),
    LANXIZHZX("兰溪市","兰溪市行政执法指挥中心"),
    YIWULANXIZHZX("义乌市","义乌市行政执法指挥中心"),
    PUJIANGLANXIZHZX("浦江县","浦江县行政执法指挥中心"),
    WUCHENGLANXIZHZX("婺城区","婺城区行政执法指挥中心"),
    JIDONGLANXIZHZX("金东区","金东区行政执法指挥中心"),
    WUYILANXIZHZX("武义县","武义县行政执法指挥中心"),
    YONGKANGLANXIZHZX("永康市","永康市行政执法指挥中心"),
    DONGYANGLANXIZHZX("东阳市","东阳市行政执法指挥中心"),
    PANANLANXIZHZX("磐安县","磐安县行政执法指挥中心"),
    KAIFAQUXIZHZX("开发区","开发区行政执法指挥中心"),
    SHIBENJI("金华市","金华市行政执法指挥中心"),
    SHAUNGLONG("双龙风景旅游区","双龙风景旅游区");

    private final String area;
    private final String dept;

    AreaDept(String area, String dept)
    {
        this.area = area;
        this.dept = dept;
    }

    public String getArea() {
        return area;
    }

    public String getDept() {
        return dept;
    }

    public static AreaDept getByDept(String  dept) {
        if(StringUtils.isEmpty(dept)){
            return null;
        }
        for(AreaDept v : values()) {
            if(v.dept.equals(dept)) {
                return v;
            }
        }
        return null;
    }
}
