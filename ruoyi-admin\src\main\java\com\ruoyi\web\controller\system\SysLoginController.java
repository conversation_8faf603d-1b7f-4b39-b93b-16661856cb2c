package com.ruoyi.web.controller.system;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.component.WxHelper;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.entity.*;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SlsUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.rsa.RSAv2Util;
import com.ruoyi.modules.hospital.dao.QualifiDao;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.system.service.ISysUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.service.ISysMenuService;


/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    public QualifiDao qualifiDao;

    @Autowired
    private ISysUserService iSysUserService;
    @Autowired
    private WxHelper wxHelper;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        SysUser sysUser = null;
        if (StringUtils.isNotEmpty(loginBody.getZzdCode())) {

            // 浙政钉扫码登录流程
            ajax = loginService.loginZzdQRCode(loginBody.getUsername(), loginBody.getZzdCode());
            if (null != ajax.get("username")) {
                sysUser = iSysUserService.selectUserByUserName(ajax.get("username").toString());
            }
            LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser.getDeptId(), sysUser, permissionService.getMenuPermission(sysUser));
            String token = tokenService.createToken(loginUser);
            ajax.put(Constants.TOKEN, token);
        }else if (StringUtils.isNotEmpty(loginBody.getAuthCode()))
        {
            // 浙政钉应用免登登录流程
            ajax = loginService.loginZzd(loginBody.getUsername(), loginBody.getAuthCode());
            if (null != ajax.get("username")) {
                sysUser = iSysUserService.selectUserByUserName(ajax.get("username").toString());
            }
            LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser.getDeptId(), sysUser, permissionService.getMenuPermission(sysUser));
            String token = tokenService.createToken(loginUser);
            ajax.put(Constants.TOKEN, token);
        } else if (StringUtils.isNotEmpty(loginBody.getWxCode())) {
            //微信静默授权登录
            Map<String, String> accessTokenByCode = wxHelper.getAccessTokenByCode(loginBody.getWxCode());
            String openid = accessTokenByCode.get("openid");
            if (org.springframework.util.CollectionUtils.isEmpty(accessTokenByCode)|| openid== null){
                return AjaxResult.error("微信登录失败");
            }
            String token =loginService.wxLogin(openid);
            ajax.put(Constants.TOKEN, token);
        } else {
            // 生成令牌
            String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                    loginBody.getUuid());
            ajax.put(Constants.TOKEN, token);
        }
        return ajax;
    }

    /**
     * 免登
     *
     * @return 结果
     */
    @GetMapping("/loginV2")
    public AjaxResult loginV2()
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.loginV2();
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }
    /**
     * 免登三色预警账号获取token
     *
     * @return 结果
     */
    @GetMapping("/loginV3")
    public AjaxResult loginV2(String value,String sion )
    {
        if (StringUtils.isEmpty(sion)){
            return AjaxResult.error("系统异常");
        }
        String key = "fjfhdeufjfabcdef"; // 16位字符 = 16字节 => AES-128
        // 创建 AES 对象
        AES aes = SecureUtil.aes(key.getBytes());
        // 解密
        String decryptStr = aes.decryptStr(sion);
        SysUser sysUser = JSONObject.parseObject(decryptStr, SysUser.class);
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.loginV3(sysUser.getUserName(), sysUser.getPassword());
        ajax.put(Constants.TOKEN, token);
        return ajax;

//        if (StringUtils.isNotEmpty(key)&& StringUtils.isNotEmpty(value)&&"sdgshgd".equals(key)&&"dgfhdghsghdgshd".equals(value)){
//            AjaxResult ajax = AjaxResult.success();
//            // 生成令牌
//            String token = loginService.loginV2();
//            ajax.put(Constants.TOKEN, token);
//            return ajax;
//        }else {
//            return AjaxResult.error("系统异常");
//        }

    }
    /**
     * 省厅免登
     *
     * @return 结果
     */
    @GetMapping("/loginV4")
    public AjaxResult loginV4()
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.loginV2();
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 账号密码登录，返回token加密
     *
     * @return 结果
     */
    @GetMapping("/loginWithSecret")
    public AjaxResult loginV5(String username, String password)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.loginV5(username, password);

        String encode = RSAv2Util.encode(token);

        ajax.put(Constants.TOKEN, encode);
        return ajax;
    }

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/jscLogin")
    public AjaxResult jscLogin(@RequestBody LoginBody loginBody) {
        AjaxResult ajax = AjaxResult.success();

            String password = Base64.decodeStr(loginBody.getPassword());
            if (loginBody.getUsername().contains("@@@@$$$$XXXXXXXXX_")){
                //用户免登会生成临时密码，2分钟过期
                ajax = loginService.loginMd(loginBody.getUsername(), password, loginBody.getCode(),
                        loginBody.getUuid());
            }else {
                // 生成令牌
                ajax = loginService.loginjsc(loginBody.getUsername(), password, loginBody.getCode(),
                        loginBody.getUuid());
            }




        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        //添加sls日志
        SlsUtils.loginSlsLog(1,"");
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        if (!loginUser.getPermissions().equals(permissions)) {
            loginUser.setPermissions(permissions);
            tokenService.refreshToken(loginUser);
        }
        AjaxResult ajax = AjaxResult.success();
        if (user.getDogUserId() != null) {
            //关联返回资质信息
            Qualifi qualifi = new Qualifi();
            qualifi.setAccount(user.getDogUserId());
            Qualifi qualifiByAccount = qualifiDao.getQualifiByAccount(qualifi);
            if (qualifiByAccount != null){
                ajax.put("dogUserQualifi",qualifiByAccount);
                ajax.put("dogQualifi",qualifiByAccount.getQualifi());
                user.setQualifiId(qualifiByAccount.getId());
            }
        }
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);

        //菜单集合
        ajax.put("menus", menuService.buildMenuTree(menuService.selectMenuList(user.getUserId())));
        //智慧城管移动端获取用户信息
        if (roles.contains(InstructionRolesConstants.ZHCG_JDY)) {
            ajax.put("roleType", 1);
            ajax.put("menu", InstructionRolesConstants.ZHCG_JDY_MENU);
        } else if (roles.contains(InstructionRolesConstants.ZYBM)) {
            ajax.put("roleType", 2);
            ajax.put("menu", InstructionRolesConstants.ZHCG_ZYBM_MENU);
        }
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfoWithSecret")
    public AjaxResult getInfoWithSecret()
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        if (!loginUser.getPermissions().equals(permissions)) {
            loginUser.setPermissions(permissions);
            tokenService.refreshToken(loginUser);
        }
        AjaxResult ajax = AjaxResult.success();
        if (user.getDogUserId() != null) {
            //关联返回资质信息
            Qualifi qualifi = new Qualifi();
            qualifi.setAccount(user.getDogUserId());
            Qualifi qualifiByAccount = qualifiDao.getQualifiByAccount(qualifi);
            if (qualifiByAccount != null){
                ajax.put("dogUserQualifi",qualifiByAccount);
                ajax.put("dogQualifi",qualifiByAccount.getQualifi());
                user.setQualifiId(qualifiByAccount.getId());
            }
        }
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);

        //菜单集合
        List<SysMenu> sysMenus = menuService.buildMenuTree(menuService.selectMenuList(user.getUserId()));
        if (CollectionUtils.isNotEmpty(sysMenus)) {
            sysMenus = sysMenus.stream()
                    .filter(sysMenu -> sysMenu.getMenuId() == 2352)
                    .collect(Collectors.toList());
        }

        ajax.put("menus", sysMenus);
        //智慧城管移动端获取用户信息
        if (roles.contains(InstructionRolesConstants.ZHCG_JDY)) {
            ajax.put("roleType", 1);
            ajax.put("menu", InstructionRolesConstants.ZHCG_JDY_MENU);
        } else if (roles.contains(InstructionRolesConstants.ZYBM)) {
            ajax.put("roleType", 2);
            ajax.put("menu", InstructionRolesConstants.ZHCG_ZYBM_MENU);
        }

        return AjaxResult.success("操作成功", RSAv2Util.encode(JSON.toJSONString(ajax)));
    }

    /**
     * 校验token
     *
     */
    @GetMapping("checkToken")
    public AjaxResult checkToken()
    {
        return AjaxResult.success();
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    /**
     * 获取菜单数组(站前app)
     *
     * @return 获取菜单数组
     */
    @GetMapping("getAPPList")
    public AjaxResult getAPPList() {
        Long userId = SecurityUtils.getUserId();
        return AjaxResult.success(menuService.getAPPList(userId));
    }

    /**
     * 获取菜单数组(监督指挥app)
     *
     * @return 获取菜单数组
     */
    @GetMapping("getJdzhAPPList")
    public AjaxResult getJdzhAPPList(String name) {
        Long userId = SecurityUtils.getUserId();
        return AjaxResult.success(menuService.getJdzhAPPList(userId,name));
    }
}
