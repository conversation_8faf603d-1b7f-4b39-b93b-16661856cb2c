package com.ruoyi.modules.hospital.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class HospitalApplyVO {

    private String id;

    /**
     * 医院名称
     */
    private String name;

    /**
     * 申请类型 1-资质审核 2-年审 3-犬牌领用
     */
    private Integer applyType;

    /**
     * 申请人
     */
    private String applyUser;

    /**
     * 联系方式
     */
    private String mobile;

    /**
     * 申请状态
     */
    private String status;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

}
