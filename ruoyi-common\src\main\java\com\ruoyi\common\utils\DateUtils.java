package com.ruoyi.common.utils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.vo.DateDTO;
import com.ruoyi.common.vo.TimeRange;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.FastDateFormat;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils
{
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYYMMDD = "yyyyMMdd";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate()
    {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate()
    {
        return dateTimeNow(YYYY_MM_DD);
    }

    /**
     * 获取当天前后几天
     * @param i
     * @return
     */
    public  static List<Date> getDate(int i){
        List<Date> list=new ArrayList<>();
        LocalDate today= LocalDate.now();
        for (int i1=0;i1<i;i1++){
            LocalDate localDate = today.plusDays(i1);
            Date date1 = localDateByDate(localDate);
            list.add(date1);
        }
        return list;
    }
    public  static List<Date> getDate(Date date,int i){
        List<Date> list=new ArrayList<>();
        LocalDate today = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//        LocalDate today= LocalDate.now();
        for (int i1=0;i1<i;i1++){
            LocalDate localDate = today.plusDays(i1);
            Date date1 = localDateByDate(localDate);
            list.add(date1);
        }
        return list;
    }

    public static Date localDateByDate(LocalDate localDate){
        ZoneId zoneId = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zoneId).toInstant();
        return  Date.from(instant);
    }

    public static final String getTime()
    {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow()
    {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format)
    {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date)
    {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date)
    {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts)
    {
        try
        {
            return new SimpleDateFormat(format).parse(ts);
        }
        catch (ParseException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str)
    {
        if (str == null)
        {
            return null;
        }
        try
        {
            return parseDate(str.toString(), parsePatterns);
        }
        catch (ParseException e)
        {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate()
    {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算相差天数
     */
    public static int differentDaysByMillisecond(Date date1, Date date2)
    {
        return Math.abs((int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24)));
    }

    /**
     * 计算时间差
     *
     * @param endDate 最后时间
     * @param startTime 开始时间
     * @return 时间差（天/小时/分钟）
     */
    public static String timeDistance(Date endDate, Date startTime)
    {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - startTime.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor)
    {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor)
    {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    public static String format(Date date,String format){
        return DateFormatUtils.format(date, format);
    }

    static String[] weeks = new String[]{"周日","周一","周二","周三","周四","周五","周六"};
    public static   String getWeek(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int index=calendar.get(Calendar.DAY_OF_WEEK)-1;
        return  weeks[index];
    }

    /**
     * 获取当天某点的时间
     * @param i 几点
     * @return
     */
    public  static  Date getNow(Integer i){
        LocalDate today = LocalDate.now();

        // 创建代表当天晚上8点的时间对象
        LocalTime timeTonight8PM = LocalTime.of(20, 0); // 20:00

        // 将当前日期和晚上8点合并，构造一个ZonedDateTime对象
        ZonedDateTime zonedDateTimeTonight8PM = ZonedDateTime.of(today, timeTonight8PM, ZoneId.systemDefault());
        Date date = Date.from(zonedDateTimeTonight8PM.toInstant());
        return  date;
    }

    /**
     * 获取前后几天日期
     * @param pattern
     * @param days
     * @return
     */
    public static String getPastDayDate(String pattern, int days) {
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, days);
        Date d = c.getTime();
        return format.format(d);
    }

    /**
     * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String formatDate(long dateTime, String pattern) {
        return formatDate(new Date(dateTime), pattern);
    }

    /**
     * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String formatDate(Date date, String pattern) {
        String formatDate = null;
        if (date != null){
//			if (StringUtils.isNotBlank(pattern)) {
//				formatDate = DateFormatUtils.format(date, pattern);
//			} else {
//				formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
//			}
            if (StringUtils.isBlank(pattern)) {
                pattern = "yyyy-MM-dd";
            }
            formatDate = FastDateFormat.getInstance(pattern).format(date);
        }
        return formatDate;
    }

    /**
     * 获取今年已过的月份,1返回2023-03 2返回月份
     */
    public static List<String> getOneMonth(Integer type) {
        List<String> monthList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        //1号就从上个月开始算
        int num = 1;
        if (isFirstDayOfMonth(calendar)){
            num = 0;
        }
        calendar.set(Calendar.MONTH,calendar.get(Calendar.MONTH)+num);
        int i1 = calendar.get(Calendar.YEAR);
        for (int i = 0; i < 12; i++) {
            calendar.add(Calendar.MONTH, -1);//1个月前
            if (calendar.get(Calendar.YEAR)==i1){
                if (type==1){
                    String month = calendar.get(Calendar.YEAR)+"-"+fillZero(calendar.get(Calendar.MONTH)+1);
                    monthList.add(month);
                }else if (type==2){
                    String s = fillZero(calendar.get(Calendar.MONTH) + 1);
                    monthList.add(s);
                }else if (type==3){
                    int i2 = calendar.get(Calendar.MONTH) + 1;
                    monthList.add(i2+"");
                }
            }
        }
        Collections.reverse(monthList);
        return monthList;
    }

    /**
     * 格式化月份
     */
    public static String fillZero(int i){
        String month = "";
        if(i<10){
            month = "0" + i;
        }else{
            month = String.valueOf(i);
        }
        return month;
    }

    /**
     * 判断今天是否是1号
     * @param calendar  日历对象
     * @return          是否第一天
     */
    public static boolean isFirstDayOfMonth(Calendar calendar){
        calendar.setTime(new Date());
        calendar.set(Calendar.DATE,calendar.get(Calendar.DATE)+1);
        if(calendar.get(Calendar.DAY_OF_MONTH) == 2){
            return true;
        }else{
            return false;
        }
    }


    /**
     * 计算两个时间差
     */
    public static String getDatePoorOne(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        String datePoor = "";
        if (day > 0) {
            datePoor = day + "天";
        }
        if (hour > 0) {
            datePoor += hour + "小时";
        }
        if (min > 0) {
            datePoor += min + "分钟";
        }

        return datePoor;
    }

    public static Long getSecBetweenTwoDate(Date startDate, Date endDate) {
        return (endDate.getTime() - startDate.getTime()) / (1000);
    }

    public static Date timeStamp2Date(String seconds, String formatStr) {
        if(seconds == null || seconds.isEmpty() || seconds.equals("null")){
            return null;
        }
        if(formatStr == null || formatStr.isEmpty()){
            formatStr = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat df = new SimpleDateFormat(formatStr);
        df.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        return dateTime(formatStr, df.format(Long.valueOf(seconds)));
    }

    /**
     * 获取当前月的最小日期
     * @return
     */
    public static Date getMinDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, calendar.getActualMinimum(Calendar.DATE));
        return calendar.getTime();
    }

    /**
     * 获取当前月的最大日期
     * @return
     */
    public static Date getMaxDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
        return calendar.getTime();
    }

    /**
     * @description: 获取当日去除时分秒的Date, 当日 00:00:00
     * @param
     * @return java.util.Date
     * @author: zjrcyky
     * @date: 2021/8/19 1:06 下午
     */
    public static Date currentDateWithoutHMS()
    {
        return currentDateWithoutHMS(0);
    }

    public static Date currentDateWithoutHMS(int day)
    {
        Calendar cal = DateUtils.toCalendar(DateUtils.getNowDate());
        cal.set(Calendar.HOUR_OF_DAY,0);
        cal.set(Calendar.MINUTE,0);
        cal.set(Calendar.SECOND,0);
        cal.add(Calendar.DAY_OF_MONTH, day);
        return  cal.getTime();
    }

    public static String getCodeDate() {
        return dateTimeNow(YYYYMMDD);
    }

    /**
     * 在制定的时间上加或减去几分钟
     *
     * @param date
     * @param minute
     * @return
     */
    public static Date minute(Date date, int minute) {
        java.util.Calendar Cal = java.util.Calendar.getInstance();
        Cal.setTime(date);
        Cal.add(java.util.Calendar.MINUTE, minute);
        return Cal.getTime();
    }

    /**
     * date转成LocalDateTime
     * @param seconds 精确到秒的字符串
     * @param formatStr
     * @return
     */
    public static LocalDateTime dateToLocalDate(Date date) {
        if(null != date){
            Instant instant = date.toInstant();
            ZoneId zoneId = ZoneId.systemDefault();
            return instant.atZone(zoneId).toLocalDateTime();
        }
        return null;
    }

    /**
     * 获取指定日期的开始时间
     *
     * @param date 指定日期
     * @return 开始时间
     */
    public static Date getStartOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定日期的结束时间
     *
     * @param date 指定日期
     * @return 结束时间
     */
    public static Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    /**
     * 根据时间范围获取开始和结束时间
     *
     * @param date      当前日期
     * @param timeRange 时间范围
     * @return 时间范围内的开始和结束时间
     */
    public static DateDTO getTimeRange(Date date, String timeRange) {
        DateDTO dateDTO = new DateDTO();

        Date startDate = null;
        Date endDate = null;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        switch (timeRange) {
            case "日":
                startDate = getStartOfDay(date);
                endDate = getEndOfDay(date);
                break;
            case "周":
                calendar.setFirstDayOfWeek(Calendar.MONDAY); // 设置每周的第一天为周一
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY); // 设置为本周一
                startDate = getStartOfDay(calendar.getTime());
                calendar.add(Calendar.WEEK_OF_YEAR, 1); // 加一周
                calendar.add(Calendar.DAY_OF_MONTH, -1); // 减一天
                endDate = getEndOfDay(calendar.getTime());
                break;
            case "月":
                calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为本月第一天
                startDate = getStartOfDay(calendar.getTime());
                calendar.add(Calendar.MONTH, 1); // 加一个月
                calendar.add(Calendar.DAY_OF_MONTH, -1); // 减一天
                endDate = getEndOfDay(calendar.getTime());
                break;
            case "季度":
                int month = calendar.get(Calendar.MONTH);
                int quarterStartMonth = (month / 3) * 3; // 计算季度的第一个月
                calendar.set(Calendar.MONTH, quarterStartMonth);
                calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为季度第一天
                startDate = getStartOfDay(calendar.getTime());
                calendar.add(Calendar.MONTH, 3); // 加三个月
                endDate = getEndOfDay(calendar.getTime());
                break;
            case "半年":
                int halfYearStartMonth = calendar.get(Calendar.MONTH) < 6 ? 0 : 6; // 判断上半年还是下半年
                calendar.set(Calendar.MONTH, halfYearStartMonth);
                calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为半年第一天
                startDate = getStartOfDay(calendar.getTime());
                calendar.add(Calendar.MONTH, 6); // 加六个月
                endDate = getEndOfDay(calendar.getTime());
                break;
            case "年":
                calendar.set(Calendar.DAY_OF_YEAR, 1); // 设置为当年第一天
                startDate = getStartOfDay(calendar.getTime());
                calendar.add(Calendar.YEAR, 1); // 加一年
                endDate = getEndOfDay(calendar.getTime());
                break;
            case "近7天":
                endDate = getEndOfDay(calendar.getTime());
                calendar.add(Calendar.DAY_OF_MONTH, -6); // 设置为本月第一天
                startDate = getStartOfDay(calendar.getTime());
                break;
            case "近3天":
                endDate = getEndOfDay(calendar.getTime());
                calendar.add(Calendar.DAY_OF_MONTH, -2); // 设置为本月第一天
                startDate = getStartOfDay(calendar.getTime());
                break;
            default:
                throw new IllegalArgumentException("Invalid time range: " + timeRange);
        }

        dateDTO.setStartTime(startDate);
        dateDTO.setEndTime(endDate);

        return dateDTO;
    }

    /**
     * 获取指定日期的小时字符串
     *
     * @param date 指定日期
     * @return 日期字符串
     */
    public static String formatHour(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH");
        return sdf.format(date);
    }

    /**
     * 获取指定日期的日期字符串
     *
     * @param date 指定日期
     * @return 日期字符串
     */
    public static String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    /**
     * 获取指定日期的日期字符串
     *
     * @param date 指定日期
     * @return 日期字符串
     */
    public static String formatDateSecond(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    /**
     * 获取指定日期的年月字符串
     *
     * @param date 指定日期
     * @return 年月字符串
     */
    public static String formatYearMonth(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        return sdf.format(date);
    }

    /**
     * 获取指定日期的年月字符串
     *
     * @param date 指定日期
     * @return 年月字符串
     */
    public static String formatMonth(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("M" + "月");
        return sdf.format(date);
    }

    /**
     * 获取指定日期的年月字符串
     *
     * @param date 指定日期
     * @return 年月字符串
     */
    public static String formatYearMonthV3(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        return sdf.format(date);
    }

    /**
     * 获取指定日期的年月字符串
     *
     * @param date 指定日期
     * @return 年月字符串
     */
    public static String formatYearMonthV2(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("M月");
        return sdf.format(date);
    }

    /**
     * 根据时间范围获取日期字符串列表
     *
     * @param timeRange 时间范围
     * @return 日期字符串列表
     */
    public static List<String> getDateRange(String timeRange) {
        Calendar calendar = Calendar.getInstance();
        Date today = calendar.getTime();

        List<String> dates = new ArrayList<>();

        switch (timeRange) {
            case "日":
                Date startOfDay = getStartOfDay(today);
                for (int hour = 0; hour <= 23; hour++) {
                    calendar.setTime(startOfDay);
                    calendar.set(Calendar.HOUR_OF_DAY, hour);
                    dates.add(formatHour(calendar.getTime()));
                }
                break;
            case "周":
                calendar.setFirstDayOfWeek(Calendar.MONDAY); // 设置每周的第一天为周一
                int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
                int daysToMonday = dayOfWeek == Calendar.SUNDAY ? -6 : Calendar.MONDAY - dayOfWeek;
                calendar.add(Calendar.DAY_OF_MONTH, daysToMonday); // 回溯到本周一
                Date startOfWeek = getStartOfDay(calendar.getTime());
                Date endOfWeek = getEndOfDay(today);
                while (startOfWeek.before(endOfWeek)) {
                    dates.add(formatDate(startOfWeek));
                    calendar.setTime(startOfWeek);
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                    startOfWeek = calendar.getTime();
                }
                break;
            case "月":
                calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为本月第一天
                Date startOfMonth = getStartOfDay(calendar.getTime());
                Date endOfMonth = getEndOfDay(today);
                while (startOfMonth.before(endOfMonth)) {
                    dates.add(formatDate(startOfMonth));
                    calendar.setTime(startOfMonth);
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                    startOfMonth = calendar.getTime();
                }
                break;
            case "年":
                calendar.set(Calendar.DAY_OF_YEAR, 1); // 设置为当年第一天
                Date startOfYear = getStartOfDay(calendar.getTime());
                Date endOfYear = getEndOfDay(today);
                while (startOfYear.before(endOfYear)) {
                    dates.add(formatYearMonth(startOfYear));
                    calendar.setTime(startOfYear);
                    calendar.add(Calendar.MONTH, 1);
                    startOfYear = calendar.getTime();
                }
                break;
            case "近七天":
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                for (int i = 0; i < 7; i++) {
                    LocalDate date = LocalDate.now().minusDays(i);
                    String formattedDate = date.format(formatter);
                    dates.add(formattedDate);
                }

                break;
            default:
                throw new IllegalArgumentException("Invalid time range: " + timeRange);
        }
        return dates;
    }

    public static List<String> getLastSixDate() {
        List<String> dates = new ArrayList<>();
        LocalDate today = LocalDate.now();

        for (int i = 0; i <= 5; i++) {
            LocalDate date = today.minusDays(i);
            String dateString = date.format(DateTimeFormatter.ISO_LOCAL_DATE);
            dates.add(dateString);
        }

        return dates;
    }

    /**
     * 获取当前时间，返回年月日时分
     * @return
     */
    public static String getFormattedCurrentTime() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年M月d号H点m分");

        // 格式化当前时间
        String formattedTime = now.format(formatter);

        return formattedTime;
    }


    /**
     * 根据传参返回日期
     * @param amount
     * @param unit
     * @return
     */
    public static Date calculateFutureDate(int amount, int unit) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());

        switch (unit) {
            case 1: // 时
                calendar.add(Calendar.HOUR_OF_DAY, amount);
                break;
            case 2: // 天
                calendar.add(Calendar.DAY_OF_MONTH, amount);
                break;
            case 3: // 周
                calendar.add(Calendar.WEEK_OF_MONTH, amount);
                break;
            case 4: // 月
                calendar.add(Calendar.MONTH, amount);
                break;
            default:
                throw new IllegalArgumentException("Invalid time unit. Please use 1 for hours, 2 for days, 3 for weeks, or 4 for months.");
        }

        return calendar.getTime();
    }

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat HOUR_FORMAT = new SimpleDateFormat("HH时");

    /**
     * 根据reportType获取相应的时间范围对象列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param reportType 报告类型
     * @return 时间范围对象列表
     */
    public static List<TimeRange> generateDateRanges(Date startTime, Date endTime, int reportType) {
        List<TimeRange> timeRanges = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();

        switch (reportType) {
            case 0:
            case 2:
                addDatesBetween(timeRanges, calendar, startTime, endTime);
                break;
            case 1:
                addHoursOfDay(timeRanges, calendar, startTime);
                break;
            case 3:
                addWeeksBetween(timeRanges, calendar, startTime, endTime);
                break;
            case 4:
            case 5:
                addMonthsBetween(timeRanges, calendar, startTime, endTime);
                break;
            case 6:
                addMonthsBetweenV2(timeRanges, calendar, startTime, endTime);
                break;
            default:
                throw new IllegalArgumentException("Invalid report type: " + reportType);
        }

        return timeRanges;
    }

    private static void addMonthsBetweenV2(List<TimeRange> timeRanges, Calendar calendar, Date startTime, Date endTime) {
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为每月第一天
        calendar.setTime(startTime);

        int year = calendar.get(Calendar.YEAR);

        while (!calendar.getTime().after(endTime)) {
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date monthStart = calendar.getTime();

            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);

            Date monthEnd = calendar.getTime();

            timeRanges.add(new TimeRange(DateUtils.format(monthEnd, "yyyy-MM"), monthStart, monthEnd));

            calendar.add(Calendar.MONTH, 1);
        }
    }


    private static void addDatesBetween(List<TimeRange> timeRanges, Calendar calendar, Date startTime, Date endTime) {
        calendar.setTime(startTime);
        while (!calendar.getTime().after(endTime)) {
            TimeRange timeRange = new TimeRange(DATE_FORMAT.format(calendar.getTime()), calendar.getTime());

            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);

            timeRange.setEndTime(calendar.getTime());

            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);

            timeRanges.add(timeRange);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
    }

    private static void addHoursOfDay(List<TimeRange> timeRanges, Calendar calendar, Date startTime) {
        calendar.setTime(startTime);
        for (int hour = 0; hour <= 23; hour++) {
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);

            calendar.set(Calendar.HOUR_OF_DAY, hour);

            TimeRange timeRange = new TimeRange(HOUR_FORMAT.format(calendar.getTime()), calendar.getTime());

            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);

            timeRange.setEndTime(calendar.getTime());

            timeRanges.add(timeRange);
        }
    }

    private static void addWeeksBetween(List<TimeRange> timeRanges, Calendar calendar, Date startTime, Date endTime) {
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(startTime);

        while (!calendar.getTime().after(endTime)) {
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date weekStart = calendar.getTime();

            calendar.add(Calendar.DAY_OF_MONTH, 6);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            Date weekEnd = calendar.getTime();
            timeRanges.add(new TimeRange("第" + calendar.get(Calendar.WEEK_OF_YEAR) + "周", weekStart, weekEnd));
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
    }

    private static void addMonthsBetween(List<TimeRange> timeRanges, Calendar calendar, Date startTime, Date endTime) {
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为每月第一天
        calendar.setTime(startTime);

        while (!calendar.getTime().after(endTime)) {
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date monthStart = calendar.getTime();

            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);

            Date monthEnd = calendar.getTime();
            timeRanges.add(new TimeRange((calendar.get(Calendar.MONTH) + 1) + "月", monthStart, monthEnd));

            calendar.add(Calendar.MONTH, 1);
        }
    }

    public static Date getLastDayOfMonth(Integer year, Integer month) {
        // 转换为YearMonth对象
        YearMonth yearMonth = YearMonth.of(year, month);
        // 获取当月最后一天
        LocalDate lastDay = yearMonth.atEndOfMonth();
        // 转换为Date对象（需处理时区）
        return Date.from(lastDay.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }
    /**
     * 判断时间距离当前时间间隔多少
     * @param inputDate
     * @return
     */
    public static String calculateTimeDifference(Date inputDate) {
        Date currentDate = new Date();
        long timeDifference = inputDate.getTime() - currentDate.getTime();

        if (timeDifference < 0) {
            return "已超期";
        }

        long days = TimeUnit.MILLISECONDS.toDays(timeDifference);
        timeDifference -= TimeUnit.DAYS.toMillis(days);

        long hours = TimeUnit.MILLISECONDS.toHours(timeDifference);
        timeDifference -= TimeUnit.HOURS.toMillis(hours);

        long minutes = TimeUnit.MILLISECONDS.toMinutes(timeDifference);

        return String.format("%d天%d时%d分", days, hours, minutes);
    }

    public static Date getFirstDayOfYear(int year) {
        // 创建LocalDate对象
        LocalDate firstDay = LocalDate.of(year, 1, 1);
        // 转换为Date对象（需处理时区）
        return Date.from(firstDay.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取上个月的第一天
     * @return 上个月第一天的Date对象
     */
    public static Date getLastMonthFirstDay() {
        Calendar calendar = Calendar.getInstance();
        // 设置为当前月的上一个月
        calendar.add(Calendar.MONTH, -1);
        // 设置为上个月的第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 时间设为00:00:00
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取上个月的最后一天
     * @return 上个月最后一天的Date对象
     */
    public static Date getLastMonthLastDay() {
        Calendar calendar = Calendar.getInstance();
        // 设置为当前月的上一个月
        calendar.add(Calendar.MONTH, -1);
        // 设置为上个月的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        // 时间设为23:59:59
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 获取上一个月的年份和月份
     * @return 包含上一个月年份和月份的数组，第一个元素是年份，第二个元素是月份(1-12)
     */
    public static int[] getLastMonthYearAndMonth() {
        LocalDate today = LocalDate.now();
        LocalDate lastMonth = today.minusMonths(1);

        int year = lastMonth.getYear();
        int month = lastMonth.getMonthValue();

        return new int[]{year, month};
    }

    /**
     * 获取前一天0点0时0分的yyyy-MM-dd HH:mm:ss字符串
     *
     * @return 前一天0点的时间字符串
     */
    public static String getLastDayStartStr() {
        Calendar calendar = Calendar.getInstance();
        // 日期减1，获取前一天
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        // 设置时分秒为00:00:00
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return parseDateToStr(YYYY_MM_DD_HH_MM_SS, calendar.getTime());
    }

    /**
     * 获取前一天23时59分59秒的yyyy-MM-dd HH:mm:ss字符串
     *
     * @return 前一天23:59:59的时间字符串
     */
    public static String getLastDayEndStr() {
        Calendar calendar = Calendar.getInstance();
        // 日期减1，获取前一天
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        // 设置时分秒为23:59:59
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        return parseDateToStr(YYYY_MM_DD_HH_MM_SS, calendar.getTime());
    }

    /**
     * 获取上一个小时的开始时间
     * 例如：当前是14:30，则返回13:00:00.000的Date对象
     *
     * @return 上一个小时开始时间的Date对象
     */
    public static String getLastHourStart() {
        Calendar calendar = Calendar.getInstance();
        // 小时减1
        calendar.add(Calendar.HOUR_OF_DAY, -1);
        // 分钟、秒、毫秒设置为0
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return parseDateToStr(YYYY_MM_DD_HH_MM_SS, calendar.getTime());
    }

    /**
     * 获取上一个小时的结束时间
     * 例如：当前是14:30，则返回13:59:59.999的Date对象
     *
     * @return 上一个小时结束时间的Date对象
     */
    public static String getLastHourEnd() {
        Calendar calendar = Calendar.getInstance();
        // 小时减1
        calendar.add(Calendar.HOUR_OF_DAY, -1);
        // 分钟、秒设置为最大值
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        return parseDateToStr(YYYY_MM_DD_HH_MM_SS, calendar.getTime());
    }

    public static long getDaysBetween(Date startDate, Date endDate) {
        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return ChronoUnit.DAYS.between(start, end);
    }

    /**
     * 获取本月的第一天（时间部分清零：00:00:00）
     *
     * @return 本月第一天的 Date 对象
     */
    public static Date getMonthFirstDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为本月第一天
        calendar.set(Calendar.HOUR_OF_DAY, 0);  // 时设为0
        calendar.set(Calendar.MINUTE, 0);       // 分设为0
        calendar.set(Calendar.SECOND, 0);       // 秒设为0
        calendar.set(Calendar.MILLISECOND, 0);  // 毫秒设为0
        return calendar.getTime();
    }

    /**
     * 获取当前季度的第一天
     * @return
     */
    public static Date getQuarterStartDate() {
        Calendar calendar = Calendar.getInstance();
        int currentMonth = calendar.get(Calendar.MONTH);
        int quarter = (currentMonth - (currentMonth % 3)); // 计算属于哪个季度
        calendar.set(Calendar.MONTH, quarter);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定日期所在季度的第一天
     * @param date
     * @return
     */
    public static Date getQuarterStartDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int currentMonth = calendar.get(Calendar.MONTH);
        int quarter = (currentMonth - (currentMonth % 3));
        calendar.set(Calendar.MONTH, quarter);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }


    /**
     * 获取当前年的第一天
     * @return
     */
    public static Date getYearStartDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_YEAR, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取从当年1月到上个月的所有月份第一天
     *
     * @return 月份第一天列表
     */
    public static List<Date> getMonthsFirstDay() {
        List<Date> result = new ArrayList<>();

        // 获取当前时间
        LocalDate now = LocalDate.now();
        // 获取当前年份
        int currentYear = now.getYear();
        // 获取上个月
        int lastMonth = now.minusMonths(1).getMonthValue();

        // 从1月到上个月，获取每个月第一天
        for (int month = 1; month <= lastMonth; month++) {
            LocalDate firstDay = LocalDate.of(currentYear, month, 1);
            // 转换为Date对象
            Date date = Date.from(firstDay.atStartOfDay(ZoneId.systemDefault()).toInstant());
            result.add(date);
        }

        return result;
    }

    /**
     * 获取从指定时间到当前时间上一个月之间的每个月第一天
     *
     * @param startDate 开始时间
     * @return 每个月第一天的Date对象列表
     */
    public static List<Date> getMonthFirstDays(Date startDate) {
        List<Date> result = new ArrayList<>();

        // 将开始时间转换为LocalDate
        LocalDate startLocalDate = startDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 获取当前时间上个月的第一天
        LocalDate endLocalDate = LocalDate.now()
                .minusMonths(1)
                .withDayOfMonth(1);

        // 如果开始时间大于结束时间，返回空列表
        if (startLocalDate.isAfter(endLocalDate)) {
            return result;
        }

        // 从开始时间所在月份的第一天开始
        LocalDate currentDate = startLocalDate.withDayOfMonth(1);

        // 循环直到当前时间上个月
        while (!currentDate.isAfter(endLocalDate)) {
            // 将LocalDate转换为Date
            Date date = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            result.add(date);
            // 移动到下个月的第一天
            currentDate = currentDate.plusMonths(1);
        }

        return result;
    }

    public static DateDTO getMonthStartAndEndDate(Date date) {
        DateDTO dateDTO = new DateDTO();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);

        dateDTO.setStartTime(calendar.getTime());

        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);

        dateDTO.setEndTime(calendar.getTime());

        return dateDTO;
    }

    public static DateDTO getLastMonthEndAndThisMonthEnd(Date date) {
        DateDTO dateDTO = new DateDTO();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);

        dateDTO.setEndTime(calendar.getTime());

        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);

        dateDTO.setStartTime(calendar.getTime());

        return dateDTO;
    }

    /**
     * 获取指定日期所在季度的开始时间
     * @param date 指定日期
     * @return 季度开始时间
     */
    public static Date getQuarterStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 获取当前月份
        int month = calendar.get(Calendar.MONTH);
        // 计算季度开始月份
        int quarterStartMonth = (month / 3) * 3;

        // 设置季度开始时间
        calendar.set(Calendar.MONTH, quarterStartMonth);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    /**
     * 获取指定日期所在季度的结束时间
     * @param date 指定日期
     * @return 季度结束时间
     */
    public static Date getQuarterEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 获取当前月份
        int month = calendar.get(Calendar.MONTH);
        // 计算季度结束月份
        int quarterEndMonth = (month / 3) * 3 + 2;

        // 设置季度结束时间
        calendar.set(Calendar.MONTH, quarterEndMonth);
        // 获取该月最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        return calendar.getTime();
    }

    /**
     * 获取上一分钟的开始时间
     * @return 上一分钟的开始时间
     */
    public static Date getLastMinuteStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -1);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取上一分钟的结束时间
     * @return 上一分钟的结束时间
     */
    public static Date getLastMinuteEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -1);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }


    public static Date getFirstDayOfMonth(Integer year, Integer month) {
        // 转换为YearMonth对象
        YearMonth yearMonth = YearMonth.of(year, month);
        // 获取当月第一天
        LocalDate firstDay = yearMonth.atDay(1);
        // 转换为Date对象（需处理时区）
        return Date.from(firstDay.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }
}
