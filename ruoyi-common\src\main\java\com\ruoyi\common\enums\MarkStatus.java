package com.ruoyi.common.enums;

/**
 * 用户状态
 *
 * <AUTHOR>
 */
public enum MarkStatus {
    ADD("0", "加"),
    SUBTRACT("1", "减");

    private final String code;
    private final String info;

    MarkStatus(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
