<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.immune.dao.ImmuneCancelDao">
    <sql id="ImmuneCancelSql">
        a.id as "id",
        a.type as "type",
        a.pet_id as "petId",
        a.cancel_name as "cancelName",
        a.cancel_reason as "cancelReason",
        a.status as "status",
        a.reason as "reason",
        a.create_date as "createDate",
        a.cancel_id as "cancelId"
    </sql>

    <select id="getList" parameterType="com.ruoyi.modules.immune.entity.ImmuneCancel" resultType="com.ruoyi.modules.immune.entity.ImmuneCancel">
        select
        <include refid="ImmuneCancelSql" />
        from immune_cancel a
        where del_flag = 1
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        order by  a.create_date desc
    </select>

    <select id="getById" resultType="com.ruoyi.modules.immune.entity.ImmuneCancel">
        SELECT
        <include refid="ImmuneCancelSql"/>
        FROM
        ( SELECT MAX( create_date ) AS create_date, pet_id FROM immune_cancel GROUP BY pet_id DESC ORDER BY
        create_date DESC ) t1
        JOIN immune_cancel a ON  t1.pet_id = a.pet_id AND t1.create_date = a.create_date
        where a.pet_id = #{id}
    </select>

    <insert id="insert" parameterType="com.ruoyi.modules.immune.entity.ImmuneCancel">
        insert into immune_cancel(
            id,
            type,
            pet_id,
            cancel_name,
            cancel_reason,
            status,
            reason,
            create_date,
            create_by,
            del_flag,
            cancel_id
        )values (
            #{id},
            #{type},
            #{petId},
            #{cancelName},
            #{cancelReason},
            #{status},
            #{reason},
            #{createDate},
            #{createBy},
            #{delFlag},
            #{cancelId}
        )
    </insert>

    <update id="update" parameterType="com.ruoyi.modules.immune.entity.ImmuneCancel">
        update immune_cancel set
        <if test="petId != null and petId != ''">
            pet_id = #{petId},
        </if>
        <if test="type != null and type != ''">
            type = #{type},
        </if>
        <if test="cancelName != null and cancelName != ''">
            cancel_name = #{cancelName},
        </if>
        <if test="cancelReason != null and cancelReason != ''">
            cancel_reason = #{cancelReason},
        </if>
        <if test="status != null and status != ''">
            status = #{status},
        </if>
        <if test="reason != null and reason != ''">
            reason = #{reason},
        </if>
            update_date = #{updateDate},
            update_by = #{updateBy}
        where id = #{id}
    </update>

    <update id="delete" parameterType="com.ruoyi.modules.immune.entity.ImmuneCancel">
        update immune_cancel set  del_flag = #{delFlag}  where id = #{id}
    </update>


    <update id="updateStatus" parameterType="com.ruoyi.modules.immune.entity.ImmuneCancel">
        update immune_cancel set status = #{status},
                                 reason = #{reason},
                                 update_date = #{updateDate},
                                 update_by = #{updateBy}
        where id = #{id}
    </update>

    <select id="getByPet" parameterType="com.ruoyi.modules.immune.entity.ImmuneCancel" resultType="com.ruoyi.modules.immune.entity.ImmuneCancel">
        select
        <include refid="ImmuneCancelSql" />
        from immune_cancel a
        where type = 2 and a.pet_id = #{petId}
        order by a.create_date desc
        LIMIT 1
    </select>

    <resultMap id="petMap" type="com.ruoyi.modules.certificates.entity.PetCertificates">
        <id property="id" column="id"/>
        <result property="ownerName" column="owner_name"/>
        <result property="petIdCard" column="pet_id_card"/>
        <result property="tel" column="tel"/>
        <result property="ownerAddress" column="owner_address"/>
        <result property="petName" column="pet_name"/>
        <result property="petNum" column="pet_num"/>
        <result property="petType" column="pet_type"/>
        <result property="petSex" column="pet_sex"/>
        <result property="petVarieties" column="pet_varieties"/>
        <result property="petHair" column="pet_hair"/>
        <result property="petAge" column="pet_age"/>
        <result property="raiseDate" column="raise_date"/>
        <result property="petAddress" column="pet_address"/>
        <result property="petDept" column="pet_dept"/>
        <result property="isAgency" column="is_agency"/>
        <result property="agencyCom" column="agency_com"/>
        <result property="source" column="source"/>
        <result property="status" column="status"/>
        <result property="immuneId" column="immune_id"/>
        <result property="transferId" column="transfer_id"/>
        <result property="cancelId" column="cancel_id"/>
        <result property="endDate" column="end_date"/>
        <result property="aboutMake" column="about_make"/>
        <result property="aboutDate" column="about_date"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="petCount" column="petCount"/>
        <result property="petImg" column="pet_img"/>
        <collection property="immuneCancel" ofType="com.ruoyi.modules.immune.entity.ImmuneCancel">
            <id property="id" column="cancelId"/>
            <result property="cancelName" column="cancel_name"/>
            <result property="cancelReason" column="cancel_reason"/>
            <result property="createDate" column="cancelDate"/>
            <result property="status" column="cancelStatus"/>
            <result property="reason" column="cancelReason"/>
        </collection>
    </resultMap>

    <select id="queryPageList" parameterType="com.ruoyi.modules.certificates.entity.PetCertificates"  resultMap="petMap">
        select  a.*,
        ca.cancel_name,ca.cancel_reason,ca.create_date as cancelDate,ca.status as cancelStatus,
        ca.id as cancelId
        from pet_certificates a
        LEFT JOIN  immune_cancel ca ON ca.id = a.cancel_id
        where 1 = 1
        <if test="petIdCard != null and petIdCard != ''">
            and a.pet_id_card = #{petIdCard}
        </if>
        <if test="cancelStatus != null and cancelStatus != ''">
            and IFNULL(ca.status,0) = #{cancelStatus}
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            and a.hospital_id = #{hospitalId}
        </if>
        <if test="petDept != null and petDept != ''">
            and a.pet_dept =  #{petDept}
        </if>
        <if test="tel != null and tel != ''">
            and a.tel =  #{tel}
        </if>
        <if test="petName != null and petName != ''">
            and a.pet_name LIKE concat("%", #{petName}, "%")
        </if>
        <if test="ownerName != null and ownerName != ''">
            and a.owner_name LIKE concat("%", #{ownerName}, "%")
        </if>
        <if test="petNum != null and petNum != ''">
            and a.pet_num = #{petNum}
        </if>
        <if test="delFlag != null and delFlag != ''">
            and a.del_flag = #{delFlag}
        </if>
        order by a.create_date desc
    </select>

</mapper>
