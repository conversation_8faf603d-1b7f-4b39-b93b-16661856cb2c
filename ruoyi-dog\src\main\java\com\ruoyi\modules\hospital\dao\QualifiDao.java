package com.ruoyi.modules.hospital.dao;


import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.vaccine.entity.Vaccine;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 10:04
 * @version: 1.0
 **/
@Repository
public interface QualifiDao extends BaseDao<Qualifi> {

    public void updateStatus(Qualifi qualifi);

    public void updateAccount(Qualifi qualifi);

    public Qualifi getQualifiByAccount(Qualifi qualifi);

    public List<Qualifi> getAllList(Qualifi qualifi);

    public Qualifi verifiUnitCode(@Param("id") String id , @Param("unifiedCode") String unifiedCode, @Param("tel") String tel);


    public List<SysUser> getApply(SysUser sysUser);


    /*复制医院资质信息到历史表*/
    public void copyQualifi(String id);

    public void deleteReduct(String id);

    /*历史表还原资质信息到医院资质表*/
    public void reductQualifi(String id);

    List<Qualifi> listByIdList(List<String> qualifiIdList);

    List<Qualifi> listQualifiByAccount(@Param("dogUserId") String dogUserId, @Param("status") String status);

    List<Qualifi> listSendMessage();

    Qualifi checkExpire(Long userId);

    Qualifi getQualifiWithOutStatus(Qualifi qualifi);
}
