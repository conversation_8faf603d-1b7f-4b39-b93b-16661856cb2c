package com.ruoyi.common.enums;

/**
 * 用户状态
 *
 * <AUTHOR>
 */
public enum ScreenStatus {
    ADD("add", "新增"),
    UPDATE("upd", "更新"),
    END("end", "完结"),
    DELETE("del", "删除");

    private final String code;
    private final String info;

    ScreenStatus(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
