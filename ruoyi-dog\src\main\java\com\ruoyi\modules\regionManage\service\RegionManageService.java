package com.ruoyi.modules.regionManage.service;

import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.regionManage.dao.RegionManageDao;
import com.ruoyi.modules.regionManage.entity.RegionManage;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 区域管理表(region_manage)表服务接口
 * <AUTHOR>
 *
 */
 @Service
public class RegionManageService  extends BaseService<RegionManageDao,RegionManage> {

   @Transactional
    public void saveOrUpdate(RegionManage entity) {
        if (entity.getId() != null && !entity.getId().equals("")) {
            // 更新操作
            entity.preUpdate();
            update(entity);
        } else {
            // 新增操作
            insert(entity);
        }
    }

}
