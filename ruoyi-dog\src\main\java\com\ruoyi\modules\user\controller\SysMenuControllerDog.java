package com.ruoyi.modules.user.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.user.entity.SysMenu;
import com.ruoyi.modules.user.service.SysMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by Administrator on 2021-3-13.
 */
@RestController
@RequestMapping("/sysMenu")
public class SysMenuControllerDog {

    @Autowired
    private SysMenuService sysMenuService ;

    /**
     * 菜单树形结构数据
     * @param sysMenu
     * @return
     */
    @RequestMapping("/getSysMenuTree")
    public AjaxResult getSysMenuTree(SysMenu sysMenu){
        List<SysMenu> list = sysMenuService.getSysMenuTree(sysMenu);
        return AjaxResult.success(list);
    }

    /**
     * 保存或更新菜单数据
     * @param sysMenu
     */
    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(SysMenu sysMenu){
        sysMenuService.saveOrUpdate(sysMenu);
        return AjaxResult.success(sysMenu);
    }

    @RequestMapping("delete")
    public AjaxResult delete(SysMenu sysMenu){
        sysMenuService.delete(sysMenu);
        return AjaxResult.success();
    }
}
