package com.ruoyi.instruction.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.InstructionPersonControl;
import com.ruoyi.instruction.mapper.InstructionPersonControlMapper;
import com.ruoyi.instruction.service.IInstructionPersonControlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 重点人员-管控人员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Service
public class InstructionPersonControlServiceImpl implements IInstructionPersonControlService {
    @Autowired
    private InstructionPersonControlMapper instructionPersonControlMapper;

    /**
     * 查询重点人员-管控人员信息
     *
     * @param id 重点人员-管控人员信息主键
     * @return 重点人员-管控人员信息
     */
    @Override
    public InstructionPersonControl selectInstructionPersonControlById(Long id) {
        return instructionPersonControlMapper.selectInstructionPersonControlById(id);
    }

    /**
     * 查询重点人员-管控人员信息列表
     *
     * @param instructionPersonControl 重点人员-管控人员信息
     * @return 重点人员-管控人员信息
     */
    @Override
    public List<InstructionPersonControl> selectInstructionPersonControlList(InstructionPersonControl instructionPersonControl) {
        return instructionPersonControlMapper.selectInstructionPersonControlList(instructionPersonControl);
    }

    /**
     * 新增重点人员-管控人员信息
     *
     * @param instructionPersonControl 重点人员-管控人员信息
     * @return 结果
     */
    @Override
    public int insertInstructionPersonControl(InstructionPersonControl instructionPersonControl) {
        String username = SecurityUtils.getUsername();
        instructionPersonControl.setCreateBy(username);
        instructionPersonControl.setCreateTime(DateUtils.getNowDate());
        return instructionPersonControlMapper.insertInstructionPersonControl(instructionPersonControl);
    }

    /**
     * 修改重点人员-管控人员信息
     *
     * @param instructionPersonControl 重点人员-管控人员信息
     * @return 结果
     */
    @Override
    public int updateInstructionPersonControl(InstructionPersonControl instructionPersonControl) {
        String username = SecurityUtils.getUsername();
        instructionPersonControl.setUpdateBy(username);
        instructionPersonControl.setUpdateTime(DateUtils.getNowDate());
        return instructionPersonControlMapper.updateInstructionPersonControl(instructionPersonControl);
    }

    /**
     * 批量删除重点人员-管控人员信息
     *
     * @param ids 需要删除的重点人员-管控人员信息主键
     * @return 结果
     */
    @Override
    public int deleteInstructionPersonControlByIds(Long[] ids) {
        return instructionPersonControlMapper.deleteInstructionPersonControlByIds(ids);
    }

    /**
     * 删除重点人员-管控人员信息信息
     *
     * @param id 重点人员-管控人员信息主键
     * @return 结果
     */
    @Override
    public int deleteInstructionPersonControlById(Long id) {
        return instructionPersonControlMapper.deleteInstructionPersonControlById(id);
    }
}
