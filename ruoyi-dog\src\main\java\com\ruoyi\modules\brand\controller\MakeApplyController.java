package com.ruoyi.modules.brand.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.brand.entity.*;
import com.ruoyi.modules.brand.service.MakeApplyService;
import com.ruoyi.modules.brand.service.PetBrandApplyService;
import com.ruoyi.modules.brand.service.PetBrandService;
import com.ruoyi.util.ZipMultiFileUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (make_apply)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("makeApply")
public class MakeApplyController {
    /**
     * 服务对象
     */
    @Resource
    private MakeApplyService service;
    @Autowired
    private PetBrandService petBrandService;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("/getPageList")
    public AjaxResult getPageList(MakeApply entity) {
        return AjaxResult.success(service.getPageList(entity));
    }

    /*
     *
     * @title 获取犬牌数据
     * <AUTHOR>
     * @date 2023/1/11 13:02
     */
    @RequestMapping("getPetBrand")
    public AjaxResult getPetBrand(String id) {
        return AjaxResult.success(service.getPetBrand(id));
    }

    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(MakeApply entity) {
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }

    @RequestMapping("/updateByEntity")
    public AjaxResult updateByEntity(MakeApply entity,PetAuditRecords records) {
        service.updateByEntity(entity,records);
        return AjaxResult.success();
    }

//    @RequestMapping("/delete")
//    public AjaxResult delete(MakeApply entity) {
//        entity.setDelFlag(2);
//        service.delete(entity);
//        return AjaxResult.success();
//    }

    @Value("${zipFilePath}")
    private String zipFilePath;
    @Value("${uploadFilePath}")
    private String uploadFilePath;

    @RequestMapping("/downDogCodeZip")
    public void downDogCodeZip(String id, HttpServletRequest request, HttpServletResponse response) {
        List<String> itemList = new ArrayList<>();
        String fatherPath = zipFilePath + System.currentTimeMillis();
        File fatherFile = new File(fatherPath);
        try {
            if (!fatherFile.exists()) {
                fatherFile.mkdirs();
            } else {
                boolean f = fatherFile.delete();
                System.out.println(f);
                fatherFile.mkdirs();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        String fatherPath1 = fatherPath + ".zip";
        File fatherFile1 = new File(fatherPath1);
        try {
            if (!fatherFile1.exists()) {
            } else {
                fatherFile1.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        MakeApply apply = service.getById(id);
        itemList.add(apply.getUser().getDeptName());
        String dogCodes = apply.getDogCodes();
        List<String> dogCodeList = Arrays.stream(dogCodes.split(",")).collect(Collectors.toList());
        List<File> fileList = new ArrayList<>();
        List<String> list = new ArrayList<>();
        dogCodeList.forEach(e -> {
            PetBrand petBrand = petBrandService.getById(e);
            fileList.add(new File(uploadFilePath + petBrand.getQrCode()));
            list.add(petBrand.getBrandNum() + ".png");
        });
        String url = fatherPath + "/" + apply.getUser().getDeptName() + ".zip";
        File zipFile = new File(url);
        ZipMultiFileUtil.zipFiles(fileList.stream().toArray(File[]::new), list.stream().toArray(String[]::new), zipFile);
        //将项目名称的文件夹 压缩为zip
        String fileDir = "";
        ZipMultiFileUtil.fileToZip(fatherPath, fileDir, fatherPath + ".zip");
        ZipMultiFileUtil.downloadPathFile(fatherPath1, request, response);
    }


}

