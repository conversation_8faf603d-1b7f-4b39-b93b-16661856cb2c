package com.ruoyi.modules.msg.service.impl;

import java.util.List;

import com.ruoyi.common.component.DxCommonComponent;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.modules.msg.domain.SendMsgInfo;
import com.ruoyi.modules.msg.mapper.SendMsgInfoMapper;
import com.ruoyi.modules.msg.service.ISendMsgInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 短信发送记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Service
public class SendMsgInfoServiceImpl implements ISendMsgInfoService
{
    @Autowired
    private SendMsgInfoMapper sendMsgInfoMapper;
    @Autowired
    private DxCommonComponent dxCommonComponent;

    /**
     * 查询短信发送记录
     *
     * @param id 短信发送记录主键
     * @return 短信发送记录
     */
    @Override
    public SendMsgInfo selectSendMsgInfoById(Long id)
    {
        return sendMsgInfoMapper.selectSendMsgInfoById(id);
    }

    /**
     * 查询短信发送记录列表
     *
     * @param sendMsgInfo 短信发送记录
     * @return 短信发送记录
     */
    @Override
    public List<SendMsgInfo> selectSendMsgInfoList(SendMsgInfo sendMsgInfo)
    {
        return sendMsgInfoMapper.selectSendMsgInfoList(sendMsgInfo);
    }

    /**
     * 新增短信发送记录
     *
     * @param sendMsgInfo 短信发送记录
     * @return 结果
     */
    @Override
    public int insertSendMsgInfo(SendMsgInfo sendMsgInfo)
    {
        return sendMsgInfoMapper.insertSendMsgInfo(sendMsgInfo);
    }

    /**
     * 修改短信发送记录
     *
     * @param sendMsgInfo 短信发送记录
     * @return 结果
     */
    @Override
    public int updateSendMsgInfo(SendMsgInfo sendMsgInfo)
    {
        return sendMsgInfoMapper.updateSendMsgInfo(sendMsgInfo);
    }

    /**
     * 批量删除短信发送记录
     *
     * @param ids 需要删除的短信发送记录主键
     * @return 结果
     */
    @Override
    public int deleteSendMsgInfoByIds(Long[] ids)
    {
        return sendMsgInfoMapper.deleteSendMsgInfoByIds(ids);
    }

    /**
     * 删除短信发送记录信息
     *
     * @param id 短信发送记录主键
     * @return 结果
     */
    @Override
    public int deleteSendMsgInfoById(Long id)
    {
        return sendMsgInfoMapper.deleteSendMsgInfoById(id);
    }

    /**
     * 发送短信
     * @param sendMsgInfo
     * @return
     */
    @Override
    public int sendMsg(SendMsgInfo sendMsgInfo) {
        if (sendMsgInfo.getPhone()==null||sendMsgInfo.getType()==null|| StringUtils.isEmpty(sendMsgInfo.getDataId())){
            throw new GlobalException("参数异常");
        }
        sendMsgInfo.setId(null);
        // List<SendMsgInfo> sendMsgInfos = sendMsgInfoMapper.selectSendMsgInfoList(sendMsgInfo);
        // if (!CollectionUtils.isEmpty(sendMsgInfos)){
        //     throw new GlobalException("已发送短信");
        // }
        String msg = "尊敬的用户，系统检测到您的犬只免疫即将超期（"+(StringUtils.isEmpty(sendMsgInfo.getCqsj())?"到期时间":sendMsgInfo.getCqsj())+"）。按时为犬只接种疫苗，是保障犬只健康、预防各类疫病传播的关键举措，同时也是养犬人应尽的责任与义务。请您尽快带犬只前往正规的免疫点进行接种。感谢您的配合!";

        if (sendMsgInfo.getType()==2){
            msg = "尊敬的用户，系统检测到您的犬只免疫已超期（"+(StringUtils.isEmpty(sendMsgInfo.getCqsj())?"到期时间":sendMsgInfo.getCqsj())+"），三次超期提醒后将依法注销犬只登记信息，请及时处理，避免因信息注销造成违规养犬行为。按时为犬只接种疫苗，是保障犬只健康、预防各类疫病传播的关键举措，同时也是养犬人应尽的责任与义务。请您尽快带犬只前往正规的免疫点进行接种。感谢您的配合！";
        }

        dxCommonComponent.send(sendMsgInfo.getPhone(),msg);
        sendMsgInfoMapper.insertSendMsgInfo(sendMsgInfo);


        return 1;
    }
}
