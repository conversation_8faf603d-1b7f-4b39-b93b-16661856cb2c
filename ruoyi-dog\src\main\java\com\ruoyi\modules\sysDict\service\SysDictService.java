package com.ruoyi.modules.sysDict.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.sysDict.dao.SysDictDao;
import com.ruoyi.modules.sysDict.entity.SysDict;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by Administrator on 2019/3/25/025.
 */
@Service
public class SysDictService extends BaseService<SysDictDao, SysDict> {
    @Transactional
    @Override
    public void saveOrUpdate(SysDict entity){
        if(entity.getId() != null && !"".equals(entity.getId())){
            entity.preUpdate();
            update(entity);
        }else{
            entity.preInsert();
            //当类型为“处罚条例”
            if("punishmentRegulations".equals(entity.getDictType())){
                entity.setDictKey(entity.getId());
            }
            if ("varieties".equals(entity.getDictType()) || "varieties_type".equals(entity.getDictType()) || "hair_type".equals(entity.getDictType())) {
                String dictKey = dao.queryDictKey(entity);
                if("1".equals(dictKey)){
                    dictKey =  entity.getValue1()+"01";
                }
                entity.setDictKey(dictKey);
            }
            insert(entity);
        }
    }


    public PageInfo<SysDict> getPetType(SysDict sysDict) {
        PageHelper.startPage(sysDict.getPageNum(), sysDict.getPageSize());
        PageInfo<SysDict> pageInfo = new PageInfo<SysDict>(dao.getPetType(sysDict));
        return pageInfo;
    }
}
