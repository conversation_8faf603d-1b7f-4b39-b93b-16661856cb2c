package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.JazzInstructionInfo;
import com.ruoyi.instruction.domain.reqVo.JazzInstructionFlowTestVo;
import com.ruoyi.instruction.service.IJazzInstructionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 指令(金安智治后台)
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@RestController
@RequestMapping("/jazzInstruction/info")
public class JazzInstructionInfoController extends BaseController {

    @Autowired
    private IJazzInstructionInfoService jazzInstructionInfoService;


    /**
     * 获取指令基本信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(jazzInstructionInfoService.selectInstructionInfoById(id));
    }

    /**
     * 新增指令基本信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:info:add')")
    @Log(title = "指令基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JazzInstructionInfo instructionInfo) {
        AjaxResult result = jazzInstructionInfoService.insertInstructionInfo(instructionInfo);
        return result;
    }

    /**
     * 修改指令基本信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:info:edit')")
    @Log(title = "指令基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JazzInstructionInfo instructionInfo) {
        return toAjax(jazzInstructionInfoService.updateInstructionInfo(instructionInfo));
    }

    /**
     * 删除指令基本信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:info:remove')")
    @Log(title = "指令基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(jazzInstructionInfoService.deleteInstructionInfoByIds(ids));
    }


    /**
     * 测试提交流程接口
     *
     * @return
     */
    @PostMapping("/testSubmit")
    public AjaxResult testSubmit(@RequestBody JazzInstructionFlowTestVo instructionFlowTestVo) {
        return jazzInstructionInfoService.testSubmit(instructionFlowTestVo);
    }

    /**
     * 处置、反馈变为多条记录 获取指令流程记录
     *
     * @param id
     * @return
     */
    @GetMapping("testGetProcessById/{id}/{type}")
    public AjaxResult testGetProcessById(@PathVariable("id") Long id, @PathVariable("type") Integer type) {
        JazzInstructionFlowTestVo flowTestVo = jazzInstructionInfoService.testGetProcessById(id, type);
        return AjaxResult.success(flowTestVo);
    }


    /**
     * 测试 指令列表
     *
     * @param instructionInfo
     * @return
     */
    @GetMapping("/testInstructionListNew")
    public TableDataInfo testInstructionListNew(JazzInstructionInfo instructionInfo) {
        return jazzInstructionInfoService.testInstructionListNew(instructionInfo);
    }


    /**
     * 查询出人员代办事件
     *
     * @return
     */
    @GetMapping("/testRedStatistics")
    public AjaxResult testRedStatistics() {
        Map<String, List<Long>> ids = jazzInstructionInfoService.testRedStatistics();
        return AjaxResult.success().put("cityIds", ids.get("cityIds").size()).put("countyIds",ids.get("countyIds").size());
    }

}
