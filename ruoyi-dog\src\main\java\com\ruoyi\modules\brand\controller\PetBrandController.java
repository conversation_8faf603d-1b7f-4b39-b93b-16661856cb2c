package com.ruoyi.modules.brand.controller;

import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.modules.brand.entity.PetBrand;
import com.ruoyi.modules.brand.service.PetBrandService;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.util.QRCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("petBrand")
public class PetBrandController {

    @Resource
    PetBrandService service;

    @Value("${petBrandServiceUrl}")
    private String petBrandServiceUrl;
    @Autowired
    private SysDeptMapper sysDeptMapper;

    @RequestMapping("getPageList")
    public AjaxResult getPageList(PetBrand petBrand) {
        // 获取用户信息
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录或会话失效");
        }
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.DOG_COUNTY_CONFIRM)){
            //获取用户区县
            SysDept userQx = sysDeptMapper.getUserQx(user.getDeptId());
            petBrand.setBrandCity(userQx.getDeptId()+"");
        }

        return AjaxResult.success(service.getPageList1(petBrand));
    }

    @RequestMapping("getList")
    public AjaxResult getList(PetBrand petBrand) {
        return AjaxResult.success(service.getList(petBrand));
    }

    @RequestMapping("getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("saveOrUpdate")
    public AjaxResult saveOrUpdate(PetBrand petBrand) {
        service.saveOrUpdate(petBrand);
        return AjaxResult.success();
    }

    @RequestMapping("upStatus")
    public AjaxResult upStatus(PetBrand petBrand) {
        service.upStatus(petBrand);
        return AjaxResult.success();
    }

    @RequestMapping("saveList")
    public AjaxResult saveList(PetBrand petBrand) {
        return AjaxResult.success(service.saveList(petBrand));
    }


    @RequestMapping("getAllList")
    public AjaxResult getAllList(PetBrand petBrand) {
        return AjaxResult.success(service.getAllList(petBrand));
    }

    /**
     * @author: tongsiyu
     * @date: 2022/12/13 17:50
     * @Description:大屏-犬牌统计
     */
    @RequestMapping("totalEchar")
    public AjaxResult totalEchar() {
        return AjaxResult.success(service.totalEchar());
    }

    /**
     * @author: tongsiyu
     * @date: 2022/12/16 16:09
     * @Description:批量修改归属企业
     */
    @RequestMapping("batchQualifi")
    public AjaxResult batchQualifi(String ids, String qualifiId) {
        service.batchQualifi(ids, qualifiId);
        return AjaxResult.success();
    }

    /*
     *
     * @title 根据地区获取剩余未分配犬牌数量
     * <AUTHOR>
     * @date 2023/1/11 19:09
     */
    @RequestMapping("getNotApplyBrandNum")
    public AjaxResult getNotApplyBrandNum(String deptId) {
        return AjaxResult.success(service.getNotApplyBrandNum(deptId));
    }

    /*
     *
     * @title 获取未分配的号码
     * <AUTHOR>
     * @date 2023/1/12 11:23
     */
    @RequestMapping("getBranchList")
    public AjaxResult getBranchList(PetBrand petBrand) {
        return AjaxResult.success(service.getBranchList(petBrand));
    }

    /*
     *
     * @title 重置犬牌归属单位
     * <AUTHOR>
     * @date 2023/1/17 12:57
     */
    @RequestMapping("handleReset")
    public AjaxResult handleReset(PetBrand brand){
        service.handleReset(brand);
        return AjaxResult.success();
    }

    @RequestMapping("getByEntity")
    public AjaxResult getByEntity(PetBrand petBrand) {
        petBrand.setIsReceipt(2);
        return AjaxResult.success(service.getByEntity(petBrand));
    }

    /**
     * 根据犬牌号获取二维码
     * @param brandNum
     */
    @RequestMapping("/getQrImg")
    public void getQrImg(String brandNum, HttpServletRequest request, HttpServletResponse response){
        try{
            if(brandNum == null){
                return;
            }
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            //3.设置content-disposition响应头控制浏览器以下载的形式打开文件
            String fileName = brandNum.replace(" ","") + ".png";
            if (request.getHeader("User-Agent").toLowerCase().indexOf("firefox") > 0) {
                fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1"); // firefox浏览器
            } else if (request.getHeader("User-Agent").toUpperCase().indexOf("MSIE") > 0) {
                fileName = URLEncoder.encode(fileName, "UTF-8");// IE浏览器
            } else if (request.getHeader("User-Agent").toUpperCase().indexOf("CHROME") > 0) {
                fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");// 谷歌
            }
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            String urlContent = petBrandServiceUrl+brandNum;
            QRCode.initQrImgToOs(urlContent, response.getOutputStream());
            OutputStream out = response.getOutputStream();
            out.flush();
            out.close();
        }catch (Exception e){
            e.printStackTrace();
        }

    }
}
