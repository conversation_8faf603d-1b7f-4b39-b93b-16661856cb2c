package com.ruoyi.modules.user.controller;

import com.github.pagehelper.PageInfo;
import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.user.entity.SysRole;
import com.ruoyi.modules.user.entity.SysRoleMenu;
import com.ruoyi.modules.user.service.SysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by Administrator on 2021-3-13.
 */
@RestController
@RequestMapping("/sysRole")
public class SysRoleControllerDog {

    @Autowired
    private SysRoleService sysRoleService;

    /**
     * 根据id获取角色信息
     * @param id
     * @return
     */
    @RequestMapping("/getById")
    public AjaxResult getById(String id){
        SysRole sysRole = sysRoleService.getById(id);
        return AjaxResult.success(sysRole);
    }

    /**
     * 分页查询角色列表数据
     * @param sysRole
     * @return
     */
    @RequestMapping("/getPageList")
    public AjaxResult getPageList(SysRole sysRole){
        PageInfo<SysRole> pageInfo = sysRoleService.getPageList(sysRole);
        return AjaxResult.success(pageInfo);
    }

    /**
     * 保存或更新数据
     * @return
     */
    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(SysRole sysRole){
        sysRoleService.saveOrUpdate(sysRole);
        return AjaxResult.success();
    }

    /**
     * 查询所有角色列表数据
     * @param sysRole
     * @return
     */
    @RequestMapping("/getAllList")
    public AjaxResult getAllList(SysRole sysRole){
        return AjaxResult.success(sysRoleService.getList(sysRole));
    }

    @RequestMapping("/updateAllSysRoleMenu")
    public AjaxResult updateAllSysRoleMenu(SysRole sysRole){
        sysRoleService.updateAllSysRoleMenu(sysRole);
        return AjaxResult.success();
    }

    /**
     * 根据角色ID获取菜单授权数据
     * @param sysRoleMenu
     * @return
     */
    @RequestMapping("/getSysRoleMenuList")
    public AjaxResult getSysRoleMenuList(SysRoleMenu sysRoleMenu){
        return AjaxResult.success(sysRoleService.getSysRoleMenuList(sysRoleMenu));
    }
}
