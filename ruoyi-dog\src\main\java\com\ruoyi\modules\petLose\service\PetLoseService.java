package com.ruoyi.modules.petLose.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.petLose.dao.PetLoseDao;
import com.ruoyi.modules.petLose.entity.PetLose;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 宠物丢失表(pet_lose)表服务接口
 * <AUTHOR>
 *
 */
 @Service
public class PetLoseService  extends BaseService<PetLoseDao, PetLose> {

   @Transactional
    public void saveOrUpdate(PetLose entity) {
        if (entity.getId() != null && !entity.getId().equals("")) {
            // 更新操作
            entity.preUpdate();
            dao.updateByEntity(entity);
        } else {
            // 新增操作
            insert(entity);
        }
    }

    @Transactional
    public void saveClue(PetLose entity) {
       dao.updateByEntity(entity);
    }

    public PageInfo<PetLose> page(PetLose entity){
        PageInfo<PetLose> pageList = this.getPageList(entity);



        return pageList;
    }



}
