package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.UndercoverProblem;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 暗访督察问题Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-23
 */
@Mapper
@DataSource(value = DataSourceType.SLAVE)
public interface UndercoverProblemMapper 
{
    /**
     * 查询暗访督察问题
     * 
     * @param id 暗访督察问题主键
     * @return 暗访督察问题
     */
    public UndercoverProblem selectUndercoverProblemById(Long id);

    /**
     * 查询暗访督察问题列表
     * 
     * @param undercoverProblem 暗访督察问题
     * @return 暗访督察问题集合
     */
    public List<UndercoverProblem> selectUndercoverProblemList(UndercoverProblem undercoverProblem);

    /**
     * 新增暗访督察问题
     * 
     * @param undercoverProblem 暗访督察问题
     * @return 结果
     */
    public int insertUndercoverProblem(UndercoverProblem undercoverProblem);

    /**
     * 修改暗访督察问题
     * 
     * @param undercoverProblem 暗访督察问题
     * @return 结果
     */
    public int updateUndercoverProblem(UndercoverProblem undercoverProblem);

    /**
     * 删除暗访督察问题
     * 
     * @param id 暗访督察问题主键
     * @return 结果
     */
    public int deleteUndercoverProblemById(Long id);

    /**
     * 批量删除暗访督察问题
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUndercoverProblemByIds(Long[] ids);
}
