package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 交办单信息对象 t_assign_info
 * 
 * <AUTHOR>
 * @date 2023-05-18
 */
@Data
public class AssignInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  主键 */
    private Long id;

    /** 交办名称 */
    @Excel(name = "交办名称")
    private String assignName;

    /** 办理期限 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "办理期限", width = 30, dateFormat = "yyyy-MM-dd")
    private Date handleTime;

    /** 反馈要求 */
    @Excel(name = "反馈要求")
    private String feedbackRequire;

    /** 交办人 */
    @Excel(name = "交办人")
    private String assigner;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String tel;

    /** 交办要求 */
    @Excel(name = "交办要求")
    private String assignRequire;

    /** 交办内容 */
    @Excel(name = "交办内容")
    private String assignConten;

    /** 反馈情况 */
    @Excel(name = "反馈情况")
    private String feedbackSuition;

    /** 交办单号 */
    @Excel(name = "交办单号")
    private String numberInfo;

    /** 下发单位 */
    @Excel(name = "下发单位")
    private String issueUnit;

    /** 状态1：正常 9：删除 */
    @Excel(name = "状态1：正常 9：删除")
    private String status;

    /** 创建部门id */
    @Excel(name = "创建部门id")
    private Long createDeptId;

    /** 接收单位 */
    @Excel(name = "接收单位")
    private String receiveUnit;

    /** 接收单位id */
    @Excel(name = "接收单位id")
    private Long receiveUnitId;

    /**
     * 接收单位 接收单位id
     */
    private List<Map<String,Object>> maps;

    /** 反馈情况 1：已反馈 2：未反馈 */
    @Excel(name = "反馈情况 1：已反馈 2：未反馈")
    private String feedbackStatus;

}
