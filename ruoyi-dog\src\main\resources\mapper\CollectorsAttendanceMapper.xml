<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.CollectorsAttendanceMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.CollectorsAttendance">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="nick_name" property="nickName" jdbcType="VARCHAR"/>
        <result column="phonenumber" property="phonenumber" jdbcType="VARCHAR"/>
        <result column="dept" property="dept" jdbcType="VARCHAR"/>
        <result column="now_longitude" property="nowLongitude" jdbcType="VARCHAR"/>
        <result column="now_latitude" property="nowLatitude" jdbcType="VARCHAR"/>
        <result column="sign_time" property="signTime" jdbcType="DATE"/>
        <result column="sign_longitude" property="signLongitude" jdbcType="VARCHAR"/>
        <result column="sign_latitude" property="signLatitude" jdbcType="VARCHAR"/>
        <result column="sign_remark" property="signRemark" jdbcType="VARCHAR"/>
        <result column="sign_filepath" property="signFilepath" jdbcType="VARCHAR"/>
        <result column="out_time" property="outTime" jdbcType="DATE"/>
        <result column="out_longitude" property="outLongitude" jdbcType="VARCHAR"/>
        <result column="out_latitude" property="outLatitude" jdbcType="VARCHAR"/>
        <result column="out_remark" property="outRemark" jdbcType="VARCHAR"/>
        <result column="out_filepath" property="outFilepath" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="insertCollectorsAttendance" parameterType="com.ruoyi.CollectorsAttendance">
        INSERT INTO collectors_attendance (
            user_id,
            user_name,
            nick_name,
            phonenumber,
            dept,
            now_longitude,
            now_latitude,
            sign_time,
            sign_longitude,
            sign_latitude,
            sign_remark,
            sign_filepath,
            out_time,
            out_longitude,
            out_latitude,
            out_remark,
            out_filepath,
            status,
            `type`
        )
        VALUES (
            #{userId},
            #{userName},
            #{nickName},
            #{phonenumber},
            #{dept},
            #{nowLongitude},
            #{nowLatitude},
            #{signTime},
            #{signLongitude},
            #{signLatitude},
            #{signRemark},
            #{signFilepath},
            #{outTime},
            #{outLongitude},
            #{outLatitude},
            #{outRemark},
            #{outFilepath},
            #{status},
            #{type}
        )
    </insert>
</mapper>