package com.ruoyi.modules.immune.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.certificates.dao.PetCertificatesDao;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.hospital.service.QualifiService;
import com.ruoyi.modules.immune.entity.ImmuneRegister;
import com.ruoyi.modules.immune.service.ImmuneRegisterService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @version: 1.0
 **/
@RestController
@RequestMapping("immuneRegister")
public class ImmuneRegisterController {
    @Autowired
    private ImmuneRegisterService service;

    /**
     * @author: tongsiyu
     * @date: 2022/11/07 16:05
     * @Description:获取首页各类免疫数据的数量
     */
    @RequestMapping("getTotalNum")
    public AjaxResult getTotalNum(ImmuneRegister immuneRegister) {
        immuneRegister.setDeptId(null);
        return AjaxResult.success(service.getTotalNum(immuneRegister));
    }

    /**
     * @author: tongsiyu
     * @date: 2022/11/09 9:28
     * @Description:导出下载
     */
    @RequestMapping("downloadFile")
    public void downloadFile(ImmuneRegister immuneRegister, HttpServletRequest request, HttpServletResponse response){
        service.downloadFile(immuneRegister,request,response);
    }

    /**
     * @author: tongsiyu
     * @date: 2022/11/07 15:20
     * @Description:获取免疫登记区域排名
     */
    @RequestMapping("getDeptSort")
    public AjaxResult getDeptSort(ImmuneRegister immuneRegister) {
        immuneRegister.setDeptId(null);
        return AjaxResult.success(service.getDeptSort(immuneRegister));
    }
    @RequestMapping("getDeptSortJD")
    public AjaxResult getDeptSortJD(ImmuneRegister immuneRegister) {
        return AjaxResult.success(service.getDeptSortJD(immuneRegister));
    }
    @RequestMapping("getPageList")
    public AjaxResult getPageList(ImmuneRegister immuneRegister) {
        return AjaxResult.success(service.getPageList(immuneRegister));
    }

    @RequestMapping("saveOrUpdate")
    public AjaxResult saveOrUpdate(ImmuneRegister immuneRegister) {
        service.saveOrUpdate(immuneRegister);
        return AjaxResult.success();
    }

    @RequestMapping("delete")
    public AjaxResult delete(ImmuneRegister immuneRegister) {
        service.delete(immuneRegister);
        return AjaxResult.success();
    }

    /**
     * @author: tongsiyu
     * @date: 2022/11/08 10:32
     * @Description:首页统计获取宠物信息列表
     */
    @RequestMapping("getPetPageList")
    public AjaxResult getPetPageList(ImmuneRegister immuneRegister){
        return AjaxResult.success(service.getPetPageList(immuneRegister));
    }
    @RequestMapping("getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    /**
     * 免疫管理审核
     *
     * @param immuneRegister
     * @return
     */
    @RequestMapping("updateStatus")
    public AjaxResult updateStatus(ImmuneRegister immuneRegister) {
        String s = service.updateStatus(immuneRegister);
        return AjaxResult.success().put("data",s);
    }

    @RequestMapping("getByRegister")
    public AjaxResult getByRegister(String id) {
        return AjaxResult.success(service.getByRegister(id));
    }

    /**
     * 犬只登记
     * @param petCertificates
     * @return
     */
    @RequestMapping("savePet")
    public AjaxResult savePet(PetCertificates petCertificates) {
        AjaxResult ajaxResult = service.savePet(petCertificates);
        return ajaxResult;
    }

    /**
     * 犬证申请
     *
     * @param petCertificates
     * @return
     */
    @RequestMapping("savePetApply")
    public AjaxResult savePetApply(PetCertificates petCertificates) {
        service.savePetApply(petCertificates);
        return AjaxResult.success();
    }

    @RequestMapping("getByPetId")
    public AjaxResult getByPetId(String petId, String immuneId) {
        return AjaxResult.success(service.getByPetId(petId, immuneId));
    }

    @RequestMapping("randomMY")
    public AjaxResult randomMY() {
        return AjaxResult.success().put("data",service.randomMY());
    }

    /**
     * @author: liguanying
     * @date: 2022/12/13 14:20
     * @Description:大屏-获取宠物医院总数
     */
    @RequestMapping("getHospital")
    public AjaxResult getHospita() {
        return AjaxResult.success(service.getHospital());
    }


    /**
     * @author: liguanying
     * @date: 2022/12/13 14:20
     * @Description:大屏-获取免疫统计
     */
    @RequestMapping("getImmune")
    public AjaxResult getImmune() {
        return AjaxResult.success(service.getImmune());
    }

    /**
     * @author: liguanying
     * @date: 2022/12/13 14:20
     * @Description:大屏-获取全市牌办理波动
     */
    @RequestMapping("getPetNum")
    public AjaxResult getPetNum() {
        return AjaxResult.success(service.getPetNum());
    }
}
