package com.ruoyi.modules.petLose.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.petLose.entity.PetLose;
import com.ruoyi.modules.petLose.service.PetLoseService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;


/**
 * 宠物丢失表(pet_lose)表控制层
 * <AUTHOR>
 */
@RestController
@RequestMapping("petLose")
public class PetLoseController {
    /**
     * 服务对象
     */
    @Resource
    private PetLoseService service;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("/getPageList")
    public AjaxResult getPageList(PetLose entity) {
        return AjaxResult.success(service.getPageList(entity));
    }

    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(PetLose entity) {
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }

    @RequestMapping("/delete")
    public AjaxResult delete(PetLose entity) {
        entity.setDelFlag(2);
        service.delete(entity);
        return AjaxResult.success();
    }

    @RequestMapping("/saveClue")
    public AjaxResult saveClue(PetLose entity) {
        service.saveClue(entity);
        return AjaxResult.success();
    }
}
