package com.ruoyi.util.wuliu;

import com.alibaba.gov.api.client.AtgBusClient;
import com.alibaba.gov.api.client.DefaultAtgBusClient;
import com.alibaba.gov.api.domain.*;
import com.alibaba.gov.api.request.AtgBizLogisticsOrderCancelRequest;
import com.alibaba.gov.api.request.AtgBizLogisticsOrderCustomerRequest;
import com.alibaba.gov.api.response.AtgBizLogisticsOrderCancelResponse;
import com.alibaba.gov.api.response.AtgBizLogisticsOrderCustomerResponse;
import com.ruoyi.modules.express.entity.Express;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 物流快递
 * <AUTHOR>
 * @date 2022/12/9 15:38
 */
public class ExpressUtil {

    private static  String  GATEWAYURL = "http://gov-bus.zjzwfw.gov.cn:8086/openapi";
    private static  String  APPID = "3002023601";
    private static  String  KEYID = "hy8mcurrxbtoqx8si47k03rmf6yp0d91";
    private static  String  SECRETKEY = "RuRMYlVRDXV=4g8CklQq718k";
    private static  String  XAPPID = "a9e8182b0e0747cdbadb98718842f9f2";
    private static  String appSecret = "edbbe1675eed479bb1d9b67a498d530e";

    public static void main(String[] args) {
        cancelExpress("1111");
    }


    //网点窗口寄件给个人用户
    public static void customerOrder(Express express) {
        //1. 初始化网关地址、秘钥信息
        String gatewayUrl = GATEWAYURL;
        String appId = APPID;
        List<AtgBusSecretKey> secretKeys = new ArrayList<AtgBusSecretKey>();
        AtgBusSecretKey atgBusSecretKey = new AtgBusSecretKey(KEYID, SECRETKEY);
        secretKeys.add(atgBusSecretKey);

        //2. 初始化客户端
        AtgBusClient atgBusClient = new DefaultAtgBusClient(gatewayUrl, appId, secretKeys);

        //3. 找到对应服务的request，拼装业务信息

        AtgBizLogisticsOrderCustomerRequest atgBizLogisticsOrderCustomerRequest = new AtgBizLogisticsOrderCustomerRequest();
        /**
         * 客户编码
         */
        atgBizLogisticsOrderCustomerRequest.setCustomerNo(express.getCustomerNo());//zjzwfwg45702024 开发区执法局网点

        /**
         * 物流代码
         */
        atgBizLogisticsOrderCustomerRequest.setLogistics(express.getExpressCode()); //sf or ems

        /**
         * 运单集合
         */
        List <MailDTO> mails = new ArrayList <MailDTO>();
        MailDTO mailDTO = new MailDTO();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String collectTime = sdf.format(express.getCollectTime());
        mailDTO.setCollectTime(collectTime);
        mailDTO.setIsRevertBill(0L);
        try {
            mailDTO.setIdCard(AESUtil.encrypt(express.getIdCard(), appSecret));
        } catch (Exception e) {
            e.printStackTrace();
        }

        mailDTO.setCreditCode("1");
        mailDTO.setOrderType(0L);
        mailDTO.setPayType(2L);
        mailDTO.setPrintType(0L);
        mailDTO.setCustomerNo(express.getCustomerNo());
        if (!"浙江省".equals(express.getProvinceName())) {
            mailDTO.setFee("20");
        } else if (express.getDeptName().equals(express.getCityName())) {
            mailDTO.setFee("8");
        } else {
            mailDTO.setFee("10");
        }

        mailDTO.setOrderNo(express.getId());
        mailDTO.setOrderType(2L);
        List<LogisticsOrderItem> listlogisticsorderitemList = new ArrayList<LogisticsOrderItem>();
        LogisticsOrderItem listLogisticsOrderItem = new LogisticsOrderItem();
        listLogisticsOrderItem.setItemValue(500L);
        listLogisticsOrderItem.setNumber(1L);
        listLogisticsOrderItem.setItemName("犬牌");
        listlogisticsorderitemList.add(listLogisticsOrderItem);
        mailDTO.setItems(listlogisticsorderitemList);

        LogisticsOrderAddress receiver = new LogisticsOrderAddress();
        receiver.setRegionName(express.getRegionName());
        receiver.setRegionCode(express.getRegionCode());
        receiver.setStreet("");
        receiver.setAddress(express.getAddress());
        receiver.setName(express.getName());
        receiver.setCityName(express.getCityName());
        receiver.setCityCode(express.getCityCode());
        receiver.setProvinceName(express.getProvinceName());
        receiver.setProvinceCode(express.getProvinceCode());
        receiver.setPostcode("");
        try {
            receiver.setPhone(AESUtil.encrypt(express.getPhone(), appSecret));
        } catch (Exception e) {
            e.printStackTrace();
        }
        mailDTO.setReceiver(receiver);
        mailDTO.setUserType(0L);
        mailDTO.setUserId(express.getIdCard());
        mails.add(mailDTO);

        atgBizLogisticsOrderCustomerRequest.setMails(mails);

        /**
         * 备注
         */
        atgBizLogisticsOrderCustomerRequest.setRemark("");
        /**
         * 注册的appId
         */
        atgBizLogisticsOrderCustomerRequest.setXAppId(XAPPID);

        //4. 请求开放服务，获取response
        AtgBizLogisticsOrderCustomerResponse response = atgBusClient.execute(atgBizLogisticsOrderCustomerRequest);
        System.out.println(response.toString());
    }

    //撤销订单
    private static void cancelExpress(String orderId) {
        //1. 初始化网关地址、秘钥信息
        //1. 初始化网关地址、秘钥信息
        String gatewayUrl = GATEWAYURL;
        String appId = APPID;
        List<AtgBusSecretKey> secretKeys = new ArrayList<AtgBusSecretKey>();
        AtgBusSecretKey atgBusSecretKey = new AtgBusSecretKey(KEYID, SECRETKEY);
        secretKeys.add(atgBusSecretKey);

        //2. 初始化客户端
        AtgBusClient atgBusClient = new DefaultAtgBusClient(gatewayUrl, appId, secretKeys);

        //3. 找到对应服务的request，拼装业务信息

        AtgBizLogisticsOrderCancelRequest atgBizLogisticsOrderCancelRequest = new AtgBizLogisticsOrderCancelRequest();
        /**
         * 注册appId
         */
        atgBizLogisticsOrderCancelRequest.setXAPPID(XAPPID);

        /**
         * 平台订单号
         */
        atgBizLogisticsOrderCancelRequest.setOrderId(orderId);

        //4. 请求开放服务，获取response
        AtgBizLogisticsOrderCancelResponse response = atgBusClient.execute(atgBizLogisticsOrderCancelRequest);
    }

}
