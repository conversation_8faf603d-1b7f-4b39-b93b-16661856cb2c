package com.ruoyi.modules.takeIn.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.takeIn.entity.ReclaimRecord;
import com.ruoyi.modules.takeIn.entity.TakeIn;
import com.ruoyi.modules.takeIn.service.TakeInService;
import com.ruoyi.modules.takeIn.vo.ReclaimRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 收容管理
 * <AUTHOR>
 */
@RestController
@RequestMapping("takeIn")
public class TakeInController {
    /**
     * 服务对象
     */
    @Resource
    private TakeInService service;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    /**
     * 收容列表
     * @param entity
     * @return
     */
    @RequestMapping("/getPageList")
    public AjaxResult getPageList(TakeIn entity) {
        return AjaxResult.success(service.getPageList(entity));
    }

    /**
     * 收容登记
     * @param entity
     * @return
     */
    @PostMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(@RequestBody TakeIn entity) {
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }

    /**
     * 审核
     * @param takeIn
     * @return
     */
    @PostMapping("updateStatus")
    public AjaxResult updateStatus(@RequestBody TakeIn takeIn){
        return AjaxResult.success(service.updateStatus(takeIn));
    }

    /**
     * 领回登记
     * @param recordVO
     * @return
     */
    @PostMapping("register")
    public AjaxResult register(@RequestBody ReclaimRecordVO recordVO){
        service.register(recordVO);
        return AjaxResult.success();
    }

    /**
     * 移动端领回登记
     * @param recordVO
     * @return
     */
    @PostMapping("registerMobile")
    public AjaxResult registerMobile(@RequestBody ReclaimRecordVO recordVO){
        service.registerMobile(recordVO);
        return AjaxResult.success();
    }

    @RequestMapping("/updateByEntity")
    public AjaxResult updateByEntity(TakeIn entity) {
        service.updateByEntity(entity);
        return AjaxResult.success();
    }
    @RequestMapping("/delete")
    public AjaxResult delete(TakeIn entity) {
        entity.setDelFlag(2);
        service.delete(entity);
        return AjaxResult.success();
    }

    /**
     * 短信提醒
     * @param id
     * @return
     */
    @GetMapping("/sendMobile/{id}")
    public AjaxResult sendMobile(@PathVariable String id) {
        service.sendMobile(id);
        return AjaxResult.success();
    }

    /**
     * 操作日志列表
     * @param id
     * @return
     */
    @GetMapping("/log/{id}")
    public AjaxResult listTakeInList(@PathVariable String id) {
        return AjaxResult.success(service.listTakeInList(id));
    }

    /**
     * 收容签收
     * @param takeInIdList
     * @return
     */
    @PostMapping("/sign")
    public AjaxResult sign(String[] takeInIdList){
        service.sign(takeInIdList);
        return AjaxResult.success();
    }

}
