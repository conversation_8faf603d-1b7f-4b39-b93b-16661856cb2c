<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.harmTrea.dao.HarmTreaDao">

    <sql id="columns">
     <trim suffixOverrides=",">
            a.id as id,
            a.phone as phone,
            a.contacts as contacts,
            a.content as content,
            a.status as status,
            a.del_flag as delFlag,
            a.create_date as createDate,
            a.create_by as createBy,
            a.update_date as updateDate,
            a.update_by as updateBy,
            a.reason as reason,
            a.handle_person as handlePerson,
            a.handle_time as handleTime,
            a.handle_type as handleType,
            a.address as address,
            a.handle_status as handleStatus,
            a.take_in_id as takeInId,
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.harmTrea.entity.HarmTrea" resultType="com.ruoyi.modules.harmTrea.entity.HarmTrea">
        select <include refid="columns"/>
        from harm_trea a
        where a.id =#{id}
    </select>



    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.harmTrea.entity.HarmTrea" resultType="com.ruoyi.modules.harmTrea.entity.HarmTrea">
        select <include refid="columns"/>
        from harm_trea a
        where a.del_flag =1
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="phone != null and phone != ''">
                and a.phone like concat('%', #{phone}, '%')
            </if>
            <if test="contacts != null and contacts != ''">
                and a.contacts concat('%', #{contacts}, '%')
            </if>
            <if test="content != null and content != ''">
                and a.content = #{content}
            </if>
            <if test="status != null">
                and a.status = #{status}
            </if>
            <if test="delFlag != null">
                and a.del_flag = #{delFlag}
            </if>
            <if test="createDate != null">
                and a.create_date = #{createDate}
            </if>
            <if test="createBy != null and createBy != ''">
                and a.create_by = #{createBy}
            </if>
            <if test="updateDate != null">
                and a.update_date = #{updateDate}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and a.update_by = #{updateBy}
            </if>
        order by a.create_date desc
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.harmTrea.entity.HarmTrea">
        insert into harm_trea(id, phone, contacts, content, status, del_flag, create_date,
                              create_by, update_date, update_by, handle_person, handle_time,
                              handle_type, county, street, area, address, handle_status, take_in_id)
        values (#{id}, #{phone}, #{contacts}, #{content}, #{status}, #{delFlag}, #{createDate},
                #{createBy}, #{updateDate}, #{updateBy}, #{handlePerson}, #{handleTime}, #{handleType},
                #{county}, #{street}, #{area}, #{address}, #{handleStatus}, #{takeInId})
    </insert>
    <update id="updateByEntity" parameterType="com.ruoyi.modules.harmTrea.entity.HarmTrea">
        update harm_trea set
        <trim suffixOverrides=",">
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="contacts != null and contacts != ''">
                contacts = #{contacts},
            </if>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="reason != null and reason != ''">
                reason = #{reason},
            </if>
            <if test="handlePerson != null and handlePerson != ''">
                handle_person = #{handlePerson},
            </if>
            <if test="handleTime != null">
                handle_time = #{handleTime},
            </if>
            <if test="handleType != null">
                handle_type = #{handleType},
            </if>
            <if test="address != null and address != ''">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="handleStatus != null">
                handle_status = #{handleStatus},
            </if>
            <if test="takeInId != null and takeInId != ''">
                take_in_id = #{takeInId,jdbcType=VARCHAR},
            </if>
        </trim>
        where id = #{id}
    </update>
    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.harmTrea.entity.HarmTrea">
        update harm_trea set
        <trim suffixOverrides=",">
               phone = #{phone},
               contacts = #{contacts},
               content = #{content},
               status = #{status},
               del_flag = #{delFlag},
               create_date = #{createDate},
               create_by = #{createBy},
               update_date = #{updateDate},
               update_by = #{updateBy},
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.harmTrea.entity.HarmTrea">
        UPDATE  harm_trea
        SET
        del_flag=#{delFlag},
        update_date = #{updateDate},
        update_by = #{updateBy}
        where id = #{id}
    </delete>

    <select id="getByTakeInId" resultType="com.ruoyi.modules.harmTrea.entity.HarmTrea">
        select <include refid="columns"/> from harm_trea a where take_in_id = #{takeInId} and del_flag = 1 limit 1
    </select>
</mapper>
