package com.ruoyi.modules.regionManage.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.regionManage.entity.RegionManage;
import com.ruoyi.modules.regionManage.service.RegionManageService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * 区域管理表(region_manage)表控制层
 * <AUTHOR>
 */
@RestController
@RequestMapping("regionManage")
public class RegionManageController {
    /**
     * 服务对象
     */
    @Resource
    private RegionManageService service;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("/getPageList")
    public AjaxResult getPageList(RegionManage entity) {
        return AjaxResult.success(service.getPageList(entity));
    }

    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(RegionManage entity) {
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }

    @RequestMapping("/delete")
    public AjaxResult delete(RegionManage entity) {
        entity.setDelFlag(2);
        service.delete(entity);
        return AjaxResult.success();
    }

}
