package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 指令销号对象 t_instruction_end
 *
 * <AUTHOR>
 * @date 2022-12-20
 */
@Data
public class InstructionEndVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 销号主键id */
    private Long id;

    /** 是否销号 1:销号 2：未销号 */
    @Excel(name = "是否销号 1:销号 2：未销号")
    private Integer instrucationIsEnd;

    /** 销号时间  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "销号时间 ", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 销号人员 */
    @Excel(name = "销号人员")
    private String endBy;

    /** 销号依据 */
    @Excel(name = "销号依据")
    private String endAccord;

    /** 指令id */
    @Excel(name = "指令id")
    private Long instructionId;

    /** 销号部门 */
    @Excel(name = "销号部门")
    private String endDept;

    /** 1:正常 9：删除 */
    @Excel(name = "1:正常 9：删除")
    private Integer status;


    /** 反馈部门 */
    private String feedbackDept;

    /**
     * 指令
     */
    private String instructionTitle;

    /**
     * 状态（添加字段）
     */
    private String statusName;
}