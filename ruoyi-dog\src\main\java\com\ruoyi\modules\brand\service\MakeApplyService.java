package com.ruoyi.modules.brand.service;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.modules.brand.dao.MakeApplyDao;
import com.ruoyi.modules.brand.dao.PetAuditRecordsDao;
import com.ruoyi.modules.brand.dao.PetBrandDao;
import com.ruoyi.modules.brand.entity.MakeApply;
import com.ruoyi.modules.brand.entity.PetAuditRecords;
import com.ruoyi.modules.brand.entity.PetBrand;
import com.ruoyi.modules.brand.entity.PetBrandApply;
import com.ruoyi.modules.user.dao.SysDeptDao;
import com.ruoyi.modules.user.entity.SysDept;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.util.PinYinUtil;
import com.ruoyi.util.QRCode;
import com.ruoyi.common.core.domain.entity.SysRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (make_apply)表服务接口
 *
 * <AUTHOR>
 */
@Service
public class MakeApplyService extends BaseService<MakeApplyDao, MakeApply> {
    @Autowired
    private PetBrandDao petBrandDao;
    @Autowired
    private PetAuditRecordsDao petAuditRecordsDao;
    @Autowired
    private SysDeptDao sysDeptDao;
    @Value("${petBrandServiceUrl}")
    private String petBrandServiceUrl;
    @Autowired
    private SysDeptMapper deptMapper;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private ISysUserService userService;

    @Transactional
    public void saveOrUpdate(MakeApply entity) {
        if (entity.getId() != null && !entity.getId().equals("")) {
//            // 更新操作
//            entity.preUpdate();
//            update(entity);
        } else {
            // 新增操作
            insert(entity);
            //制作犬牌
            String dogCodes = generateDogBrand(entity.getId());
            entity.setDogCodes(dogCodes);
            dao.updateByEntity(entity);
        }
    }

    @Transactional
    public void updateByEntity(MakeApply entity, PetAuditRecords records) {
        records.setApplyId(entity.getId());
        records.setStatus(entity.getStatus());
        records.setCreateBy(entity.getUserId());
        records.setId(null);
        records.preInsert();
        petAuditRecordsDao.insert(records);
        /*生成犬牌*/
//        if (entity.getStatus().intValue() == 2) {
//            String dogCodes = generateDogBrand(entity.getId());
//            entity.setDogCodes(dogCodes);
//        }
        dao.updateByEntity(entity);
        /*确认收货*/
        if (entity.getStatus().intValue() == 6) {
            entity = dao.getById(entity.getId());
            if (entity.getDogCodes() != null) {
                String[] idList = entity.getDogCodes().split(",");
                for (String id : idList) {
                    PetBrand brand = petBrandDao.getById(id);
                    brand.setIsReceipt(2);//已收货
                    petBrandDao.updateByEntity(brand);
                }
            }
        }

    }

    public String generateDogBrand(String id) {
        String dogCodes = "";
        MakeApply apply = dao.getById(id);
        SysDept dept = sysDeptDao.getById(apply.getDeptId());
        //生成犬牌
        String prefix = "NO " + new PinYinUtil().StringAlpha(dept.getDeptName().substring(0, 2));
        //获取犬牌末尾号
        String lastBrandNum = petBrandDao.getLastBrandNum(prefix);
        Integer num = 0;
        List<PetBrand> list = new ArrayList<>();
        if (lastBrandNum != null) {
            num = Integer.parseInt(lastBrandNum);
        }
        for (int i = 0; i < apply.getNum(); i++) {
            num++;
            PetBrand brand = new PetBrand();
            brand.preInsert();
            brand.setIsRecovery(1);          //未使用
            brand.setIsCancellation(1);      //未注销
            brand.setIsUse(2);               //未使用
            brand.setIsReceipt(1);               //未收货
            brand.setArea(apply.getDeptId());
            String dogCode = prefix + String.format("%06d", num);
            dogCodes += brand.getId() + ",";
            brand.setBrandNum(dogCode);
            brand.setBrandCity(dept.getId());
            brand.setQrCode(QRCode.createQrImg(petBrandServiceUrl + dogCode));
            list.add(brand);
        }
        petBrandDao.saveList(list);
        System.out.println(lastBrandNum);
        //    编辑状态
        //生成的犬牌号
        if (dogCodes != "") {
            dogCodes = dogCodes.substring(0, dogCodes.lastIndexOf(","));
        }
        return dogCodes;
    }

    public List<PetBrand> getPetBrand(String id) {
        MakeApply apply = dao.getById(id);
        if (apply.getDogCodes() != null) {
            List<PetBrand> list = petBrandDao.getPetBrand(Arrays.asList(Arrays.stream(apply.getDogCodes().split(",")).toArray()));
            return list;
        }
        return null;
    }

    @Override
    public PageInfo<MakeApply> getPageList(MakeApply entity) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());

        // 1. 定义角色分组
        List<String> superAdminRoles = List.of("admin", "dogAdmin", "common");
        List<String> bureauRoles = List.of("enforceBureau", "dogConfirm", "dogZfj");

        // 2. 权限判断
        if (roleList.stream().anyMatch(superAdminRoles::contains) || roleList.stream().anyMatch(bureauRoles::contains))
        {
            // A. 如果是超级管理员 或 执法局角色，则清空所有前端传入的普通筛选条件
            entity.setDeptName(null);
            entity.setUserId(null);
            entity.setStatus(null);

            if (roleList.stream().anyMatch(bureauRoles::contains)) {
                List<Long> managedDeptIds = userService.getManagedDeptIdsByUserId(user.getUserId());
                if (managedDeptIds.isEmpty()) {
                    managedDeptIds.add(0L);
                }
                entity.setDeptIds(managedDeptIds);
            }
        }
        else
        {
            // C. 其他角色（如医院），只能看自己部门的数据
            if (user.getDeptId() != null) {
                entity.setDeptIds(List.of(user.getDeptId()));
            } else {
                entity.setDeptIds(List.of(0L));
            }
        }

        // 3. 设置分页默认值
        if (entity.getPageNum() == null) entity.setPageNum(1);
        if (entity.getPageSize() == null) entity.setPageSize(10);
        PageHelper.startPage(entity.getPageNum(), entity.getPageSize());

        // 4. 执行查询
        List<MakeApply> list = dao.getList(entity);

        return new PageInfo<>(list);
    }
}
