package com.ruoyi.common.getui;

import com.alibaba.fastjson.JSONObject;
import com.getui.push.v2.sdk.ApiHelper;
import com.getui.push.v2.sdk.common.ApiResult;
import com.getui.push.v2.sdk.dto.CommonEnum;
import com.getui.push.v2.sdk.dto.req.*;
import com.getui.push.v2.sdk.dto.req.message.PushBatchDTO;
import com.getui.push.v2.sdk.dto.req.message.PushChannel;
import com.getui.push.v2.sdk.dto.req.message.PushDTO;
import com.getui.push.v2.sdk.dto.req.message.PushMessage;
import com.getui.push.v2.sdk.dto.req.message.android.AndroidDTO;
import com.getui.push.v2.sdk.dto.req.message.android.GTNotification;
import com.getui.push.v2.sdk.dto.req.message.android.ThirdNotification;
import com.getui.push.v2.sdk.dto.req.message.android.Ups;
import com.getui.push.v2.sdk.dto.req.message.ios.Alert;
import com.getui.push.v2.sdk.dto.req.message.ios.Aps;
import com.getui.push.v2.sdk.dto.req.message.ios.IosDTO;
import com.getui.push.v2.sdk.dto.res.ScheduleTaskDTO;
import com.getui.push.v2.sdk.dto.res.TaskIdDTO;
import com.ruoyi.common.getui.dto.Getui;
import org.junit.jupiter.api.Test;

import java.util.*;

/**
 * create by getui on 2020/8/8
 *
 * <AUTHOR>
 */
public class PushApiUtil {

    PushApi pushApi;
    ApiContext apiContext;
    String cid = "2680993bf9464c7d146a6e9591b3d585";

    public PushApiUtil() {
        apiContext = ApiContext.build();
        apiContext.configuration.setAnalyseStableDomainInterval(500);
//        apiContext.configuration.setOpenCheckHealthDataSwitch(true);
        apiContext.configuration.setCheckHealthInterval(500);
        apiContext.configuration.setOpenAnalyseStableDomainSwitch(false);  //关闭
        ApiHelper apiHelper = ApiHelper.build(apiContext.configuration);
        cid = apiContext.cid;
        pushApi = apiHelper.creatApi(PushApi.class);
    }

    public ApiResult<Map<String, Map<String, String>>> pushToSingleByCid(Getui getui){
        PushDTO<Audience> pushDTO = pushGetui(getui);
        Audience audience = new Audience();
        audience.addCid(getui.getPhoneClientCode());
        pushDTO.setAudience(audience);
        pushDTO.setRequestId(System.currentTimeMillis() + "");
        return pushApi.pushToSingleByCid(pushDTO);
    }

    @Test
    public void pushToSingleByAlias() {
        PushDTO<Audience> pushDTO = pushDTO();
        fullAlias(pushDTO);
        ApiResult<Map<String, Map<String, String>>> apiResult = pushApi.pushToSingleByAlias(pushDTO);
        System.out.println(apiResult);
    }

    @Test
    public void pushBatchByCid(Getui getui) throws Exception{
        PushBatchDTO pushBatchDTO = new PushBatchDTO();
        PushDTO<Audience> pushDTO = pushGetui(getui);
        Audience audience = new Audience();
        audience.addCid(getui.getPhoneClientCode());
        pushDTO.setAudience(audience);
        pushBatchDTO.addPushDTO(pushDTO);
        System.out.println(pushApi.pushBatchByCid(pushBatchDTO));
    }

    @Test
    public void pushBatchByCidOne(Getui getui) throws Exception{
        PushBatchDTO pushBatchDTO = new PushBatchDTO();
        PushDTO<Audience> pushDTO = pushTransmission(getui);
        Audience audience = new Audience();
        audience.addCid(getui.getPhoneClientCode());
        pushDTO.setAudience(audience);
        pushBatchDTO.addPushDTO(pushDTO);
        System.out.println(pushApi.pushBatchByCid(pushBatchDTO));
    }

    @Test
    public void pushBatchByCid1() throws Exception{
        Getui getui = new Getui();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", 1);
        jsonObject.put("type", 1);
        jsonObject.put("title", "穿透");
        String msg = JSONObject.toJSONString(jsonObject);
        getui.setTransmission(msg);
        getui.setText("11111111111111");
        PushBatchDTO pushBatchDTO = new PushBatchDTO();
        PushDTO<Audience> pushDTO = pushTransmission(getui);
        Audience audience = new Audience();
        audience.addCid("2680993bf9464c7d146a6e9591b3d585");
        pushDTO.setAudience(audience);
        pushBatchDTO.addPushDTO(pushDTO);
        System.out.println(pushApi.pushBatchByCid(pushBatchDTO));
    }

    @Test
    public void pushBatchByAlias() {
        PushBatchDTO pushBatchDTO = new PushBatchDTO();
        PushDTO<Audience> pushDTO = pushDTO();
        fullAlias(pushDTO);
        pushBatchDTO.addPushDTO(pushDTO);
        System.out.println(pushApi.pushBatchByAlias(pushBatchDTO));
    }

    @Test
    public ApiResult<TaskIdDTO> createMsg(Getui getui) {
        PushDTO<Audience> pushDTO = pushGetui(getui);
        pushDTO.setGroupName("g-name1");
        return pushApi.createMsg(pushDTO);
    }

    @Test
    public ApiResult<TaskIdDTO> createMsgTransmission(Getui getui) {
        PushDTO<Audience> pushDTO = pushTransmission(getui);
        pushDTO.setGroupName("g-name2");
        return pushApi.createMsg(pushDTO);
    }

    @Test
    public void pushListByCid() {
            String taskId = "taskId";
            AudienceDTO audienceDTO = new AudienceDTO();
            audienceDTO.setTaskid(taskId);
            Audience audience = new Audience();
            audience.addCid("8f340e46e4e2f1244d60c547bc55c6f7");
            audience.addCid("2680993bf9464c7d146a6e9591b3d585");
            audienceDTO.setAudience(audience);
            System.out.println(pushApi.pushListByCid(audienceDTO));
    }

    @Test
    public void pushList(Getui getui) {
        TaskIdDTO data = createMsg(getui).getData();
        String taskId = data.getTaskId();
        AudienceDTO audienceDTO = new AudienceDTO();
        audienceDTO.setTaskid(taskId);
        Audience audience = new Audience();
        List<String> clientIds = getui.getClientIds();
        if(null != clientIds && clientIds.size() > 0){
            for (String cid : clientIds) {
                audience.addCid(cid);
            }
        }
        audienceDTO.setAudience(audience);
        System.out.println(pushApi.pushListByCid(audienceDTO));
    }

    @Test
    public void pushTransmissionList(Getui getui) {
        TaskIdDTO data = createMsgTransmission(getui).getData();
        String taskId = data.getTaskId();
        AudienceDTO audienceDTO = new AudienceDTO();
        audienceDTO.setTaskid(taskId);
        Audience audience = new Audience();
        List<String> clientIds = getui.getClientIds();
        if(null != clientIds && clientIds.size() > 0){
            for (String cid : clientIds) {
                audience.addCid(cid);
            }
        }
        audienceDTO.setAudience(audience);
        System.out.println(pushApi.pushListByCid(audienceDTO));
    }

    @Test
    public void pushListByAlias() {
        String taskId = "taskId";
        AudienceDTO audienceDTO = new AudienceDTO();
        audienceDTO.setTaskid(taskId);
        Audience audience = new Audience();
        String alias = "";
        audience.addAlias(alias);
        audienceDTO.setAudience(audience);
        System.out.println(pushApi.pushListByAlias(audienceDTO));
    }

    @Test
    public void pushAll() {
        PushDTO<String> pushDTO = pushDTOAll();
        System.out.println(pushApi.pushAll(pushDTO));
    }

    @Test
    public void pushByTag() {
        final PushDTO<Audience> pushDTO = pushDTO();
        Audience audience = new Audience();
        final Condition condition = new Condition();
        condition.setOptType(CommonEnum.OptTypeEnum.TYPE_OR.type);
        condition.setKey("portrait");
        condition.addValue("si0901").addValue("025000");
        audience.addCondition(condition);
        pushDTO.setAudience(audience);
        final ApiResult<TaskIdDTO> result = pushApi.pushByTag(pushDTO);
        System.out.println(result);
    }

    @Test
    public void pushByFastCustomTag() {
        final PushDTO<Audience> pushDTO = pushDTO();
        Audience audience = new Audience();
        audience.setFastCustomTag("tag4");
        pushDTO.setAudience(audience);
        final ApiResult<TaskIdDTO> result = pushApi.pushByFastCustomTag(pushDTO);
        System.out.println(result);
    }

    @Test
    public void stopPush() {
        final ApiResult<Void> result = pushApi.stopPush("taskId");
        System.out.println(result);
    }

    @Test
    public void query() {
        final ApiResult<Map<String, ScheduleTaskDTO>> result = pushApi.queryScheduleTask("taskId");
        System.out.println(result);
    }

    @Test
    public void delete() {
        final ApiResult<Void> result = pushApi.deleteScheduleTask("taskId");
        System.out.println(result);
    }

    private PushDTO<Audience> pushDTO() {
        PushDTO<Audience> pushDTO = new PushDTO<Audience>();
        pushDTO.setRequestId(System.currentTimeMillis() + "");
        pushDTO.setGroupName("g-name1");

        Settings settings = new Settings();
        settings.setTtl(3600000);
        Strategy strategy = new Strategy();
        strategy.setSt(1);
        settings.setStrategy(strategy);

        pushDTO.setSettings(settings);

        PushMessage pushMessage = new PushMessage();
        GTNotification notification = new GTNotification();
        // notification.setBigImage("https://url");
        // notification.setLogo("https://url");
        notification.setLogoUrl("https://url");
        notification.setTitle("新消息通知-" + System.currentTimeMillis());
        notification.setBody("新消息通知");
        notification.setChannelId("新消息通知");
        notification.setChannelName("新消息通知");
        notification.setChannelLevel(String.valueOf(CommonEnum.ChannelLevelEnum.LEVEL_THREE.level));
        notification.setClickType(CommonEnum.ClickTypeEnum.TYPE_STARTAPP.type);
        notification.setUrl("https//:www.getui.com");
        notification.setBadgeAddNum("1");

        pushMessage.setNotification(notification);
        pushDTO.setPushMessage(pushMessage);

        PushChannel pushChannel = new PushChannel();
        AndroidDTO androidDTO = new AndroidDTO();
        Ups ups = new Ups();
        ThirdNotification thirdNotification = new ThirdNotification();
        thirdNotification.setClickType(CommonEnum.ClickTypeEnum.TYPE_STARTAPP.type);
        thirdNotification.setTitle("title-" + System.currentTimeMillis());
        thirdNotification.setBody("context");
        ups.setNotification(thirdNotification);


        //设置options 方式一
        ups.addOption("HW","badgeAddNum",3);
        ups.addOption("HW","badgeClass","com.getui.demo.GetuiSdkDemoActivity");
        ups.addOption("OP","app_message_id",11);
        ups.addOption("VV","message_sort",1);
        ups.addOption("XM","sound_uri","android.resource://com.pp.infonew/ring001");
        ups.addOptionAll("channel","新消息通知");

        //设置options 方式二
        Map<String, Map<String,Object>> options = new HashMap<String, Map<String, Object>>();
        Map<String,Object> all = new HashMap<String, Object>();
        all.put("channel","新消息通知");
        options.put("ALL",all);
        Map<String,Object> hw = new HashMap<String, Object>();
        all.put("badgeAddNum",3);
        all.put("badgeClass","com.getui.demo.GetuiSdkDemoActivity");
        options.put("HW",hw);
        ups.setOptions(options);

        androidDTO.setUps(ups);
        pushChannel.setAndroid(androidDTO);

        IosDTO iosDTO = new IosDTO();
        Aps aps = new Aps();
        Alert alert = new Alert();
        alert.setTitle("title-" + System.currentTimeMillis());
        alert.setBody("ios_body");
        aps.setAlert(alert);
        iosDTO.setAps(aps);
        pushChannel.setIos(iosDTO);
        pushDTO.setPushChannel(pushChannel);

        return pushDTO;
    }

    private PushDTO<Audience> pushTransmission(Getui getui) {
        PushDTO<Audience> pushDTO = new PushDTO<Audience>();
        pushDTO.setRequestId(System.currentTimeMillis() + "");
        pushDTO.setGroupName("g-name2");

        Settings settings = new Settings();
        //消息离线时间设置，单位毫秒，-1表示不设离线, -1 ～ 3 * 24 * 3600 * 1000之间
        settings.setTtl(3600000);
        Strategy strategy = new Strategy();
        strategy.setSt(1);
        settings.setStrategy(strategy);

        pushDTO.setSettings(settings);

        PushMessage pushMessage = new PushMessage();
        pushMessage.setTransmission(getui.getTransmission());
        pushDTO.setPushMessage(pushMessage);

        PushChannel pushChannel = new PushChannel();
        AndroidDTO androidDTO = new AndroidDTO();
        Ups ups = new Ups();
        ThirdNotification thirdNotification = new ThirdNotification();
        thirdNotification.setClickType(CommonEnum.ClickTypeEnum.TYPE_STARTAPP.type);
        thirdNotification.setTitle("title-" + System.currentTimeMillis());
        thirdNotification.setBody("context");
        ups.setNotification(thirdNotification);


        //设置options 方式一
        ups.addOption("HW","badgeAddNum",3);
        ups.addOption("HW","badgeClass","com.getui.demo.GetuiSdkDemoActivity");
        ups.addOption("OP","app_message_id",11);
        ups.addOption("VV","message_sort",1);
        ups.addOption("XM","sound_uri","android.resource://com.pp.infonew/ring001");
        ups.addOptionAll("channel","新消息通知");

        //设置options 方式二
        Map<String, Map<String,Object>> options = new HashMap<String, Map<String, Object>>();
        Map<String,Object> all = new HashMap<String, Object>();
        all.put("channel","新消息通知");
        options.put("ALL",all);
        Map<String,Object> hw = new HashMap<String, Object>();
        all.put("badgeAddNum",3);
        all.put("badgeClass","com.getui.demo.GetuiSdkDemoActivity");
        options.put("HW",hw);
        ups.setOptions(options);

        androidDTO.setUps(ups);
        pushChannel.setAndroid(androidDTO);

        IosDTO iosDTO = new IosDTO();
        Aps aps = new Aps();
        Alert alert = new Alert();
        alert.setTitle("title-" + System.currentTimeMillis());
        alert.setBody("ios_body");
        aps.setAlert(alert);
        iosDTO.setAps(aps);
        pushChannel.setIos(iosDTO);
        pushDTO.setPushChannel(pushChannel);

        return pushDTO;
    }

    @Test
    public void pushBatchByCid2() throws Exception{
        PushBatchDTO pushBatchDTO = new PushBatchDTO();
        PushDTO<Audience> pushDTO = pushDTO1();
        Audience audience = new Audience();
        audience.addCid("2680993bf9464c7d146a6e9591b3d585");
        pushDTO.setAudience(audience);
        pushBatchDTO.addPushDTO(pushDTO);
        System.out.println(pushApi.pushBatchByCid(pushBatchDTO));
    }

    private PushDTO<Audience> pushDTO1() {
        PushDTO<Audience> pushDTO = new PushDTO<Audience>();
        pushDTO.setRequestId(System.currentTimeMillis() + "");
        pushDTO.setGroupName("g-name1");

        Settings settings = new Settings();
        settings.setTtl(3600000);
        Strategy strategy = new Strategy();
        strategy.setSt(1);
        settings.setStrategy(strategy);

        pushDTO.setSettings(settings);

        PushMessage pushMessage = new PushMessage();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", 1);
        jsonObject.put("type", 1);
        String msg = JSONObject.toJSONString(jsonObject);
        pushMessage.setTransmission(msg);
        pushDTO.setPushMessage(pushMessage);

        PushChannel pushChannel = new PushChannel();
        AndroidDTO androidDTO = new AndroidDTO();
        Ups ups = new Ups();
        ThirdNotification thirdNotification = new ThirdNotification();
        thirdNotification.setClickType(CommonEnum.ClickTypeEnum.TYPE_STARTAPP.type);
        thirdNotification.setTitle("title-" + System.currentTimeMillis());
        thirdNotification.setBody("context");
        ups.setNotification(thirdNotification);


        //设置options 方式一
        ups.addOption("HW","badgeAddNum",3);
        ups.addOption("HW","badgeClass","com.getui.demo.GetuiSdkDemoActivity");
        ups.addOption("OP","app_message_id",11);
        ups.addOption("VV","message_sort",1);
        ups.addOption("XM","sound_uri","android.resource://com.pp.infonew/ring001");
        ups.addOptionAll("channel","新消息通知");

        //设置options 方式二
        Map<String, Map<String,Object>> options = new HashMap<String, Map<String, Object>>();
        Map<String,Object> all = new HashMap<String, Object>();
        all.put("channel","新消息通知");
        options.put("ALL",all);
        Map<String,Object> hw = new HashMap<String, Object>();
        all.put("badgeAddNum",3);
        all.put("badgeClass","com.getui.demo.GetuiSdkDemoActivity");
        options.put("HW",hw);
        ups.setOptions(options);

        androidDTO.setUps(ups);
        pushChannel.setAndroid(androidDTO);

        IosDTO iosDTO = new IosDTO();
        Aps aps = new Aps();
        Alert alert = new Alert();
        alert.setTitle("title-" + System.currentTimeMillis());
        alert.setBody("ios_body");
        aps.setAlert(alert);
        iosDTO.setAps(aps);
        pushChannel.setIos(iosDTO);
        pushDTO.setPushChannel(pushChannel);

        return pushDTO;
    }

    private PushDTO<Audience> pushGetui(Getui getui) {
        PushDTO<Audience> pushDTO = new PushDTO<Audience>();
        pushDTO.setRequestId(System.currentTimeMillis() + "");
        pushDTO.setGroupName("g-name1");

        Settings settings = new Settings();
        settings.setTtl(3600000);
        Strategy strategy = new Strategy();
        strategy.setSt(1);
        settings.setStrategy(strategy);

        pushDTO.setSettings(settings);

        PushMessage pushMessage = new PushMessage();
        GTNotification notification = new GTNotification();
        // notification.setBigImage("https://url");
        // notification.setLogo("https://url");
        notification.setLogoUrl("https://url");
        notification.setTitle(getui.getTitle());
        notification.setBody(getui.getBody());
        notification.setChannelId("新消息通知");
        notification.setChannelName("新消息通知");
        notification.setChannelLevel(String.valueOf(CommonEnum.ChannelLevelEnum.LEVEL_THREE.level));
        notification.setClickType(CommonEnum.ClickTypeEnum.TYPE_STARTAPP.type);
        notification.setUrl("https//:www.getui.com");
        notification.setBadgeAddNum("1");


        pushMessage.setNotification(notification);
        pushDTO.setPushMessage(pushMessage);

        PushChannel pushChannel = new PushChannel();
        AndroidDTO androidDTO = new AndroidDTO();
        Ups ups = new Ups();
        ThirdNotification thirdNotification = new ThirdNotification();
        thirdNotification.setClickType(CommonEnum.ClickTypeEnum.TYPE_STARTAPP.type);
        thirdNotification.setTitle("title-" + System.currentTimeMillis());
        thirdNotification.setBody(getui.getText());
        ups.setNotification(thirdNotification);


        //设置options 方式一
        ups.addOption("HW","badgeAddNum",3);
        ups.addOption("HW","badgeClass","com.getui.demo.GetuiSdkDemoActivity");
        ups.addOption("OP","app_message_id",11);
        ups.addOption("VV","message_sort",1);
        ups.addOption("XM","sound_uri","android.resource://com.pp.infonew/ring001");
        ups.addOptionAll("channel","新消息通知");

        //设置options 方式二
        Map<String, Map<String,Object>> options = new HashMap<String, Map<String, Object>>();
        Map<String,Object> all = new HashMap<String, Object>();
        all.put("channel","新消息通知");
        options.put("ALL",all);
        Map<String,Object> hw = new HashMap<String, Object>();
        all.put("badgeAddNum",3);
        all.put("badgeClass","com.getui.demo.GetuiSdkDemoActivity");
        options.put("HW",hw);
        ups.setOptions(options);

        androidDTO.setUps(ups);
        pushChannel.setAndroid(androidDTO);

        IosDTO iosDTO = new IosDTO();
        Aps aps = new Aps();
        Alert alert = new Alert();
        alert.setTitle("title-" + System.currentTimeMillis());
        alert.setBody("ios_body");
        aps.setAlert(alert);
        iosDTO.setAps(aps);
        pushChannel.setIos(iosDTO);
        pushDTO.setPushChannel(pushChannel);

        return pushDTO;
    }

    private PushDTO<String> pushDTOAll() {
        PushDTO<String> pushDTO = new PushDTO<String>();
        pushDTO.setRequestId(UUID.randomUUID().toString().substring(0, 16));
        pushDTO.setGroupName("g-name");

        Settings settings = new Settings();
        settings.setTtl(3600000);

        pushDTO.setSettings(settings);
        pushDTO.setAudience("all");

        PushMessage pushMessage = new PushMessage();
        GTNotification notification = new GTNotification();
        notification.setTitle("title" + System.currentTimeMillis());
        notification.setBody("body" + System.currentTimeMillis());
        notification.setClickType(CommonEnum.ClickTypeEnum.TYPE_URL.type);
        notification.setUrl("https//:www.getui.com");
        pushMessage.setNotification(notification);
        pushDTO.setPushMessage(pushMessage);
        return pushDTO;
    }

    private void fullCid(PushDTO<Audience> pushDTO) {
        Audience audience = new Audience();
        //audience.addCid("2680993bf9464c7d146a6e9591b3d585");
        //audience.addCid("3b8c088523fc070aa661b79f88aa0623");
        audience.addCid("05249c01e5333cc757c7afabd55cdf7e");
        pushDTO.setAudience(audience);
    }

    private void fullAlias(PushDTO<Audience> pushDTO) {
        Audience audience = new Audience();
        String alias = "";
        audience.addAlias(alias);
        pushDTO.setAudience(audience);
    }

    public static void main(String[] args) {
        Getui getui = new Getui();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", 1);
        jsonObject.put("type", 1);
        jsonObject.put("title", "穿透");
        String msg = JSONObject.toJSONString(jsonObject);
        List<String> list = new ArrayList<>();
        list.add("2680993bf9464c7d146a6e9591b3d585");
        getui.setClientIds(list);
        getui.setBody(msg);
        getui.setText("11111111111111");
    }

}
