package com.ruoyi.modules.user.entity;


import com.ruoyi.base.entity.BaseEntity;

/**
 * Created by Administrator on 2021-3-13.
 * 用户对应角色
 */
public class SysUserRole extends BaseEntity {

    private String userId;  // 用户ID
    private String roleId;  // 角色ID
    private String managerOrgs; // 分管领导角色 管辖得科室ID  角色ID == 3有效

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getManagerOrgs() {
        return managerOrgs;
    }

    public void setManagerOrgs(String managerOrgs) {
        this.managerOrgs = managerOrgs;
    }
}
