package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

import java.util.List;

/**
 * 驾驶舱重点群体统计
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/23 9:24
 */
@Data
public class BigScreenGroupDetailsVo {

    /**
     * 涉事人员
     */
   private Integer personCount;
    /**
     * 涉事县（市、区）
     */
   private Integer regionNum;

    /**
     * 群体关联事件数
     */
    private Integer eventCount;
    /**
     * 群体特征
     */
   private  String groupCharact;
    /**
     * 基本情况
     */
   private  String baseInfo;
    /**
     * 防控措施
     */
   private  String preventionControlMeasures;
    /**
     * 处置流程步骤，4交办涉稳问题，5化解处置反馈
     */
   private Integer procedureStep;

   private  String groupName;

    List<DisposalProcessEventVo> disposalProcessEventVoList;

}
