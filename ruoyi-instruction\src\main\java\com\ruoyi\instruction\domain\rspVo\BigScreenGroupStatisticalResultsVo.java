package com.ruoyi.instruction.domain.rspVo;

import com.ruoyi.instruction.domain.InstructionGroup;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 驾驶舱重点群体统计
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/23 9:24
 */
@Data
public class BigScreenGroupStatisticalResultsVo {

    private List<InstructionGroup>  instructionGroups;

    /**
     * 重点群体数量
     */
   private Integer groupNum;
    /**
     * 关联事件数量
     */
   private Integer eventNum;
    /**
     * 涉事人员
     */
   private Integer personnelNum;
    /**
     * 涉事县（市、区）
     */
   private Integer regionNum;


    public void setInstructionGroups(List<InstructionGroup> instructionGroups) {
        this.instructionGroups = instructionGroups;
        if (CollectionUtils.isEmpty(instructionGroups)){
            this.groupNum=0;
        }else {
            this.groupNum=instructionGroups.size();
        }
    }
}
