package com.ruoyi.xzzfj.controller;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.xzzfj.domain.vo.ZyyAesReqVo;
import com.ruoyi.xzzfj.domain.zyy.ZyyCallRecords;
import com.ruoyi.xzzfj.service.IZyyCallRecordsService;
import com.ruoyi.xzzfj.util.ZyyAesUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;
import com.ruoyi.common.core.domain.R;

/**
 * 接收中移云话单推送消息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/xzzfj/ZyyCallRecords")
@Slf4j
public class ZyyCallRecordsController  extends BaseController
{
    @Autowired
    private IZyyCallRecordsService zyyCallRecordsService;

    /**
     * 查询接收中移云话单推送消息列表
     */
    @GetMapping("/list")
    public R<PageInfo<ZyyCallRecords>> list(ZyyCallRecords zyyCallRecords)
    {
        startPage();
        PageInfo<ZyyCallRecords> list = new PageInfo<>( zyyCallRecordsService.selectZyyCallRecordsList(zyyCallRecords));
        return R.ok(list);
    }


    /**
     * 获取接收中移云话单推送消息详细信息
     */
    @GetMapping(value = "/getInfo")
    public R<ZyyCallRecords> getInfo( Long id)
    {
        return R.ok(zyyCallRecordsService.selectZyyCallRecordsById(id));
    }

    /**
     * 新增接收中移云话单推送消息
     */
    @PostMapping("/add")
    public R<Integer> add(@RequestBody ZyyCallRecords zyyCallRecords)
    {
        return R.ok(zyyCallRecordsService.insertZyyCallRecords(zyyCallRecords));
    }
    /**
     * 新增接收中移云话单推送消息
     */
    @PostMapping("/add1")
    public R<Integer> add1(@RequestBody ZyyAesReqVo vo )
    {
        System.out.println(vo);
        String decrypt = ZyyAesUtil.decrypt(vo.getParams().getRecord(), "123");
        ZyyCallRecords zyyCallRecords = JSONObject.parseObject(decrypt, ZyyCallRecords.class);
        return R.ok(zyyCallRecordsService.insertZyyCallRecords(zyyCallRecords));
    }

    /**
     * 修改接收中移云话单推送消息
     */
    @PostMapping("/edit")
    public R<Integer> edit(@RequestBody ZyyCallRecords zyyCallRecords)
    {
        return R.ok(zyyCallRecordsService.updateZyyCallRecords(zyyCallRecords));
    }

    /**
     * 删除接收中移云话单推送消息
     */
	@PostMapping("/remove/{ids}")
    public R<Integer> remove(@PathVariable Long[] ids)
    {
        return R.ok(zyyCallRecordsService.deleteZyyCallRecordsByIds(ids));
    }
}
