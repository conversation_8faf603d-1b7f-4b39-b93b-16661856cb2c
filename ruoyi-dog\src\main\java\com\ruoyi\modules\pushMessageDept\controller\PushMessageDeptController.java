package com.ruoyi.modules.pushMessageDept.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.notice.entity.Notice;
import com.ruoyi.modules.notice.service.NoticeService;
import com.ruoyi.modules.pushMessageDept.entity.PushMessageDept;
import com.ruoyi.modules.pushMessageDept.service.PushMessageDeptService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * 工作通知关联机构(push_message_dept)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("pushMessageDept")
public class PushMessageDeptController {
    /**
     * 服务对象
     */
    @Resource
    private PushMessageDeptService service;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("/getPageList")
    public AjaxResult getPageList(PushMessageDept entity) {
        return AjaxResult.success(service.getPageList(entity));
    }

    @RequestMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(PushMessageDept entity) {
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }

    @RequestMapping("/delete")
    public AjaxResult delete(PushMessageDept entity) {
        entity.setDelFlag(2);
        service.delete(entity);
        return AjaxResult.success();
    }

}
