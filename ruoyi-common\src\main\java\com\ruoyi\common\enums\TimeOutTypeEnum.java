package com.ruoyi.common.enums;

/**
 * 超时类型
 */
public enum TimeOutTypeEnum {

    CENTER(1, "指挥中心下派超时"),
    SQUADRONLEADER(3, "组长下派超时"),
    OURFORCASE(4, "出警超时"),
    ARRIVE(5, "到达现场超时"),
    FEEDBACK(6, "反馈超时"),
    APPROVE(9, "审核超时");

    private Integer type;
    private String desc;

    TimeOutTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(int type) {
        for (TimeOutTypeEnum time : TimeOutTypeEnum.values()) {
            if (time.getType() == type) {
                return time.desc;
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
