<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.punish.dao.PunishDao">

    <sql id="columns">
        <trim suffixOverrides=",">
            a.id as id,
            a.type as type,
            a.status as status,
            a.pet_num as petNum,
            a.name as name,
            a.id_card as idCard,
            a.contact_number as contactNumber,
            a.illegal_reason as illegalReason,
            a.illegal_address as illegalAddress,
            a.illegal_time as illegalTime,
            a.regulations_key as regulationsKey,
            a.penalty_amount as penaltyAmount,
            a.remarks as remarks,
            a.processing_record as processingRecord,
            a.del_flag as delFlag,
            a.create_date as createDate,
            a.create_by as createBy,
            a.update_date as updateDate,
            a.update_by as updateBy,
            (select real_name from sys_user su where su.id=a.create_by) as createName,
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.punish.entity.Punish"
            resultType="com.ruoyi.modules.punish.entity.Punish">
        select
        <include refid="columns"/>

        from punish a
        where a.id =#{id}
    </select>


    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.punish.entity.Punish"
            resultType="com.ruoyi.modules.punish.entity.Punish">
        select
        <include refid="columns"/>
        ,u.dept_name as deptName
        from punish a
        left join sys_user u on u.id=a.create_by
        where a.del_flag =1
        <if test="deptName!=null and deptName!=''">
            and u.dept_name like concat('%',#{deptName},'%')
        </if>
        <if test="deptId!=null and deptId!=''">
            and u.dept_id =#{deptId}
        </if>
        <if test="keyword!=null and keyword!=''">
            and (a.name like concat('%',#{keyword},'%') or
            a.id_card like concat('%',#{keyword},'%') or
            a.contact_number like concat('%',#{keyword},'%') or
            a.illegal_reason like concat('%',#{keyword},'%') or
            a.illegal_address like concat('%',#{keyword},'%')or
            a.pet_num like concat('%',#{keyword},'%')or
            a.remarks like concat('%',#{keyword},'%') )
        </if>
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        <if test="petNum != null and petNum != ''">
            and a.pet_num = #{petNum}
        </if>
        <if test="name != null and name != ''">
            and a.name = #{name}
        </if>
        <if test="idCard != null and idCard != ''">
            and a.id_card = #{idCard}
        </if>
        <if test="contactNumber != null and contactNumber != ''">
            and a.contact_number = #{contactNumber}
        </if>
        <if test="illegalReason != null and illegalReason != ''">
            and a.illegal_reason = #{illegalReason}
        </if>
        <if test="illegalAddress != null and illegalAddress != ''">
            and a.illegal_address = #{illegalAddress}
        </if>
        <if test="illegalTime != null">
            and a.illegal_time = #{illegalTime}
        </if>
        <if test="regulationsKey != null and regulationsKey != ''">
            and a.regulations_key = #{regulationsKey}
        </if>
        <if test="penaltyAmount != null">
            and a.penalty_amount = #{penaltyAmount}
        </if>
        <if test="remarks != null and remarks != ''">
            and a.remarks = #{remarks}
        </if>
        <if test="delFlag != null">
            and a.del_flag = #{delFlag}
        </if>
        <if test="createDate != null">
            and a.create_date = #{createDate}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by = #{createBy}
        </if>
        <if test="updateDate != null">
            and a.update_date = #{updateDate}
        </if>
        <if test="updateBy != null and updateBy != ''">
            and a.update_by = #{updateBy}
        </if>
        order by a.create_date desc
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.punish.entity.Punish">
        insert into punish(id, type, status, pet_num, name, id_card, contact_number, illegal_reason, illegal_address,
                           illegal_time,
                           regulations_key, penalty_amount, remarks, processing_record, del_flag, create_date,
                           create_by, update_date,
                           update_by)
        values (#{id}, #{type}, #{status}, #{petNum}, #{name}, #{idCard}, #{contactNumber}, #{illegalReason},
                #{illegalAddress},
                #{illegalTime}, #{regulationsKey}, #{penaltyAmount}, #{remarks}, #{processingRecord}, #{delFlag},
                #{createDate}, #{createBy},
                #{updateDate}, #{updateBy})
    </insert>
    <update id="updateByEntity" parameterType="com.ruoyi.modules.punish.entity.Punish">
        update punish set
        <trim suffixOverrides=",">
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="petNum != null and petNum != ''">
                pet_num = #{petNum},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="idCard != null and idCard != ''">
                id_card = #{idCard},
            </if>
            <if test="contactNumber != null and contactNumber != ''">
                contact_number = #{contactNumber},
            </if>
            <if test="illegalReason != null and illegalReason != ''">
                illegal_reason = #{illegalReason},
            </if>
            <if test="illegalAddress != null and illegalAddress != ''">
                illegal_address = #{illegalAddress},
            </if>
            <if test="illegalTime != null">
                illegal_time = #{illegalTime},
            </if>
            <if test="regulationsKey != null and regulationsKey != ''">
                regulations_key = #{regulationsKey},
            </if>
            <if test="penaltyAmount != null">
                penalty_amount = #{penaltyAmount},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
            <if test="processingRecord != null and processingRecord != ''">
                processing_record = #{processingRecord},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
        </trim>
        where id = #{id}
    </update>
    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.punish.entity.Punish">
        update punish set
        <trim suffixOverrides=",">
            type = #{type},
            status = #{status},
            pet_num = #{petNum},
            name = #{name},
            id_card = #{idCard},
            contact_number = #{contactNumber},
            illegal_reason = #{illegalReason},
            illegal_address = #{illegalAddress},
            illegal_time = #{illegalTime},
            regulations_key = #{regulationsKey},
            penalty_amount = #{penaltyAmount},
            remarks = #{remarks},
            processing_record = #{processingRecord},
            del_flag = #{delFlag},
            create_date = #{createDate},
            create_by = #{createBy},
            update_date = #{updateDate},
            update_by = #{updateBy},
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.punish.entity.Punish">
        UPDATE punish
        SET del_flag=#{delFlag},
            update_date = #{updateDate},
            update_by = #{updateBy}
        where id = #{id}
    </delete>
<select id="halfYearChar" resultType="java.util.HashMap">
    SELECT
        IFNULL(count(i.id), 0) AS num,
        substr(CONVERT (t2.year_month_str, CHAR),6)	 mm
    FROM
        (
            SELECT
                @rownum :=@rownum + 1 AS num,
			date_format(
				DATE_SUB(now(), INTERVAL @rownum MONTH),
				'%Y/%m'
			) AS year_month_str
            FROM
                (SELECT @rownum := - 1) AS r_init,
                ( SELECT c.id FROM sys_dict c LIMIT 6 ) AS c_init ) t2
            LEFT JOIN punish AS i ON (
                    CONCAT( DATE_FORMAT(i.create_date, '%Y'), '/', DATE_FORMAT(i.create_date, '%m') ) = t2.year_month_str
                AND i.del_flag = 1
            )
    GROUP BY
        t2.year_month_str
</select>
</mapper>
