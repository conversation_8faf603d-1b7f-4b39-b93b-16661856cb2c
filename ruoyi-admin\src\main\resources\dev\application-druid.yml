# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
#            master:
#                url: **********************************************************************************************************************************************************************************
#                username: szjh_2024
#                password: w6t$Tyvg^Y$qY8C
            # 主库数据源
            master:
                url: **********************************************************************************************************************************************************************************
                username: szjh_2024
                password: w6t$Tyvg^Y$qY8C
#                driverClassName: com.mysql.cj.jdbc.Driver
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: true
                url: *****************************************************************************************************************************************************************************************
                username: szjh_2024
                password: w6t$Tyvg^Y$qY8C
            # 从库数据源
            zqzf:
                # 从数据源开关/默认关闭
                enabled: true
                url: ************************************************************************************************************************************************************************************
                username: szjh_2024
                password: w6t$Tyvg^Y$qY8C
            #案件回访(大脑)
            ajhf:
                # 从数据源开关/默认关闭
                enabled: false
                url: ******************************************************************************************************************************************************
                username: xzzfj_admin1
                password: 9!CJb^cEaMNT&Z
            #大脑
            csdn:
                # 从数据源开关/默认关闭
                enabled: false
                url: ******************************************************************************************************************************************************
                username: readonly_ygf
                password: uT7!yY7@hH3
            # 从库数据源
            shlqzk:
                # 从数据源开关/默认关闭
                enabled: false
                url: ************************************************************************************************************************************************************************************
                username: 1
                password: 1
            # 牛皮藓呼停呼死数据源
            npx:
                # 从数据源开关/默认关闭
                enabled: false
                url: ************************************************************************************************************************
                username: sa
                password: 123456
                driverClassName: net.sourceforge.jtds.jdbc.Driver
#            # 主库数据源
#            master:
#                url: **********************************************************************************************************************************************************************************
#                username: szjh_2024
#                password: w6t$Tyvg^Y$qY8C
#            # 从库数据源
#            slave:
#                # 从数据源开关/默认关闭
#                enabled: true
#                url: *****************************************************************************************************************************************************************************************
#                username: szjh_2024
#                password: w6t$Tyvg^Y$qY8C
#            # 从库数据源
#            zqzf:
#                # 从数据源开关/默认关闭
#                enabled: true
#                url: ************************************************************************************************************************************************************************************
#                username: szjh_2024
#                password: w6t$Tyvg^Y$qY8C
#            # 牛皮藓呼停呼死数据源
#            npx:
#                # 从数据源开关/默认关闭
#                enabled: false
#                url: *************************************************************************************************************************************************
#                username: szjh_2024
#                password: w6t$Tyvg^Y$qY8C
#                driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
            # 初始连接数
            initialSize: 10
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: false
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: sdfgfghf
                login-password: dgfhghdfgd234#DFDG#
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
