package com.ruoyi.common.enums;

/**
 * 号牌种类
 *
 * <AUTHOR>
 */
public enum CarNoType {

    DCLX("01", "大型汽车"),
    XCLX("02", "小型汽车"),
    SGQC("03", "使馆汽车"),
    LGQC("04", "领馆汽车"),
    JWQC("05", "境外汽车"),
    WJQC("06", "外籍汽车"),
    LSLMTC("07", "两、三轮摩托车"),
    QBMTC("08", "轻便摩托车"),
    SGMTC("09", "使馆摩托车"),
    LGMTC("10", "领馆摩托车"),
    JWMTC("11", "境外摩托车"),
    WJMTC("12", "外籍摩托车"),
    NYYSC("13", "农用运输车"),
    TLJ("14", "拖拉机"),
    GC("15", "挂车"),
    JLQC("16", "教练汽车"),
    JLMTC("17", "教练摩托车"),
    SYQC("18", "试验汽车"),
    SYMTC("19", "试验摩托车"),
    LSRJQC("20", "临时入境汽车"),
    LSRJMTC("21", "临时入境摩托车"),
    LSXSC("22", "临时行驶车"),
    GAJC("23", "公安警车"),
    QTLX("24", "其他类型"),
    YNYHP("25", "原农机号牌"),
    XGCRJC("26", "香港出入境车"),
    AMCRJC("27", "澳门出入境车"),
    WJC("31", "武警车"),
    JDHP("32", "军队号牌"),
    DXXNYQC("51", "大型新能源汽车"),
    XXXNYQC("52", "小型新能源汽车");

    private final String code;
    private final String info;

    CarNoType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
