package com.ruoyi.modules.taskData;

import com.ruoyi.modules.certificates.dao.PetCertificatesDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

@Component
public class pushCertDataTsk {

    @Autowired
    private PetCertificatesDao petCertificatesDao;

    // 该任务每5分钟执行一次
    // @Scheduled(cron = "0 0/5 * * * ?")
    public void certDataTsk() throws Exception {

        System.out.println("执行定时推送犬类登记信息任务");

        Connection connection = null;
        PreparedStatement preparedStatement = null;

        String databaseURL = "*******************************************************************************************************************************************************************************************";
        String user = "ytj_qzk_xzzf";
        String password = "ytj_qzk_xzzf123";

        try {
            connection = DriverManager.getConnection(databaseURL, user, password);
            // 关闭自动提交事务，改为手动提交
            connection.setAutoCommit(false);
            System.out.println("===== 获取最大的cert_date ====");
            String sql = "SELECT MAX(cert_date) AS max_cert_date FROM biz_0700_jhsyqgl_qzdjxx";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            String formattedDate = "";

            if(rs.next()) {
                java.sql.Timestamp maxTimestamp = rs.getTimestamp("max_cert_date");
                // 格式化日期时间输出
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                formattedDate = sdf.format(maxTimestamp);
                System.out.println("最大的登记日期时间是: " + formattedDate);
            } else {
                System.out.println("没有记录在表biz_0700_jhsyqgl_qzdjxx中。");
            }

            // 查询是否有新数据需要上传
            List<Map<String, Object>> list = petCertificatesDao.findViewData(formattedDate);
            if (list == null && list.size() == 0) {
                System.out.println("没有数据");
                return;
            }

            // 上传数据
            System.out.println("===== 开始插入数据 =====");
            long startTime = System.currentTimeMillis();
            String sqlInsert = "INSERT INTO biz_0700_jhsyqgl_qzdjxx (id, owner_name, pet_id_card, pet_num, status, cert_date, end_date, dept_name) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            preparedStatement = connection.prepareStatement(sqlInsert);
            for (Map<String, Object> map : list) {
                System.out.println(map.toString());
                String id = (String) map.get("id");
                String ownerName = (String) map.get("owner_name");
                String petIdCart = (String) map.get("pet_id_cart");
                String petNum = (String) map.get("pet_num");
                String status = (String) map.get("status");
                Date certDate = (Date) map.get("cert_date");
                java.sql.Timestamp certTime = new java.sql.Timestamp(certDate.getTime());
                Date endDate = (Date) map.get("end_date");
                java.sql.Timestamp endTime = new java.sql.Timestamp(endDate.getTime());
                String deptName = (String) map.get("dept_name");
                preparedStatement.setString(1, id);
                preparedStatement.setString(2, ownerName);
                preparedStatement.setString(3, petIdCart);
                preparedStatement.setString(4, petNum);
                preparedStatement.setString(5, status);
                preparedStatement.setTimestamp(6, certTime);
                preparedStatement.setTimestamp(7, endTime);
                preparedStatement.setString(8, deptName);

                // 添加到批处理中
                preparedStatement.addBatch();
            }
            preparedStatement.executeBatch();
            connection.commit();
            long spendTime = System.currentTimeMillis() - startTime;
            System.out.println("耗时：" + spendTime + "毫秒");
        } catch (SQLException e) {
            System.out.println("Error: " + e.getMessage());
        } finally {
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                    System.out.println("关闭连接");
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }

            if (connection != null) {
                try {
                    connection.close();
                    System.out.println("关闭连接");
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
