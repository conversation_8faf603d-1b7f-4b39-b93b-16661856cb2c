package com.ruoyi.common.enums;

/**
 * 案件状态
 */
public enum CheckStatusEnum {

    STAY_HANDLE("1", "待处理"),
    GROUP("2", "推送班长"),
    MEMBER("3", "推送组员"),
    FINISH("9", "已办结");

    private String type;
    private String desc;

    CheckStatusEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
