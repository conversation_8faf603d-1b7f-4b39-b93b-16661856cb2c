<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.brand.dao.PetBrandApplyDao">
    <sql id="apply">
        a
        .
        id
        as "id",
a.type as "type",
        a.user_id as "userId",
        a.qualifi_id as "qualifiId",
        a.area as "area",
        a.street as "street",
        a.county as "county",
        a.brand_city as "brandCity",
        a.brand_num as "brandNum",
        a.qualifi_address as "qualifiAddress",
a.payment_voucher as "paymentVoucher",
a.dog_codes as "dogCodes",
a.courier_name as "courierName",
        a.courier_number as "courierNumber",
        a.remarks as "remarks",
        a.status as "status",a.create_date as "createDate"
    </sql>

  <!--  <select id="unitStatistics" parameterType="com.ruoyi.modules.brand.entity.PetBrandApply"
            resultType="java.util.HashMap">
        select * from ( select county,type,qualifi_id,user_id
        , (select IFNULL( sum(brand_num),0) from pet_brand_apply where county=a.county and type is not null and
        `status`=7 and qualifi_id=a.qualifi_id
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ) as "created"
        , (select count(id) from pet_brand b where b.area = a.county and b.brand_com=a.qualifi_id and b.is_use = 2
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(b.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(b.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ) as "notUse"
        , (select count(id) from pet_brand b where b.area = a.county and b.brand_com=a.qualifi_id and b.is_use = 1
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(b.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(b.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ) as "inUse"
        ,(select count(id) from pet_brand b where b.area = a.county and b.is_cancellation = 2 and off_com=a.user_id
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(b.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(b.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ) as "yzx"
        ,(select dept_name from sys_dept where id=county) as "countyName"

        ,(case when type=1 then (select name from qualifi where id=qualifi_id)
        when type=2||type=3 then (select real_name from sys_user where id=user_id) else '' end) as "name"
        from pet_brand_apply a
        where del_flag = 1 and county=#{county}
        group by a.user_id
        )res where 1=1
        <if test="qualifiName!=null and qualifiName!=''">
            and res.name like concat('%',#{qualifiName},'%')
        </if>
        <if test="sortStr!=null and sortStr!=''">
            ${sortStr}
        </if>
    </select>-->
    <!--只查询角色包含执法窗口、执法审批、宠物医院的数据-->
    <select id="unitStatistics" parameterType="com.ruoyi.modules.brand.entity.PetBrandApply"
            resultType="java.util.HashMap">
        select a.*
        ,(select count(id) FROM pet_brand where area=a.county and brand_com=a.qualifi_id and is_use=2) notUse
        ,(select count(id) FROM pet_brand where area=a.county and brand_com=a.qualifi_id and is_use=1) inUse
        ,IFNULL((select SUM(brand_num) from pet_brand_apply where del_flag=1 and `status`=10 and ((type=1 and qualifi_id=a.qualifi_id)or (type=2 and user_id=a.qualifi_id) )),0) as applyCount

        ,(select count(id) from pet_brand b where b.area = a.county and b.is_cancellation = 2 and off_com=a.id) yzx
        ,(select dept_name from sys_dept where id=a.county) countyName
        from(
        select u.dog_user_id id,u.dept_id county,u.user_type
        ,(case WHEN q.id is null then u.dog_user_id else q.id end) as qualifi_id
        ,(case WHEN q.id is null then (select u.nick_name from sys_user where id=u.dog_user_id) else q.name end) name
        from sys_user u
        left join qualifi q on q.account=u.dog_user_id
        where u.dept_id in (select id from sys_dept where id=#{county} or parent_id=#{county})
        and u.del_flag=1
--         and u.id in (select user_id from sys_user_role
--         where role_id
--         in('13198e269824405cb0eac2ee0038d789','8c1e3aa5cc2e42d0a9edcb2c86aec7c1','f373083354374a12a5ead6db09daf1dc'))
        )
        a where 1=1
        <if test="qualifiName!=null and qualifiName!=''">
            and name like concat('%',#{qualifiName},'%')
        </if>
        <if test="sortStr!=null and sortStr!=''">
            ${sortStr}
        </if>
    </select>
    <select id="countyStatistics" parameterType="com.ruoyi.modules.brand.entity.PetBrandApply"
            resultType="java.util.HashMap">
        select county
        , (select IFNULL( sum(brand_num),0) from pet_brand_apply where county=a.county and type is not null and
        `status`=7
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(b.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(b.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ) as "created"
        , (select count(id) from pet_brand b where b.area = a.county and b.is_use = 2
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(b.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(b.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ) as "notUse"
        , (select count(id) from pet_brand b where b.area = a.county and b.is_use = 1
        <if test="bTime!=null and eTime!=null">
            and DATE_FORMAT(b.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(b.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        ) as "inUse"
        , (select dept_name from sys_dept where id = county) as "countyName"
        from pet_brand_apply a
        where del_flag = 1
        and status in (4, 6, 7)

        <if test="county!=null and county!=''">
            and a.county=#{county}
        </if>
        group by a.county
        <if test="sortStr!=null and sortStr!=''">
            ${sortStr}
        </if>
    </select>
    <select id="getManufacturerPageList" parameterType="com.ruoyi.modules.brand.entity.PetBrandApply"
            resultType="java.util.HashMap">
        select a.county
        ,sum(a.brand_num) mergeBrandNum
        ,a.`status`
        ,(select send_address from sys_dept where id=a.county) sendAddress
        ,a.payment_voucher paymentVoucher
        ,count(a.id) mergeNum
        ,(select DATE_FORMAT(create_date,'%Y-%m-%d') from pet_audit_records where del_flag=1 and apply_id=a.id order by create_date desc limit
        1) as "createDate"
        ,a.courier_name as "courierName"
        ,a.courier_number as "courierNumber"
        from pet_brand_apply a
        where a.del_flag = 1 and a.status in (4,6,7)
        <if test="qualifiName != null and qualifiName != ''">
            and a.qualifi_id in (select id from qualifi where name LIKE concat('%', #{qualifiName}, '%'))
        </if>
        <if test="userRealName!=null and userRealName!=''">
            and (a.user_id in (select id from sys_user where del_flag=1 and real_name like
            concat('%',#{userRealName},'%'))
            or  a.qualifi_id in (select id from qualifi where name LIKE concat('%',#{userRealName}, '%')))
        </if>
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="county != null and county != ''">
            and a.county = #{county}
        </if>
        <if test="area != null and area != ''">
            and a.area = #{area}
        </if>
        <if test="userId != null and userId != ''">
            and a.user_id = #{userId}
        </if>
        <if test="qualifiId != null and qualifiId != ''">
            and a.qualifi_id = #{qualifiId}
        </if>
        <if test="brandNum != null and brandNum != ''">
            and a.brand_num = #{brandNum}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        group by a.payment_voucher order by mergeNum desc,a.status, a.create_date desc
    </select>

    <select id="getMergeList" parameterType="com.ruoyi.modules.brand.entity.PetBrandApply" resultType="java.util.HashMap">
        select a.county
        ,sum(a.brand_num) mergeBrandNum
        ,a.`status`
        ,(select send_address from sys_dept where id=a.county) sendAddress
        ,a.payment_voucher paymentVoucher
        ,count(a.id) mergeNum
        from pet_brand_apply a
        where a.del_flag = 1 and a.payment_voucher is not null and a.payment_voucher!='' and a.status in (30,4,5,6,7)
        <if test="qualifiName != null and qualifiName != ''">
            and a.qualifi_id in (select id from qualifi where name LIKE concat('%', #{qualifiName}, '%'))
        </if>
        <if test="brandNum != null and brandNum != ''">
            and a.brand_num = #{brandNum}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        group by a.payment_voucher order by mergeNum desc,a.status
    </select>
    <select id="getList" parameterType="com.ruoyi.modules.brand.entity.PetBrandApply"
            resultType="com.ruoyi.modules.brand.entity.PetBrandApply">
        select
        a.id as "id",
        a.type as "type",
        a.user_id as "userId",
        a.qualifi_id as "qualifiId",
        a.area as "area",
        a.street as "street",
        a.county as "county",
        a.brand_city as "brandCity",
        a.brand_num as "brandNum",
        a.qualifi_address as "qualifiAddress",
        a.payment_voucher as "paymentVoucher",
        a.dog_codes as "dogCodes",
        a.courier_name as "courierName",
        a.courier_number as "courierNumber",
        a.remarks as "remarks",
        a.status as "status",
        a.create_date as "createDate",
        su.nick_name as "userRealName",
--         a.brand_num as 'notUsedNum',
        q.name as 'qualifiName',
        q.tel as 'tel',
        (case WHEN a.type=1 then ( select count(id) from pet_brand where is_use=2 and is_receipt=2 and a.del_flag=1 and
        brand_com=a.qualifi_id )
        else (select count(id) from pet_brand where is_use=2 and is_receipt=2 and a.del_flag=1 and area=a.county and brand_com is null)
        end) as notUsedNum
        from pet_brand_apply a
        left join sys_user su on a.user_id = su.dog_user_id
        left join qualifi q on a.qualifi_id = q.id
        where a.del_flag = 1
        and a.qualifi_id is not null
        and a.qualifi_id != ''
        <if test="bTime!=null and eTime !=null">
            and DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{bTime},'%Y-%m-%d')
            AND DATE_FORMAT(a.create_date,'%Y-%m-%d')&lt;=DATE_FORMAT(#{eTime},'%Y-%m-%d')
        </if>
        <if test="paymentVoucher!=null and paymentVoucher!=''">
            and a.payment_voucher=#{paymentVoucher}
        </if>
        <if test="queryStatus!=null and queryStatus=='10'">
            and a.status in (1,2)
        </if>
        <if test="queryStatus!=null and queryStatus=='20'">
            and a.status not in (1,2)
        </if>
        <if test="qualifiName != null and qualifiName != ''">
            and a.qualifi_id in (select id from qualifi where name LIKE concat('%', #{qualifiName}, '%'))
        </if>
        <if test="userRealName!=null and userRealName!=''">
            and ((su.nick_name like concat('%',#{userRealName}, '%'))
            or  a.qualifi_id in (select id from qualifi where name LIKE concat('%',#{userRealName}, '%')))
        </if>
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="county != null and county != ''">
            and a.county = #{county}
        </if>
        <if test="area != null and area != ''">
            and a.area = #{area}
        </if>
        <if test="userId != null and userId != ''">
            and a.user_id = #{userId}
        </if>
        <if test="qualifiId != null and qualifiId != ''">
            and a.qualifi_id = #{qualifiId}
        </if>
        <if test="brandNum != null and brandNum != ''">
            and a.brand_num = #{brandNum}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by = #{createBy}
        </if>
        <if test="upStatus != null">
            and a.status <![CDATA[ >= ]]> #{upStatus,jdbcType=INTEGER}
        </if>
        order by a.create_date desc
    </select>

    <select id="getById" resultType="com.ruoyi.modules.brand.entity.PetBrandApply"
            parameterType="com.ruoyi.modules.brand.entity.PetBrandApply">
<!--        select-->
<!--        <include refid="apply"/>-->
<!--        ,SUBSTR((select dept_name from sys_dept where del_flag=1 and id =a.county)FROM 1 FOR 2) as countyName-->
<!--        ,(select nick_name from sys_user where del_flag=1 and id =a.user_id) as "userRealName"-->
<!--        from pet_brand_apply a-->
<!--        where a.id = #{id}-->
        select
        a.id as "id",
        a.type as "type",
        a.user_id as "userId",
        a.qualifi_id as "qualifiId",
        a.area as "area",
        a.street as "street",
        a.county as "county",
        a.brand_city as "brandCity",
        a.brand_num as "brandNum",
        a.qualifi_address as "qualifiAddress",
        a.payment_voucher as "paymentVoucher",
        a.dog_codes as "dogCodes",
        a.courier_name as "courierName",
        a.courier_number as "courierNumber",
        a.remarks as "remarks",
        a.status as "status",
        a.create_date as "createDate",
        su.nick_name as "userRealName",
        a.brand_num as 'notUsedNum',
        q.name as 'qualifiName',
        q.tel as 'tel'
        <!--        (case WHEN a.type=1 then ( select count(id) from pet_brand where is_use=2 and is_receipt=2 and a.del_flag=1 and-->
        <!--        brand_com=a.qualifi_id )-->
        <!--        else (select count(id) from pet_brand where is_use=2 and is_receipt=2 and a.del_flag=1 and area=a.county and brand_com is null)-->
        <!--        end) as notUsedNum-->
        from pet_brand_apply a
        left join sys_user su on a.user_id = su.dog_user_id
        left join qualifi q on a.qualifi_id = q.id
        where a.id = #{id,jdbcType=VARCHAR}
    </select>

    <insert id="insert" parameterType="com.ruoyi.modules.brand.entity.PetBrandApply">
        insert into pet_brand_apply
        (id, type, user_id, qualifi_id,
         area, street, county,
         brand_num, status, qualifi_address, payment_voucher, dog_codes,
         remarks, create_date, create_by,
         del_flag)
        values (#{id}, #{type}, #{userId}, #{qualifiId},
                #{area}, #{street}, #{county},
                #{brandNum}, #{status}, #{qualifiAddress}, #{paymentVoucher}, #{dogCodes},
                #{remarks}, #{createDate}, #{createBy},
                #{delFlag})
    </insert>
    <update id="update" parameterType="com.ruoyi.modules.brand.entity.PetBrandApply">
        update pet_brand_apply set
        <trim suffixOverrides=",">
            <if test="courierName != null and courierName != ''">
                courier_name = #{courierName},
            </if>
            <if test="courierNumber != null and courierNumber != ''">
                courier_number = #{courierNumber},
            </if>
            <if test="dogCodes != null and dogCodes != ''">
                dog_codes = #{dogCodes},
            </if>
            <if test="brandNum != null and brandNum != ''">
                brand_num = #{brandNum},
            </if>
            <if test="paymentVoucher != null">
                payment_voucher = #{paymentVoucher},
            </if>
            <if test="qualifiAddress != null and qualifiAddress != ''">
                qualifi_address = #{qualifiAddress},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
            <if test="area != null and area != ''">
                area = #{area},
            </if>
            <if test="street != null and street != ''">
                street = #{street},
            </if>
            <if test="county != null and county != ''">
                county = #{county},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate}
            </if>
        </trim>
        where id = #{id}
    </update>
    <select id="getNotDistributionNum" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(id)
        from pet_brand
        where is_use = 2
          and area = #{area}
          and brand_city is null
    </select>
    <delete id="delete" parameterType="com.ruoyi.modules.brand.entity.PetBrandApply">
        update pet_brand_apply
        set del_flag= #{delFlag}
        where id = #{id}
    </delete>

    <select id="getUnUsedBrandByQualifiId" resultType="java.lang.String">
        select count(id) from pet_brand
        where is_use=2 and is_receipt=2
        and brand_com = #{qualifiId,jdbcType=VARCHAR}


        <!--        (case WHEN a.type=1 then (  )-->
        <!--        else () as notUsedNum-->
    </select>

    <select id="getUnUsedBrandByCounty" resultType="java.lang.String">
        select count(id) from pet_brand
        where is_use=2
          and is_receipt=2
          and area=#{county,jdbcType=VARCHAR}
          and brand_com is null
    </select>
</mapper>
