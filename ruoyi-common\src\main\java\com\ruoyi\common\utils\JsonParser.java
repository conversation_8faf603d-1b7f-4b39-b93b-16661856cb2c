package com.ruoyi.common.utils;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public final class JsonParser {

    private static final ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        objectMapper.configure(com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static <T> T parseJson(String json) {
        try {
            return objectMapper.readValue(json, new TypeReference<T>() {
            });
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("json格式错误");
        }
    }


//    public static final <T> List<T> parseArray(String text, Class<T> clazz) {
//
//        return ;
//    }

    public static List<Integer> stringParseIntList(String json) {
        List<String> stringList = Arrays.asList(json.split(","));
        List<Integer> list = new ArrayList<>(stringList.size());
        try {
            for (String s : stringList) {
                list.add(Integer.parseInt(s));
            }
        } catch (NumberFormatException e) {
            throw new RuntimeException("数字格式错误");
        }
        return list;
    }

    public static String writeJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            throw new RuntimeException("json生成失败");
        }
    }

    /**
     * <b>标题：从json数组的字符串中解析domain[类型为T]的列表</b> <br>
     * 2015-5-12 上午9:15:18 <br>
     * 说明：
     *
     * @param text
     * @return
     */
    public static <T> List<T> parseJsonArray(String text, Class<T> clazz) {
        List<T> list = new ArrayList<T>(JSONArray.parseArray(text, clazz));
        return list;
    }

    public static void main(String[] args) {
        String json = "[\n" +
                "{'flowLineRelaCardId':'6','fieldId':'4','fieldName':'新建属性','fieldWay':'三行','options':'前端存储方式','fieldOrder ':'1','must ':'1'},\n" +
                "{'flowLineRelaCardId':'6','fieldId':'5','fieldName':'新建属性','fieldWay':'三行','options':'前端存储方式','fieldOrder ':'1','must ':'1'},\n" +
                "{'flowLineRelaCardId':'6','fieldId':'6','fieldName':'新建属性','fieldWay':'三行','options':'前端存储方式','fieldOrder ':'1','must ':'1'},\n" +
                "{'flowLineRelaCardId':'6','fieldId':'7','fieldName':'新建属性','fieldWay':'三行','options':'前端存储方式','fieldOrder ':'1','must ':'1'},\n" +
                "{'flowLineRelaCardId':'6','fieldId':'3','fieldName':'新建属性','fieldWay':'三行','options':'前端存储方式','fieldOrder ':'1','must ':'1'}\n" +
                "]";
        parseJson(json);
    }

}
