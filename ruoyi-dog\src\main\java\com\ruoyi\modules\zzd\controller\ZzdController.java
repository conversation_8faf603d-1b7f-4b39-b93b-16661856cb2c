package com.ruoyi.modules.zzd.controller;


import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.brand.entity.PetBrand;
import com.ruoyi.modules.brand.service.PetBrandService;
import com.ruoyi.modules.sysLog.entity.SysLog;
import com.ruoyi.modules.sysLog.service.SysLogService;
import com.ruoyi.modules.user.entity.SysUser;
import com.ruoyi.modules.user.service.SysUserService;
import com.ruoyi.modules.zzd.dao.SysUserZzdDao;
import com.ruoyi.modules.zzd.entity.SysUserZzd;
import com.ruoyi.modules.zzd.service.SysUserZzdService;
import com.ruoyi.util.IdGen;
import com.ruoyi.util.QRCode;
import com.ruoyi.util.UserCache;
import com.ruoyi.util.UserMapCache;
import com.ruoyi.util.zzd.ZzdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Value;

import jakarta.servlet.http.HttpServletRequest;
import java.awt.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/31 9:20
 */
@RestController
@RequestMapping("/zzd")
public class ZzdController {

    @Autowired
    public SysUserService sysUserService ;
    @Autowired
    private SysUserZzdService sysUserZzdService;
    @Autowired
    public SysLogService sysLogService ;
    @Autowired
    private PetBrandService petBrandService;
    @Value("${petBrandServiceUrl}")
    private String petBrandServiceUrl;
    @RequestMapping("/getZzdInfoByauthCode")
    public AjaxResult getZzdInfoByauthCode(String authCode, HttpServletRequest request) {
        System.out.println(authCode);
        JSONObject userInfo = ZzdUtil.getZzdInfoByauthCode(authCode);
        SysUserZzd sysUserZzd = new SysUserZzd();
        if (userInfo == null || userInfo.getString("employeeCode") == null ) {
            System.out.println(1);
            return AjaxResult.error("用户不存在");
        }
        sysUserZzd.setEmployeeCode(userInfo.getString("employeeCode"));
        List<SysUserZzd> zzds = sysUserZzdService.getList(sysUserZzd);
        if (zzds.size() == 0) {
            insertSysUserzzd();
            System.out.println(2);
            return AjaxResult.error("用户不存在");
        }
        SysUser sysUser = userLogin(zzds.get(0).getUserId());
        sysUser.setUserId(userInfo.getString("accountId"));
        sysUser.setNickName(userInfo.getString("username"));
        if (sysUser == null) {
            System.out.println(3);
            return AjaxResult.error("用户不存在");
        }
        //    登录日志
        SysLog log=new SysLog();
        log.setType("2");
        log.setPlatform("3");//浙证钉
        log.setCreateBy(sysUser.getId());
        sysLogService.saveOrUpdate(log,request);
        return AjaxResult.success(sysUser);
    }



    public void getZzdInfoByMobile(String mobile) {
        JSONObject userInfo =  ZzdUtil.findUserInfoByMobile(mobile);
        SysUser sysUser =  sysUserService.getByUserName(mobile);
        if (sysUser.getUserType() == 3) {
            SysUserZzd sysUserZzd = new SysUserZzd();
            sysUserZzd.setUserId(sysUser.getId());
            sysUserZzd.setEmployeeCode(userInfo.getString("employeeCode"));
            sysUserZzdService.insert(sysUserZzd);
        }
    }
    @RequestMapping("/getZzdInfoByMobile")
    public void insertSysUserzzd() {
        SysUser sysUser = new SysUser();
        sysUser.setUserType(3);
        List<SysUser> sysUserList = sysUserService.getList(sysUser);
        sysUserList.forEach(sys -> {
            SysUserZzd sysUserZzd = new SysUserZzd();
            sysUserZzd.setUserId(sys.getId());
            System.out.println(sys.getId());
            List<SysUserZzd> list = sysUserZzdService.getList(sysUserZzd);
            if (list.size() == 0) {
                JSONObject userInfo =  ZzdUtil.findUserInfoByMobile(sys.getUserName());
                if (userInfo != null && userInfo.getString("employeeCode") != null) {
                    sysUserZzd.setEmployeeCode(userInfo.getString("employeeCode"));
                    sysUserZzd.setAccount(userInfo.getString("accountId"));
                    sysUserZzdService.insert(sysUserZzd);
                }
            }
        });
    }


    @RequestMapping("/updateQrCode")
    public void updateQrCode() {
        PetBrand petBrand = new PetBrand();
        List<PetBrand> petBrands = petBrandService.getList(petBrand);
        petBrands.forEach(res -> {
            if (res.getQrCode() != null) {
                System.out.println(petBrandServiceUrl + res.getId());
                res.setQrCode(QRCode.createQrImg(petBrandServiceUrl + res.getBrandNum()));
                petBrandService.saveOrUpdate(res);
            }
        });
    }



    //浙政钉用户登录
    private SysUser userLogin(String id) {
        SysUser sysUser  = sysUserService.getById(id);
        String token = IdGen.uuid();
        // 用户信息存入缓存中
        UserCache userCache = new UserCache();
        userCache.setId(sysUser.getId()+"");
        UserMapCache.login(token,userCache);
        // 用户令牌返回页面
        sysUser.setToken(token);

        return sysUser;
    }

}
