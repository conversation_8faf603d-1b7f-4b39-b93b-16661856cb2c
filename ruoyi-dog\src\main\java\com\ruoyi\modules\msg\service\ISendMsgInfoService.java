package com.ruoyi.modules.msg.service;

import java.util.List;

import com.ruoyi.modules.msg.domain.SendMsgInfo;

/**
 * 短信发送记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface ISendMsgInfoService 
{
    /**
     * 查询短信发送记录
     * 
     * @param id 短信发送记录主键
     * @return 短信发送记录
     */
    public SendMsgInfo selectSendMsgInfoById(Long id);

    /**
     * 查询短信发送记录列表
     * 
     * @param sendMsgInfo 短信发送记录
     * @return 短信发送记录集合
     */
    public List<SendMsgInfo> selectSendMsgInfoList(SendMsgInfo sendMsgInfo);

    /**
     * 新增短信发送记录
     * 
     * @param sendMsgInfo 短信发送记录
     * @return 结果
     */
    public int insertSendMsgInfo(SendMsgInfo sendMsgInfo);

    /**
     * 修改短信发送记录
     * 
     * @param sendMsgInfo 短信发送记录
     * @return 结果
     */
    public int updateSendMsgInfo(SendMsgInfo sendMsgInfo);

    /**
     * 批量删除短信发送记录
     * 
     * @param ids 需要删除的短信发送记录主键集合
     * @return 结果
     */
    public int deleteSendMsgInfoByIds(Long[] ids);

    /**
     * 删除短信发送记录信息
     * 
     * @param id 短信发送记录主键
     * @return 结果
     */
    public int deleteSendMsgInfoById(Long id);

    /**
     * 发送短信
     * @param sendMsgInfo
     * @return
     */
    int sendMsg(SendMsgInfo sendMsgInfo);
}
