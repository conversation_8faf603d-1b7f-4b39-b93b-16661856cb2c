package com.ruoyi.modules.petRecord.dao;

import com.ruoyi.modules.petRecord.entity.PetRecord;
import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.petRecord.entity.PetRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 犬只操作记录表(pet_record)表数据库访问层
 * <AUTHOR>
 */
@Repository
public interface PetRecordDao extends BaseDao<PetRecord> {

    List<String> getPetIdList(@Param("node") int node,@Param("beginTime") String beginTime, @Param("endTime") String endTime);
}
