package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 驾驶舱事件详情
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/23 9:24
 */
@Data
public class BigScreenEventInfo {

    /**
     * id
     */
    private Long id;

    /**
     * 事件标题
     */
    private String eventTitle;


    /**
     * 基本情况
     */
    private String baseSituation;


    /**
     * 领导批示
     */
    private String leadIndication;

    /**
     * 动态更进
     */
    private String dynamicAdvance;

    /**
     * 复盘总结
     */
    private String reviewSummary;

    /**
     * 处置情况
     */
    private String disposeSituation;

    /**
     * 反馈情况
     */
    private String feedBack;

    /**
     * 指令id
     */
    private Long instructionId;

    /**
     * 领导批示时间 时分
     */
    private String leadIndicationTime;

    /**
     * 领导批示时间 月日
     */
    private String leadIndicationDate;

    /**
     * 交办情况时间
     */
    private String disposeSituationTime;

    /**
     * 交办情况月日
     */
    private String disposeSituationDate;

    /**
     * 动态更进时间
     */
    private String dynamicAdvanceTime;

    /**
     * 动态更进月日
     */
    private String dynamicAdvanceDate;

    /**
     * 处置反馈时间
     */
    private String feedBackTime;

    /**
     * 处置反馈时间 月日
     */
    private String feedBackDate;

    /**
     *  事件类型 1：事件 2：重大事件
     */
    private String eventType;


    /**
     * 推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pushTime;


}
