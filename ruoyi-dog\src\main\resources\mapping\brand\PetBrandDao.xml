<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.brand.dao.PetBrandDao">
    <sql id="brand">
        a
        .
        id
        as "id",
        a.brand_num as "brandNum",
        a.area as "area",
        a.street as "street",
        a.county as "county",
        a.brand_city as "brandCity",
        a.brand_com as "brandCom",
        a.off_date as "offDate",
        a.remark as "remark",
        (select d.user_name from sys_user d where d.dog_user_id = a.off_com) as "offCom",
        a.is_cancellation as "isCancellation",
        a.is_recovery as "isRecovery",
        a.is_use as "isUse",
a.qr_code as "qrCode",
        a.create_date as "createDate",a.is_receipt as isReceipt,a.apply_id as applyId
    </sql>
    <select id="getPetBrand" parameterType="java.util.List" resultType="com.ruoyi.modules.brand.entity.PetBrand">
        select
        <include refid="brand"/>
        from pet_brand a where id in (
        <foreach collection="list" item="item" index="index" separator=",">
            #{item}
        </foreach>)
        order by brand_num
    </select>
    <select id="getList" parameterType="com.ruoyi.modules.brand.entity.PetBrand"
            resultType="com.ruoyi.modules.brand.entity.PetBrand">
        select
        <include refid="brand"/>
         ,(case when (select name from qualifi where del_flag=1 and id=a.brand_com)is null then (select user_name from sys_user where id=a.brand_com) else
         (select name from qualifi where del_flag=1 and id=a.brand_com ) end) brandComStr
        from pet_brand a
        where 1=1
        <if test="brandComStr != null and brandComStr != ''">
            and (a.brand_com in (select id from qualifi where name like concat('%',#{brandComStr},'%'))
            or ((select count(id) from qualifi where id=a.brand_com) &lt; 1 and a.brand_com in (select id from sys_user
            where user_name like concat('%',#{brandComStr},'%'))))
        </if>
        <if test="brandNum != null and brandNum != ''">
        and a.brand_num like concat('%',#{brandNum},'%')
    </if>
        <if test="sNum != null and sNum != ''">
            and a.brand_num like concat('%',#{sNum},'%')
        </if>
        <if test="eNum != null and eNum != ''">
            and a.brand_num like concat('%',#{eNum},'%')
        </if>
        <if test="area != null and area != ''">
            and a.area = #{area}
        </if>
        <if test="brandCity != null and brandCity != ''">
            and a.area = #{brandCity}
        </if>
        <if test="brandCom != null and brandCom != ''">
            and a.brand_com = #{brandCom}
        </if>
        <if test="isRecovery != null and isRecovery != ''">
            and a.is_recovery = #{isRecovery}
        </if>
        <if test="isCancellation != null and isCancellation != ''">
            and a.is_cancellation = #{isCancellation}
        </if>
        <if test="isUse != null and isUse != ''">
            and a.is_use = #{isUse}
        </if>
        <if test="isReceipt != null">
            and a.is_receipt = #{isReceipt}
        </if>
        order by a.brand_num desc
    </select>
    <select id="count" parameterType="com.ruoyi.modules.brand.entity.PetBrand"
            resultType="java.lang.Integer">
        select
       count(*)
        from pet_brand a
        where 1=1
        <if test="brandComStr != null and brandComStr != ''">
            and (a.brand_com in (select id from qualifi where name like concat('%',#{brandComStr},'%'))
            or ((select count(id) from qualifi where id=a.brand_com) &lt; 1 and a.brand_com in (select id from sys_user
            where user_name like concat('%',#{brandComStr},'%'))))
        </if>
        <if test="brandNum != null and brandNum != ''">
        and a.brand_num like concat('%',#{brandNum},'%')
    </if>
        <if test="sNum != null and sNum != ''">
            and a.brand_num like concat('%',#{sNum},'%')
        </if>
        <if test="eNum != null and eNum != ''">
            and a.brand_num like concat('%',#{eNum},'%')
        </if>
        <if test="area != null and area != ''">
            and a.area = #{area}
        </if>
        <if test="brandCity != null and brandCity != ''">
            and a.area = #{brandCity}
        </if>
        <if test="brandCom != null and brandCom != ''">
            and a.brand_com = #{brandCom}
        </if>
        <if test="isRecovery != null and isRecovery != ''">
            and a.is_recovery = #{isRecovery}
        </if>
        <if test="isCancellation != null and isCancellation != ''">
            and a.is_cancellation = #{isCancellation}
        </if>
        <if test="isUse != null and isUse != ''">
            and a.is_use = #{isUse}
        </if>
        <if test="isReceipt != null">
            and a.is_receipt = #{isReceipt}
        </if>
        order by a.brand_num desc
    </select>

    <select id="getByEntity" parameterType="com.ruoyi.modules.brand.entity.PetBrand"
            resultType="com.ruoyi.modules.brand.entity.PetBrand">
        select
        <include refid="brand"/>
        from pet_brand a
        where 1=1
        <if test="brandNum != null and brandNum != ''">
            and a.brand_num =#{brandNum}
        </if>

        <if test="area != null and area != ''">
            and a.area = #{area}
        </if>
        <if test="brandCity != null and brandCity != ''">
            and a.area = #{brandCity}
        </if>
        <if test="brandCom != null and brandCom != ''">
            and a.brand_com = #{brandCom}
        </if>
        <if test="isRecovery != null and isRecovery != ''">
            and a.is_recovery = #{isRecovery}
        </if>
        <if test="isUse != null and isUse != ''">
            and a.is_use = #{isUse}
        </if>
        <if test="isReceipt != null">
            and a.is_receipt = #{isReceipt}
        </if>
        order by a.brand_num desc
    </select>
    <select id="getLastBrandNum" resultType="java.lang.String" parameterType="java.lang.String">
        select SUBSTR(brand_num FROM 6) as num
        from pet_brand
        WHERE brand_num like concat(#{str}, '%')
        order by brand_num desc LIMIT 1
    </select>
    <select id="getById" parameterType="com.ruoyi.modules.brand.entity.PetBrand"
            resultType="com.ruoyi.modules.brand.entity.PetBrand">
        select
        <include refid="brand"/>
        from pet_brand a
        where a.id = #{id}
    </select>
    <select id="getBranchList" parameterType="com.ruoyi.modules.brand.entity.PetBrand" resultType="com.ruoyi.modules.brand.entity.PetBrand">
        select
        <include refid="brand"/>
        from pet_brand a
        where a.brand_com is null and a.is_use=2 and a.area=#{area} and a.apply_id is null and is_receipt=2
        order by a.brand_num asc
        <if test="num!=null">
            limit #{num}
        </if>
    </select>
    <select id="getBranchListNew" parameterType="com.ruoyi.modules.brand.entity.PetBrand" resultType="com.ruoyi.modules.brand.entity.PetBrand">
        select
        <include refid="brand"/>
        from pet_brand a
        where a.brand_com is null and a.is_use=2 and a.area=#{area} and a.apply_id is null and is_receipt=2
        order by a.brand_num asc
        <if test="num!=null">
            limit #{num}
        </if>
    </select>
    <insert id="insert" parameterType="com.ruoyi.modules.brand.entity.PetBrand">
        insert into pet_brand
        (id, brand_num, brand_city,
         area, street, county,
         brand_com, off_date, remark,
         off_com, is_recovery, is_use, qr_code,
         create_date, create_by,apply_id,is_receipt)
        values (#{id}, #{brandNum}, #{brandCity},
                #{area}, #{street}, #{county},
                #{brandCom}, #{offDate}, #{remark},
                #{offCom}, #{isRecovery}, #{isUse}, #{qrCode},
                #{createDate}, #{createBy},#{applyId},#{isReceipt})
    </insert>

    <insert id="saveList" parameterType="java.util.List">
        insert into pet_brand
        (
        id, brand_num,brand_city,
        area, street, county,
        brand_com, off_date, remark,
        off_com, is_recovery, is_use,qr_code,
        create_date, create_by,is_receipt
        )values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id}, #{item.brandNum}, #{item.brandCity},
            #{item.area}, #{item.street}, #{item.county},
            #{item.brandCom}, #{item.offDate}, #{item.remark},
            #{item.offCom}, #{item.isRecovery}, #{item.isUse}, #{item.qrCode},
            #{item.createDate}, #{item.createBy},#{item.isReceipt}
            )
        </foreach>
    </insert>

    <update id="update" parameterType="com.ruoyi.modules.brand.entity.PetBrand">
        update pet_brand
        set brand_num   = #{brandNum},
            brand_city  = #{brandCity},
            area        = #{area},
            street      = #{street},
            county      = #{county},
            brand_com   = #{brandCom},
            off_date    = #{offDate},
            remark      = #{remark},
            off_com     = #{offCom},
            is_recovery = #{isRecovery},
            is_use      = #{isUse},
            qr_code=#{qrCode},
            update_date = #{updateDate},
            update_by   = #{updateBy},apply_id=#{applyId}
        where id = #{id}
    </update>

    <update id="upStatus" parameterType="com.ruoyi.modules.brand.entity.PetBrand">
        update pet_brand set
        <if test="isCancellation != null">
            is_cancellation = #{isCancellation},
        </if>
        <if test="offDate != null">
            off_date = #{offDate},
        </if>
        <if test="offCom != null">
            off_com = #{offCom},
        </if>
        <if test="isRecovery != null">
            is_recovery = #{isRecovery},
        </if>
        <if test="isUse != null">
            is_use = #{isUse},
        </if>
        update_date = #{updateDate},
        update_by = #{updateBy}
        where id = #{id}
    </update>

    <update id="updatePetNumUse" parameterType="java.lang.String">
        update pet_brand
        set is_use = #{isUse}
        where brand_num = #{brandNum}
    </update>

    <select id="getListByNum" parameterType="com.ruoyi.modules.brand.entity.PetBrand"
            resultType="com.ruoyi.modules.brand.entity.PetBrand">
        select a.id as "id",a.brand_num as "brandNum"
        from pet_brand a
        where a.is_recovery = 1 and a.is_use = 2
        and a.brand_com = #{brandCom}
        <if test="area != null and area !=''">
            and a.area = #{area}
        </if>
        order by a.create_date
        LIMIT ${num}
    </select>
    <update id="assignDogCard" parameterType="com.ruoyi.modules.brand.entity.PetBrand">
        update pet_brand
        set brand_com=#{brandCom},
            qr_code=#{qrCode}
        where id = #{id}
    </update>
    <update id="updateByEntity" >
        update pet_brand
        <set>
            <trim suffixOverrides=",">
                <if test="brandCom!=null and brandCom!=''">
                    brand_com=#{brandCom},
                </if>
                <if test="applyId!=null and applyId!=''">
                    apply_id=#{applyId},
                </if>
                <if test="isReceipt!=null">
                    is_receipt=#{isReceipt},
                </if>

            </trim>
        </set>
        where id = #{id}
    </update>
    <update id="updateList" parameterType="java.util.List">
        update pet_brand
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="brand_com =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.brandCom != null ">
                        when id=#{item.id} then #{item.brandCom}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getAllList" parameterType="com.ruoyi.modules.brand.entity.PetBrand"
            resultType="com.ruoyi.modules.brand.entity.PetBrand">
             <if test="brandNum != null and brandNum !=''">
                 select replace(uuid(),'-','') AS ID,#{brandNum} AS brandNum union all
             </if>
            (select a.id, a.brand_num as brandNum from pet_brand a
                where a.is_use = 2 and is_receipt = 2
              <if test="brandCom != null and brandCom !=''">
                 and  brand_com = #{brandCom}
              </if>
              <if test="area != null and area !=''">
                 and  area = #{area} and brand_com is null
              </if>
            order by a.create_date)
    </select>

    <update id="updateIsCancellation" parameterType="com.ruoyi.modules.brand.entity.PetBrand">
        update pet_brand
        set is_cancellation = #{isCancellation},
            off_date        = #{offDate},
            off_com         = #{offCom}
        where brand_num = #{brandNum}
    </update>
<select id="totalEchar" resultType="java.util.HashMap">
    select
            (select count(id) from pet_brand ) zs
         ,(select count(id) from pet_brand where is_use=1) ysy
         ,(select count(id) from pet_brand where is_cancellation=2) yzx
         ,(select count(id) from immune_reissue where del_flag=1 and status=2 ) bb
         ,(select count(id) from pet_brand where is_cancellation=2 and DATE_FORMAT(create_date,'%Y-%m-%d')=DATE_FORMAT(now(),'%Y-%m-%d')) nowbl
         ,(select count(id) from pet_brand where is_cancellation=2 and DATE_FORMAT(create_date,'%Y-%m-%d')=DATE_FORMAT(now(),'%Y-%m-%d')) nowzx
    from dual



</select>
    <select id="deptEchar" resultType="java.util.HashMap">
        select dept_name name
             ,(select count(id) from pet_brand where area=d.id) num
        from sys_dept d where del_flag=1
        order by num desc
            LIMIT 5
    </select>
<select id="getNotApplyBrandNum" parameterType="java.lang.String" resultType="java.lang.Integer">
    select count(id) from pet_brand where  is_use=2 and is_receipt=2  and brand_com is null and area=#{deptId}
</select>

    <select id="findViewData" resultType="java.util.Map">
        SELECT
        p.id,
        p.pet_num,
        p.pet_address dept_name,
        p.owner_name,
        p.pet_id_card,
        p.id dogId,
        p.create_date create_date,
        IFNULL(p.update_date,p.create_date) update_date,
        dept.dept_name name,
        ai.area_code area
        FROM
        pet_certificates p LEFT JOIN sys_dept dept on p.pet_dept = dept.id
        LEFT JOIN area_info ai on dept.dept_name = ai.area_name
        WHERE
        p.del_flag = 1
        <if test="formattedDate != null  and formattedDate != ''"> and p.create_date > #{formattedDate}</if>
    </select>

    <select id="listByPetNumList" resultType="com.ruoyi.modules.brand.entity.PetBrand">
        select <include refid="brand"/>
        from pet_brand a where brand_num in
        <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
