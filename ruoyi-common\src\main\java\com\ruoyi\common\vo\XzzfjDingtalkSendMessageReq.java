package com.ruoyi.common.vo;

import java.util.List;

/**
 * 行政执法局浙政钉发送工作通知
 */
public class XzzfjDingtalkSendMessageReq {

    /**
     * 1根据手机号发送，2根据员工code发送
     */
    private  Integer type;
//    List<String> phones;//要发送的手机号集合
//
//    Long tenantId;//租户id、
//
//    Long accountId;//账号id
    /**
     * 应用来源，什么应用发这个消息通知
     */
    private String source;
    /**
     * 工作通知级别
     */
    private  String level;
    /**
     * 跳转地址
     */
    private  String url;
    /**
     * 员工code
     */
//    List<String> code;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    private String msg;//消息
    /**
     * 用户信息
     */
    List<XzzfjUserInfoReq> list;

    /**
     * 任务来源
     */
    private  String taskSource;
    /**
     * 类型，1一键通知，2一键调度，3告警，4指令
     */
    private Integer dataType;
    /**
     * 业务id
     */
    private String detailsId;
    /**
     * 县市区
     */
    private String area;
    /**
     * 部门
     */
    private String deptName;
    /**
     * 工作通知父类id
     */
    private Long parentId ;

    /**
     * 任务编号
     */
    private String taskNo;

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }
    //    public List<String> getPhones() {
//        return phones;
//    }
//
//    public void setPhones(List<String> phones) {
//        this.phones = phones;
//    }
//
//    public Long getTenantId() {
//        return tenantId;
//    }
//
//    public void setTenantId(Long tenantId) {
//        this.tenantId = tenantId;
//    }
//
//    public Long getAccountId() {
//        return accountId;
//    }
//
//    public void setAccountId(Long accountId) {
//        this.accountId = accountId;
//    }

//    public List<String> getCode() {
//        return code;
//    }
//
//    public void setCode(List<String> code) {
//        this.code = code;
//    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public List<XzzfjUserInfoReq> getList() {
        return list;
    }

    public void setList(List<XzzfjUserInfoReq> list) {
        this.list = list;
    }

    public String getTaskSource() {
        return taskSource;
    }

    public void setTaskSource(String taskSource) {
        this.taskSource = taskSource;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public String getDetailsId() {
        return detailsId;
    }

    public void setDetailsId(String detailsId) {
        this.detailsId = detailsId;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
}
