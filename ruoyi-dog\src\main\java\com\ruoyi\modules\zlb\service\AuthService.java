package com.ruoyi.modules.zlb.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.util.ydsms.Md5Util;
import com.ruoyi.util.zlb.IrsSignRes;
import com.ruoyi.util.zlb.IrsUtils;
import com.ruoyi.util.zlb.ZLBConstants;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-03-30 15:49
 */
@Component
public class AuthService {


    @Autowired
    private RestTemplateBuilder restTemplateBuilder;

    private RestTemplate restTemplate;

    @PostConstruct
    void init() {
        restTemplate = restTemplateBuilder.build();
    }


    public String getTokenByTicketId(String ticketId) {

        HttpHeaders headers = getHttpHeaders(ZLBConstants.ACCESS_TOKEN_URL);
        JSONObject body = new JSONObject();
        body.put("appId", ZLBConstants.APP_ID);
        body.put("ticketId", ticketId);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);

        ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(ZLBConstants.ACCESS_TOKEN_URL, request, String.class);
        return checkResponse(stringResponseEntity).getJSONObject("data").getString("accessToken");
    }

    public String getZlbTokenByTicketId(String ticketId) {
        PostMethod postMethod = getrequestHeaders(ZLBConstants.ZLB_TICKET_VALIDATION_URL);
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String time = simpleDateFormat.format(date);
        NameValuePair[] data = {
                new NameValuePair("servicecode",ZLBConstants.IRS_AK),
                new NameValuePair("st",ticketId),
                new NameValuePair("time",time),
                new NameValuePair("sign",Md5Util.MD5(ZLBConstants.IRS_AK+ZLBConstants.IRS_SK+time)),
                new NameValuePair("datatype","json"),
                new NameValuePair("method","ticketValidation"),
        };
        postMethod.setRequestBody(data);
        HttpClient httpClient = new HttpClient();
        try {
            int response = httpClient.executeMethod(postMethod); // 执行POST方法
            String result = postMethod.getResponseBodyAsString() ;  //返回结果
            if (response == 200 && result != null) {
                JSONObject re = JSONObject.from(JSON.parseObject(result));
                if (re.getString("result").equals("0")) {
                    return re.getString("token");
                } else {
                    throw new RuntimeException(re.getString("errmsg"));
                }
            }
        }catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    public JSONObject getZlbUserInfoByToken(String accessToken) {
        PostMethod postMethod = getrequestHeaders(ZLBConstants.ZLB_GET_USER_INFO_URL);
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String time = simpleDateFormat.format(date);
        NameValuePair[] data = {
                new NameValuePair("servicecode",ZLBConstants.IRS_AK),
                new NameValuePair("token",accessToken),
                new NameValuePair("time",time),
                new NameValuePair("sign",Md5Util.MD5(ZLBConstants.IRS_AK+ZLBConstants.IRS_SK+time)),
                new NameValuePair("datatype","json"),
                new NameValuePair("method","getUserInfo"),
        };
        postMethod.setRequestBody(data);
        HttpClient httpClient = new HttpClient();
        try {
            int response = httpClient.executeMethod(postMethod); // 执行POST方法
            String result = postMethod.getResponseBodyAsString() ;  //返回结果
            if (response == 200 && result != null) {
                JSONObject re = JSONObject.from(JSON.parseObject(result));
                if ("0".equals(re.getString("result"))) {
                    return re;
                } else {
                    throw new RuntimeException(re.getString("errmsg"));
                }
            } else {
                throw new RuntimeException(Integer.valueOf(response).toString() + time);
            }
        }catch (IOException e) {
            e.printStackTrace();
        }
        JSONObject e = new JSONObject();
        e.put("333", "222");
        return e;
    }


    public JSONObject getUserInfoByToken(String accessToken) {
        HttpHeaders headers = getHttpHeaders(ZLBConstants.GET_USER_INFO_URL);
        JSONObject body = new JSONObject();
        body.put("token", accessToken);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);

        ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(ZLBConstants.GET_USER_INFO_URL, request, String.class);
        return checkResponse(stringResponseEntity).getJSONObject("data");
    }

    private JSONObject checkResponse(ResponseEntity<String> stringResponseEntity) {
        if (!stringResponseEntity.getStatusCode().is2xxSuccessful()) {
            //请求失败
            throw new RuntimeException("status:" + stringResponseEntity.getStatusCodeValue() + " " + stringResponseEntity.getBody());
        }
        JSONObject result = JSONObject.from(JSON.parseObject(stringResponseEntity.getBody()));
        if (result.containsKey("errorCode") && result.getString("errorCode") != null && !result.getBooleanValue("success")) {
            //业务错误
            throw new RuntimeException(result.toString());
        }
        return result;
    }

    private HttpHeaders getHttpHeaders(String url) {
        IrsSignRes res = IrsUtils.sign(url, "POST");

        HttpHeaders headers = new HttpHeaders();
        headers.add(ZLBConstants.X_BG_HMAC_ACCESS_KEY, res.getAccessKey());
        headers.add(ZLBConstants.X_BG_HMAC_ALGORITHM, res.getAlgorithm());
        headers.add(ZLBConstants.X_BG_HMAC_SIGNATURE, res.getSignature());
        headers.add(ZLBConstants.X_BG_DATE_TIME, res.getDateTime());
        return headers;
    }
    private PostMethod getrequestHeaders(String url) {
        IrsSignRes res = IrsUtils.sign(url, "POST");

        PostMethod postMethod = new PostMethod(url) ;
        postMethod.setRequestHeader(ZLBConstants.X_BG_HMAC_ACCESS_KEY, res.getAccessKey());
        postMethod.setRequestHeader(ZLBConstants.X_BG_HMAC_ALGORITHM, res.getAlgorithm());
        postMethod.setRequestHeader(ZLBConstants.X_BG_HMAC_SIGNATURE, res.getSignature());
        postMethod.setRequestHeader(ZLBConstants.X_BG_DATE_TIME, res.getDateTime());
        postMethod.setRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8") ;
        return postMethod;
    }


}
