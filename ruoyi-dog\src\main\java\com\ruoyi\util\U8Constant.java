package com.ruoyi.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * @classname:
 * @description:
 * @author: 侯旭
 * @date: 2021/3/10 11:30
 * @version: 1.0
 **/
@Component
public class U8Constant {

    // 上传附件通用路径配置
    @Value("${uploadFilePath}")
    private String uploadFilePath ;

    @Bean
    public int initStatic(){
        UploadLocalUtil.setUploadFilePath(uploadFilePath);
        return 0;
    }
    //七牛公钥/密钥（中海）
/*    public static final String QINIU_ACCESS_KEY = "Q63eZthleB3WKG-j7Iu4amClsZOb_uM8MxwC68v1";
    public static final String QINIU_SECRET_KEY = "5_VE9WmHXY_DTU_SVczBduiNy7YWvvfppxtYnElr";*/
    //七牛公钥/密钥（游巴）
    public static final String QINIU_ACCESS_KEY = "U1rvfJoaUpelRcIbvXdRTaEmJsHFLz74H9274xo-";
    public static final String QINIU_SECRET_KEY = "OBIRnkIG8ZwyJzzny7gXNxqAsh03dRI4pV53G5NP";

    //七牛bucket地址（中海）
/*    public static final String QINIU_PUBLIC_BUCKET_DOMAIN = "http://p31ehpdhj.bkt.clouddn.com";
    public static final String QINIU_SECRET_BUCKET_DOMAIN = "http://p2zikcbug.bkt.clouddn.com";
    public static final Integer QINIU_BUCKET_PREFIX_LENGTH = 33;*/
    //七牛bucket地址（游巴）
    public static final String QINIU_PUBLIC_BUCKET_DOMAIN = "http://imagepub.chinau8.cn";
    public static final String QINIU_SECRET_BUCKET_DOMAIN = "http://imagepri.chinau8.cn";
    public static final Integer QINIU_BUCKET_PREFIX_LENGTH = 33;
}
