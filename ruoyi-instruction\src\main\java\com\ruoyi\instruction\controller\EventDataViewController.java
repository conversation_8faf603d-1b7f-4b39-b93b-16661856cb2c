package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.instruction.domain.rspVo.EventAssignRspVo;
import com.ruoyi.instruction.domain.rspVo.EventDataRspVo;
import com.ruoyi.instruction.service.IInstructionEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/17 10:00
 * 事件工作台controller
 */
@RestController
@RequestMapping("/event/dateView")
public class EventDataViewController {

    @Autowired
    private IInstructionEventService eventService;

    /**
     * 获取事件总数、重大事件数、区县事件数、市级事件数
     *
     * @return
     */
    @PostMapping("/getEventData")
    public AjaxResult getEventData(@RequestBody(required = false) Map<String,Object> map) {
        EventDataRspVo rspVo = eventService.getEventData(map);
        return AjaxResult.success(rspVo);
    }

    /**
     * 获取事件交办数量
     *
     * @return
     */
    @GetMapping("/getAssignData")
    public AjaxResult getAssignData() {
        List<EventAssignRspVo> assignRspVoList = eventService.getAssignData();
        return AjaxResult.success(assignRspVoList);
    }

    /**
     * 获取信息类别
     *
     * @return
     */
    @GetMapping("/getInfoCategory")
    public AjaxResult getInfoCategory() {
        List<Map<String, Integer>> maps = eventService.getInfoCategory();
        return AjaxResult.success(maps);
    }

    /**
     * 获取事件类型
     *
     * @return
     */
    @GetMapping("/getType")
    public AjaxResult getType() {
        List<Map<String, Integer>> maps = eventService.getType();
        return AjaxResult.success(maps);
    }

    /**
     * 获取热词分析
     * @return
     */
    @GetMapping("/getHotWord")
    public AjaxResult getHotWord(){
        List<String> hotWords = eventService.getHotWord();
        return AjaxResult.success(hotWords);
    }

    /**
     * 获取信息
     * @return
     */
    @GetMapping("/getInfoSource")
    public AjaxResult getInfoSource(){
        List<Map<String,Integer>> maps = eventService.getInfoSource();
        return AjaxResult.success(maps);
    }

    /**
     * 获取事件多发区域排名
     * @return
     */
    @GetMapping("/getAreaRank")
    public AjaxResult getAreaRank(){
        List<Map<String,Integer>> maps = eventService.getAreaRank();
        return AjaxResult.success(maps);
    }

    /**
     * 获取事件子类型数据
     * @return
     */
    @GetMapping("/getChildType/{id}")
    public AjaxResult getChildType(@PathVariable("id")Integer id){
        List<Map<String,Integer>> maps = eventService.getChildType(id);
        return AjaxResult.success(maps);
    }
}
