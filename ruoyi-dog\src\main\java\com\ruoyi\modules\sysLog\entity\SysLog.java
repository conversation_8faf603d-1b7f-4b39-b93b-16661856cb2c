package com.ruoyi.modules.sysLog.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.user.entity.SysUser;

import java.util.Date;
import java.io.Serializable;

/**
 * 系统日志(SysLog)实体类
 *
 * <AUTHOR>
 * @since 2022-12-26 10:58:05
 */
public class SysLog extends BaseEntity {

    /**
     * 类型：1.登陆日志
     */
    private String type;
    /**
     * 登陆平台：1.pc 2.浙里办 3.浙政钉
     */
    private String platform;
    /*ip*/
    private String ip;
    /**/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date bDate;//
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date eDate;//
private SysUser user;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public SysUser getUser() {
        return user;
    }

    public void setUser(SysUser user) {
        this.user = user;
    }

    public Date getbDate() {
        return bDate;
    }

    public void setbDate(Date bDate) {
        this.bDate = bDate;
    }

    public Date geteDate() {
        return eDate;
    }

    public void seteDate(Date eDate) {
        this.eDate = eDate;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }


}

