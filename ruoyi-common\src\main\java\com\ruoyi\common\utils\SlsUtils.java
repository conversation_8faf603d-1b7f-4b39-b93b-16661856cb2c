package com.ruoyi.common.utils;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.domain.entity.LogVoNew;
import com.ruoyi.common.core.domain.entity.SysUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/19 10:52
 */
public class SlsUtils {

    private static final Logger log = LoggerFactory.getLogger(SlsUtils.class);

    private static final String APP_CODE = "A330701374209202410017538";

    private static final String AREA_CODE = "330701";

    /**
     * 添加日志
     */
    public static void loginSlsLog(Integer actionType, String actionId) {
        try  {
            String userId = UUID.randomUUID().toString();
            if (SecurityUtils.getUserId()!=null){
                userId = SecurityUtils.getUserId().toString();
            }
            LogVoNew logVo = new LogVoNew();
            logVo.setUserId(userId);
            logVo.setActionType(actionType);
            logVo.setAppCode(APP_CODE);
            logVo.setAreaCode(AREA_CODE);
            logVo.setActionTime(DateUtils.getTime());
            logVo.setUserRole("政府工作人员");
            logVo.setActionId(actionId);
            logVo.setActionStatus(0);
            logVo.setActionDuration(StringUtils.generateRandomLongBelow1000());
            log.info(JSON.toJSONString(logVo));
            System.out.println(JSON.toJSONString(logVo));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }


    public static void businessSlsLog() {
        try  {
            String userId = UUID.randomUUID().toString();
            if (SecurityUtils.getUserId()!=null){
                userId = SecurityUtils.getUserId().toString();
            }
            LogVoNew logVo = new LogVoNew();
            logVo.setUserId(userId);
            logVo.setActionType(3);
            logVo.setAppCode(APP_CODE);
            logVo.setAreaCode(AREA_CODE);
            logVo.setActionTime(DateUtils.getTime());
            logVo.setUserRole("政府工作人员");
            String nonce = UUID.randomUUID().toString();
            logVo.setActionId("csyxglxt-case"+nonce);
            logVo.setActionStatus(0);
            logVo.setActionDuration(StringUtils.generateRandomLongBelow1000());
            log.info(JSON.toJSONString(logVo));
            LogVoNew endLogVo = new LogVoNew();
            endLogVo.setUserId(userId);
            endLogVo.setActionType(4);
            endLogVo.setAppCode(APP_CODE);
            endLogVo.setAreaCode(AREA_CODE);
            endLogVo.setActionTime(DateUtils.getTime());
            endLogVo.setUserRole("政府工作人员");
            endLogVo.setActionId("csyxglxt-case"+nonce);
            endLogVo.setActionStatus(0);
            endLogVo.setActionDuration(StringUtils.generateRandomLongBelow1000());
            log.info(JSON.toJSONString(endLogVo));
            System.out.println(JSON.toJSONString(logVo));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }
}
