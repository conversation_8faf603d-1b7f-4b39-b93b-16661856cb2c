<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.user.dao.SysUploadFileDao" >
    <sql id="sysUploadFileSql">
        a.id AS "id",
        a.instance_id AS "instanceId",
        a.model_type AS "modelType",
        a.file_url AS "fileUrl",
        a.file_name AS "fileName",
        a.create_date AS "createDate",
        a.del_flag AS "delFlag",
        a.url as "url"
    </sql>

    <select id="getById" resultType="com.ruoyi.modules.user.entity.SysUploadFile">
        SELECT
          <include refid="sysUploadFileSql"/>
        FROM sys_upload_file a
        WHERE a.id = #{id}
    </select>

    <select id="getList" resultType="com.ruoyi.modules.user.entity.SysUploadFile">
        SELECT
          <include refid="sysUploadFileSql"/>
        FROM sys_upload_file a
        WHERE del_flag = 1
        <if test='instanceId != null and instanceId != ""'>
            AND a.instance_id = #{instanceId}
        </if>
        <if test='modelType != null and modelType != ""'>
            AND a.model_type = #{modelType}
        </if>
        order by a.create_date desc
    </select>
    <select id="getByEntity" resultType="com.ruoyi.modules.user.entity.SysUploadFile">
        SELECT
        <include refid="sysUploadFileSql"/>
        FROM sys_upload_file a
        WHERE del_flag = 1
        <if test='instanceId != null and instanceId != ""'>
            AND a.instance_id = #{instanceId}
        </if>
        <if test='modelType != null and modelType != ""'>
            AND a.model_type = #{modelType}
        </if>
        order by a.create_date desc
    </select>
    <insert id="saveAllList" parameterType="java.util.List">
        INSERT INTO sys_upload_file (
            id,
            instance_id,
            model_type,
            file_url,
            file_name,
            create_date,
            del_flag,
            url
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
                #{item.id},
                #{item.instanceId},
                #{item.modelType},
                #{item.fileUrl},
                #{item.fileName},
                #{item.createDate},
                #{item.delFlag},
                #{item.url}
            )
        </foreach>
    </insert>

    <delete id="delByInstanceAndModel" parameterType="com.ruoyi.modules.user.entity.SysUploadFile">
        UPDATE sys_upload_file SET del_flag = 2 where instance_id = #{instanceId}
        <if test='modelType != null and modelType != ""'>
            AND model_type = #{modelType}
        </if>
    </delete>

    <insert id="insert" parameterType="com.ruoyi.modules.user.entity.SysUploadFile">
        insert into sys_upload_file(
            id,
            instance_id,
            model_type,
            file_url,
            file_name,
            create_date,
            del_flag,
            url
        )values (
            #{id},
            #{instanceId},
            #{modelType},
            #{fileUrl},
            #{fileName},
            #{createDate},
            #{delFlag},
            #{url}
        )
    </insert>

    <select id="listByInstanceId" resultType="com.ruoyi.modules.user.entity.SysUploadFile">
        SELECT
        <include refid="sysUploadFileSql"/>
        FROM sys_upload_file a
        WHERE del_flag = 1
        <if test="instanceIdList != null and instanceIdList.size() != 0">
            and instance_id in
            <foreach collection="instanceIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by a.create_date desc
    </select>

    <delete id="deleteByInstanceIdAndTypeList">
        delete from sys_upload_file
        where instance_id = #{id,jdbcType=VARCHAR}
        and model_type in
        <foreach collection="typeList" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
