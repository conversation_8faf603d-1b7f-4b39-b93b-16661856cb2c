package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.InstructionEvent;
import com.ruoyi.instruction.domain.rspVo.BigScreenJazzCommonVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * 金安智治驾驶舱统计维稳的事件
 *
 * <AUTHOR> @date 2022-12-16
 */
@Mapper
@DataSource(value = DataSourceType.SLAVE)
public interface InstructionJazzWwEventMapper {
    /**
     * 金安大数据基础数据
     * @return
     */
    List<BigScreenJazzCommonVo> basicData();

    /**
     * 统计某一类事件的数量
     * @return
     */
    List<String> selectCountByType(@Param("type") Integer type);

    /**
     * 统计某一类事件的详情
     * @param instructionEvent
     * @return
     */
    List<InstructionEvent> selectCountByTypeForDetails(InstructionEvent instructionEvent);

    /**
     *
     * @param instructionEvent
     * @return
     */
    List<HashMap> countTypeNum(InstructionEvent instructionEvent);
    /**
     *
     * @param type
     * @return
     */
    BigScreenJazzCommonVo countTypeBig(Integer type);
}
