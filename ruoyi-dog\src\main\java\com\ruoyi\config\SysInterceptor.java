package com.ruoyi.config;

import com.ruoyi.util.ThreadLocalUtil;
import com.ruoyi.util.UserCache;
import com.ruoyi.util.UserMapCache;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 系统拦截器
 * Created by Administrator on 2019/12/25/025.
 */
@Component
public class SysInterceptor implements HandlerInterceptor {

    /**
     * 请求执行前执行的
     * @param request
     * @param response
     * @param handler
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
//        System.out.println("拦截器执行------");
        String token = (String) request.getParameter("token");
        if(token != null){
            UserCache user = UserMapCache.getByToken(token);
            if(user != null){
                ThreadLocalUtil.addCurrentUser(user);
            }
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        /**
         * 请求后执行
         */
        ThreadLocalUtil.remove();
    }
}
