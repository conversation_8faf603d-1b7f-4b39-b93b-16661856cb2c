package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.InstructionRealtimeinfo;

import java.util.List;

/**
 * 【实时信息】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-12
 */
@DataSource(value = DataSourceType.SLAVE)
public interface InstructionRealtimeinfoMapper
{
    /**
     * 查询【实时信息】
     *
     * @param id 【实时信息】主键id
     * @return
     */
    public InstructionRealtimeinfo selectInstructionRealtimeinfoById(Long id);

    /**
     * 查询【实时信息】列表
     *
     * @param instructionRealtimeinfo
     * @return 【实时信息】集合
     */
    public List<InstructionRealtimeinfo> selectInstructionRealtimeinfoList(InstructionRealtimeinfo instructionRealtimeinfo);

    /**
     * 新增【实时信息】
     *
     * @param instructionRealtimeinfo
     * @return 结果
     */
    public int insertInstructionRealtimeinfo(InstructionRealtimeinfo instructionRealtimeinfo);

    /**
     * 修改【实时信息】
     *
     * @param instructionRealtimeinfo
     * @return 结果
     */
    public int updateInstructionRealtimeinfo(InstructionRealtimeinfo instructionRealtimeinfo);

    /**
     * 删除【实时信息】
     *
     * @param id 【实时信息】主键
     * @return 结果
     */
    public int deleteInstructionRealtimeinfoById(Long id);

    /**
     * 批量删除【实时信息】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionRealtimeinfoByIds(Long[] ids);
}
