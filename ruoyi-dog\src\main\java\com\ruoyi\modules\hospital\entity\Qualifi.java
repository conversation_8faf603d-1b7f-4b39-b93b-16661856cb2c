package com.ruoyi.modules.hospital.entity;

import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.user.entity.SysUploadFile;

import java.util.List;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 9:52
 * @version: 1.0
 **/
public class Qualifi extends BaseEntity {
    private String name;                         //医院名称
    private String county;                       //所在县
    private String street;                       //所在街道
    private String area;                         //所在地区
    private String address;                      //详细地址
    private String lon;                          //经纬度
    private String lat;                          //经纬度
    private String person;                       //负责人
    private String tel;                          //联系电话
    private String type;                         //类型 0：宠物医院，1：执法部门 2：救助站
    private String qualifi;                      //资质类型 0：免疫资质 1：发放资质
    private String account;                      //账号
    private String password;                     //密码
    private String remarks;                      //备注
    private String status;                       //状态 1:审核中，2:已通过，3:未通过
    private String unifiedCode;             //统一信用社代码
    private String lawStatus;   //执法局状态（1：待审核 2：通过 3：未通过）
    private String aniStatus;   //畜牧局状态（1：待审核 2：通过 3：未通过）
    private String canStatus;   //免疫注销状态（1：待审核 2：通过 3：未通过）
    private String brandStatus; //犬牌注销状态（1：待审核 2：通过 3：未通过）
    private List<SysUploadFile> uploadFileList;  //附件
    private String uploadFileStr;                //附件字符串
    private String aboutAuth; //预约方式权限
    private String canType; //注销类型 （1：免疫注销 2：犬牌注销）

    private String reason;//审批意见
    private String canReason;//免疫注销原因
    private String brandReason;//犬牌注销原因

    private String distance;//距离
    private String doorway;                //门头照片
    private String keyWord;//关键字查询条件

    private Integer node; //节点名称
    private String createName; //操作人名称
    private Integer recordType;//操作类型
    private String recordRemark;//审核意见
    private Integer recordStatus;//状态

    private List<String> vaccineList;

    public List<String> getVaccineList() {
        return vaccineList;
    }

    public void setVaccineList(List<String> vaccineList) {
        this.vaccineList = vaccineList;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPerson() {
        return person;
    }

    public void setPerson(String person) {
        this.person = person;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getQualifi() {
        return qualifi;
    }

    public void setQualifi(String qualifi) {
        this.qualifi = qualifi;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<SysUploadFile> getUploadFileList() {
        return uploadFileList;
    }

    public void setUploadFileList(List<SysUploadFile> uploadFileList) {
        this.uploadFileList = uploadFileList;
    }

    public String getUploadFileStr() {
        return uploadFileStr;
    }

    public void setUploadFileStr(String uploadFileStr) {
        this.uploadFileStr = uploadFileStr;
    }

    public String getUnifiedCode() {
        return unifiedCode;
    }

    public void setUnifiedCode(String unifiedCode) {
        this.unifiedCode = unifiedCode;
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getDistance() {
        return distance;
    }

    public void setDistance(String distance) {
        this.distance = distance;
    }

    public String getLawStatus() {
        return lawStatus;
    }

    public void setLawStatus(String lawStatus) {
        this.lawStatus = lawStatus;
    }

    public String getAniStatus() {
        return aniStatus;
    }

    public void setAniStatus(String aniStatus) {
        this.aniStatus = aniStatus;
    }

    public String getCanStatus() {
        return canStatus;
    }

    public void setCanStatus(String canStatus) {
        this.canStatus = canStatus;
    }

    public String getDoorway() {
        return doorway;
    }

    public void setDoorway(String doorway) {
        this.doorway = doorway;
    }

    public String getAboutAuth() {
        return aboutAuth;
    }

    public void setAboutAuth(String aboutAuth) {
        this.aboutAuth = aboutAuth;
    }

    public String getCanType() {
        return canType;
    }

    public void setCanType(String canType) {
        this.canType = canType;
    }

    public Integer getNode() {
        return node;
    }

    public void setNode(Integer node) {
        this.node = node;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Integer getRecordType() {
        return recordType;
    }

    public void setRecordType(Integer recordType) {
        this.recordType = recordType;
    }

    public String getRecordRemark() {
        return recordRemark;
    }

    public void setRecordRemark(String recordRemark) {
        this.recordRemark = recordRemark;
    }

    public String getBrandStatus() {
        return brandStatus;
    }

    public void setBrandStatus(String brandStatus) {
        this.brandStatus = brandStatus;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(Integer recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getCanReason() {
        return canReason;
    }

    public void setCanReason(String canReason) {
        this.canReason = canReason;
    }

    public String getBrandReason() {
        return brandReason;
    }

    public void setBrandReason(String brandReason) {
        this.brandReason = brandReason;
    }
}
