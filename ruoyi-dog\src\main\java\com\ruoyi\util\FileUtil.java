package com.ruoyi.util;

import com.ruoyi.modules.user.dao.SysUploadLocalDao;
import com.ruoyi.modules.user.entity.SysUploadLocal;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Date;

/**
 * Created by Administrator on 2021-10-21.
 */
public class FileUtil {

    /**
     * 上传本地附件对象
     */
    private static SysUploadLocalDao sysUploadLocalDao = SpringApplicationContextUtil.getBean(SysUploadLocalDao.class);

    /**
     * 文件上传本地路径
     * @param multipartFile
     * @throws IOException
     */
    public static String upload(MultipartFile multipartFile,String localUploadPrefix) throws IOException {
        //文件信息
        String fileName = multipartFile.getOriginalFilename();
        String fileType = fileName.substring(fileName.lastIndexOf("."),fileName.length());
        String fileKey = IdGen.uuid();
        String absoluteDirectory = localUploadPrefix + DateUtil.format(new Date(),"yyyy-MM-dd") + "/";
        File pathFile = new File(absoluteDirectory);
        if (!pathFile.exists()){
            pathFile.mkdirs();
        }
        File targetFile = new File(pathFile, fileKey + fileType);
        multipartFile.transferTo(targetFile);

        // 保存附件
        SysUploadLocal sysUploadLocal = new SysUploadLocal();
        sysUploadLocal.preInsert();
        sysUploadLocal.setFileKey(fileKey);
        sysUploadLocal.setFileUrl(targetFile.getPath());
        sysUploadLocal.setFileName(fileName);
        sysUploadLocalDao.insert(sysUploadLocal);
        return fileKey;
    }

}
