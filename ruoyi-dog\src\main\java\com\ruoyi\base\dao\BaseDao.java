package com.ruoyi.base.dao;

import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by Administrator on 2020/1/2/002.
 */
@Repository
public interface BaseDao <T> {

    /**
     * 根据ID获取单条数据
     * @param id
     * @return
     */
    public T getById(String id);

    /**
     * 根据条件获取单条数据
     * @param entity
     * @return
     */
    public T getByEntity(T entity);

    /**
     * 根据条件获取多条数据
     * @param entity
     * @return
     */
    public List<T> getList(T entity);

    /**
     * 插入数据
     * @param entity
     * @return
     */
    public void insert(T entity);

    /**
     * 更新数据
     * @param entity
     * @return
     */
    public void update(T entity);

    /**
     * 删除数据
     * @param entity
     * @return
     */
    public void delete(T entity);

}
