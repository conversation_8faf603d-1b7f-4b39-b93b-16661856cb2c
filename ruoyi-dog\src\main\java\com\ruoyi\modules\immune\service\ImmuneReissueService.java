package com.ruoyi.modules.immune.service;


import com.alibaba.fastjson2.JSON;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.modules.brand.dao.PetBrandDao;
import com.ruoyi.modules.brand.entity.PetBrand;
import com.ruoyi.modules.certificates.dao.PetCertificatesDao;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.immune.dao.ImmuneReissueDao;
import com.ruoyi.modules.immune.entity.ImmuneReissue;
import com.ruoyi.modules.petRecord.dao.PetRecordDao;
import com.ruoyi.modules.petRecord.entity.PetRecord;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.service.SysUploadFileService;
import com.ruoyi.util.QRCode;
import com.ruoyi.util.ThreadLocalUtil;
import com.ruoyi.util.UserCache;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.Date;
import java.util.List;

@Service
public class ImmuneReissueService extends BaseService<ImmuneReissueDao, ImmuneReissue> {

    @Resource
    PetCertificatesDao petCertificatesDao;

    @Resource
    SysUploadFileService sysUploadFileService;
    @Resource
    PetBrandDao petBrandDao;
    @Value("${petBrandServiceUrl}")
    private String petBrandServiceUrl;

    @Autowired
    PetRecordDao petRecordDao;

    @Transactional
    @Override
    public void saveOrUpdate(ImmuneReissue immuneReissue){
        super.saveOrUpdate(immuneReissue);
        //修改犬只表状态
        PetCertificates petCertificates = new PetCertificates();
        petCertificates.setId(immuneReissue.getPetId());
        petCertificates.setIsReissue(immuneReissue.getStatus());
        petCertificates.setPetNum(immuneReissue.getDogBrand());//新犬牌
//        petCertificates.setApplyStatus(2);//犬牌申请状态 2：待审核
        petCertificatesDao.updateIsReissue(petCertificates);
        if(StringUtils.isNotEmpty(immuneReissue.getDogBrand())){//申请补办，新犬牌不为空
            //更改原犬牌状态，补办审核通过不修改原来犬牌
            PetBrand petBrand = new PetBrand();
            petBrand.setBrandNum(immuneReissue.getBrandNum());
            petBrand.setIsCancellation(2);
            petBrand.setOffDate(new Date());
            SysUser user = SecurityUtils.getLoginUser().getUser();
            // UserCache user = ThreadLocalUtil.getCurrentUser();
            petBrand.setOffCom(user.getDogUserId());
            petBrandDao.updateIsCancellation(petBrand);
            petBrandDao.updatePetNumUse(immuneReissue.getDogBrand(),"1");//新犬牌已使用

        }
        String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
        //流程记录
        PetRecord petRecord = new PetRecord();
        petRecord.preInsert();
        petRecord.setCreateName(nickName);
        petRecord.setPetId(immuneReissue.getPetId());//犬牌ID
        petRecord.setPetIdCard("");
        petRecord.setPetNum(immuneReissue.getDogBrand());
        petRecord.setNode(4);
        petRecord.setStatus(1);
        petRecord.setStrDate(JSON.toJSONString(immuneReissue));
        petRecord.setRemark(immuneReissue.getContent());
        petRecord.setOldPetNum(immuneReissue.getBrandNum());
        petRecordDao.insert(petRecord);
    }
    @Transactional
    public void saveOrUpdateGr(ImmuneReissue immuneReissue){
        super.saveOrUpdate(immuneReissue);
        //修改犬只表状态
        PetCertificates petCertificates = new PetCertificates();
        petCertificates.setId(immuneReissue.getPetId());
        petCertificates.setIsReissue(2);
        petCertificates.setPetNum(immuneReissue.getDogBrand());//新犬牌
        petCertificatesDao.updateIsReissue(petCertificates);

    }

    @Transactional
    public String updateStatus(ImmuneReissue immuneReissue){
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // UserCache user = ThreadLocalUtil.getCurrentUser();
        String result = null;
        if (immuneReissue.getStatus() == 2 && StringUtils.isEmpty(immuneReissue.getDogBrand())) {//用户补办审核通过
            throw  new GlobalException("未选择犬牌");
//            PetCertificates petCertificates = petCertificatesDao.getById(immuneReissue.getPetId());
//            // start 更改原犬牌状态，补办审核通过，注销原来犬牌
//            PetBrand petBrand = new PetBrand();
//            petBrand.setBrandNum(immuneReissue.getBrandNum());
//            petBrand.setIsCancellation(2);
//            petBrand.setOffDate(new Date());
//            petBrand.setOffCom(user.getDogUserId());
//            petBrandDao.updateIsCancellation(petBrand);
//            /*end*/
//            //start 修改犬只表
//            petCertificates.setPetNum("");
//            petCertificates.setIsReissue(2);
//            petCertificates.preUpdate();
//            petCertificates.setActivation("1");//用户犬证补办变成未激活
//            petCertificates.setZfckId(immuneReissue.getZfckId());
//            petCertificates.setExpresRegion(immuneReissue.getArea());
//            petCertificates.setExpresAddress(immuneReissue.getAddress());
//            petCertificates.setExpresType(immuneReissue.getSendType()+"");
//            petCertificatesDao.updatePetNum(petCertificates);
            /*end*/
//            result = "审核成功";
        } else if (immuneReissue.getStatus() == 2 && StringUtils.isNotEmpty(immuneReissue.getDogBrand())) {//宠物医院补办审核通过
            PetCertificates petCertificates = petCertificatesDao.getById(immuneReissue.getPetId());
            petCertificates.setPetNum(immuneReissue.getDogBrand());
            petCertificates.setIsReissue(2);
            petCertificates.preUpdate();
            petCertificates.setActivation("2");//用户犬证补办变成已激活
            petCertificates.setExpresRegion(immuneReissue.getArea());
            petCertificates.setExpresAddress(immuneReissue.getAddress());
            petCertificates.setExpresType(immuneReissue.getSendType()+"");
            petCertificatesDao.updatePetNum(petCertificates);
            /*生成犬牌码*/
            PetBrand p = new PetBrand();
            p.setBrandNum(immuneReissue.getDogBrand());
            PetBrand brand = petBrandDao.getByEntity(p);
            if (brand != null) {
                brand.setQrCode(QRCode.createQrImg(petBrandServiceUrl + brand.getBrandNum()));
                petBrandDao.assignDogCard(brand);
            }
            result = "审核成功";
        } else {
            PetCertificates petCertificates = petCertificatesDao.getById(immuneReissue.getPetId());
            petCertificates.setIsReissue(3);
            petCertificates.preUpdate();
            petCertificates.setPetNum(immuneReissue.getBrandNum());//不通过还原犬牌
            petCertificatesDao.updatePetNum(petCertificates);
            if(StringUtils.isNotEmpty(immuneReissue.getDogBrand())){//不通过，新犬牌不为空
                //更改原犬牌状态，补办审核通过不修改原来犬牌
//                PetBrand petBrand = new PetBrand();
//                petBrand.setBrandNum(immuneReissue.getBrandNum());
//                petBrand.setIsCancellation(1);
//                petBrand.setOffDate(null);
//                petBrand.setOffCom("");
//                petBrandDao.updateIsCancellation(petBrand);//注销状态还原
//                petBrandDao.updatePetNumUse(immuneReissue.getDogBrand(),"2");//新犬牌未使用
//                immuneReissue.setDogBrand("");
            }
            result = "审核成功";
        }
        dao.updateStatus(immuneReissue);
        String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
        //流程记录
        PetRecord petRecord = new PetRecord();
        petRecord.preInsert();
        petRecord.setCreateName(nickName);
        petRecord.setPetId(immuneReissue.getPetId());//犬牌ID
        petRecord.setPetIdCard("");
        petRecord.setPetNum(immuneReissue.getDogBrand());
        petRecord.setNode(5);
        petRecord.setStatus(immuneReissue.getStatus());
        petRecord.setStrDate(JSON.toJSONString(immuneReissue));
        petRecord.setRemark(immuneReissue.getReason());
        petRecord.setOldPetNum(immuneReissue.getBrandNum());
        petRecordDao.insert(petRecord);
        return result;
    }

    public ImmuneReissue getByPetId(ImmuneReissue immuneReissue){
        return dao.getByPetId(immuneReissue);
    }
}
