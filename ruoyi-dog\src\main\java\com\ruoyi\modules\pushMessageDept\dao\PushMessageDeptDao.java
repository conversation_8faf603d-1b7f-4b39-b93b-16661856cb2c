package com.ruoyi.modules.pushMessageDept.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.notice.entity.Notice;
import com.ruoyi.modules.pushMessageDept.entity.PushMessageDept;
import com.ruoyi.modules.user.entity.SysUploadFile;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 工作通知关联机构(push_message_dept)表数据库访问层
 *
 * <AUTHOR>
 */
@Repository
public interface PushMessageDeptDao extends BaseDao<PushMessageDept> {

    public void saveAllList(List<PushMessageDept> list);

    public void deleteMessageId(String messageId);

}
