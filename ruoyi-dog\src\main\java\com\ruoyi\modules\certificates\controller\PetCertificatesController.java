package com.ruoyi.modules.certificates.controller;


import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.modules.certificates.dao.DongyangDogDao;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.certificates.service.PetCertificatesService;
import com.ruoyi.modules.immune.dao.ImmuneRegisterDao;
import com.ruoyi.modules.immune.entity.ImmuneRegister;
import com.ruoyi.modules.immune.service.ImmuneTransferService;
import com.ruoyi.modules.petRecord.dao.PetRecordDao;
import com.ruoyi.util.IdGen;
import com.ruoyi.util.QRCode;
import org.apache.catalina.security.SecurityUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("petCertificates")
public class PetCertificatesController  extends BaseController {

    @Autowired
    private PetCertificatesService service;

    @Autowired
    private ImmuneRegisterDao immuneRegisterDao;

    @Autowired
    private PetRecordDao petRecordDao;

    @Autowired
    private ImmuneTransferService immuneTransferService;

    @Value("qrServiceUrl")
    private String qrServiceUrl;

    @Autowired
    private DongyangDogDao dongyangDogDao;

    /**
     * 犬类移动端 业务审核
     * @param petCertificates
     * @return
     */
    @RequestMapping("getToDoForYD")
    public AjaxResult getToDoForYD(PetCertificates petCertificates){
        if (petCertificates.getType() == 1) {
            //犬牌发放
            petCertificates.setApplyStatus(2);
            return AjaxResult.success(service.queryPageList(petCertificates));
        } else if (petCertificates.getType() == 2) {
            //犬牌补办
            petCertificates.setIsReissueFlag(1);
            petCertificates.setIsReissue(1);
            return AjaxResult.success(service.getPagerList(petCertificates));
        } else if (petCertificates.getType() == 3) {
            //犬只登记过户
            petCertificates.setTransferStatus("2");
            return AjaxResult.success(immuneTransferService.queryPageList(petCertificates));
        }else if (petCertificates.getType() == 4) {
            //已审核业务
            petCertificates.setDealPersonId(getUserId()+"");
            return AjaxResult.success(service.getPagerList(petCertificates));
        }
        return AjaxResult.success();
    }

    @RequestMapping("getAuditDoForYD")
    public AjaxResult getAuditDoForYD(PetCertificates petCertificates, String startTime, String endTime){
        if (petCertificates.getType()==null){
            petCertificates.setType(1);
        }

        petCertificates.setPetDept(SecurityUtils.getDeptId().toString());

        if (petCertificates.getType() == 1) {
            List<String> petIds = getPetIdList(2,startTime,endTime);
            if (petIds.size() == 0 && StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                return AjaxResult.success(new PageInfo<PetCertificates>()).put("examineType", petCertificates.getType());
            }
            //犬牌发放  已审核
            petCertificates.setApplyStatus(7);
            petCertificates.setPetIds(petIds);
            petCertificates.setEndTime(null);
            petCertificates.setStartTime(null);
            return AjaxResult.success(service.queryPageList(petCertificates)).put("examineType",petCertificates.getType());
        } else if (petCertificates.getType() == 2) {
            //犬牌补办
            List<String> petIds = getPetIdList(5,startTime,endTime);
            if (petIds.size() == 0 && StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                return AjaxResult.success(new PageInfo<PetCertificates>()).put("examineType", petCertificates.getType());
            }
            petCertificates.setPetIds(petIds);
            petCertificates.setEndTime(null);
            petCertificates.setStartTime(null);
            petCertificates.setIsReissueFlag(1);
            petCertificates.setSearchType(3);
            return AjaxResult.success(service.getPagerList(petCertificates)).put("examineType",petCertificates.getType());
        } else if (petCertificates.getType() == 3) {
            //犬只登记过户
            List<String> petIds = getPetIdList(8,startTime,endTime);
            if (petIds.size() == 0 && StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                return AjaxResult.success(new PageInfo<PetCertificates>()).put("examineType", petCertificates.getType());
            }
            petCertificates.setPetIds(petIds);
            petCertificates.setEndTime(null);
            petCertificates.setStartTime(null);
            petCertificates.setSearchType(3);
            return AjaxResult.success(immuneTransferService.queryPageList(petCertificates)).put("examineType",petCertificates.getType());
        }
        return AjaxResult.success(new PageInfo<PetCertificates>()).put("examineType", petCertificates.getType());
    }

    /**
     * 获取犬只Id
     * @param node
     * @param beginTime
     * @param endTime
     * @return
     */
    private List<String> getPetIdList(final int node, final String beginTime, final String endTime) {
        if (StringUtils.isEmpty(beginTime) && StringUtils.isEmpty(endTime)){
            return Lists.newArrayList();
        }
        List<String> petIds = petRecordDao.getPetIdList(node,beginTime,endTime);
        return petIds;
    }

    @RequestMapping("getPageList")
    public AjaxResult getPageList(PetCertificates petCertificates){
        return AjaxResult.success(service.getPagerList(petCertificates));
    }
    @RequestMapping("getPageListH5")
    public AjaxResult getPageListH5(PetCertificates petCertificates){
        return service.getPageListH5(petCertificates);
    }
    @RequestMapping("getByPetNum")
    public AjaxResult getByPetNum(String brandId){
        return AjaxResult.success(service.getByPetNum(brandId));
    }

    @RequestMapping("getByBrandNum")
    public AjaxResult getByBrandNum(String brandNum){
        return AjaxResult.success(service.getByBrandNum(brandNum));
    }

    @RequestMapping("getList")
    public AjaxResult getList(PetCertificates petCertificates){
        return AjaxResult.success(service.getPageList(petCertificates));
    }

    @RequestMapping("getById")
    public AjaxResult getById(String id){
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("saveOrUpdate")
    public AjaxResult saveOrUpdate(PetCertificates petCertificates){
        service.saveOrUpdate(petCertificates);
        return AjaxResult.success();
    }

    @RequestMapping("delete")
    public AjaxResult delete(PetCertificates petCertificates){
        service.delete(petCertificates);
        return AjaxResult.success();
    }

    @RequestMapping("upStatus")
    public AjaxResult upStatus(PetCertificates petCertificates){
        service.upStatus(petCertificates);
        return AjaxResult.success();
    }

    /**
     * 根据信息 查询登记办证，犬只过户，犬只注销列表
     * @param petCertificates
     * @return
     */
    @RequestMapping("queryPageList")
    public AjaxResult queryPageList(PetCertificates petCertificates){
        return AjaxResult.success(service.queryPageList(petCertificates));
    }


    /**
     *  根据犬只ID 查询犬只信息
     * @param petCertificates
     * @return
     */
    @RequestMapping("queryPetCount")
    public AjaxResult queryPetCount(PetCertificates petCertificates){
        return AjaxResult.success(service.queryPetCount(petCertificates));
    }

    /**
     *  根据信息查询犬只绑定免疫信息
     * @param petCertificates
     * @return
     */
    @RequestMapping("getPetAndRegister")
    public AjaxResult getPetAndRegister(PetCertificates petCertificates){
        return AjaxResult.success(service.getPetAndRegister(petCertificates));
    }

    /**
     *  根据信息查询犬只绑定免疫信息 移动端
     * @param petCertificates
     * @return
     */
    @RequestMapping("getPetAndRegisterH5")
    public AjaxResult getPetAndRegisterH5(PetCertificates petCertificates){
        return service.getPetAndRegisterH5(petCertificates);
    }

    /**
     * 根据犬只ID 查询犬只详细信息（犬主信息，免疫信息，过户信息等）
     * @param petId
     * @return
     */
    @RequestMapping("getPetDetails")
    public AjaxResult getPetDetails(String petId){
        return AjaxResult.success(service.getPetDetails(petId));
    }

    /**
     * 根据免疫过期、未免疫、即将过期进行免疫通知
     * @param type，content
     * @return
     */
    @RequestMapping("immuneNotice")
    public AjaxResult immuneNotice(String type, String content) {
        PetCertificates petCertificates = new PetCertificates();
        petCertificates.setStatus(Integer.valueOf(type));
        List<PetCertificates> list =service.getList(petCertificates);

        return AjaxResult.success(service.getList(petCertificates));
    }


    /**
     * 初始化车辆二维码图片 单个下载图片
     *
     * @return
     */
    @RequestMapping("/initPetQrById")
    public AjaxResult initBusQrById(String id, HttpServletRequest request, HttpServletResponse response) {
        PetCertificates petCertificates = service.getById(id);
        QRCode.createQrImg(qrServiceUrl + id);
        return AjaxResult.success(petCertificates);
    }

    @RequestMapping("updateImmune")
    public AjaxResult updateImmune(PetCertificates petCertificates){
        service.updateImmune(petCertificates);
        return AjaxResult.success();
    }

    /**
     * 犬证激活
     * @return
     */
    @RequestMapping("petActivation")
    public AjaxResult petActivation(PetCertificates petCertificates){
        service.petActivation(petCertificates);
        return AjaxResult.success();
    }

    /**
     * 犬证激活
     * @return
     */
    @RequestMapping("updateAbout")
    public AjaxResult updateAbout(PetCertificates petCertificates){
        service.updateAbout(petCertificates);
        return AjaxResult.success();
    }

    /**
     * @author: tongsiyu
     * @date: 2022/12/13 14:20
     * @Description:大屏-获取犬只登记
     */
    @RequestMapping("getQZDJ")
    public AjaxResult getQZDJ() {
        return AjaxResult.success(service.getQZDJ());
    }
    /**
     * @author: tongsiyu
     * @date: 2022/12/13 14:20
     * @Description:大屏-获取执法收容
     */
    @RequestMapping("getZFSR")
    public AjaxResult getZFSR() {
        return AjaxResult.success(service.getZFSR());
    }

    /**
     * 大屏数据获取
     * @return
     */
    @RequestMapping("/getScreenMap")
    public AjaxResult getScreenMap(){
        return AjaxResult.success(service.getScreenMap());
    }

    /**
     * 犬类驾驶舱统计接口
     * @param type
     * @return
     */
    @RequestMapping("getDogJscStatistics")
    public AjaxResult getDogJscStatistics(Integer type){
        Date startTime = null;
        //根据type 赋值startTime  当type=1 时 参考赋值本月第一天，当type=2时，赋值本季度第一天，当type=3时，赋值本年第一天
        switch (type){
            case 1:
                startTime = DateUtils.getMonthFirstDay();
                break;
            case 2:
                startTime = DateUtils.getQuarterStartDate();
                break;
            case 3:
                startTime = DateUtils.getYearStartDate();
                break;
        }

        Map<String,Object> map = immuneRegisterDao.getDogJscStatistics(startTime);
        return AjaxResult.success(map);
    }

    /**
     * 把东阳的犬类数据刷到当前表中
     * @return
     */
    @RequestMapping("flushDongyangDog")
    public AjaxResult flushDongyangDog() {
        try {
            // 查询东阳犬主数据
            List<Map<String, Object>> owners = dongyangDogDao.selectDongyangOwners();
            // 查询东阳犬只数据
            List<Map<String, Object>> pets = dongyangDogDao.selectDongyangPets();
            // 查询东阳审批数据
            List<Map<String, Object>> approves = dongyangDogDao.selectDongyangApproves();

            int successCount = 0;
            int errorCount = 0;

            // 遍历犬只数据，因为犬只是主要数据
            for (Map<String, Object> petData : pets) {
                try {
                    // 根据OID找到对应的犬主信息
                    Map<String, Object> ownerData = null;
                    List<Map<String, Object>> approveData = null;
                    Object oid = petData.get("OID");
                    if (oid != null) {
                        Map<Object, Map<String, Object>> ownMap = owners.stream()
                                .collect(Collectors.toMap(item -> item.get("OID"), Function.identity()));

                        ownerData = ownMap.get("OID");
                    }
                    // 根据OID找到犬只审核信息
                    if (oid != null) {
                        Map<Object, List<Map<String, Object>>> approveMap = approves.stream()
                                .collect(Collectors.groupingBy(item -> item.get("DID")));

                        approveData = approveMap.get("DID");
                    }

                    // 创建犬证登记记录
                    PetCertificates petCertificates = new PetCertificates();
                    petCertificates.setId(IdGen.uuid());

                    // 设置犬主信息
                    if (ownerData != null) {
                        petCertificates.setOwnerName(getString(ownerData.get("ONAME")));
                        petCertificates.setPetIdCard(getString(ownerData.get("OIDCARD")));
                        petCertificates.setTel(getString(ownerData.get("OMOBILE")));
                        petCertificates.setOwnerAddress(getString(ownerData.get("OADDRESS")));
                        petCertificates.setStreet(getString(ownerData.get("OJD")));

                        // 设置快递信息
                        String stype = getString(ownerData.get("STYPE"));
                        if ("自取".equals(stype)) {
                            petCertificates.setExpresType("1");
                        } else {
                            petCertificates.setExpresType("2");
                            petCertificates.setExpresAddress(getString(ownerData.get("SADDRESS")));
                        }
                    }

                    // 设置犬只信息
                    petCertificates.setPetName(getString(petData.get("DNAME")));
                    petCertificates.setPetNum(getString(petData.get("CNUM")));
                    petCertificates.setPetType("1"); // 默认为犬类

                    // 性别转换
                    String dsex = getString(petData.get("DSEX"));
                    if ("公".equals(dsex) || "雄".equals(dsex)) {
                        petCertificates.setPetSex(1);
                    } else if ("母".equals(dsex) || "雌".equals(dsex)) {
                        petCertificates.setPetSex(2);
                    }

                    petCertificates.setPetVarieties(getString(petData.get("DTYPE")));
                    petCertificates.setPetHair(getString(petData.get("DCOLOR")));

                    // 出生日期转换为年龄
                    String dbday = getString(petData.get("DBDAY"));
                    if (StringUtils.isNotEmpty(dbday)) {
                        try {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            Date birthDate = sdf.parse(dbday);
                            Date now = new Date();
                            long ageInMillis = now.getTime() - birthDate.getTime();
                            long ageInYears = ageInMillis / (365L * 24 * 60 * 60 * 1000);
                            petCertificates.setPetAge(String.valueOf(ageInYears));
                        } catch (ParseException e) {
                            petCertificates.setPetAge("1"); // 默认1岁
                        }
                    } else {
                        petCertificates.setPetAge("1"); // 默认1岁
                    }

                    // 设置犬只用途
                    petCertificates.setUseDescription(getString(petData.get("DUSE")));

                    // 设置犬只照片
                    String dogPicSplit = getString(petData.get("QZPICS"));
                    if (StringUtils.isNotBlank(dogPicSplit)) {
                        String[] split = dogPicSplit.split(",");
                        petCertificates.setPetImg(split[0]);
                    }

                    // 设置状态信息
                    Object flag = petData.get("FLAG");
                    if (flag != null) {
                        int flagValue = Integer.parseInt(flag.toString());
                        switch (flagValue) {
                            case 0:
                                petCertificates.setStatus(1); // 待审批
                                petCertificates.setApplyStatus(2); // 待审核
                                break;
                            case 1:
                                petCertificates.setStatus(2); // 已通过
                                petCertificates.setApplyStatus(3); // 审核通过
                                break;
                            case 2:
                                petCertificates.setStatus(4); // 走失注销
                                break;
                            case 3:
                                petCertificates.setStatus(3); // 已注销
                                break;
                            default:
                                petCertificates.setStatus(1); // 默认待审批
                                petCertificates.setApplyStatus(2);
                                break;
                        }
                    } else {
                        petCertificates.setStatus(1); // 默认待审批
                        petCertificates.setApplyStatus(2);
                    }

                    // 设置其他默认值
                    petCertificates.setSource(1); // 个人用户登记
                    petCertificates.setIsAgency(2); // 非代办
                    petCertificates.setIsReissue(1); // 正常
                    petCertificates.setYearStatus(1); // 待办理
                    petCertificates.setDelFlag(1); // 正常
                    petCertificates.setActivation("1"); // 未激活
                    petCertificates.setReceiveStatus("2"); // 未领取
                    petCertificates.setTricolor(1); // 绿色

                    // 设置创建信息
                    petCertificates.setCreateDate(new Date());
                    petCertificates.setCreateBy("dongyang_flush");

                    // 保存犬证信息
                    service.saveOrUpdate(petCertificates);

                    // 如果有免疫信息，创建免疫记录
                    String dmynum = getString(petData.get("DMYNUM"));
                    String dmyday = getString(petData.get("DMYDAY"));
                    if (StringUtils.isNotEmpty(dmynum) || StringUtils.isNotEmpty(dmyday)) {
                        ImmuneRegister immuneRegister = new ImmuneRegister();
                        immuneRegister.setId(IdGen.uuid());
                        immuneRegister.setPetId(petCertificates.getId());
                        immuneRegister.setImmuneCard(dmynum);

                        // 免疫日期
                        if (StringUtils.isNotEmpty(dmyday)) {
                            try {
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                Date immuneDate = sdf.parse(dmyday);
                                immuneRegister.setInjectionDate(immuneDate);

                                // 计算到期日期（一般免疫有效期为1年）
                                Date endDate = new Date(immuneDate.getTime() + 365L * 24 * 60 * 60 * 1000);
                                immuneRegister.setInjectionEnddate(endDate);
                                petCertificates.setEndDate(endDate);
                            } catch (ParseException e) {
                                // 解析失败，使用当前时间
                                immuneRegister.setInjectionDate(new Date());
                            }
                        }

                        // 设置免疫照片
                        immuneRegister.setVaccineBrand(getString(petData.get("MYIMG")));

                        // 设置默认值
                        immuneRegister.setType(1); // 正常免疫
                        immuneRegister.setStatus("3"); // 已通过
                        immuneRegister.setHospital("东阳市动物医院");
                        immuneRegister.setDoctor("系统导入");
                        immuneRegister.setVaccineBrand("狂犬疫苗");
                        immuneRegister.setCrossType(1); // 非跨区办理
                        immuneRegister.setDelFlag(1); // 正常
                        immuneRegister.setCreateDate(new Date());
                        immuneRegister.setCreateBy("dongyang_flush");

                        // 保存免疫记录
                        immuneRegisterDao.insert(immuneRegister);

                        // 更新犬证的免疫ID
                        petCertificates.setImmuneId(immuneRegister.getId());
                        service.saveOrUpdate(petCertificates);
                    }

                    successCount++;
                } catch (Exception e) {
                    errorCount++;
                    logger.error("处理犬只数据失败，DID: " + petData.get("DID"), e);
                }
            }

            return AjaxResult.success("数据刷新完成，成功: " + successCount + " 条，失败: " + errorCount + " 条");

        } catch (Exception e) {
            logger.error("刷新东阳犬类数据失败", e);
            return AjaxResult.error("数据刷新失败: " + e.getMessage());
        }
    }

    /**
     * 安全获取字符串值
     */
    private String getString(Object obj) {
        return obj != null ? obj.toString().trim() : "";
    }
}
