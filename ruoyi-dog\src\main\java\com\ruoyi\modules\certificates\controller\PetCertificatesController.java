package com.ruoyi.modules.certificates.controller;


import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.modules.certificates.dao.DongyangDogDao;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.certificates.service.PetCertificatesService;
import com.ruoyi.modules.immune.dao.ImmuneRegisterDao;
import com.ruoyi.modules.immune.service.ImmuneTransferService;
import com.ruoyi.modules.petRecord.dao.PetRecordDao;
import com.ruoyi.util.QRCode;
import org.apache.catalina.security.SecurityUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("petCertificates")
public class PetCertificatesController  extends BaseController {

    @Autowired
    private PetCertificatesService service;

    @Autowired
    private ImmuneRegisterDao immuneRegisterDao;

    @Autowired
    private PetRecordDao petRecordDao;

    @Autowired
    private ImmuneTransferService immuneTransferService;

    @Value("qrServiceUrl")
    private String qrServiceUrl;

    @Autowired
    private DongyangDogDao dongyangDogDao;

    /**
     * 犬类移动端 业务审核
     * @param petCertificates
     * @return
     */
    @RequestMapping("getToDoForYD")
    public AjaxResult getToDoForYD(PetCertificates petCertificates){
        if (petCertificates.getType() == 1) {
            //犬牌发放
            petCertificates.setApplyStatus(2);
            return AjaxResult.success(service.queryPageList(petCertificates));
        } else if (petCertificates.getType() == 2) {
            //犬牌补办
            petCertificates.setIsReissueFlag(1);
            petCertificates.setIsReissue(1);
            return AjaxResult.success(service.getPagerList(petCertificates));
        } else if (petCertificates.getType() == 3) {
            //犬只登记过户
            petCertificates.setTransferStatus("2");
            return AjaxResult.success(immuneTransferService.queryPageList(petCertificates));
        }else if (petCertificates.getType() == 4) {
            //已审核业务
            petCertificates.setDealPersonId(getUserId()+"");
            return AjaxResult.success(service.getPagerList(petCertificates));
        }
        return AjaxResult.success();
    }

    @RequestMapping("getAuditDoForYD")
    public AjaxResult getAuditDoForYD(PetCertificates petCertificates, String startTime, String endTime){
        if (petCertificates.getType()==null){
            petCertificates.setType(1);
        }

        petCertificates.setPetDept(SecurityUtils.getDeptId().toString());

        if (petCertificates.getType() == 1) {
            List<String> petIds = getPetIdList(2,startTime,endTime);
            if (petIds.size() == 0 && StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                return AjaxResult.success(new PageInfo<PetCertificates>()).put("examineType", petCertificates.getType());
            }
            //犬牌发放  已审核
            petCertificates.setApplyStatus(7);
            petCertificates.setPetIds(petIds);
            petCertificates.setEndTime(null);
            petCertificates.setStartTime(null);
            return AjaxResult.success(service.queryPageList(petCertificates)).put("examineType",petCertificates.getType());
        } else if (petCertificates.getType() == 2) {
            //犬牌补办
            List<String> petIds = getPetIdList(5,startTime,endTime);
            if (petIds.size() == 0 && StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                return AjaxResult.success(new PageInfo<PetCertificates>()).put("examineType", petCertificates.getType());
            }
            petCertificates.setPetIds(petIds);
            petCertificates.setEndTime(null);
            petCertificates.setStartTime(null);
            petCertificates.setIsReissueFlag(1);
            petCertificates.setSearchType(3);
            return AjaxResult.success(service.getPagerList(petCertificates)).put("examineType",petCertificates.getType());
        } else if (petCertificates.getType() == 3) {
            //犬只登记过户
            List<String> petIds = getPetIdList(8,startTime,endTime);
            if (petIds.size() == 0 && StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                return AjaxResult.success(new PageInfo<PetCertificates>()).put("examineType", petCertificates.getType());
            }
            petCertificates.setPetIds(petIds);
            petCertificates.setEndTime(null);
            petCertificates.setStartTime(null);
            petCertificates.setSearchType(3);
            return AjaxResult.success(immuneTransferService.queryPageList(petCertificates)).put("examineType",petCertificates.getType());
        }
        return AjaxResult.success(new PageInfo<PetCertificates>()).put("examineType", petCertificates.getType());
    }

    /**
     * 获取犬只Id
     * @param node
     * @param beginTime
     * @param endTime
     * @return
     */
    private List<String> getPetIdList(final int node, final String beginTime, final String endTime) {
        if (StringUtils.isEmpty(beginTime) && StringUtils.isEmpty(endTime)){
            return Lists.newArrayList();
        }
        List<String> petIds = petRecordDao.getPetIdList(node,beginTime,endTime);
        return petIds;
    }

    @RequestMapping("getPageList")
    public AjaxResult getPageList(PetCertificates petCertificates){
        return AjaxResult.success(service.getPagerList(petCertificates));
    }
    @RequestMapping("getPageListH5")
    public AjaxResult getPageListH5(PetCertificates petCertificates){
        return service.getPageListH5(petCertificates);
    }
    @RequestMapping("getByPetNum")
    public AjaxResult getByPetNum(String brandId){
        return AjaxResult.success(service.getByPetNum(brandId));
    }

    @RequestMapping("getByBrandNum")
    public AjaxResult getByBrandNum(String brandNum){
        return AjaxResult.success(service.getByBrandNum(brandNum));
    }

    @RequestMapping("getList")
    public AjaxResult getList(PetCertificates petCertificates){
        return AjaxResult.success(service.getPageList(petCertificates));
    }

    @RequestMapping("getById")
    public AjaxResult getById(String id){
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("saveOrUpdate")
    public AjaxResult saveOrUpdate(PetCertificates petCertificates){
        service.saveOrUpdate(petCertificates);
        return AjaxResult.success();
    }

    @RequestMapping("delete")
    public AjaxResult delete(PetCertificates petCertificates){
        service.delete(petCertificates);
        return AjaxResult.success();
    }

    @RequestMapping("upStatus")
    public AjaxResult upStatus(PetCertificates petCertificates){
        service.upStatus(petCertificates);
        return AjaxResult.success();
    }

    /**
     * 根据信息 查询登记办证，犬只过户，犬只注销列表
     * @param petCertificates
     * @return
     */
    @RequestMapping("queryPageList")
    public AjaxResult queryPageList(PetCertificates petCertificates){
        return AjaxResult.success(service.queryPageList(petCertificates));
    }


    /**
     *  根据犬只ID 查询犬只信息
     * @param petCertificates
     * @return
     */
    @RequestMapping("queryPetCount")
    public AjaxResult queryPetCount(PetCertificates petCertificates){
        return AjaxResult.success(service.queryPetCount(petCertificates));
    }

    /**
     *  根据信息查询犬只绑定免疫信息
     * @param petCertificates
     * @return
     */
    @RequestMapping("getPetAndRegister")
    public AjaxResult getPetAndRegister(PetCertificates petCertificates){
        return AjaxResult.success(service.getPetAndRegister(petCertificates));
    }

    /**
     *  根据信息查询犬只绑定免疫信息 移动端
     * @param petCertificates
     * @return
     */
    @RequestMapping("getPetAndRegisterH5")
    public AjaxResult getPetAndRegisterH5(PetCertificates petCertificates){
        return service.getPetAndRegisterH5(petCertificates);
    }

    /**
     * 根据犬只ID 查询犬只详细信息（犬主信息，免疫信息，过户信息等）
     * @param petId
     * @return
     */
    @RequestMapping("getPetDetails")
    public AjaxResult getPetDetails(String petId){
        return AjaxResult.success(service.getPetDetails(petId));
    }

    /**
     * 根据免疫过期、未免疫、即将过期进行免疫通知
     * @param type，content
     * @return
     */
    @RequestMapping("immuneNotice")
    public AjaxResult immuneNotice(String type, String content) {
        PetCertificates petCertificates = new PetCertificates();
        petCertificates.setStatus(Integer.valueOf(type));
        List<PetCertificates> list =service.getList(petCertificates);

        return AjaxResult.success(service.getList(petCertificates));
    }


    /**
     * 初始化车辆二维码图片 单个下载图片
     *
     * @return
     */
    @RequestMapping("/initPetQrById")
    public AjaxResult initBusQrById(String id, HttpServletRequest request, HttpServletResponse response) {
        PetCertificates petCertificates = service.getById(id);
        QRCode.createQrImg(qrServiceUrl + id);
        return AjaxResult.success(petCertificates);
    }

    @RequestMapping("updateImmune")
    public AjaxResult updateImmune(PetCertificates petCertificates){
        service.updateImmune(petCertificates);
        return AjaxResult.success();
    }

    /**
     * 犬证激活
     * @return
     */
    @RequestMapping("petActivation")
    public AjaxResult petActivation(PetCertificates petCertificates){
        service.petActivation(petCertificates);
        return AjaxResult.success();
    }

    /**
     * 犬证激活
     * @return
     */
    @RequestMapping("updateAbout")
    public AjaxResult updateAbout(PetCertificates petCertificates){
        service.updateAbout(petCertificates);
        return AjaxResult.success();
    }

    /**
     * @author: tongsiyu
     * @date: 2022/12/13 14:20
     * @Description:大屏-获取犬只登记
     */
    @RequestMapping("getQZDJ")
    public AjaxResult getQZDJ() {
        return AjaxResult.success(service.getQZDJ());
    }
    /**
     * @author: tongsiyu
     * @date: 2022/12/13 14:20
     * @Description:大屏-获取执法收容
     */
    @RequestMapping("getZFSR")
    public AjaxResult getZFSR() {
        return AjaxResult.success(service.getZFSR());
    }

    /**
     * 大屏数据获取
     * @return
     */
    @RequestMapping("/getScreenMap")
    public AjaxResult getScreenMap(){
        return AjaxResult.success(service.getScreenMap());
    }

    /**
     * 犬类驾驶舱统计接口
     * @param type
     * @return
     */
    @RequestMapping("getDogJscStatistics")
    public AjaxResult getDogJscStatistics(Integer type){
        Date startTime = null;
        //根据type 赋值startTime  当type=1 时 参考赋值本月第一天，当type=2时，赋值本季度第一天，当type=3时，赋值本年第一天
        switch (type){
            case 1:
                startTime = DateUtils.getMonthFirstDay();
                break;
            case 2:
                startTime = DateUtils.getQuarterStartDate();
                break;
            case 3:
                startTime = DateUtils.getYearStartDate();
                break;
        }

        Map<String,Object> map = immuneRegisterDao.getDogJscStatistics(startTime);
        return AjaxResult.success(map);
    }

    /**
     * 把东阳的犬类数据刷到当前表中
     * @return
     */
    @RequestMapping("flushDongyangDog")
    public AjaxResult flushDongyangDog() {


        return AjaxResult.success();
    }
}
