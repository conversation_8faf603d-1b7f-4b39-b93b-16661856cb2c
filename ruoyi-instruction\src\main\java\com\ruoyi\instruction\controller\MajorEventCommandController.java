package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.MajorEventCommand;
import com.ruoyi.instruction.service.IMajorEventCommandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 重大事件-跟踪指挥Controller
 * 
 * <AUTHOR>
 * @date 2023-03-02
 */
@RestController
@RequestMapping("/instruction/majorEventCommand")
public class MajorEventCommandController extends BaseController
{
    @Autowired
    private IMajorEventCommandService majorEventCommandService;

    /**
     * 查询重大事件-跟踪指挥列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:majorEventCommand:list')")
    @GetMapping("/list")
    public TableDataInfo list(MajorEventCommand majorEventCommand)
    {
        startPage();
        List<MajorEventCommand> list = majorEventCommandService.selectMajorEventCommandList(majorEventCommand);
        return getDataTable(list);
    }

    /**
     * 导出重大事件-跟踪指挥列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:majorEventCommand:export')")
    @Log(title = "重大事件-跟踪指挥", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MajorEventCommand majorEventCommand)
    {
        List<MajorEventCommand> list = majorEventCommandService.selectMajorEventCommandList(majorEventCommand);
        ExcelUtil<MajorEventCommand> util = new ExcelUtil<MajorEventCommand>(MajorEventCommand.class);
        util.exportExcel(response, list, "重大事件-跟踪指挥数据");
    }

    /**
     * 获取重大事件-跟踪指挥详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:majorEventCommand:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(majorEventCommandService.selectMajorEventCommandById(id));
    }

    /**
     * 新增重大事件-跟踪指挥
     */
    @PreAuthorize("@ss.hasPermi('instruction:majorEventCommand:add')")
    @Log(title = "重大事件-跟踪指挥", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MajorEventCommand majorEventCommand)
    {
        return toAjax(majorEventCommandService.insertMajorEventCommand(majorEventCommand));
    }

    /**
     * 修改重大事件-跟踪指挥
     */
    @PreAuthorize("@ss.hasPermi('instruction:majorEventCommand:edit')")
    @Log(title = "重大事件-跟踪指挥", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MajorEventCommand majorEventCommand)
    {
        return toAjax(majorEventCommandService.updateMajorEventCommand(majorEventCommand));
    }

    /**
     * 删除重大事件-跟踪指挥
     */
    @PreAuthorize("@ss.hasPermi('instruction:majorEventCommand:remove')")
    @Log(title = "重大事件-跟踪指挥", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(majorEventCommandService.deleteMajorEventCommandByIds(ids));
    }
}
