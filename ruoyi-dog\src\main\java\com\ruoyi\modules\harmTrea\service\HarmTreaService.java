package com.ruoyi.modules.harmTrea.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.modules.harmTrea.dao.HarmTreaDao;
import com.ruoyi.modules.harmTrea.entity.HarmTrea;
import com.ruoyi.modules.harmTrea.vo.HarmTreaBatchVO;
import com.ruoyi.modules.harmTrea.vo.HarmTreaVO;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.takeIn.dao.TakeInDao;
import com.ruoyi.modules.takeIn.entity.TakeIn;
import com.ruoyi.modules.takeIn.service.TakeInService;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.service.SysUploadFileService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 无害化处理记录表(harm_trea)表服务接口
 *
 * <AUTHOR>
 */
@Service
public class HarmTreaService extends BaseService<HarmTreaDao, HarmTrea> {
    @Autowired
    private TakeInDao takeInDao;
    @Autowired
    private TakeInService takeInService;
    @Autowired
    private SysUploadFileService uploadFileService;

    @Value("${pic.prefix-url}")
    private String picPrefixUrl;

    @Override
    public HarmTrea getById(String id){
        HarmTrea harmTrea = dao.getById(id);

        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.setInstanceId(id);
        harmTrea.setUploadFileList(uploadFileService.getList(sysUploadFile));

        return harmTrea;
    }

    @Transactional
    public void saveOrUpdate(HarmTrea entity) {
        if (entity.getId() != null && !entity.getId().equals("")) {
            // 更新操作
            entity.preUpdate();
            update(entity);

            takeInService.recordLog(entity.getTakeInId(), "无害化处理修改", null);
        } else {
            // 新增操作
            insert(entity);

            if (CollectionUtils.isNotEmpty(entity.getUploadFileList())) {
                List<SysUploadFile> list = entity.getUploadFileList();
                for (SysUploadFile s : list) {
                    s.setInstanceId(entity.getId());
                }

                uploadFileService.saveAllList(list);
            }

            takeInService.recordLog(entity.getTakeInId(), "无害化处理", null);
        }
    }

    @Transactional
    public void updateHarmStatus(HarmTrea entity) {
        dao.updateByEntity(entity);
    }

    public List<TakeIn> getNeedHarmList(String keywords) {
        List<TakeIn> takeInList = takeInDao.getNeedHarmList(keywords);

        return takeInList;
    }

    public PageInfo<HarmTreaVO> getNeedHarmListV2(HarmTreaVO harmTreaVO) {
        if (Objects.nonNull(harmTreaVO.getStartDate())) {
            harmTreaVO.setStartDate(DateUtils.getStartOfDay(harmTreaVO.getStartDate()));
            harmTreaVO.setEndDate(DateUtils.getEndOfDay(harmTreaVO.getEndDate()));
        }

        PageHelper.startPage(harmTreaVO.getPageNum(),harmTreaVO.getPageSize());

        List<HarmTreaVO> harmTreaVOList = takeInDao.getNeedHarmListV2(harmTreaVO);

        if (CollectionUtils.isNotEmpty(harmTreaVOList)) {
            for (HarmTreaVO treaVO : harmTreaVOList) {
                fillOneHarmTreaVO(treaVO);

                if (Objects.isNull(treaVO.getHarmTreaId())) {
                    treaVO.setStatus(0);
                } else {
                    treaVO.setStatus(1);
                }
            }
        }

        PageInfo<HarmTreaVO> pageInfo = new PageInfo<HarmTreaVO>(harmTreaVOList);

        return pageInfo;
    }

    private void fillOneHarmTreaVO(HarmTreaVO treaVO) {
        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.setInstanceId(treaVO.getId());

        List<SysUploadFile> fileList = uploadFileService.getList(sysUploadFile);
        if (CollectionUtils.isNotEmpty(fileList)) {
            treaVO.setUploadFileList(fileList);

            Optional<SysUploadFile> fileOpt = fileList.stream().filter(s -> "petImg".equals(s.getModelType())).findFirst();
            fileOpt.ifPresent(uploadFile -> treaVO.setImgUrl(picPrefixUrl + uploadFile.getFileUrl()));
        }

        long daysBetween = DateUtils.getDaysBetween(treaVO.getTakeInDate(), new Date());

        treaVO.setTakeInDuration(daysBetween + "天");
    }

    public void batchHandle(HarmTreaBatchVO batchVO) {
        for (String takeInId : batchVO.getTakeInIdList()) {
            HarmTrea exist = dao.getByTakeInId(takeInId);
            if (Objects.nonNull(exist)) {
                continue;
            }

            HarmTrea harmTrea = new HarmTrea();
            BeanUtils.copyProperties(batchVO, harmTrea);

            harmTrea.setTakeInId(takeInId);

            saveOrUpdate(harmTrea);
        }
    }

    public HarmTreaVO getByTakeInId(String id) {
        HarmTreaVO harmTreaVO = takeInDao.getHarmVOById(id);

        fillOneHarmTreaVO(harmTreaVO);

        HarmTrea harmTrea = dao.getByTakeInId(harmTreaVO.getId());
        if (Objects.nonNull(harmTrea)) {
            harmTreaVO.setHarmTrea(harmTrea);
        }

        return harmTreaVO;
    }
}
