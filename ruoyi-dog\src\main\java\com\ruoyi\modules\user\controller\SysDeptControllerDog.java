package com.ruoyi.modules.user.controller;

import com.ruoyi.base.entity.JsonResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.modules.sysDict.entity.SysDict;
import com.ruoyi.modules.user.entity.SysDept;
import com.ruoyi.modules.user.service.SysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @classname:
 * @description:
 * @author: 侯旭
 * @date: 2021/4/7 14:51
 * @version: 1.0
 **/
@RestController
@RequestMapping("dept")
public class SysDeptControllerDog {
    @Autowired
    private SysDeptService service;

    /**
     * 菜单树形结构数据
     * @param sysDept
     * @return
     */
    @RequestMapping("/getSecondDeptTree")
    public AjaxResult getSecondDeptTree(SysDept sysDept){
        List<SysDept> list = service.getSecondDeptTree(sysDept);
        return AjaxResult.success(list);
    }

    /**
     * 菜单树形结构数据
     * @param sysDept
     * @return
     */
    @RequestMapping("/getSysDeptTree")
    public AjaxResult getSysMenuTree(SysDept sysDept){
        List<SysDept> list = service.getSysDeptTree(sysDept);
        return AjaxResult.success(list);
    }

    @RequestMapping("getAllList")
    public AjaxResult getAllList(SysDept sysDept){
        sysDept.setPageNum(null);
        sysDept.setPageSize(null);
        return AjaxResult.success(service.getList(sysDept));
    }

    @RequestMapping("getPageList")
    public AjaxResult getPageList(SysDept sysDept){
        return AjaxResult.success(service.getPageList(sysDept));
    }

    @RequestMapping("getById")
    public AjaxResult getById(String id){
        return AjaxResult.success(service.getById(id));
    }

    @RequestMapping("saveOrUpdate")
    public AjaxResult saveOrUpdate(SysDept sysDept){
        service.saveOrUpdate(sysDept);
        return AjaxResult.success(sysDept);
    }

    @RequestMapping("delete")
    public AjaxResult delete(SysDept sysDept){
        service.delete(sysDept);
        return AjaxResult.success();
    }

    /**
     * 获取权限内部门数据
     * @return
     */
//    @RequestMapping("getRightSysDept")
//    public AjaxResult getRightSysDept(){
//        return AjaxResult.success(service.getRightSysDept());
//    }

}
