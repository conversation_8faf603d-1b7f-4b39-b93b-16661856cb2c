package com.ruoyi.modules.hospital.service;

import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.hospital.dao.QualifiRecordDao;
import com.ruoyi.modules.hospital.entity.QualifiRecord;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 资质审批记录(qualifi_record)表服务接口
 * <AUTHOR>
 *
 */
 @Service
public class QualifiRecordService  extends BaseService<QualifiRecordDao, QualifiRecord> {

   @Transactional
    public void saveOrUpdate(QualifiRecord entity) {
        if (entity.getId() != null && !entity.getId().equals("")) {
            // 更新操作
            entity.preUpdate();
            update(entity);
        } else {
            // 新增操作
            insert(entity);
        }
    }

}
