<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.notice.dao.NoticeDao">
    <sql id="columns">
        <trim suffixOverrides=",">
            a.id as id,
            a.notice_type as noticeType,
            a.title as title,
            a.content as content,
            a.is_first_page as isFirstPage,
            a.status as status,
            a.summary as summary,
            a.promulgator as promulgator,
            a.promulgator_id as promulgatorId,
            a.del_flag as delFlag,
            a.create_date as createDate,
            a.create_by as createBy,
            a.update_date as updateDate,
            a.update_by as updateBy,
            a.start_date as startDate,
            a.end_date as endDate,
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.notice.entity.Notice" resultType="com.ruoyi.modules.notice.entity.Notice">
        select
        <include refid="columns"/>
        from notice a
        where a.id =#{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.notice.entity.Notice" resultType="com.ruoyi.modules.notice.entity.Notice">
        select
        <include refid="columns"/>
        from notice a
        where a.del_flag =1
        <if test="title != null and title != ''">
            and  a.title like concat('%',#{title},'%')
        </if>
        <if test="content != null and content != ''">
            and a.content like concat('%',#{content},'%')
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        <if test="noticeType != null and noticeType != ''">
            and a.notice_type = #{noticeType}
        </if>
        <if test="isFirstPage != null and isFirstPage != ''">
            and a.is_first_page = #{isFirstPage}
            and a.end_date>CURDATE()
        </if>
        order by a.create_date desc
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.notice.entity.Notice">
        insert into notice
              (id,
               notice_type,
               title,
               content,
               is_first_page,
               status,
               summary,
               promulgator,
               promulgator_id,
               del_flag,
               create_date,
               create_by,
               update_date,
               update_by,
               start_date,
               end_date)
        values (#{id},
                #{noticeType},
                #{title},
                #{content},
                #{isFirstPage},
                #{status},
                #{summary},
                #{promulgator},
                #{promulgatorId},
                #{delFlag},
                #{createDate}, #{createBy},
                #{updateDate}, #{updateBy}, #{startDate}, #{endDate})
    </insert>

    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.notice.entity.Notice">
        update notice set
        <trim suffixOverrides=",">
            notice_type = #{noticeType},
            title = #{title},
            content = #{content},
            is_first_page = #{isFirstPage},
            status = #{status},
            summary = #{summary},
            promulgator = #{promulgator},
            promulgator_id = #{promulgatorId},
            del_flag = #{delFlag},
            create_by = #{createBy},
            update_date = #{updateDate},
            update_by = #{updateBy},
            start_date = #{startDate},
            end_date = #{endDate},
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.notice.entity.Notice">
        UPDATE notice
        SET del_flag=#{delFlag}, update_date = #{updateDate}, update_by = #{updateBy}
        where id = #{id}
    </delete>

    <update id="updateStatus" parameterType="com.ruoyi.modules.notice.entity.Notice">
        update notice set status = #{status} where id = #{id}
    </update>

</mapper>
