<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.user.dao.SysDeptDao">
    <sql id="userTreeColumns">
        a.id as "id",
        a.dept_name as "deptName",
        a.parent_id as "parentId",
        a.dept_admin as "deptAdmin",
        a.level as "level",
        a.is_main_flag as "isMainFlag",
        a.sort as "sort",
        a.lon as "lon",
        a.lat as "lat",
        a.un_del_flag as "unDelFlag",
        a.create_date as "createDate",
        a.update_date as "updateDate",a.send_address as sendAddress
    </sql>

    <select id="getList" resultType="com.ruoyi.modules.user.entity.SysDept">
        select
        <include refid="userTreeColumns" />
        from sys_dept_dog a
        where a.del_flag = 1
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="isMainFlag != null">
            and a.is_main_flag = #{isMainFlag}
        </if>
        <if test="deptName != null and deptName != ''">
            and a.dept_name = #{deptName}
        </if>
        <if test="parentId != null and parentId != ''">
            and a.parent_id = #{parentId}
        </if>
        <if test="deptAdmin != null and deptAdmin != ''">
            and a.dept_admin = #{deptAdmin}
        </if>
        <if test="level != null and level != ''">
            and a.level = #{level}
        </if>
        order by a.sort
    </select>

    <select id="getSysDeptNameList" resultType="com.ruoyi.modules.user.entity.SysDept">
        select
        <include refid="userTreeColumns" />
        from sys_dept_dog a
        where a.dept_name is not null
    </select>
    <select id="getById" resultType="com.ruoyi.modules.user.entity.SysDept">
        select
        <include refid="userTreeColumns" />
        from sys_dept_dog a
        where a.id = #{id}
    </select>

    <insert id="insert" parameterType="com.ruoyi.modules.user.entity.SysDept">
        insert into sys_dept_dog(
            id,
            dept_name,
            parent_id,
            dept_admin,
            level,
            is_main_flag,
            sort,
            lon,
            lat,
            create_date,
            create_by,
            del_flag,send_address
        )values (
                    #{id},
                    #{deptName},
                    #{parentId},
                    #{deptAdmin},
                    #{level},
                    #{isMainFlag},
                    #{sort},
                    #{lon},
                    #{lat},
                    #{createDate},
                    #{createBy},
                    #{delFlag},#{sendAddress}
        )
    </insert>

    <update id="update" >
        update sys_dept_dog set
                            dept_name = #{deptName},
                            parent_id = #{parentId},
                            dept_admin = #{deptAdmin},
                            level = #{level},
                            is_main_flag = #{isMainFlag},
                            sort = #{sort},
                            lon = #{lon},
                            lat = #{lat},
                            update_date = #{updateDate},
                            update_by = #{updateBy},send_address=#{sendAddress}
        where id = #{id}
    </update>

    <update id="delete" >
        update sys_dept_dog set
            del_flag = 2
        where id = #{id}
    </update>
</mapper>
