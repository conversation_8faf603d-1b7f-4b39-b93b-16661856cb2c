package com.ruoyi.common.utils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import cn.xfyun.api.LfasrClient;
import cn.xfyun.config.LfasrFailTypeEnum;
import cn.xfyun.config.LfasrOrderStatusEnum;
import cn.xfyun.model.response.lfasr.LfasrOrderResult;
import cn.xfyun.model.response.lfasr.LfasrPredictResult;
import cn.xfyun.model.response.lfasr.LfasrResponse;
import cn.xfyun.model.response.lfasr.LfasrTransResult;

import org.apache.commons.codec.binary.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Type;
import java.security.SignatureException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/23 16:04
 */
public class LfasrClientApp {
    private static final Logger logger = LoggerFactory.getLogger(LfasrClientApp.class);

    private static final Gson GSON = new Gson();

    /**
     * 服务鉴权参数
     */
    private static final String APP_ID = "1b5ca459";
    private static final String SECRET_KEY = "f5f000ca2cdc99c5db535081607ea158";

    /**
     * 音频文件路径
     * - 本地文件（默认、调用uploadFile方法）
     * - 远程Url（配合参数audioMode = urlLink使用、调用uploadUrl方法）
     */
    private static String audioFilePath;

    // private static final String AUDIO_URL = "https://openres.xfyun.cn/xfyundoc/2025-03-19/e7b6a79d-124f-44e0-b8aa-0e799410f453/1742353716311/lfasr.wav";
    private static final String AUDIO_URL = "http://10.45.178.221:9100/file/upload/2025/05/23/tts1_20250523162138A001.mp3";

    /**
     * 任务类型
     * - transfer：转写
     * - translate：翻译（配合参数transLanguage和transMode使用）
     * - predict：质检（配合控制台质检词库使用）
     * - transfer,predict：转写 + 质检
     */
    private static final String TASK_TYPE = "transfer";

    static {
        try {
            audioFilePath = "E:/szjh_tll/codeFile/websdk-java-demo-main/target/classes//audio/lfasr.wav";
        } catch (Exception e) {
            logger.error("资源路径获取失败", e);
        }
    }

    /**
     * 获取音频文件转写结果
     *
     * @param audioFilePath 音频文件路径
     * @return 转写结果文本
     * @throws SignatureException 签名异常
     * @throws InterruptedException 中断异常
     */
    public static String getTranscriptionResult(String audioFilePath) throws SignatureException, InterruptedException {
        // 1、创建客户端实例
        LfasrClient lfasrClient = new LfasrClient.Builder(APP_ID, SECRET_KEY)
                .build();

        // 2、上传音频文件
        logger.info("音频上传中...");
        LfasrResponse uploadResponse = lfasrClient.uploadFile(audioFilePath);
        if (uploadResponse == null) {
            logger.error("上传失败，响应为空");
            return null;
        }
        if (!org.apache.commons.codec.binary.StringUtils.equals(uploadResponse.getCode(), "000000")) {
            logger.error("上传失败，错误码：{}，错误信息：{}", uploadResponse.getCode(), uploadResponse.getDescInfo());
            return null;
        }
        String orderId = uploadResponse.getContent().getOrderId();
        logger.info("转写任务orderId：{}", orderId);

        // 3、查询转写结果
        int status = LfasrOrderStatusEnum.CREATED.getKey();
        // 循环直到订单完成或失败
        while (status != LfasrOrderStatusEnum.COMPLETED.getKey() && status != LfasrOrderStatusEnum.FAILED.getKey()) {
            LfasrResponse resultResponse = lfasrClient.getResult(orderId, TASK_TYPE);
            if (!StringUtils.equals(resultResponse.getCode(), "000000")) {
                logger.error("转写任务失败，错误码：{}，错误信息：{}", resultResponse.getCode(), resultResponse.getDescInfo());
                return null;
            }

            // 获取订单状态信息
            if (resultResponse.getContent() != null && resultResponse.getContent().getOrderInfo() != null) {
                status = resultResponse.getContent().getOrderInfo().getStatus();
                int failType = resultResponse.getContent().getOrderInfo().getFailType();

                // 根据状态输出日志
                LfasrOrderStatusEnum statusEnum = LfasrOrderStatusEnum.getEnum(status);
                if (statusEnum != null) {
                    logger.info("订单状态：{}", statusEnum.getValue());

                    // 如果订单失败，输出失败原因
                    if (statusEnum == LfasrOrderStatusEnum.FAILED) {
                        LfasrFailTypeEnum failTypeEnum = LfasrFailTypeEnum.getEnum(failType);
                        logger.error("订单处理失败，失败原因：{}", failTypeEnum.getValue());
                        return null;
                    }
                    // 如果订单已完成，返回结果
                    if (statusEnum == LfasrOrderStatusEnum.COMPLETED) {
                        return parseOrderResult(resultResponse.getContent().getOrderResult());
                    }
                } else {
                    logger.error("未知的订单状态：{}", status);
                    return null;
                }
            } else {
                logger.error("返回结果中缺少订单信息");
                return null;
            }

            TimeUnit.SECONDS.sleep(5);
        }
        return null;
    }

    /**
     * 解析转写结果
     */
    private static String parseOrderResult(String orderResultStr) {
        try {
            LfasrOrderResult orderResult = GSON.fromJson(orderResultStr, LfasrOrderResult.class);
            return getLatticeText(orderResult.getLattice());
        } catch (Exception e) {
            logger.error("转写结果解析失败", e);
            return null;
        }
    }

    /**
     * 从转写结果的lattice数组中提取文本
     */
    private static String getLatticeText(List<LfasrOrderResult.Lattice> latticeList) {
        StringBuilder resultText = new StringBuilder();
        for (LfasrOrderResult.Lattice lattice : latticeList) {
            LfasrOrderResult.Json1Best json1Best = lattice.getJson1Best();
            if (json1Best == null || json1Best.getSt() == null || json1Best.getSt().getRt() == null) {
                continue;
            }
            String rl = json1Best.getSt().getRl();
            StringBuilder rlText = getRlText(json1Best);
            resultText.append(rlText);
        }
        return resultText.toString();
    }

    /**
     * 从Json1Best中提取识别结果文本并拼接
     */
    private static StringBuilder getRlText(LfasrOrderResult.Json1Best json1Best) {
        StringBuilder rlText = new StringBuilder();
        for (LfasrOrderResult.RecognitionResult rt : json1Best.getSt().getRt()) {
            if (rt.getWs() == null) {
                continue;
            }
            for (LfasrOrderResult.WordResult ws : rt.getWs()) {
                if (ws.getCw() != null && !ws.getCw().isEmpty()) {
                    // 获取每个词的识别结果
                    String word = ws.getCw().get(0).getW();
                    if (word != null && !word.isEmpty()) {
                        rlText.append(word);
                    }
                }
            }
        }
        return rlText;
    }

    // 测试方法
    public static void main(String[] args) throws SignatureException, InterruptedException {
        String audioFilePath = "E:/szjh_tll/codeFile/websdk-java-demo-main/target/classes//audio/lfasr.wav";
        String result = getTranscriptionResult(audioFilePath);
        System.out.println("转写结果：" + result);
    }
}
