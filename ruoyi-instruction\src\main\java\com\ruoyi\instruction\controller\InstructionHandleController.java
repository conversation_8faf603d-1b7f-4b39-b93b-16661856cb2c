package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.InstructionHandle;
import com.ruoyi.instruction.service.IInstructionHandleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 督办单Controller
 * 
 * <AUTHOR>
 * @date 2023-05-17
 */
@RestController
@RequestMapping("/instruction/handle")
public class InstructionHandleController extends BaseController
{
    @Autowired
    private IInstructionHandleService instructionHandleService;

    /**
     * 查询督办单列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:handle:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionHandle instructionHandle)
    {
        Long deptId = SecurityUtils.getDeptId();
        instructionHandle.setCreateDeptId(deptId);
        startPage();
        List<InstructionHandle> list = instructionHandleService.selectInstructionHandleList(instructionHandle);
        return getDataTable(list);
    }

    /**
     * 导出督办单列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:handle:export')")
    @Log(title = "督办单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionHandle instructionHandle)
    {
        List<InstructionHandle> list = instructionHandleService.selectInstructionHandleList(instructionHandle);
        ExcelUtil<InstructionHandle> util = new ExcelUtil<InstructionHandle>(InstructionHandle.class);
        util.exportExcel(response, list, "督办单数据");
    }

    /**
     * 获取督办单详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:handle:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(instructionHandleService.selectInstructionHandleById(id));
    }

    /**
     * 新增督办单
     */
    @PreAuthorize("@ss.hasPermi('instruction:handle:add')")
    @Log(title = "督办单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionHandle instructionHandle)
    {
        return toAjax(instructionHandleService.insertInstructionHandle(instructionHandle));
    }

    /**
     * 修改督办单
     */
    @PreAuthorize("@ss.hasPermi('instruction:handle:edit')")
    @Log(title = "督办单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionHandle instructionHandle)
    {
        return toAjax(instructionHandleService.updateInstructionHandle(instructionHandle));
    }

    /**
     * 删除督办单
     */
    @PreAuthorize("@ss.hasPermi('instruction:handle:remove')")
    @Log(title = "督办单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(instructionHandleService.deleteInstructionHandleByIds(ids));
    }

    /**
     * 根据指令id、指令环节、当前环节事件获取督办信息
     * @param map
     * @return
     */
    @PostMapping("/getBaseInfo")
    public AjaxResult getBaseInfo(@RequestBody(required = false) Map<String,Object> map){
        Map<String,Object> result = instructionHandleService.getInfo(map);
        return AjaxResult.success(result);
    }
}
