<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.user.dao.SysUploadLocalDao" >
    <sql id="sysUploadLocalSql">
        a.id AS "id",
        a.file_key AS "fileKey",
        a.file_url AS "fileUrl",
        a.file_name AS "fileName",
        a.create_date AS "createDate",
        a.create_by AS "createBy"
    </sql>

    <select id="getByKey" resultType="com.ruoyi.modules.user.entity.SysUploadLocal">
        SELECT
        <include refid="sysUploadLocalSql"/>
        FROM sys_upload_local a
        WHERE a.file_key = #{key} limit 1
    </select>

    <select id="getById" resultType="com.ruoyi.modules.user.entity.SysUploadLocal">
        SELECT
          <include refid="sysUploadLocalSql"/>
        FROM sys_upload_local a
        WHERE a.id = #{id}
    </select>

    <insert id="insert">
        INSERT INTO sys_upload_local (
            id,
            file_key,
            file_url,
            file_name,
            create_date,
            create_by
        ) VALUES
        (
            #{id},
            #{fileKey},
            #{fileUrl},
            #{fileName},
            #{createDate},
            #{createBy}
        )
    </insert>

</mapper>
