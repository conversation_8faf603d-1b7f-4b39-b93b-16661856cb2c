<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.petRecord.dao.PetRecordDao">

    <sql id="columns">
        <trim suffixOverrides=",">
            a.id as id,
            a.node as node,
            a.pet_id as petId,
            a.pet_id_card as petIdCard,
            a.pet_num as petNum,
            a.status as status,
            a.remark as remark,
            a.str_date as strDate,
            (select su.nick_name from sys_user su where su.user_id = a.create_by) as createName,
            a.create_date as createDate,
            a.create_by as createBy,
            a.update_date as updateDate,
            a.update_by as updateBy,
            a.del_flag as delFlag,
            a.old_pet_num as oldPetNum,
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.petRecord.entity.PetRecord"
            resultType="com.ruoyi.modules.petRecord.entity.PetRecord">
        select
        <include refid="columns"/>
        from pet_record a
        where a.id =#{id}
    </select>


    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.petRecord.entity.PetRecord"
            resultType="com.ruoyi.modules.petRecord.entity.PetRecord">
        select
        <include refid="columns"/>
        from pet_record a
        where a.del_flag =1
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="node != null">
            and a.node = #{node}
        </if>
        <if test="petId != null and petId != ''">
            and a.pet_id = #{petId}
        </if>
        <if test="petIdCard != null and petIdCard != ''">
            and a.pet_id_card = #{petIdCard}
        </if>
        <if test="petNum != null and petNum != ''">
            and a.pet_num = #{petNum}
        </if>
        <if test="status != null">
            and a.status = #{status}
        </if>
        <if test="remark != null and remark != ''">
            and a.remark = #{remark}
        </if>
        <if test="strDate != null and strDate != ''">
            and a.str_date = #{strDate}
        </if>
        <if test="createName != null and createName != ''">
            and a.create_name = #{createName}
        </if>
        <if test="createDate != null">
            and a.create_date = #{createDate}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by = #{createBy}
        </if>
        <if test="updateDate != null">
            and a.update_date = #{updateDate}
        </if>
        <if test="updateBy != null and updateBy != ''">
            and a.update_by = #{updateBy}
        </if>
        <if test="delFlag != null">
            and a.del_flag = #{delFlag}
        </if>
        order by create_date desc
    </select>
    <select id="getPetIdList" resultType="java.lang.String">
        select pet_id
        from pet_record
        where node = #{node}
          and DATE_FORMAT(create_date, '%Y-%m-%d') >= DATE_FORMAT(#{beginTime}, '%Y-%m-%d')
          AND DATE_FORMAT(create_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{endTime}, '%Y-%m-%d')

    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.petRecord.entity.PetRecord">
        insert into pet_record(id,node, pet_id, pet_id_card, pet_num, status, remark, str_date,
                               create_name, create_date, create_by, update_date, update_by,
                               del_flag,old_pet_num)
        values (#{id},#{node}, #{petId}, #{petIdCard}, #{petNum}, #{status}, #{remark}, #{strDate},
                #{createName}, #{createDate}, #{createBy}, #{updateDate}, #{updateBy}, #{delFlag}, #{oldPetNum})
    </insert>
    <update id="updateByEntity" parameterType="com.ruoyi.modules.petRecord.entity.PetRecord">
        update pet_record set
        <trim suffixOverrides=",">
            <if test="node != null">
                node = #{node},
            </if>
            <if test="petId != null and petId != ''">
                pet_id = #{petId},
            </if>
            <if test="petIdCard != null and petIdCard != ''">
                pet_id_card = #{petIdCard},
            </if>
            <if test="petNum != null and petNum != ''">
                pet_num = #{petNum},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="strDate != null and strDate != ''">
                str_date = #{strDate},
            </if>
            <if test="createName != null and createName != ''">
                create_name = #{createName},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
        </trim>
        where id = #{id}
    </update>
    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.petRecord.entity.PetRecord">
        update pet_record set
        <trim suffixOverrides=",">
            node = #{node},
            pet_id = #{petId},
            pet_id_card = #{petIdCard},
            pet_num = #{petNum},
            status = #{status},
            remark = #{remark},
            str_date = #{strDate},
            create_name = #{createName},
            create_date = #{createDate},
            create_by = #{createBy},
            update_date = #{updateDate},
            update_by = #{updateBy},
            del_flag = #{delFlag},
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.petRecord.entity.PetRecord">
        UPDATE pet_record
        SET del_flag=#{delFlag},
            update_date = #{updateDate},
            update_by = #{updateBy}
        where id = #{id}
    </delete>

</mapper>
