package com.ruoyi.common.utils;

import com.alibaba.fastjson2.JSON;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.security.Key;
import java.util.Date;
import java.util.HashMap;

public class JwtUtil {
    /**
     * 生成Jwt的方法
     *
     * @param id        用户ID  非必须
     * @param subject   系统账号
     * @param appSecret   appSecret
     * @param expiresIn   令牌有效期
     * @return Token String 凭证
     */
    public static String createJWT(String id, String subject,String appSecret,long expiresIn) {
        // 签名方法 HS256
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;
        // 生成Jwt的时间
        long nowMillis = System.currentTimeMillis();
        Date now = new Date(nowMillis);
        // 生成秘钥
        //byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(CommonConstants.JWT_PRIVATE_KEY);
        byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(appSecret);
        Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());
        // 设置JWT所存储的信息
        JwtBuilder builder = Jwts.builder().setId(id).setIssuedAt(now).setSubject(subject)
                .signWith(signatureAlgorithm, signingKey);
        // 设置过期时间
        long expMillis = 0;
        if (expiresIn >= 0) {
            expMillis = nowMillis + expiresIn;
        } else {
            expMillis = nowMillis + expiresIn;
        }
        Date exp = new Date(expMillis);
        builder.setExpiration(exp);
        // 构建JWT并将其序列化为紧凑的URL安全字符串
        return builder.compact();
    }

    /**
     * 解析Jwt字符串
     *
     * @param jwt Jwt字符串
     * @return Claims 解析后的对象
     */
    public static Claims parseJWT(String jwt, String appSecret) {
        Claims claims = Jwts.parser()
                .setSigningKey(DatatypeConverter.parseBase64Binary(appSecret))
                .parseClaimsJws(jwt).getBody();

        // portToken: jh*374bf2l4mh2m5m^uvdtyd5!9u86#8998t754%qnjkyiniugu
        // token: abcdefghijklmnopqrstuvwxyz
//        Claims claims = Jwts.parser().setSigningKey(DatatypeConverter.parseBase64Binary("abcdefghijklmnopqrstuvwxyz"))
//                .parseClaimsJws(jwt).getBody();

        return claims;
    }

    /**
     * authorizeToken 加密案例
     * @param args
     */
    public static void main(String[] args) {
        HashMap hashMap=new HashMap();
        hashMap.put("userName","wyxzhxzzfj");
        hashMap.put("password","xzzfj@123");
//        String appSecrte="28fc22e2b98e48ceb096b66f43745563";
        String appSecrte="28fc22e2b98e48ceb096b66f43745563z1111112wsxc";
        String authorizeToken = JwtUtil.createJWT(null, JSON.toJSONString(hashMap), appSecrte, 30000000);
        System.out.println(authorizeToken);
    }
}
