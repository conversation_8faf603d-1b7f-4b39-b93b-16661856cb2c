package com.ruoyi.config.log;

import jakarta.servlet.http.HttpServletRequest;

/**

 * 获取用户真实IP

 * Created by ya<PERSON><PERSON> on 2018/10/29.

 */

public class NetUtils {

    private static final String[] HEADERS_TO_TRY = {

            "X-Forwarded-For",

            "Proxy-Client-IP",

            "WL-Proxy-Client-IP",

            "HTTP_X_FORWARDED_FOR",

            "HTTP_X_FORWARDED",

            "HTTP_X_CLUSTER_CLIENT_IP",

            "HTTP_CLIENT_IP",

            "HTTP_FORWARDED_FOR",

            "HTTP_FORWARDED",

            "HTTP_VIA",

            "REMOTE_ADDR"

    };

    /**

     * 获取用户真实IP地址

     * @param request

     * @return

     */

    public static String getClientIpAddress(HttpServletRequest request) {

        String rip = request.getRemoteAddr();

        for (String header : HEADERS_TO_TRY) {

            String ip = request.getHeader(header);

            if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {

                rip = ip;

                break;

            }

        }

        int pos = rip.indexOf(',');

        if (pos >= 0) {

            rip = rip.substring(0, pos);

        }

        return rip;

    }

}
