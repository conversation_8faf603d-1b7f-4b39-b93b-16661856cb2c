package com.ruoyi.modules.punish.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.punish.entity.Punish;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 * 违法处罚表(punish)表数据库访问层
 *
 * <AUTHOR>
 */
@Repository
public interface PunishDao extends BaseDao<Punish> {
    public void updateByEntity(Punish punish);
    /**
     * @author: tongsiyu
     * @date: 2022/12/13 15:16
     * @Description: 半年内数量统计
     */
    public List<HashMap> halfYearChar();
}
