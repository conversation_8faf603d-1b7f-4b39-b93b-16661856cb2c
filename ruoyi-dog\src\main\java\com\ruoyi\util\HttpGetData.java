package com.ruoyi.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.collections.map.HashedMap;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2022-7-4.
 */
public class HttpGetData {

    private static String loginPath = "http://emis.jindong.gov.cn/api/sysUser/login";
    private static String oldPersonPath = "http://emis.jindong.gov.cn/api/yearData/getByComIdByDataType";

    public static int countStr(String str1, String str2) {
        int count=0;
        while (str1.contains(str2)){
            str1=str1.substring(str1.indexOf(str2)+1);
            count++;
        }
        return count;
    }

    public static void main(String[] args) {
        getData("",1);
    }

    public static Map<String,Object> getData(String keywords,Integer pageNum) {
        Map<String,Object> map = new HashedMap();
        // 登录保持链接
        CloseableHttpClient client = login(loginPath, "admin", "JDjx330703");
//        CloseableHttpClient client = login(loginPath, "admin", "123456");

        String filePath = "D:\\数据0719.xlsx";
        try {
            XSSFWorkbook wookbook = new XSSFWorkbook(new FileInputStream(filePath));
            XSSFSheet sheet = wookbook.getSheet("Sheet1");
            int rows = sheet.getPhysicalNumberOfRows();
            Map<String,Boolean> sonMap = null;
            for(int i=1;i<rows;i++){
                System.out.println(i);
                XSSFRow row = sheet.getRow(i);
                // 途径地  处理途径地
                String comId = row.getCell(0).getStringCellValue();

                String tempPath = oldPersonPath;
                tempPath = tempPath + "?dataType=10&year=2021&comId="+comId;
                // 获取数据
                HttpGet getUrl = new HttpGet(tempPath);
                // 其他参数 。。。
                try {
                    HttpResponse httpResponse = client.execute(getUrl);
                    String result = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
                    JSONObject obj = JSON.parseObject(result);
                    JSONObject obj2 = obj.getJSONObject("result");
                    if(obj2 == null){
                        continue;
                    }
                    JSONObject json = obj2.getJSONObject("jsonData");
                    if(json != null){
                        XSSFCell ziyou = row.createCell(3);
                        ziyou.setCellValue(json.get("ziyou").toString());

                        XSSFCell build = row.createCell(4);
                        build.setCellValue(json.get("build").toString());

                        XSSFCell chuzu = row.createCell(5);
                        chuzu.setCellValue(json.get("chuzu").toString());

                        XSSFCell chengzu = row.createCell(6);
                        chengzu.setCellValue(json.get("chengzu").toString());

                        XSSFCell sjydmj = row.createCell(7);
                        sjydmj.setCellValue(json.get("sjydmj").toString());
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            FileOutputStream fos=new FileOutputStream(filePath);
            wookbook.write(fos);
            fos.close();//关闭文件输出流
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }


        return map;
    }


    public static CloseableHttpClient login(String loginUrl, String username, String password) {

        CloseableHttpClient httpClient = HttpClients.createDefault();
        // 使用的是post请求方法
        HttpPost httPost = new HttpPost(loginUrl);
        // 设置请求参数
        List<NameValuePair> nvp = new ArrayList<>();
        // 用户名
        nvp.add(new BasicNameValuePair("userName", username));
        // 密码
        nvp.add(new BasicNameValuePair("password", password));
        /**
         * 以及其他参数 。。。
         */
        try {
            // 将请求参数放入到post entity中
            httPost.setEntity(new UrlEncodedFormEntity(nvp, "UTF-8"));
            // 第一次建立链接 获取隐藏得ViewState
            CloseableHttpResponse response = httpClient.execute(httPost);
            String result = EntityUtils.toString(response.getEntity(), "UTF-8");
            System.out.println(result);
            //输出网页源码
        } catch (UnsupportedEncodingException e) {
            System.out.println("设置post请求参数出错!");
        } catch (ClientProtocolException e) {
            System.out.println("ClientProtocolException 登录请求出错!");
        } catch (IOException e) {
            System.out.println("IOException 登录请求出错!");
        } finally {
            httPost.abort();
        }
        return httpClient;
    }
}
