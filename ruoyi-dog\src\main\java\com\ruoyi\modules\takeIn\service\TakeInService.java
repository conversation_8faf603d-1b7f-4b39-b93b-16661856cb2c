package com.ruoyi.modules.takeIn.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.RedisConstants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.common.yw.mapper.XxInfoMapper;
import com.ruoyi.modules.brand.dao.PetBrandDao;
import com.ruoyi.modules.brand.entity.PetBrand;
import com.ruoyi.modules.certificates.dao.PetCertificatesDao;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.hospital.entity.QualifiYear;
import com.ruoyi.modules.punish.entity.Punish;
import com.ruoyi.modules.takeIn.dao.TakeInDao;
import com.ruoyi.modules.takeIn.dao.TakeInOperateLogMapper;
import com.ruoyi.modules.takeIn.entity.ReclaimRecord;
import com.ruoyi.modules.takeIn.entity.TakeIn;
import com.ruoyi.modules.takeIn.entity.TakeInOperateLog;
import com.ruoyi.modules.takeIn.vo.ReclaimRecordVO;
import com.ruoyi.modules.user.dao.SysUploadFileDao;
import com.ruoyi.modules.user.dao.SysUserDao;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.service.SysUploadFileService;
import com.ruoyi.modules.user.service.SysUserService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.util.DateUtil;
import com.ruoyi.util.DianXinSMS;
import com.ruoyi.util.UploadLocalUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Base64Utils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 收容处置表(take_in)表服务接口
 * <AUTHOR>
 *
 */
 @Service
public class TakeInService  extends BaseService<TakeInDao,TakeIn> {
    @Autowired
    private SysUserDao sysUserDao;
    @Autowired
    private PetCertificatesDao petCertificatesDao;
    @Autowired
    private TakeInOperateLogMapper takeInOperateLogMapper;
    @Autowired
    private SysUploadFileService uploadFileService;
    @Autowired
    private SysUploadFileDao sysUploadFileDao;
    @Autowired
    private PetBrandDao petBrandDao;
    @Autowired
    @Lazy
    private ReclaimRecordService reclaimRecordService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private XxInfoMapper xxInfoMapper;

    public TakeIn getById(String id) {
        TakeIn entity = dao.getById(id);
//        JSONArray array = JSON.parseArray(entity.getProcessingRecord());
//        if (array!=null && array.size() > 0) {
//            for (int i = 0; i < array.size(); i++) {
//                if (array.getJSONObject(i).getString("userId") != null) {
//                    SysUser user = sysUserDao.getById(array.getJSONObject(i).getString("userId"));
//                    if (user != null) {
//                        array.getJSONObject(i).put("userName", user.getRealName());
//                    }
//                }
//            }
//            array.sort(Comparator.comparing(obj -> ((JSONObject) obj).getDate("time")).reversed());
//            entity.setRecordList(array);
//        }
        if (Objects.nonNull(entity)) {
            fillDetail(Lists.newArrayList(entity));
        }

        return entity;
    }

    private void fillPetAndOwnerInfo(TakeIn entity) {
        if ("2".equals(entity.getType())) {
            //有牌
            HashMap petNum = petCertificatesDao.getByPetNum(entity.getPetNum());
            if (MapUtils.isNotEmpty(petNum)) {
                entity.setPetName((String) petNum.get("petName"));
                entity.setPetHair((String) petNum.get("petHair"));
                entity.setPetImg((String) petNum.get("petImg"));
                entity.setOwnerName((String) petNum.get("ownerName"));
                entity.setTel((String) petNum.get("tel"));
                entity.setPetIdCard((String) petNum.get("petIdCard"));
            }
        }
    }

    @Override
    public List<TakeIn> getList(TakeIn entity) {
        List<TakeIn> list = dao.getList(entity);

        if (CollectionUtils.isNotEmpty(list)) {
            for (TakeIn takeIn : list) {
                fillPetAndOwnerInfo(takeIn);
            }
        }

        return list;
    }

    @Override
    public PageInfo<TakeIn> getPageList(TakeIn entity) {
        PageHelper.startPage(entity.getPageNum(),entity.getPageSize());

        List<TakeIn> list = dao.getPageList(entity);

        fillDetail(list);

        PageInfo<TakeIn> pageInfo = new PageInfo<TakeIn>(list);
        return pageInfo;

    }

    private void fillDetail(List<TakeIn> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> instanceIdList = list.stream()
                    .map(TakeIn::getId)
                    .collect(Collectors.toList());

            List<SysUploadFile> fileList = uploadFileService.listByInstanceId(instanceIdList);
            //附件信息
            Map<String, List<SysUploadFile>> fileMap = fileList.stream()
                    .collect(Collectors.groupingBy(SysUploadFile::getInstanceId));

            List<String> petNumList = list.stream()
                    .filter(takeIn -> StringUtils.isNotBlank(takeIn.getPetNum()))
                    .map(TakeIn::getPetNum)
                    .collect(Collectors.toList());

            for (TakeIn takeIn : list) {
                takeIn.setUploadFileList(fileMap.get(takeIn.getId()));
            }

            if (CollectionUtils.isNotEmpty(petNumList)) {
                List<PetBrand> petBrandList = petBrandDao.listByPetNumList(petNumList);

                Map<String, PetBrand> petBrandMap = petBrandList.stream()
                        .collect(Collectors.toMap(PetBrand::getBrandNum, Function.identity()));

                List<PetCertificates> petCertificatesList = petCertificatesDao.listByPetNumList(petNumList);

                Map<String, PetCertificates> petCertificatesMap = petCertificatesList.stream()
                        .collect(Collectors.toMap(PetCertificates::getPetNum, Function.identity()));

                for (TakeIn takeIn : list) {
                    PetBrand petBrand = petBrandMap.get(takeIn.getPetNum());
                    if (Objects.nonNull(petBrand)) {
                        takeIn.setCounty(petBrand.getCounty());
                        takeIn.setStreet(petBrand.getStreet());
                    }

                    PetCertificates petCertificates = petCertificatesMap.get(takeIn.getPetNum());
                    if (Objects.nonNull(petCertificates)) {
                        takeIn.setPetName(petCertificates.getPetName());
                        takeIn.setPetSex(petCertificates.getPetSex());
                        if (StringUtils.isNotBlank(petCertificates.getPetVarietiesOne())) {
                            takeIn.setPetVarietiesOne(petCertificates.getPetVarietiesOne());
                        }
                        takeIn.setPetAge(petCertificates.getPetAge());
//                    takeIn.setPetVarieties(petCertificates.getPetVarieties());
                    }
                }
            }
        }
    }

    @Transactional
    public void saveOrUpdate(TakeIn entity) {
        if (entity.getId() != null && !entity.getId().equals("")) {
            // 更新操作
            entity.preUpdate();
            update(entity);

            recordLog(entity.getId(), "更新", null);
        } else {
            if (CollectionUtils.isNotEmpty(entity.getUploadFileList())) {
                SysUploadFile sysUploadFile = entity.getUploadFileList().get(0);
                entity.setPetImg(sysUploadFile.getFileUrl());
            }

            entity.setStatus("0");
            entity.setBizCode("SR" + IdUtils.getBizCode());

            // 新增操作
            insert(entity);

            if (CollectionUtils.isNotEmpty(entity.getUploadFileList())) {
                List<SysUploadFile> result = new ArrayList<>();
                for (SysUploadFile s : entity.getUploadFileList()) {
                    s.preInsert();
                    s.setInstanceId(entity.getId());
                    result.add(s);

                    entity.setPetImg(s.getFileUrl());
                }
                sysUploadFileDao.saveAllList(result);
            }

            recordLog(entity.getId(), "新增", null);
        }
    }

    public void recordLog(String businessId, String operateType, String content) {
        TakeInOperateLog takeInOperateLog = new TakeInOperateLog();
        takeInOperateLog.setBusinessId(businessId);
        takeInOperateLog.setOperateType(operateType);
        takeInOperateLog.setOperateContent(content);
        takeInOperateLog.setOperateUserId(SecurityUtils.getUserId());
        takeInOperateLog.setOperateUserName(SecurityUtils.getUsername());
        takeInOperateLog.setCreateTime(new Date());
        takeInOperateLog.setCreateBy(SecurityUtils.getUsername());

        takeInOperateLogMapper.insertSelective(takeInOperateLog);
    }
    public void updateByEntity(TakeIn entity) {
        entity.preUpdate();
        if (entity.getProcessingRecord() != null && !"".equals(entity.getProcessingRecord())) {
            TakeIn old = dao.getById(entity.getId());
            JSONArray array = JSON.parseArray(old.getProcessingRecord());
            if (array == null) {
                array = new JSONArray();
            }
            array.add(JSONObject.parseObject(entity.getProcessingRecord()));
            entity.setProcessingRecord(JSONArray.toJSONString(array));
        }
        dao.updateByEntity(entity);
    }

    public void sendMobile(String id) {
        TakeIn takeIn = dao.getById(id);

        if (Objects.isNull(takeIn)) {
            throw new RuntimeException("医院不存在");
        }

        String tel = takeIn.getContactNumber();

        if (StringUtils.isNotBlank(tel)) {
            DianXinSMS.sendMessage(tel, "您有一条新的收容提醒", xxInfoMapper);
        } else {
            throw new RuntimeException("没有饲主的联系方式，无法发送短信提醒");
        }
    }

    public boolean updateStatus(TakeIn takeIn) {
        TakeIn exist = dao.getById(takeIn.getId());
        if (Objects.isNull(exist)) {
            throw new RuntimeException("收容记录不存在");
        }

        boolean flag = true;
        if (StringUtils.isNotBlank(exist.getPetNum())) {
            HashMap petNum = petCertificatesDao.getByBrandNum(exist.getPetNum());
            if (Objects.nonNull(petNum)) {
                LocalDateTime endDate = (LocalDateTime) petNum.get("endDate");
                if (endDate.isBefore(LocalDateTime.now())) {
                    throw new RuntimeException("该犬的疫苗已到期，请接种疫苗后收容认领");
                }
            }

            Long count = dao.countByPetNum(exist.getPetNum());
            if (count > 2) {
                flag = false;
            }
        }

        TakeIn updateTakeIn = new TakeIn();

        updateTakeIn.setId(takeIn.getId());
        updateTakeIn.setApprovalStatus(takeIn.getApprovalStatus());
        updateTakeIn.setUpdateBy(SecurityUtils.getUsername());
        updateTakeIn.setUpdateDate(new Date());

        dao.updateByEntity(updateTakeIn);

        String content = "通过";
        if ("3".equals(takeIn.getApprovalStatus())) {
            content = "驳回";
        }

        recordLog(takeIn.getId(), "审批", content);

        return flag;
    }

    @Transactional
    public void register(ReclaimRecordVO recordVO) {
        TakeIn exist;
        if (StringUtils.isBlank(recordVO.getTakeInId())) {
            exist = dao.getByBizCode(recordVO.getBizCode());
            if (Objects.isNull(exist)) {
                throw new RuntimeException("收容记录不存在");
            }
        } else {
            exist = dao.getById(recordVO.getTakeInId());
            if (Objects.isNull(exist)) {
                throw new RuntimeException("收容记录不存在");
            }
        }

        if (!"1".equals(exist.getStatus())) {
            throw new RuntimeException("该犬已被认领");
        }

        TakeIn updateTakeIn = new TakeIn();

        updateTakeIn.setId(exist.getId());
        updateTakeIn.setStatus("2");
        updateTakeIn.setUpdateBy(SecurityUtils.getUsername());
        updateTakeIn.setUpdateDate(new Date());

        dao.updateByEntity(updateTakeIn);

        ReclaimRecord reclaimRecord = new ReclaimRecord();
        reclaimRecord.setType("1");
        reclaimRecord.setTakeInId(exist.getId());
        reclaimRecord.setStatus("0");
        if (StringUtils.isBlank(recordVO.getIdCard())) {
            TakeIn takeIn = getById(exist.getId());
            reclaimRecord.setContactNumber(takeIn.getContactNumber());
            reclaimRecord.setIdCard(takeIn.getIdCard());
            reclaimRecord.setStreet(takeIn.getStreet());
            reclaimRecord.setCounty(takeIn.getCounty());
            reclaimRecord.setAddress(takeIn.getAddress());
            reclaimRecord.setName(takeIn.getName());
        } else {
            reclaimRecord.setContactNumber(recordVO.getContactNumber());
            reclaimRecord.setIdCard(recordVO.getIdCard());
            reclaimRecord.setStreet(recordVO.getStreet());
            reclaimRecord.setCounty(recordVO.getCounty());
            reclaimRecord.setAddress(recordVO.getAddress());


            reclaimRecord.setType("1");
        }

        //新增认领记录
        reclaimRecordService.saveOrUpdate(reclaimRecord);

        if (CollectionUtils.isNotEmpty(recordVO.getUploadFileList())) {
            List<SysUploadFile> result = new ArrayList<>();
            for (SysUploadFile s : recordVO.getUploadFileList()) {
                s.preInsert();
                s.setInstanceId(exist.getId());
                result.add(s);
            }
            sysUploadFileDao.saveAllList(result);
        }

        recordLog(exist.getId(), "领回", null);

        // 将字节数组转换为图片并保存
        String today = DateUtil.format(new Date(), "yyyyMMdd");
        String uploadFileName = DateUtil.format(new Date(), "yyyyMMddHHmmss") + (int) ((Math.random() * 9 + 1) * 100000);
        String resultPath = "/" + today + "/" + uploadFileName + "/" + uploadFileName + ".png";
        String fullFilePath = RuoYiConfig.getUploadPath() + resultPath;
        convertBase64StrToImage(recordVO.getImgStr(), fullFilePath);

        List<SysUploadFile> list = Lists.newArrayList();
        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.preInsert();
        sysUploadFile.setInstanceId(reclaimRecord.getTakeInId());
        sysUploadFile.setModelType("signature");
        sysUploadFile.setFileUrl(resultPath);
        sysUploadFile.setFileName(IdUtils.fastUUID() + ".png");
        list.add(sysUploadFile);

        sysUploadFileDao.saveAllList(list);
    }

    public String base64ToPicture(String imgStr) {
        // 去除非法字符（例如空格）
        imgStr = imgStr.replaceAll("[^A-Za-z0-9+/=]", "");

        // 如果字符串是URL编码的，先进行URL解码
        try {
            imgStr = URLDecoder.decode(imgStr, StandardCharsets.UTF_8.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 解码Base64字符串
        byte[] imageBytes = Base64.getMimeDecoder().decode(imgStr);

        // 将字节数组转换为图片并保存
        String today = DateUtil.format(new Date(), "yyyyMMdd");
        String uploadFileName = DateUtil.format(new Date(), "yyyyMMddHHmmss") + (int) ((Math.random() * 9 + 1) * 100000);
        String resultPath = today + "/" + uploadFileName + "/" + uploadFileName + ".png";
        String fullFilePath = UploadLocalUtil.getUploadFilePath() + resultPath;

        // 确保目录存在
        File destDir = new File(fullFilePath).getParentFile();
        if (!destDir.exists()) {
            destDir.mkdirs();
        }

        // 保存图片
        try (FileOutputStream fos = new FileOutputStream(fullFilePath)) {
            fos.write(imageBytes);
            System.out.println("图片已成功保存到 " + fullFilePath);
            return resultPath;
        } catch (IOException e) {
            e.printStackTrace();
        }

        return "";
    }

    /**
     * Base64字符串转图片
     * @param base64String
     * @param imageFileName
     */
    public static void convertBase64StrToImage(String base64String, String imageFileName) {
        ByteArrayInputStream bais = null;
        try {
            //获取图片类型
            String suffix = imageFileName.substring(imageFileName.lastIndexOf(".") + 1);

            // 通用去除data:image/xxx;base64,头部
            if (base64String != null && base64String.contains("base64,")) {
                base64String = base64String.substring(base64String.indexOf("base64,") + 7);
            }
            //获取JDK8里的解码器Base64.Decoder,将base64字符串转为字节数组
            byte[] bytes = Base64.getDecoder().decode(base64String);
            //构建字节数组输入流
            bais = new ByteArrayInputStream(bytes);
            //通过ImageIO把字节数组输入流转为BufferedImage
            BufferedImage bufferedImage = ImageIO.read(bais);
            //构建文件
            File imageFile = new File(imageFileName);

            File destDir = new File(imageFileName).getParentFile();
            if (!destDir.exists()) {
                destDir.mkdirs();
            }
            //写入生成文件
            ImageIO.write(bufferedImage, suffix, imageFile);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (bais != null) {
                    bais.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public List<TakeInOperateLog> listTakeInList(String id) {
        List<TakeInOperateLog> logList = takeInOperateLogMapper.listTakeInList(id);

        return logList;
    }

    public List<TakeIn> listNonComplete(TakeIn takeIn) {
        return dao.listNonComplete(takeIn);
    }

    public void registerMobile(ReclaimRecordVO recordVO) {
        TakeIn exist;
        if (StringUtils.isBlank(recordVO.getTakeInId())) {
            exist = dao.getByBizCode(recordVO.getBizCode());
            if (Objects.isNull(exist)) {
                throw new RuntimeException("收容记录不存在");
            }
        } else {
            exist = dao.getById(recordVO.getTakeInId());
            if (Objects.isNull(exist)) {
                throw new RuntimeException("收容记录不存在");
            }
        }

        if ("3".equals(exist.getStatus())) {
            throw new RuntimeException("该犬已被认领");
        }

        TakeIn updateTakeIn = new TakeIn();

        updateTakeIn.setId(exist.getId());
        updateTakeIn.setStatus("3");
        updateTakeIn.setUpdateBy(SecurityUtils.getUsername());
        updateTakeIn.setUpdateDate(new Date());

        dao.updateByEntity(updateTakeIn);

        ReclaimRecord reclaimRecord = new ReclaimRecord();
        reclaimRecord.setType("1");
        reclaimRecord.setTakeInId(exist.getId());
        reclaimRecord.setStatus("1");
        if (StringUtils.isBlank(recordVO.getIdCard())) {
            TakeIn takeIn = getById(exist.getId());
            reclaimRecord.setContactNumber(takeIn.getContactNumber());
            reclaimRecord.setIdCard(takeIn.getIdCard());
            reclaimRecord.setStreet(takeIn.getStreet());
            reclaimRecord.setCounty(takeIn.getCounty());
            reclaimRecord.setAddress(takeIn.getAddress());
            reclaimRecord.setName(takeIn.getName());
        } else {
            reclaimRecord.setContactNumber(recordVO.getContactNumber());
            reclaimRecord.setIdCard(recordVO.getIdCard());
            reclaimRecord.setStreet(recordVO.getStreet());
            reclaimRecord.setCounty(recordVO.getCounty());
            reclaimRecord.setAddress(recordVO.getAddress());


            reclaimRecord.setType("1");
        }

        //新增认领记录
        reclaimRecordService.saveOrUpdate(reclaimRecord);

        if (CollectionUtils.isNotEmpty(recordVO.getUploadFileList())) {
            List<SysUploadFile> result = new ArrayList<>();
            for (SysUploadFile s : recordVO.getUploadFileList()) {
                s.preInsert();
                s.setInstanceId(exist.getId());
                result.add(s);
            }
            sysUploadFileDao.saveAllList(result);
        }

        recordLog(exist.getId(), "领回", null);

        // 将字节数组转换为图片并保存
        String today = DateUtil.format(new Date(), "yyyyMMdd");
        String uploadFileName = DateUtil.format(new Date(), "yyyyMMddHHmmss") + (int) ((Math.random() * 9 + 1) * 100000);
        String resultPath = "/" + today + "/" + uploadFileName + "/" + uploadFileName + ".png";
        String fullFilePath = RuoYiConfig.getUploadPath() + resultPath;
        convertBase64StrToImage(recordVO.getImgStr(), fullFilePath);

        List<SysUploadFile> list = Lists.newArrayList();
        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.preInsert();
        sysUploadFile.setInstanceId(reclaimRecord.getTakeInId());
        sysUploadFile.setModelType("signature");
        sysUploadFile.setFileUrl(resultPath);
        sysUploadFile.setFileName(IdUtils.fastUUID() + ".png");
        list.add(sysUploadFile);

        sysUploadFileDao.saveAllList(list);
    }

    public void sign(String[] takeInIdList) {
        List<TakeIn> takeInNeedUpdateList = dao.getNeedSignByIdList(takeInIdList);

        if (CollectionUtils.isNotEmpty(takeInNeedUpdateList)) {
            List<String> takeInIdNeedUpdateList = takeInNeedUpdateList.stream()
                    .map(TakeIn::getId)
                    .collect(Collectors.toList());

            Long userId = SecurityUtils.getUserId();

            SysUser sysUser = sysUserService.selectUserById(userId);

            dao.sign(takeInIdNeedUpdateList, sysUser.getUserId(), sysUser.getNickName());

            for (TakeIn takeIn : takeInNeedUpdateList) {
                if (StringUtils.isNotBlank(takeIn.getContactNumber())) {
                    DianXinSMS.sendMessage(takeIn.getContactNumber(), "您的爱犬"+takeIn.getPetName()+"已被"+sysUser.getNickName()+"收容，请尽快认领！", xxInfoMapper);
                    redisCache.setCacheMapValue(RedisConstants.HOSPITAL_MESSAGE, takeIn.getId(), 1);
                }
            }
        }
    }

    public List<TakeIn> listSendMessage() {
        return dao.listSendMessage();
    }

    public List<TakeIn> listUnSign(TakeIn takeIn) {
        return dao.listUnSign(takeIn);
    }
}
