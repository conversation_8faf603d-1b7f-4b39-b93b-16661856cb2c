package com.ruoyi.modules.immune.entity;

import com.ruoyi.base.entity.BaseEntity;

public class ImmuneReissue extends BaseEntity {

    private String userId;                  //发起人id
    private String petId;                   //犬只id
    private String qualifiId;               //医院id
    private String brandNum;                //当前犬牌
    private Integer type;                   //用途方式：1观赏，2导盲，3辅助，4其他
    private Integer sendType;               //寄送方式：1快递，2自取
    private String content;                 //补办内容
    private String name;                    //收件人
    private String phone;                  //收件人手机号
    private String address;                 //收件地址
    private Integer isCollect;              //是否到付：1是，2否
    private Integer status;                 //补办状态：1待审核，2已通过，3未通过
    private String reason;                  //审核意见
    private String dogBrand;                  //新申请的犬牌
    private String zfckId;                  //执法窗口

    private String fileUrl;                 //前端传的文件地址
    private String area;                  //收货地址所在区
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPetId() {
        return petId;
    }

    public void setPetId(String petId) {
        this.petId = petId;
    }

    public String getQualifiId() {
        return qualifiId;
    }

    public void setQualifiId(String qualifiId) {
        this.qualifiId = qualifiId;
    }

    public String getBrandNum() {
        return brandNum;
    }

    public void setBrandNum(String brandNum) {
        this.brandNum = brandNum;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSendType() {
        return sendType;
    }

    public void setSendType(Integer sendType) {
        this.sendType = sendType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getIsCollect() {
        return isCollect;
    }

    public void setIsCollect(Integer isCollect) {
        this.isCollect = isCollect;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getDogBrand() {
        return dogBrand;
    }

    public void setDogBrand(String dogBrand) {
        this.dogBrand = dogBrand;
    }

    public String getZfckId() {
        return zfckId;
    }

    public void setZfckId(String zfckId) {
        this.zfckId = zfckId;
    }
}
