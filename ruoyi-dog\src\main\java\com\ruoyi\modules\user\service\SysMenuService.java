package com.ruoyi.modules.user.service;

import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.user.dao.SysMenuDao;
import com.ruoyi.modules.user.entity.SysMenu;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2021-3-13.
 */
@Service
public class SysMenuService extends BaseService<SysMenuDao, SysMenu> {

    /**
     * 获取所有的菜单（树形结构）
     * @param sysMenu
     * @return
     */
    public List<SysMenu> getSysMenuTree(SysMenu sysMenu){
        // 定义返回结果
        List<SysMenu> resultList = new ArrayList<>();
        // 系统所有菜单数据
        List<SysMenu> allList = null;
        if(sysMenu.getUserId() != null && !"".equals(sysMenu.getUserId())){
            // 按照用户查询权限下餐单数据
            allList = dao.getRightList(sysMenu);
        } else {
            allList = dao.getList(sysMenu);
        }
        // 所有菜单按照父级别进行分类
        Map<String,List<SysMenu>> pidMenuMap = new HashMap<>();
        for(SysMenu menu : allList){
            if(menu.getMenuOrder() != null && menu.getMenuOrder().intValue() == 1){
                // 一级菜单存入list中
                resultList.add(menu);
            }else{
                // 非一级菜单  按照父级别进行分类
                List<SysMenu> pidMenuList = pidMenuMap.get(menu.getParentId());
                if(pidMenuList == null){
                    pidMenuList = new ArrayList<>();
                }
                pidMenuList.add(menu);
                pidMenuMap.put(menu.getParentId(),pidMenuList);
            }
        }
        // 处理一级菜单和子数据
        for(SysMenu menu: resultList){
            if(menu != null){
                dealMenu(menu,pidMenuMap);
            }
        }
        return resultList;
    }

    /**
     * 递归处理子菜单数据
     * @param menu
     * @param pidMenuMap
     */
    private void dealMenu(SysMenu menu, Map<String,List<SysMenu>> pidMenuMap){
        List<SysMenu> sonList = pidMenuMap.get(menu.getId());
        if(sonList != null && sonList.size() > 0){
            // 子菜单保存到数据中
            menu.setSonList(sonList);
            // 存在子菜单继续递归处理 3或4级菜单
            for(SysMenu sonMenu : sonList){
                dealMenu(sonMenu,pidMenuMap);
            }
        }
    }

}
