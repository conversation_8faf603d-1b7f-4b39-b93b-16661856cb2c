package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.InstructionGroup;
import com.ruoyi.instruction.domain.rspVo.GroupDataRspVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 群体基本信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@DataSource(value = DataSourceType.SLAVE)
public interface InstructionGroupMapper 
{
    /**
     * 查询群体基本信息
     * 
     * @param id 群体基本信息主键
     * @return 群体基本信息
     */
    public InstructionGroup selectInstructionGroupById(Long id);

    /**
     * 查询群体基本信息列表
     * 
     * @param instructionGroup 群体基本信息
     * @return 群体基本信息集合
     */
    public List<InstructionGroup> selectInstructionGroupList(InstructionGroup instructionGroup);

    /**
     * 新增群体基本信息
     * 
     * @param instructionGroup 群体基本信息
     * @return 结果
     */
    public int insertInstructionGroup(InstructionGroup instructionGroup);

    /**
     * 修改群体基本信息
     * 
     * @param instructionGroup 群体基本信息
     * @return 结果
     */
    public int updateInstructionGroup(InstructionGroup instructionGroup);

    /**
     * 删除群体基本信息
     * 
     * @param id 群体基本信息主键
     * @return 结果
     */
    public int deleteInstructionGroupById(Long id);

    /**
     * 批量删除群体基本信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionGroupByIds(Long[] ids);

    /**
     * 根据群体名称查询群体信息
     * @param groupName
     * @return
     */
    InstructionGroup findByGroupName(@Param("groupName") String groupName);

    /**
     * 根据群体名称查询关联人员id是
     * @param groupName
     * @return
     */
    String selectPersonIdsByGroupName(@Param("groupName") String groupName);

    /**
     * 获取群体总数、高、中、低管控级别数据
     * @return
     */
    GroupDataRspVo getGroupData(Map<String,Object> map);

    /**
     * 获取群体关联人员排名
     * @return
     */
    List<Map<String, Integer>> getPersonCount();

    /**
     * 获取群体关联事件排名
     * @return
     */
    List<Map<String, Integer>> getEventCount();

    /**
     * 获取群体类型
     * @return
     */
    List<Map<String, Integer>> getType();

    /**
     * 获取群体高频人员
     * @return
     */
    List<Map<String, Integer>> getGroupPerson();

    /**
     * 驾驶舱查询群体基本信息列表，事件在新的一年有提交，也能在新的一年中查出
     *
     * @param instructionGroup 群体基本信息
     * @return 群体基本信息集合
     */
    public List<InstructionGroup> selectInstructionGroupListYear(InstructionGroup instructionGroup);

    /**
     * 根据群体id获取群体人员信息
     * @param id
     * @return
     */
    String getPersonIdsById(@Param("id") Long id);
}
