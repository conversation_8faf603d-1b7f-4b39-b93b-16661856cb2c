package com.ruoyi.modules.regionManage.entity;

import com.ruoyi.base.entity.BaseEntity;

import java.util.Date;
import java.io.Serializable;

/**
 * 区域管理表(RegionManage)实体类
 *
 * <AUTHOR>
 * @since 2022-12-02 13:27:35
 */
public class RegionManage extends BaseEntity {

    private String id;
    /**
     * 名称
     */
    private String name;
    /**
     * 类型 1 行政区域 2 重点区域 3 非重点区域
     */
    private String type;
    /**
     * 坐标集合
     */
    private String marks;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 修改时间
     */
    private Date updateDate;

    private String updateBy;
    /**
     * 状态： 1正常，2删除
     */
    private Integer delFlag;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMarks() {
        return marks;
    }

    public void setMarks(String marks) {
        this.marks = marks;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

}

