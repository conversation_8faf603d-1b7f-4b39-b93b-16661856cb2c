package com.ruoyi.modules.hospital.service;


import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.yw.mapper.XxInfoMapper;
import com.ruoyi.modules.hospital.dao.QualifiRecordDao;
import com.ruoyi.modules.hospital.dao.QualifiYearDao;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.hospital.entity.QualifiRecord;
import com.ruoyi.modules.hospital.entity.QualifiYear;
import com.ruoyi.modules.user.dao.SysUploadFileDao;
import com.ruoyi.modules.user.dao.SysUserRoleDao;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.entity.SysUser;
import com.ruoyi.modules.user.entity.SysUserRole;
import com.ruoyi.modules.user.service.SysUploadFileService;
import com.ruoyi.modules.user.service.SysUserService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.util.DianXinSMS;
import com.ruoyi.util.IdGen;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 10:52
 * @version: 1.0
 **/
@Service
public class QualifiYearService extends BaseService<QualifiYearDao, QualifiYear> {

    @Autowired
    SysUploadFileService uploadFileService;
    @Autowired
    SysUploadFileDao uploadFileDao;
    @Autowired
    SysUserService sysUserService;
    @Autowired
    SysUserRoleDao sysUserRoleDao;

    @Autowired
    QualifiRecordDao qualifiRecordDao;//资质审核记录
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private QualifiService qualifiService;
    @Autowired
    private XxInfoMapper xxInfoMapper;

    /**
     * 分页获取数据对象
     * @param entity
     * @return
     */
    public PageInfo<QualifiYear> getPageList(QualifiYear entity){
        PageHelper.startPage(entity.getPageNum(),entity.getPageSize());

        // 获取用户信息
        com.ruoyi.common.core.domain.entity.SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录或会话失效");
        }
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());

        if (roleList.contains(InstructionRolesConstants.DOG_COUNTY_CONFIRM)) {
            //根据区县过滤
            entity.setCounty(user.getDeptId() + "");
        }

        PageInfo<QualifiYear> pageInfo = new PageInfo<QualifiYear>(getList(entity));
        return pageInfo;
    }

    @Transactional
    public String save(QualifiYear entity) {
        Qualifi qualifi = qualifiService.getCurrentQualifi();
        if (Objects.isNull(qualifi)) {
            throw new RuntimeException("未申请资质无法进行年审");
        }

        entity.setQualifiId(qualifi.getId());

        QualifiYear qualifiYear = dao.checkExist(qualifi.getId(), entity.getYear());
        if (Objects.nonNull(qualifiYear)) {
            throw new RuntimeException("已提交年审资质，请勿重复提交");
        }

        entity.setCreateDate(new Date());

        super.saveOrUpdate(entity);
        uploadFileService.delByInstanceAndModel(entity.getId(), "");
        if (entity.getUploadFileStr() != null && !"".equals(entity.getUploadFileStr())) {
            List<SysUploadFile> list = JSON.parseArray(entity.getUploadFileStr(), SysUploadFile.class);
            if (CollectionUtils.isNotEmpty(list)) {
                list = list.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
            List<SysUploadFile> result = new ArrayList<>();
            for (SysUploadFile s : list) {
                s.preInsert();
                s.setInstanceId(entity.getId());
                result.add(s);
            }
            uploadFileDao.saveAllList(result);
        }
        if (!"2".equals(entity.getStatus())) {//不是审核通过，在记录表中记录
            QualifiRecord qualifiRecord = new QualifiRecord();
            qualifiRecord.setQualifiId(entity.getId());//医院ID
            qualifiRecord.setRecordType(6);//操作类型 4：免疫注销 5 犬牌注销 6 医院提交 7 审批结果
            qualifiRecord.setNode(3);//流程节点 1：畜牧局 2：执法局 3 ：（宠物医院，执法队员，执法窗口）
            qualifiRecord.setStatus(1);//状态 1：已提交 2：已通过 3 ：未通过
            qualifiRecord.setCreateName(entity.getName());
            qualifiRecord.setId(IdGen.uuid());
            qualifiRecord.preInsert();
            qualifiRecordDao.insert(qualifiRecord);
        }

        return "0";
    }

    public QualifiYear getById(String id){
        QualifiYear qualifi = dao.getById(id);
        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.setInstanceId(id);
        qualifi.setUploadFileList(uploadFileService.getList(sysUploadFile));
        return qualifi;
    }

    public void delete(QualifiYear qualifi){
        dao.delete(qualifi);
        uploadFileService.delByInstanceAndModel(qualifi.getId(), "policy_id");
    }

    @Transactional
    public void updateStatus(QualifiYear qualifi) {
        dao.updateStatus(qualifi);
        if ("2".equals(qualifi.getStatus())) {//复制
            dao.deleteReduct(qualifi.getId());
            dao.copyQualifi(qualifi.getId());
        }
        if ("3".equals(qualifi.getStatus())) {//还原
            dao.reductQualifi(qualifi.getId());
        }
        //审核操作在记录表记录
        QualifiRecord qualifiRecord = new QualifiRecord();
        qualifiRecord.setQualifiId(qualifi.getId());//医院ID
        qualifiRecord.setRecordType(qualifi.getRecordType());//操作类型 4：免疫注销 5 犬牌注销 6 医院提交 7 审批结果
        qualifiRecord.setNode(qualifi.getNode());//流程节点 1：畜牧局 2：执法局 3 ：（宠物医院，执法队员，执法窗口）
        qualifiRecord.setStatus(qualifi.getRecordStatus());//状态 1：已提交 2：已通过 3 ：未通过
        qualifiRecord.setCreateName(qualifi.getCreateName());
        qualifiRecord.setRemark(qualifi.getRecordRemark());
        qualifiRecord.setId(IdGen.uuid());
        qualifiRecord.preInsert();
        qualifiRecordDao.insert(qualifiRecord);
    }


    public void createAccount(QualifiYear qualifi) {
        SysUser sysUser = new SysUser();
        sysUser.setMobile(qualifi.getTel());
        List<SysUser> sysUserList = sysUserService.getList(sysUser);
        if (sysUserList.size() > 0) {

        } else {
            sysUser.preInsert();
            sysUser.setUserName(qualifi.getTel());//账号 默认手机号
            sysUser.setRealName(qualifi.getName());//真实名称
            sysUser.setMobile(qualifi.getTel());//手机号
            sysUser.setDeptId(qualifi.getCounty());//所属机构
            if ("0".equals(qualifi.getType())) {
                sysUser.setUserType(2);//用户类型 2：宠物医院
            } else if ("1".equals(qualifi.getType())) {
                sysUser.setUserType(3);//用户类型 3：执法局
            } else if ("2".equals(qualifi.getType())) {
                sysUser.setUserType(3);//用户类型 5：执法窗口
            }
            if (qualifi.getTel() != null && qualifi.getTel().length() > 10) {
                String pass = qualifi.getTel().substring(qualifi.getTel().length() - 6);
                sysUser.setPassword(DigestUtils.md5DigestAsHex(pass.getBytes()));//密码
            } else {
                sysUser.setPassword(DigestUtils.md5DigestAsHex("123456".getBytes()));//密码
            }
            sysUserService.insert(sysUser);
            qualifi.setAccount(sysUser.getId());
            dao.updateAccount(qualifi);
            //创建医院账号自动分权限。
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            SysUserRole userRole = new SysUserRole();
            userRole.preInsert();
            userRole.setUserId(sysUser.getId());
            if ("0".equals(qualifi.getType())) {
                userRole.setRoleId("f373083354374a12a5ead6db09daf1dc");//宠物医院 权限宠物医院
            } else if ("1".equals(qualifi.getType())) {
                userRole.setRoleId("b5285fedecd54d9f800ba804e686380a");//执法队员  权限执法局
            } else if ("2".equals(qualifi.getType())) {
                userRole.setRoleId("13198e269824405cb0eac2ee0038d789");//执法窗口 权限执法窗口
            }
            list.add(userRole);
            if (list.size() > 0) {
                sysUserRoleDao.saveList(list);
            }
        }
    }

    public QualifiYear getQualifiByAccount(QualifiYear qualifi){
        qualifi = dao.getQualifiByAccount(qualifi);
        if(qualifi!=null){
            SysUploadFile sysUploadFile = new SysUploadFile();
            sysUploadFile.setInstanceId(qualifi.getId());
            qualifi.setUploadFileList(uploadFileService.getList(sysUploadFile));
        }
        return qualifi;
    }

    public List<QualifiYear> getAllList(QualifiYear qualifi){
        return dao.getAllList(qualifi);
    }


    public List<SysUser> getApply(SysUser sysUser){
        return dao.getApply(sysUser);
    }

    public PageInfo<QualifiYear> getAlarmList(QualifiYear entity) {
        PageHelper.startPage(entity.getPageNum(),entity.getPageSize());

        // 获取用户信息
        com.ruoyi.common.core.domain.entity.SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录或会话失效");
        }
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());

        if (roleList.contains(InstructionRolesConstants.DOG_COUNTY_CONFIRM)) {
            //根据区县过滤
            entity.setCounty(user.getDeptId() + "");
        }

        List<QualifiYear> qualifiYearList = dao.getAlarmList(entity);

        PageInfo<QualifiYear> pageInfo = new PageInfo<QualifiYear>(qualifiYearList);

        return pageInfo;
    }

    public void sendMobile(String id) {
        QualifiYear qualifiYear = dao.getById(id);

        if (Objects.isNull(qualifiYear)) {
            throw new RuntimeException("医院不存在");
        }

        String tel = qualifiYear.getTel();

        DianXinSMS.sendMessage(tel, qualifiYear.getName() + "年审资质已过期，请及时提交审核", xxInfoMapper);
    }
}
