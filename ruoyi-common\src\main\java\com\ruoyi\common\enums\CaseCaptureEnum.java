package com.ruoyi.common.enums;

/**
 * 千寻违规类型对应数据字典id
 */
public enum CaseCaptureEnum {

    SYJQJX("62001", "2"),//饲养家禽家畜
    FSLJSY("62002", "2"),//焚烧垃圾树叶
    DLYS("62003", "3"),//道路遗撒
    LLQT("62004", "2"), //流浪乞讨
    LMTX("62005", "3"), //路面塌陷
    DLBJ("62007", "3"),//道路不洁
    XGBYBF("62008", "2"), //悬挂横幅标语
    SLZDS("62009", "3"),//水篦子堵塞
    GDWLLDF("62010", "2"),//工地物料乱堆放
    XCLPS("62011", "3"),//宣传栏破损
    LDWS("62012", "2"),//乱倒污水
    YHXD("62013", "6"),//沿河洗涤
    FQJJ("62014", "2"),//废弃家具
    HSSS("62015", "4"),//护树设施
    LDLJ("62016", "2"),//私搭乱建
    WGPBBZ("62017", "2"),//违规牌匾标识
    JCLJZT("62018", "2"),//积存垃圾渣土
    LJTZW("62019", "2"),//垃圾桶或收集容脏污、破损、不整洁
    RXGLT("62020", "3"),//割车道柔性隔离体设施,破损、 歪斜
    DLPS("62021", "3"),//城市道路出现开裂、 坑洼、 损坏等影响通行的现象
    LTSK("62023", "2"),//露天烧烤
    FZSXSG("62025", "2"),//非装饰性树挂
    XFSS("62026", "3"),//消防设施防栓， 破损、 歪倒、老旧等
    SGZD("62027", "3"),//施工占道
    JSGXGX("62028", "3"),//架设管线杆线
    FFXGG("62029", "3"),//非法小广告
    JGWT("62030", "3"),//井盖问题
    KTWJDG("62031", "2"),//空调外机低挂
    HLZL("62032", "4"),//毁绿占绿
    ZDFPSG("62033", "2"),//占道废品收购
    LJTZ("62034", "2"),//临街屠宰
    CCLJT("62036", "2"),//餐厨垃圾桶
    FZT("62037", "5"),//防撞桶
    DLSS("62038", "5"),//电力设施
    BZD("62039", "5"),//便道桩
    JDCLTF("62040", "1"),//机动车乱停放
    LLQL("62041", "2"),//绿化弃料
    XDS("62042", "4"),//行道树
    SGFQL("62043", "2"),//施工废弃料
    LJTBGF("62044", "2"),//垃圾桶不规范
    LJX("62045", "2"),//垃圾箱:方形垃圾箱， 破损、 缺失、 门未关闭、倒伏。
    DWSTWQL("62046", ""),//动物尸体未清理
    GGZPPS("62047", "2"),//广告招牌破损
    WZJP("62048", "3"),//违章接坡
    BYQX("62049", "3"),//变压器箱
    DLJS("62050", "3"),//道路积水
    PFW("62051", "2"),//漂浮物
    JTSFGG("62052", "2"),//街头散发广告
    RYJJ("62053", "2"),//人员聚集
    FJDCLTF("62054", "3"),//非机动车
    LDWUDL("62055", "2"),//乱堆物堆料
    DWJY("62056", "2"),//店外经营
    WZJYYS("62057", "8"),//无照经营游商
    WGHWGG("62059", "2"),//违规户外广告
    PLLJ("62060", "2"),//暴露垃圾
    YJLG("62061", "3"),//沿街晾挂
    LJXMY("62062", "2"),//垃圾箱满溢
    DBLJ("62063", "2"),//打包垃圾
    JYCS("62064", "2");//经营撑伞

    private String type;
    private String typeId;

    CaseCaptureEnum(String type, String typeId) {
        this.type = type;
        this.typeId = typeId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public static String getTypeId(String type) {
        for (CaseCaptureEnum cce : CaseCaptureEnum.values()) {
            if (cce.getType().equals(type)) {
                return cce.typeId;
            }
        }
        return null;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }
}
