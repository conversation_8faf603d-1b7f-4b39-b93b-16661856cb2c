package com.ruoyi.modules.user.entity;


import com.ruoyi.base.entity.BaseEntity;

/**
 * Created by Administrator on 2021-3-13.
 * 系统角色对象
 */
public class SysRole extends BaseEntity {

    private String systemFlag ; // 所属系统配置的服务名 示例：service-mdinfo
    private String roleName ; // 角色名称
    private String description ; // 角色描述
    private Integer deptFlag;   //矛盾部门用户标记：1是 2否

    private String menuListStr; // 菜单列表字符串数据

    public String getSystemFlag() {
        return systemFlag;
    }

    public void setSystemFlag(String systemFlag) {
        this.systemFlag = systemFlag;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getDeptFlag() {
        return deptFlag;
    }

    public void setDeptFlag(Integer deptFlag) {
        this.deptFlag = deptFlag;
    }

    public String getMenuListStr() {
        return menuListStr;
    }

    public void setMenuListStr(String menuListStr) {
        this.menuListStr = menuListStr;
    }
}
