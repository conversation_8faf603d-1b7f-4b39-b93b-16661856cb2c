package com.ruoyi.instruction.domain.rspVo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/6 10:00
 * 重点人员
 */
@Data
public class InstructionPersonRspVo {

    /** 人员姓名 */
    @Excel(name = "人员姓名")
    private String personName;

    /** 证件号码 */
    @Excel(name = "证件号码")
    private String idCard;

    /** 户籍所在地 */
    @Excel(name = "户籍所在地")
    private String housePlace;

    /** 当前居住地 */
    @Excel(name = "当前居住地")
    private String currentPlace;

    /** 责任所在地 */
    @Excel(name = "管控级别", combo = {"高","中","低"})
    private String controlLevel;

    /** 电话号码 */
    @Excel(name = "电话号码")
    private String personPhone;
}
