package com.ruoyi.common.client.ocr;

import ch.qos.logback.classic.Logger;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.zlb.HmacAuthUtil;
import com.ruoyi.common.vo.ocr.req.OcrAtoCapCardReqVO;
import com.ruoyi.common.vo.ocr.req.OcrAtoCapMixCardReqVO;
import com.ruoyi.common.vo.ocr.req.OcrAtoCapPdfReqVO;
import com.ruoyi.common.vo.ocr.req.OcrAtoCapPicReqVO;
import com.ruoyi.common.vo.ocr.res.OcrAtoCapCardResVO;
import com.ruoyi.common.vo.ocr.res.OcrAtoCapMixCardResVO;
import com.ruoyi.common.vo.ocr.res.OcrAtoCapPicResVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class OcrIrsClient {
    @Value("${irs.component.accessKey}")
    private String accessKey;
    @Value("${irs.component.secretKey}")
    private String secretKey;

    /**
     * 图片识别接口
     */
    public OcrAtoCapPicResVO ocrAtoCapPic(OcrAtoCapPicReqVO reqVO) {
        String url = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020230614000006/ocrapidocker/ocrservice.json";

        String method = "POST";

        Map params = JSON.parseObject(JSON.toJSONString(reqVO), Map.class);

        Map resultMap = sendIrsRemote(url, method, params, reqVO.getMethod());

        String data = JSON.toJSONString(resultMap.get("data"));

        OcrAtoCapPicResVO res = JSON.parseObject(data, OcrAtoCapPicResVO.class);

        return res;
    }

    /**
     * PDF文件识别
     */
    public String ocrAtoCapPdf(OcrAtoCapPdfReqVO reqVO) {
        String url = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020230614000007/ocrapidocker/ocrservice.json";

        String method = "POST";

        Map params = JSON.parseObject(JSON.toJSONString(reqVO), Map.class);

        Map resultMap = sendIrsRemote(url, method, params, reqVO.getMethod());

        String data = JSON.toJSONString(resultMap.get("taskid"));

        return data;
    }

    /**
     * PDF文件识别结果获取接口
     */
    public OcrAtoCapPicResVO ocrAtoCapPdfext(String taskId) {
        String url = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020230614000008/ocrapidocker/ocrservice.json";

        String method = "POST";

        Map<String, String> params = new HashMap<>();
        params.put("taskid", taskId);
        params.put("method", "getPDFResult");

        Map resultMap = sendIrsRemote(url, method, params, "getPDFResult");

        String data = JSON.toJSONString(resultMap.get("data"));

        OcrAtoCapPicResVO res = JSON.parseObject(data, OcrAtoCapPicResVO.class);

        return res;
    }

    /**
     * 卡证票据结构化识别
     */
    public OcrAtoCapCardResVO ocrAtoCapCard(OcrAtoCapCardReqVO reqVO) {
        String url = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020230614000009/ocrapidocker/ocrservice.json";

        String method = "POST";

        Map params = JSON.parseObject(JSON.toJSONString(reqVO), Map.class);

        Map resultMap = sendIrsRemote(url, method, params, reqVO.getMethod());

        String data = JSON.toJSONString(resultMap.get("data"));

        OcrAtoCapCardResVO res = JSON.parseObject(data, OcrAtoCapCardResVO.class);

        return res;
    }

    /**
     * 卡证票据结构化识别
     */
    public OcrAtoCapMixCardResVO ocrAtoCapMixCard(OcrAtoCapMixCardReqVO reqVO) {
        String url = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020230614000010/ocrapidocker/ocrservice.json";

        String method = "POST";

        Map params = JSON.parseObject(JSON.toJSONString(reqVO), Map.class);

        Map resultMap = sendIrsRemote(url, method, params, reqVO.getMethod());

        String data = JSON.toJSONString(resultMap.get("data"));

        OcrAtoCapMixCardResVO res = JSON.parseObject(data, OcrAtoCapMixCardResVO.class);

        return res;
    }

    /**
     * 根据高精版接口识别结果生成文件接口
     */
    public String ocrAtoCapExportFfile(OcrAtoCapMixCardReqVO reqVO) {
        String url = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020230615000004/ocrapidocker/ocrservice.json";

        String method = "POST";

        Map params = JSON.parseObject(JSON.toJSONString(reqVO), Map.class);

        Map resultMap = sendIrsRemote(url, method, params, reqVO.getMethod());

        String data = JSON.toJSONString(resultMap.get("data"));

        return data;
    }


    private Map sendIrsRemote(String url, String httpMethod, Map params, String method) {
        Map<String, String> header = HmacAuthUtil.generateHeader(url, httpMethod, accessKey, secretKey);

        String response = null;
        try {
            response = HttpUtils.sendPost(url, params, header);
        } catch (Exception e) {
            log.error("method:{}, httpError, error:{}", method, e.toString());
        }

        Map map =  JSON.parseObject(response, Map.class);

        Integer code = (Integer) map.get("code");

        if (code != 200) {
            log.info("method:{}, response:{}", method, response);
            throw new RuntimeException("method:" + method + ", error, response:" + response);
        }

        return map;
    }


    public String voice(MultipartFile multipartFile) {
        String url = "https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220805000005/prod-api/asrService/api/jd/zwzx";
        String method = "POST";
        Map resultMap = sendVoiceRemote(url, method,multipartFile);
        Object dataObj = resultMap.get("data");
        if (dataObj == null) return null;
        JSONObject dataJson = JSON.parseObject(JSON.toJSONString(dataObj));
        JSONArray sentences = dataJson.getJSONArray("sentences");
        if (sentences == null || sentences.isEmpty()) return null;
        JSONObject firstSentence = sentences.getJSONObject(0);
        return firstSentence.getString("text");
    }


    private Map sendVoiceRemote(String url, String httpMethod, MultipartFile file) {
        Map<String, String> header = HmacAuthUtil.generateHeader(url, httpMethod, accessKey, secretKey);
        header.put("type","1");
        String response = null;
        try {
            response = HttpUtils.sendPost(url, null, header,file);
        } catch (Exception e) {
            log.error("httpError, error:{}", e.toString());
        }

        Map map = JSON.parseObject(response, Map.class);

        Integer code = (Integer) map.get("code");

        if (code != 0) {
            log.info(" response:{}", response);
            throw new RuntimeException("method:" + ", error, response:" + response);
        }
        return map;
    }

}
