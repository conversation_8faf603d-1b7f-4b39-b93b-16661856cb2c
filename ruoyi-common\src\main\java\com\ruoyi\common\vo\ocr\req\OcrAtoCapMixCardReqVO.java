package com.ruoyi.common.vo.ocr.req;

import lombok.Data;

@Data
public class OcrAtoCapMixCardReqVO {

    /**
     * 图像二进制数据的base64编码
     */
    private String img;

    /**
     * 结构化图片的类型，身份证(idcard)，增值税发票(invoice)，营业执照(blicense)，毕业证书(graduation)，房产证(house_cert)，银行卡(bank_card)，车牌号(car_no)，机动车发票(car_invoice)，驾驶证(driving_license)，行驶证(vehicle_license)，行驶证副页(vehicle_license_back)，汽车vin码(car_vin)，图片自动分类识别(doctype)，护照(passport)，火车票(train_ticket)，不动产证(estate_cert)，食品经营许可证(food_blicense)，银行开户许可证(bank_account_permit)，常住人口登记卡(p_reg_cards), 组织机构代码证(org_code_cert)，统一社会信用代码证(social_credit_cert)，出生证明(birth_certification)，户口户主页(household_head)，常住人口页(household_resident)，结婚证(marriage)，律师执业证(lawyer_license)，定额发票(quota_invoice)，出租车发票(taxi_ticket)，机票行程单(air_itinerary)，卷票(roll_ticket)
     */
    private String type = "multi_invoice";

    /**
     * 是否需要图案坐标输出，如增值税发票印章
     */
    private String figure;

    /**
     * 固定值
     */
    private String method = "ocrMixedService";
}
