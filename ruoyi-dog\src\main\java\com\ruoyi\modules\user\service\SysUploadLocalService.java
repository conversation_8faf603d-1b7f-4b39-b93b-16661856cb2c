package com.ruoyi.modules.user.service;

import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.user.dao.SysUploadLocalDao;
import com.ruoyi.modules.user.entity.SysUploadLocal;
import org.springframework.stereotype.Service;

/**
 * Created by Administrator on 2021-10-22.
 */
@Service
public class SysUploadLocalService extends BaseService<SysUploadLocalDao,SysUploadLocal> {

    /**
     * 根据key获取附件相关路径及文件名
     * @param key
     * @return
     */
    public SysUploadLocal getByKey(String key){
        return dao.getByKey(key);
    }
}
