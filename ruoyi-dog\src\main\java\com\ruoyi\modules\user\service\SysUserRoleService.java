package com.ruoyi.modules.user.service;

import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.user.dao.SysUserRoleDao;
import com.ruoyi.modules.user.entity.SysUserRole;
import org.springframework.stereotype.Service;

/**
 * Created by Administrator on 2021/3/23/023.
 */
@Service
public class SysUserRoleService extends BaseService<SysUserRoleDao, SysUserRole> {

    public void insertOrUpdate(SysUserRole sysUserRole){
        if(sysUserRole.getId() != null && !sysUserRole.getId().equals("")){
            //更新用户角色关联表
            super.update(sysUserRole);
        }else{
            //新增用户角色关联表
            super.insert(sysUserRole);
        }

    }

}
