package com.ruoyi.modules.takeIn.entity;

import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.brand.entity.PetBrand;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.user.entity.SysUploadFile;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * 收容处置表(TakeIn)实体类
 *
 * <AUTHOR>
 * @since 2022-08-18 14:31:27
 */
@Data
public class TakeIn extends BaseEntity {

    private String bizCode;
    /**
    * 类型：1.犬只无牌 2.犬只有牌
    */
    private String type;
    /**
    * 收容原因类型：1.处罚收容 2.无主收容
    */
    private String takeInType;
    /**
    * 收容原因类型==1 :1.待认领、2.已申请、3.已认领;收容原因类型==2:1.待收养、2.已申请、3.已收养
    */
    private String status;
    /**
     * 审批状态 1:审核中，2:已通过，3:未通过
     */
    private String approvalStatus;
    /**
    * 犬牌编号
    */
    private String petNum;
    /**
    * 姓名
    */
    private String name;
    /**
    * 身份证号
    */
    private String idCard;
    /**
    * 联系电话
    */
    private String contactNumber;
    /**
    * 原因
    */
    private String reason;
    /**
    * 地点
    */
    private String address;
    /**
    * 收容时间
    */
    @JsonFormat(timezone = "GMT+8" ,pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;
    /**
    * 备注
    */
    private String remarks;
    /**
    * 处理记录
    */
    private String processingRecord;
    /**
     * 是否发布：1.是 2.否
     */
    private String isRelease;
    /**/
    private String keyword;//关键字
    private PetCertificates pet;//犬牌信息
    private JSONArray recordList;//处罚记录
    private String deptName;//所属机构名称
    private String deptId;//所属区域

    /**
     * 宠物名
     */
    private String petName;

    /**
     * 毛发
     */
    private String petHair;

    /**
     * 照片
     */
    private String petImg;

    /**
     * 饲主姓名
     */
    private String ownerName;

    /**
     * 联系方式
     */
    private String tel;

    /**
     * 身份证号码
     */
    private String petIdCard;

    /**
     * 品种
     */
    private String petVarieties;

    private ReclaimRecord reclaimRecord;

    private List<String> statusList;

    /**
     * 附件
     */
    private List<SysUploadFile> uploadFileList;

    private String petLength;

    private String petAge;

    private Integer petSex;

    private String petVarietiesOne;

    private String county;

    private String street;

    private String reclaimRecordStatus;

    private Long takeInUserId;

    private String takeInUserName;

    private Long takeInDeptId;

    private String takeInDeptName;

    private String signUserName;

    private Long signUserId;

}
