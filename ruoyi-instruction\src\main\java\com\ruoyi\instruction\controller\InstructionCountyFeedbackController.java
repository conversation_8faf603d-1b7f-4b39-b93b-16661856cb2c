package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.InstructionCountyFeedback;
import com.ruoyi.instruction.service.IInstructionCountyFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 县市区反馈Controller
 * 
 * <AUTHOR>
 * @date 2023-05-25
 */
@RestController
@RequestMapping("/instruction/countyFeedback")
public class InstructionCountyFeedbackController extends BaseController
{
    @Autowired
    private IInstructionCountyFeedbackService instructionCountyFeedbackService;

    /**
     * 查询县市区反馈列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:countyFeedback:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionCountyFeedback instructionCountyFeedback)
    {
        startPage();
        List<InstructionCountyFeedback> list = instructionCountyFeedbackService.selectInstructionCountyFeedbackList(instructionCountyFeedback);
        return getDataTable(list);
    }

    /**
     * 导出县市区反馈列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:countyFeedback:export')")
    @Log(title = "县市区反馈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionCountyFeedback instructionCountyFeedback)
    {
        List<InstructionCountyFeedback> list = instructionCountyFeedbackService.selectInstructionCountyFeedbackList(instructionCountyFeedback);
        ExcelUtil<InstructionCountyFeedback> util = new ExcelUtil<InstructionCountyFeedback>(InstructionCountyFeedback.class);
        util.exportExcel(response, list, "县市区反馈数据");
    }

    /**
     * 获取县市区反馈详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:countyFeedback:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(instructionCountyFeedbackService.selectInstructionCountyFeedbackById(id));
    }

    /**
     * 新增县市区反馈
     */
    @PreAuthorize("@ss.hasPermi('instruction:countyFeedback:add')")
    @Log(title = "县市区反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionCountyFeedback instructionCountyFeedback)
    {
        return toAjax(instructionCountyFeedbackService.insertInstructionCountyFeedback(instructionCountyFeedback));
    }

    /**
     * 修改县市区反馈
     */
    @PreAuthorize("@ss.hasPermi('instruction:countyFeedback:edit')")
    @Log(title = "县市区反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionCountyFeedback instructionCountyFeedback)
    {
        return toAjax(instructionCountyFeedbackService.updateInstructionCountyFeedback(instructionCountyFeedback));
    }

    /**
     * 删除县市区反馈
     */
    @PreAuthorize("@ss.hasPermi('instruction:countyFeedback:remove')")
    @Log(title = "县市区反馈", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(instructionCountyFeedbackService.deleteInstructionCountyFeedbackByIds(ids));
    }
}
