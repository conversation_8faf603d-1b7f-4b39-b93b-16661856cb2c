package com.ruoyi.modules.user.entity;


import com.ruoyi.base.entity.BaseEntity;

import java.util.List;

/**
 * @classname:
 * @description:
 * @author: 侯旭
 * @date: 2021/4/7 14:31
 * @version: 1.0
 **/
public class SysDept extends BaseEntity {
private String sendAddress;//寄送地址
    private String deptName;                //部门名称
    private String parentId;                //父级id
    private String deptAdmin;               //部门负责人
    private Integer level;                  //级别
    private Integer isMainFlag;             //是否是信访局部门：1是 2否
    private Integer sort;                   //排序
    private Integer unDelFlag;              //不可删除标记，仅数据库修改：1不可删除
    private String lon;                     //地理位置经度
    private String lat;                     //地理位置维度
    private List<SysDept> sonList;          //子结构
    private int userCount = 0 ;                 // 联村干部数量，查询使用

    private String name;                // 名称 用部门+人员数量拼接
    private List<SysDept> children;     // 返回子结构列表
    private String value ;              // 返回id

    public String getSendAddress() {
        return sendAddress;
    }

    public void setSendAddress(String sendAddress) {
        this.sendAddress = sendAddress;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getDeptAdmin() {
        return deptAdmin;
    }

    public void setDeptAdmin(String deptAdmin) {
        this.deptAdmin = deptAdmin;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public List<SysDept> getSonList() {
        return sonList;
    }

    public void setSonList(List<SysDept> sonList) {
        this.sonList = sonList;
    }

    public Integer getIsMainFlag() {
        return isMainFlag;
    }

    public void setIsMainFlag(Integer isMainFlag) {
        this.isMainFlag = isMainFlag;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getUnDelFlag() {
        return unDelFlag;
    }

    public void setUnDelFlag(Integer unDelFlag) {
        this.unDelFlag = unDelFlag;
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public int getUserCount() {
        return userCount;
    }

    public void setUserCount(int userCount) {
        this.userCount = userCount;
    }

    public String getName() {
        return this.deptName + ""+this.userCount+"人";
    }

    public List<SysDept> getChildren() {
        return this.sonList;
    }

    public String getValue() {
        return this.getId();
    }
}
