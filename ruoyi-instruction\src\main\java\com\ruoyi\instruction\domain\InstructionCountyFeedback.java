package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 县市区反馈对象 t_instruction_county_feedback
 * 
 * <AUTHOR>
 * @date 2023-05-25
 */
@Data
public class InstructionCountyFeedback extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 县市区反馈id */
    private Long id;

    /** 接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "接收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    /** 反馈对象 */
    @Excel(name = "反馈对象")
    private String feedbackObject;

    /** 反馈情况 */
    @Excel(name = "反馈情况")
    private String situation;

    /** 反馈人员 */
    @Excel(name = "反馈人员")
    private String feedbackBy;

    /** 反馈部门 */
    @Excel(name = "反馈部门")
    private String feedbackDept;

    /** 文件ids */
    @Excel(name = "文件ids")
    private String fileIds;

    /** 指令id */
    @Excel(name = "指令id")
    private Long instructionId;

    /** 1:正常 9：删除 */
    @Excel(name = "1:正常 9：删除")
    private Integer status;

    /** 反馈部门id */
    @Excel(name = "反馈部门id")
    private Long feedbackDeptId;

    /** 是否办结 1：是 2：否 */
    @Excel(name = "是否办结 1：是 2：否")
    private Integer isEnd;

    /** 驳回部门id */
    private String rejectDeptId;


    private String remark;

    private List<Map<String,Object>> maps;


}
