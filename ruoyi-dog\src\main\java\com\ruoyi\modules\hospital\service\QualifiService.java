package com.ruoyi.modules.hospital.service;

import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.modules.brand.entity.PetBrandApply;
import com.ruoyi.modules.brand.service.PetBrandApplyService;
import com.ruoyi.modules.hospital.dao.QualifiDao;
import com.ruoyi.modules.hospital.dao.QualifiRecordDao;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.hospital.entity.QualifiRecord;
import com.ruoyi.modules.hospital.entity.QualifiYear;
import com.ruoyi.modules.hospital.vo.HospitalApplyVO;
import com.ruoyi.modules.hospital.vo.HospitalRecordVO;
import com.ruoyi.modules.user.dao.SysUploadFileDao;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.service.SysUploadFileService;
import com.ruoyi.modules.vaccine.entity.Vaccine;
import com.ruoyi.modules.vaccine.service.VaccineService;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.util.IdGen;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 10:52
 * @version: 1.0
 **/
@Service
public class QualifiService extends BaseService<QualifiDao, Qualifi> {

    @Autowired
    SysUploadFileService uploadFileService;
    @Autowired
    SysUploadFileDao uploadFileDao;
//    @Autowired
//    SysUserService sysUserService;
//    @Autowired
//    SysUserRoleDao sysUserRoleDao;

    @Autowired
    QualifiRecordDao qualifiRecordDao;//资质审核记录
    @Autowired
    @Lazy
    private QualifiYearService qualifiYearService;
    @Autowired
    private PetBrandApplyService petBrandApplyService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private VaccineService vaccineService;

    /**
     * 分页获取数据对象
     * @param entity
     * @return
     */
    public PageInfo<Qualifi> getPageList(Qualifi entity){
        PageHelper.startPage(entity.getPageNum(),entity.getPageSize());

        // 获取用户信息
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录或会话失效");
        }
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());

        if (roleList.contains(InstructionRolesConstants.DOG_COUNTY_CONFIRM)) {
            //根据区县过滤
            entity.setCounty(user.getDeptId() + "");
        } else if (roleList.contains(InstructionRolesConstants.HOSPITAL_ROLE)) {
            entity.setAccount(user.getDogUserId());
        }

        PageInfo<Qualifi> pageInfo = new PageInfo<Qualifi>(getList(entity));
        return pageInfo;
    }

    public List<String> checkQualifi() {
        Qualifi qualifi = getCurrentQualifi();
        if (Objects.isNull(qualifi)) {
            return Lists.newArrayList();
        }

        return Lists.newArrayList("犬只登记", "登记年审", "登记过户", "犬牌补办");
    }

    @Transactional
    public String save(Qualifi entity) {
        String str = "1";
        Qualifi qualifi = dao.verifiUnitCode(entity.getId(), entity.getUnifiedCode(), entity.getTel());
        if (Objects.nonNull(qualifi)
                && !"3".equals(qualifi.getStatus()) && !"4".equals(qualifi.getStatus())) {
//            return "1";
            throw new RuntimeException("已提交资质审核，请勿重复提交");
        }

        if (StringUtils.isBlank(entity.getStatus())) {
            entity.setStatus("1");
        }

        entity.setCreateDate(new Date());

//        SysUser user = SecurityUtils.getLoginUser().getUser();

        SysUser sysUser = sysUserMapper.getByMobile(entity.getTel());
        if (Objects.nonNull(sysUser)) {
            entity.setAccount(sysUser.getDogUserId());
        }

        saveOrUpdate(entity);

        uploadFileService.delByInstanceAndModel(entity.getId(), "");
        if (entity.getUploadFileStr() != null && !"".equals(entity.getUploadFileStr())) {
            List<SysUploadFile> list = JSON.parseArray(entity.getUploadFileStr(), SysUploadFile.class);
            List<SysUploadFile> result = new ArrayList<>();
            for (SysUploadFile s : list) {
                s.preInsert();
                s.setInstanceId(entity.getId());
                result.add(s);
            }
            uploadFileDao.saveAllList(result);
        }

        //更新疫苗信息
        vaccineService.deleteByQualifyId(entity.getId());
        if (CollectionUtils.isNotEmpty(entity.getVaccineList())) {
            for (String vaccineName : entity.getVaccineList()) {
                Vaccine vaccine = new Vaccine();
                vaccine.setId(IdUtils.fastUUID());
                vaccine.setName(vaccineName);
                vaccine.setManufacturer(vaccineName);
                vaccine.setStatus("2");
                vaccine.setReason("通过");
                vaccine.setAuditTime(new Date());
                vaccine.setApplyPersonId(entity.getId());

                vaccineService.insert(vaccine);
            }
        }

        if (!"2".equals(entity.getStatus())) {//不是审核通过，在记录表中记录
            QualifiRecord qualifiRecord = new QualifiRecord();
            qualifiRecord.setQualifiId(entity.getId());//医院ID
            qualifiRecord.setRecordType(6);//操作类型 4：免疫注销 5 犬牌注销 6 医院提交 7 审批结果
            qualifiRecord.setNode(3);//流程节点 1：畜牧局 2：执法局 3 ：（宠物医院，执法队员，执法窗口）
            qualifiRecord.setStatus(1);//状态 1：已提交 2：已通过 3 ：未通过
            qualifiRecord.setCreateName(entity.getName());
            qualifiRecord.setId(IdGen.uuid());
            qualifiRecord.preInsert();
            qualifiRecordDao.insert(qualifiRecord);
        }
        return str;
    }

    public Qualifi getById(String id){
        Qualifi qualifi = dao.getById(id);
        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.setInstanceId(id);
        qualifi.setUploadFileList(uploadFileService.getList(sysUploadFile));

        Vaccine query = new Vaccine();
        query.setCreateBy(qualifi.getId());
        List<Vaccine> allList = vaccineService.getAllList(query);
        if (CollectionUtils.isNotEmpty(allList)) {
            List<String> list = allList.stream()
                    .map(Vaccine::getName)
                    .collect(Collectors.toList());

            qualifi.setVaccineList(list);
        }

        return qualifi;
    }

    public void delete(Qualifi qualifi){
        dao.delete(qualifi);
        uploadFileService.delByInstanceAndModel(qualifi.getId(), "policy_id");
    }

    @Override
    public void saveOrUpdate(Qualifi entity) {
        if(entity.getId() != null && !"".equals(entity.getId())){
            // 更新用户
            entity.preUpdate();
            update(entity);
        }else{
            // 新增用户
            entity.preInsert();
//            entity.setAccount(entity.getId());
            insert(entity);
        }
    }

    @Transactional
    public void updateStatus(Qualifi qualifi) {
        dao.updateStatus(qualifi);
        if ("2".equals(qualifi.getStatus()) && ("".equals(qualifi.getAccount()) || qualifi.getAccount() == null)) {
            Qualifi tem = dao.getById(qualifi.getId());
            createAccount(tem);
        }
        if ("2".equals(qualifi.getStatus())) {//复制
            dao.deleteReduct(qualifi.getId());
            dao.copyQualifi(qualifi.getId());
        }
        if ("3".equals(qualifi.getStatus())) {//还原
            dao.reductQualifi(qualifi.getId());
        }
        //审核操作在记录表记录
        QualifiRecord qualifiRecord = new QualifiRecord();
        qualifiRecord.setQualifiId(qualifi.getId());//医院ID
        qualifiRecord.setRecordType(qualifi.getRecordType());//操作类型 4：免疫注销 5 犬牌注销 6 医院提交 7 审批结果
        qualifiRecord.setNode(qualifi.getNode());//流程节点 1：畜牧局 2：执法局 3 ：（宠物医院，执法队员，执法窗口）
        qualifiRecord.setStatus(qualifi.getRecordStatus());//状态 1：已提交 2：已通过 3 ：未通过
        qualifiRecord.setCreateName(qualifi.getCreateName());
        qualifiRecord.setRemark(qualifi.getRecordRemark());
        qualifiRecord.setId(IdGen.uuid());
        qualifiRecord.preInsert();
        qualifiRecordDao.insert(qualifiRecord);
    }


    public void createAccount(Qualifi qualifi) {
        Qualifi existQualifi = dao.getById(qualifi.getId());

        SysUser sysUser = new SysUser();
        sysUser.setPhonenumber(existQualifi.getTel());
        List<SysUser> sysUserList = sysUserMapper.selectUserList(sysUser);
        if (CollectionUtils.isEmpty(sysUserList)) {
            sysUser.setCreateTime(new Date());
            sysUser.setUserName(existQualifi.getTel());//账号 默认手机号
            sysUser.setNickName(existQualifi.getName());//真实名称
            sysUser.setPhonenumber(existQualifi.getTel());//手机号

            try {
                long deptId = Long.parseLong(existQualifi.getCounty());
                sysUser.setDeptId(deptId);//所属机构
            } catch (Exception e) {
                sysUser.setDeptId(100L);//所属机构
            }

            if ("0".equals(existQualifi.getType())) {
                sysUser.setUserType(2);//用户类型 2：宠物医院
            } else if ("1".equals(existQualifi.getType())) {
                sysUser.setUserType(3);//用户类型 3：执法局
            } else if ("2".equals(existQualifi.getType())) {
                sysUser.setUserType(3);//用户类型 5：执法窗口
            } else {
                sysUser.setUserType(2);//用户类型 2：宠物医院
            }
            if (existQualifi.getTel() != null && existQualifi.getTel().length() > 10) {
                String pass = "QL" + existQualifi.getTel().substring(existQualifi.getTel().length() - 6) + "!@#";
                sysUser.setPassword(SecurityUtils.encryptPassword(pass));//密码
            } else {
                sysUser.setPassword(SecurityUtils.encryptPassword("123456"));//密码
            }

            sysUser.setUserSystemType("106");

            sysUserMapper.insertUser(sysUser);

            sysUser.setDogUserId(sysUser.getUserId() + "");
            sysUserMapper.updateUser(sysUser);

            existQualifi.setAccount(sysUser.getUserId() + "");
            dao.updateAccount(existQualifi);
            //创建医院账号自动分权限。
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            SysUserRole userRole = new SysUserRole();
            userRole.setUserId(sysUser.getUserId());
            if ("0".equals(existQualifi.getType())) {
                userRole.setRoleId(100L);//宠物医院 权限宠物医院
            } else if ("1".equals(existQualifi.getType())) {
                userRole.setRoleId(101L);//执法队员  权限执法局
            } else if ("2".equals(existQualifi.getType())) {
                userRole.setRoleId(102L);//执法窗口 权限执法窗口
            } else {
                userRole.setRoleId(100L);//宠物医院 权限宠物医院
            }
            list.add(userRole);

            if (CollectionUtils.isNotEmpty(list)) {
                sysUserRoleMapper.batchUserRole(list);
            }
        } else {

        }
    }

    public Qualifi getQualifiByAccount(Qualifi qualifi){
        qualifi = dao.getQualifiWithOutStatus(qualifi);

        fillFileList(Lists.newArrayList(qualifi));

        return qualifi;
    }

    public List<Qualifi> getAllList(Qualifi qualifi){
        List<Qualifi> allList = dao.getAllList(qualifi);

//        fillFileList(allList);

        return allList;
    }

    private void fillFileList(List<Qualifi> allList) {
        for (Qualifi qualifi : allList) {
            if (qualifi!=null) {
                SysUploadFile sysUploadFile = new SysUploadFile();
                sysUploadFile.setInstanceId(qualifi.getId());
                qualifi.setUploadFileList(uploadFileService.getList(sysUploadFile));
            }
        }
    }


    public List<SysUser> getApply(SysUser sysUser){
        return dao.getApply(sysUser);
    }

    public PageInfo<HospitalApplyVO> getApplyList(HospitalRecordVO recordVO) {
        PageHelper.startPage(recordVO.getPage(),recordVO.getPageSize());

        PageInfo resultInfo = new PageInfo();

        // 获取用户信息
        com.ruoyi.common.core.domain.entity.SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录或会话失效");
        }
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());



        List<HospitalApplyVO> resultList = Lists.newArrayList();
        if (recordVO.getApplyType() == 1) {
            //资质审核
            Qualifi req = new Qualifi();
            req.setName(recordVO.getName());
            req.setStatus(recordVO.getStatus());

            if (roleList.contains(InstructionRolesConstants.DOG_COUNTY_CONFIRM)) {
                //根据区县过滤
                req.setCounty(user.getDeptId() + "");
            } else if (roleList.contains(InstructionRolesConstants.HOSPITAL_ROLE)) {
                req.setAccount(user.getDogUserId());
            }

            List<Qualifi> list = dao.getList(req);

            PageInfo<Qualifi> pageInfo = new PageInfo<Qualifi>(list);
            if (CollectionUtils.isNotEmpty(list)) {
                for (Qualifi qualifi : list) {
                    HospitalApplyVO applyVO = new HospitalApplyVO();
                    applyVO.setId(qualifi.getId());
                    applyVO.setName(qualifi.getName());
                    applyVO.setApplyType(recordVO.getApplyType());
                    applyVO.setApplyUser(qualifi.getPerson());
                    applyVO.setMobile(qualifi.getTel());
                    applyVO.setStatus(qualifi.getStatus());
                    applyVO.setCreateDate(qualifi.getCreateDate());

                    resultList.add(applyVO);
                }
            }

            resultInfo.setTotal(pageInfo.getTotal());
            resultInfo.setList(resultList);

        } else if (recordVO.getApplyType() == 2) {
            //年审审核
            QualifiYear req = new QualifiYear();
            req.setName(recordVO.getName());
            req.setStatus(recordVO.getStatus());

            if (roleList.contains(InstructionRolesConstants.DOG_COUNTY_CONFIRM)) {
                //根据区县过滤
                req.setCounty(user.getDeptId() + "");
            } else if (roleList.contains(InstructionRolesConstants.HOSPITAL_ROLE)) {
                req.setAccount(user.getDogUserId());
            }

            List<QualifiYear> list = qualifiYearService.getList(req);

            PageInfo<QualifiYear> pageInfo = new PageInfo<QualifiYear>(list);
            if (CollectionUtils.isNotEmpty(list)) {
                for (QualifiYear qualifiYear : list) {
                    HospitalApplyVO applyVO = new HospitalApplyVO();
                    applyVO.setId(qualifiYear.getId());
                    applyVO.setName(qualifiYear.getName());
                    applyVO.setApplyType(recordVO.getApplyType());
                    applyVO.setApplyUser(qualifiYear.getPerson());
                    applyVO.setMobile(qualifiYear.getTel());
                    applyVO.setStatus(qualifiYear.getStatus());
                    applyVO.setCreateDate(qualifiYear.getCreateDate());

                    resultList.add(applyVO);
                }
            }

            resultInfo.setTotal(pageInfo.getTotal());
            resultInfo.setList(resultList);
        } else {
            //犬牌
            PetBrandApply petBrandApply = new PetBrandApply();
            petBrandApply.setQualifiName(recordVO.getName());
            if (StringUtils.isNotBlank(recordVO.getStatus())) {
                petBrandApply.setStatus(Integer.parseInt(recordVO.getStatus()));
            }

            if (roleList.contains(InstructionRolesConstants.DOG_COUNTY_CONFIRM)) {
                //根据区县过滤
                petBrandApply.setCounty(user.getDeptId() + "");
            } else if (roleList.contains(InstructionRolesConstants.HOSPITAL_ROLE)) {
                petBrandApply.setUserId(user.getDogUserId());
            }

            List<PetBrandApply> list = petBrandApplyService.getList(petBrandApply);

            PageInfo<PetBrandApply> pageInfo = new PageInfo<PetBrandApply>(list);

            if (CollectionUtils.isNotEmpty(list)) {
                //获取资质
                List<String> qualifiIdList = list.stream()
                        .map(PetBrandApply::getQualifiId)
                        .collect(Collectors.toList());

                List<Qualifi> qualifiList = dao.listByIdList(qualifiIdList);

                Map<String, Qualifi> map = qualifiList.stream()
                        .collect(Collectors.toMap(Qualifi::getAccount, Function.identity()));

                for (PetBrandApply brandApply : list) {
                    Qualifi qualifi = map.get(brandApply.getUserId());

                    HospitalApplyVO applyVO = new HospitalApplyVO();
                    applyVO.setId(qualifi.getId());
                    applyVO.setName(qualifi.getName());
                    applyVO.setApplyType(recordVO.getApplyType());
                    applyVO.setApplyUser(qualifi.getPerson());
                    applyVO.setMobile(qualifi.getTel());
                    applyVO.setStatus(qualifi.getStatus());
                    applyVO.setCreateDate(brandApply.getCreateDate());

                    resultList.add(applyVO);
                }

            }

            resultInfo.setTotal(pageInfo.getTotal());
            resultInfo.setList(resultList);
        }

        return resultInfo;
    }

    public Qualifi getCurrentQualifi() {
        Long userId = SecurityUtils.getUserId();
        com.ruoyi.common.core.domain.entity.SysUser sysUser = sysUserMapper.selectUserById(userId);
        if (2 == sysUser.getUserType()) {
            String dogUserId = sysUser.getDogUserId();
            if (StringUtils.isNotBlank(dogUserId)) {
                Qualifi query = new Qualifi();
                query.setAccount(dogUserId);
                query.setStatus("2");

                Qualifi qualifiByAccount = dao.getQualifiByAccount(query);

                if (Objects.isNull(qualifiByAccount)) {
                    Qualifi query2 = new Qualifi();
                    query2.setStatus("2");
                    query2.setTel(sysUser.getPhonenumber());

                    qualifiByAccount = dao.getQualifiByAccount(query);
                }

                if (Objects.nonNull(qualifiByAccount)) {
                    SysUploadFile sysUploadFile = new SysUploadFile();
                    sysUploadFile.setInstanceId(qualifiByAccount.getId());
                    qualifiByAccount.setUploadFileList(uploadFileService.getList(sysUploadFile));
                }

                return qualifiByAccount;
            }
        }

        return null;
    }

    public Qualifi getCurrentQualifiV2() {
        Long userId = SecurityUtils.getUserId();
        com.ruoyi.common.core.domain.entity.SysUser sysUser = sysUserMapper.selectUserById(userId);
        if (2 == sysUser.getUserType()) {
            String dogUserId = sysUser.getDogUserId();
            if (StringUtils.isNotBlank(dogUserId)) {
                Qualifi query = new Qualifi();
                query.setAccount(dogUserId);

                Qualifi qualifiByAccount = dao.getQualifiByAccount(query);

                if (Objects.isNull(qualifiByAccount)) {
                    Qualifi query2 = new Qualifi();
                    query2.setTel(sysUser.getPhonenumber());

                    qualifiByAccount = dao.getQualifiByAccount(query);
                }

                return qualifiByAccount;
            }
        }

        return null;
    }

    public List<HospitalApplyVO> getHospitalApplyList(HospitalRecordVO recordVO) {
        Long userId = SecurityUtils.getUserId();
        com.ruoyi.common.core.domain.entity.SysUser sysUser = sysUserMapper.selectUserById(userId);

        if ("0".equals(recordVO.getStatus())) {
            recordVO.setStatus(null);
        }

        List<Qualifi> allQualifiList = dao.listQualifiByAccount(sysUser.getDogUserId(), recordVO.getStatus());

//        Qualifi currentQualifi = getCurrentQualifiV2();
        if (CollectionUtils.isEmpty(allQualifiList)) {
            return Lists.newArrayList();
        }

        List<HospitalApplyVO> resultList = Lists.newArrayList();

        for (Qualifi qualifi : allQualifiList) {
            HospitalApplyVO applyVO = new HospitalApplyVO();
            applyVO.setId(qualifi.getId());
            applyVO.setName(qualifi.getName());
            applyVO.setApplyType(1);
            applyVO.setApplyUser(qualifi.getPerson());
            applyVO.setMobile(qualifi.getTel());
            applyVO.setStatus(qualifi.getStatus());
            applyVO.setCreateDate(qualifi.getCreateDate());

            resultList.add(applyVO);
        }

        Optional<Qualifi> first = allQualifiList.stream()
                .filter(qualifi -> "2".equals(qualifi.getStatus()))
                .findFirst();

        if (!first.isPresent()) {
            return resultList;
        }

        Qualifi approveQualifi = first.get();

        //年审审核
        QualifiYear qualifiYearReq = new QualifiYear();
        qualifiYearReq.setName(recordVO.getName());
        qualifiYearReq.setStatus(recordVO.getStatus());
        qualifiYearReq.setQualifiId(approveQualifi.getId());

        List<QualifiYear> qualifiYearReqList = qualifiYearService.getList(qualifiYearReq);
        if (CollectionUtils.isNotEmpty(qualifiYearReqList)) {
            for (QualifiYear qualifiYear : qualifiYearReqList) {
                HospitalApplyVO yearApplyVO = new HospitalApplyVO();
                yearApplyVO.setId(qualifiYear.getId());
                yearApplyVO.setName(qualifiYear.getName());
                yearApplyVO.setApplyType(2);
                yearApplyVO.setApplyUser(qualifiYear.getPerson());
                yearApplyVO.setMobile(qualifiYear.getTel());
                yearApplyVO.setStatus(qualifiYear.getStatus());
                yearApplyVO.setCreateDate(qualifiYear.getCreateDate());

                resultList.add(yearApplyVO);
            }
        }

        //犬牌
        PetBrandApply petBrandApply = new PetBrandApply();
        petBrandApply.setQualifiName(recordVO.getName());
        if (StringUtils.isNotBlank(recordVO.getStatus())) {
            if ("2".equals(recordVO.getStatus())) {
                petBrandApply.setUpStatus(3);
            } else if ("3".equals(recordVO.getStatus())) {
                petBrandApply.setStatus(2);
            }
        }
        petBrandApply.setQualifiId(approveQualifi.getId());

        List<PetBrandApply> petBrandApplyList = petBrandApplyService.getList(petBrandApply);

        if (CollectionUtils.isNotEmpty(petBrandApplyList)) {
            //获取资质
            List<String> qualifiIdList = petBrandApplyList.stream()
                    .map(PetBrandApply::getQualifiId)
                    .collect(Collectors.toList());

            List<Qualifi> qualifiList = dao.listByIdList(qualifiIdList);

            Map<String, Qualifi> map = qualifiList.stream()
                    .collect(Collectors.toMap(Qualifi::getId, Function.identity()));

            for (PetBrandApply brandApply : petBrandApplyList) {
                Qualifi qualifi = map.get(brandApply.getQualifiId());

                HospitalApplyVO petApplyVO = new HospitalApplyVO();
                petApplyVO.setId(brandApply.getId());
                petApplyVO.setName(qualifi.getName());
                petApplyVO.setApplyType(3);
                petApplyVO.setApplyUser(qualifi.getPerson());
                petApplyVO.setMobile(brandApply.getTel());
                petApplyVO.setStatus(brandApply.getStatus() + "");
                petApplyVO.setCreateDate(brandApply.getCreateDate());

                resultList.add(petApplyVO);
            }
        }

        resultList.sort(Comparator.comparing(HospitalApplyVO::getCreateDate).reversed());

        return resultList;
    }

    public List<String> getAllVaccine() {
        return vaccineService.getDistinctList();
    }

    public List<Qualifi> listSendMessage() {
        return dao.listSendMessage();
    }

    public Qualifi checkExpire(Long userId) {
        return dao.checkExpire(userId);
    }
}
