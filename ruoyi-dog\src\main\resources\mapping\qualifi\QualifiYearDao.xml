<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.hospital.dao.QualifiYearDao">
    <resultMap id="BaseResultMap" type="com.ruoyi.modules.hospital.entity.QualifiYear">
        <!--@mbg.generated-->
        <!--@Table qualifi_year-->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="year" jdbcType="INTEGER" property="year" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="county" jdbcType="VARCHAR" property="county" />
        <result column="street" jdbcType="VARCHAR" property="street" />
        <result column="area" jdbcType="VARCHAR" property="area" />
        <result column="address" jdbcType="VARCHAR" property="address" />
        <result column="lon" jdbcType="VARCHAR" property="lon" />
        <result column="lat" jdbcType="VARCHAR" property="lat" />
        <result column="person" jdbcType="VARCHAR" property="person" />
        <result column="tel" jdbcType="VARCHAR" property="tel" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="qualifi" jdbcType="VARCHAR" property="qualifi" />
        <result column="unified_code" jdbcType="VARCHAR" property="unifiedCode" />
        <result column="account" jdbcType="VARCHAR" property="account" />
        <result column="password" jdbcType="VARCHAR" property="password" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="can_status" jdbcType="INTEGER" property="canStatus" />
        <result column="can_reason" jdbcType="VARCHAR" property="canReason" />
        <result column="law_status" jdbcType="INTEGER" property="lawStatus" />
        <result column="law_reason" jdbcType="VARCHAR" property="lawReason" />
        <result column="ani_status" jdbcType="INTEGER" property="aniStatus" />
        <result column="reason" jdbcType="VARCHAR" property="reason" />
        <result column="about_auth" jdbcType="INTEGER" property="aboutAuth" />
        <result column="brand_status" jdbcType="INTEGER" property="brandStatus" />
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, `name`, county, street, area, address, lon, lat, person, tel, `type`, qualifi,
        unified_code, account, `password`, remarks, `status`, create_by, create_date, del_flag,
        update_by, update_date, can_status, can_reason, law_status, law_reason, ani_status,
        reason, about_auth, brand_status, year
    </sql>

    <sql id="qualifiApplySql">
        a.id as "id",
        a.year as "year",
        a.name as "name",
        a.county as "county",
        a.street as "street",
        a.area as "area",
        a.address as "address",
        a.person as "person",
        a.tel as "tel",
        a.type as "type",
        a.qualifi as "qualifi",
        a.account as "account",
        a.password as "password",
        a.remarks as "remarks",
        a.status as "status",
        a.unified_code as "unifiedCode",
        a.lon as "lon",
        a.lat as "lat",
        a.law_status as "lawStatus",
        a.ani_status as "aniStatus",
        a.can_status as "canStatus",
        a.about_auth as "aboutAuth",
        a.brand_status as "brandStatus",
        a.reason as "reason",
        a.create_date as "createDate"
    </sql>

    <select id="getList" parameterType="com.ruoyi.modules.hospital.entity.QualifiYear" resultType="com.ruoyi.modules.hospital.entity.QualifiYear">
        select
        <include refid="qualifiApplySql" />
        from qualifi_year a
        where del_flag = 1
        <if test="keyWord!=null and keyWord!=''">
            and (a.name LIKE concat("%", #{keyWord}, "%")
            or a.address LIKE concat("%", #{keyWord}, "%")
            or a.person LIKE concat("%", #{keyWord}, "%")
            or a.tel LIKE concat("%", #{keyWord}, "%")
            )
        </if>
        <if test="name != null and name != ''">
            and a.name LIKE concat("%", #{name}, "%")
        </if>
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        <if test="county != null and county != ''">
            and a.county = #{county}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.create_by = #{createBy}
        </if>
        <if test="canStatus != null and canStatus != ''">
            and a.can_status = #{canStatus}
        </if>
        <if test="brandStatus != null and brandStatus != ''">
            and a.brand_status = #{brandStatus}
        </if>
        <if test="lawStatus != null and lawStatus != ''">
            and a.law_status = #{lawStatus}
        </if>
        <if test="aniStatus != null and aniStatus != ''">
            and a.ani_status = #{aniStatus}
        </if>
        <if test="account != null and account != ''">
            and a.account = #{account}
        </if>
        <if test="canType != null and canType != ''">
            and exists (select 1 from qualifi_year_record b where b.qualifi_id = a.id and b.record_type=#{canType}  )
        </if>
        <if test="qualifiId != null and qualifiId != ''">
            and qualifi_id  = #{qualifiId}
        </if>
        order by  a.create_date desc
    </select>

    <select id="getById" resultType="com.ruoyi.modules.hospital.entity.QualifiYear">
        select
        <include refid="qualifiApplySql" />,
        (select qr.remark from qualifi_year_record qr where qr.record_type = '4' and qr.qualifi_id = a.id order by qr.create_date desc limit 0,1) AS canReason,
        (select qr.remark from qualifi_year_record qr where qr.record_type = '5' and qr.qualifi_id = a.id order by qr.create_date desc limit 0,1) AS brandReason
        from qualifi_year a
        where id = #{id}
    </select>

    <insert id="insert" parameterType="com.ruoyi.modules.hospital.entity.QualifiYear">
        insert into qualifi_year(
            id,
            qualifi_id,
            year,
            name,
            county,
            street,
            area,
            address,
            person,
            tel,
            type,
            qualifi,
            account,
            password,
            remarks,
            status,
            create_date,
            create_by,
            del_flag,
            unified_code,
            lon,
            lat,
            ani_status,
            law_status
        )values (
            #{id},
            #{qualifiId},
            #{year},
            #{name},
            #{county},
            #{street},
            #{area},
            #{address},
            #{person},
            #{tel},
            #{type},
            #{qualifi},
            #{account},
            #{password},
            #{remarks},
            #{status},
            #{createDate},
            #{createBy},
            #{delFlag},
            #{unifiedCode},
            #{lon},
            #{lat},
            #{aniStatus},
            #{lawStatus}
        )
    </insert>

    <update id="update" parameterType="com.ruoyi.modules.hospital.entity.QualifiYear">
        update qualifi_year set
            year = #{year},
            name = #{name},
            county = #{county},
            street = #{street},
            area = #{area},
            address = #{address},
            person = #{person},
            tel = #{tel},
            type = #{type},
            qualifi = #{qualifi},
            account = #{account},
            password = #{password},
            remarks = #{remarks},
            status = #{status},
            update_date = #{updateDate},
            update_by = #{updateBy},
            unified_code = #{unifiedCode},
            lon = #{lon},
            lat = #{lat},
            ani_status = #{aniStatus},
            law_status = #{lawStatus}
        where id = #{id}
    </update>

    <update id="delete" parameterType="com.ruoyi.modules.hospital.entity.QualifiYear">
        update qualifi_year set  del_flag = #{delFlag}  where id = #{id}
    </update>

    <update id="updateStatus" parameterType="com.ruoyi.modules.hospital.entity.QualifiYear">
        update qualifi_year a
        <set>
            <if test="aniStatus != null and aniStatus != ''">
                a.ani_status = #{aniStatus},
            </if>
            <if test="lawStatus != null and lawStatus != ''">
                a.law_status = #{lawStatus},
            </if>
            <if test="status != null and status != ''">
                a.status = #{status},
            </if>
            <if test="canStatus != null and canStatus != ''">
                a.can_status = #{canStatus},
            </if>
            <if test="brandStatus != null and brandStatus != ''">
                a.brand_status = #{brandStatus},
            </if>
             <if test="reason != null and reason != ''">
                a.reason = #{reason},
            </if>
            <if test="aboutAuth != null and aboutAuth != ''">
                a.about_auth = #{aboutAuth},
            </if>
            <if test="qualifi != null and qualifi != ''">
                a.qualifi = #{qualifi},
            </if>
        </set>
        where a.id = #{id}
    </update>

    <update id="updateAccount" parameterType="com.ruoyi.modules.hospital.entity.QualifiYear">
        update qualifi_year set  password = #{password},account = #{account} where id = #{id}
    </update>

    <select id="getQualifiByAccount" resultType="com.ruoyi.modules.hospital.entity.QualifiYear" parameterType="com.ruoyi.modules.hospital.entity.Qualifi">
        select
        <include refid="qualifiApplySql" />
        from qualifi_year a
        where 1=1
        <if test="account != null and account != ''">
            and a.account = #{account}
        </if>
        <if test="unifiedCode != null and unifiedCode != ''">
            and a.unified_code = #{unifiedCode}
        </if>
        <if test="tel != null and tel != ''">
            and a.tel = #{tel}
        </if>
    </select>

    <select id="verifiUnitCode" resultType="java.lang.String" parameterType="java.lang.String">
        select  a.tel  from qualifi_year a
        where a.del_flag = 1
          and a.tel = #{tel}
        <if test="id != null and id != ''">
            and a.id != #{id}
        </if>
    </select>

    <select id="getAllList" parameterType="com.ruoyi.modules.hospital.entity.QualifiYear" resultType="com.ruoyi.modules.hospital.entity.QualifiYear">
        select a.id, a.name,type,person,tel,qualifi,address,about_auth,
        (select file_url from sys_upload_file b where a.id=b.instance_id and b.model_type='doorway'
        and b.del_flag='1' LIMIT 1) as doorway
        <if test="lat != null and lat != ''">
            ,CONVERT (ACOS(COS( ${lat} * PI()/ 180 )* COS( a.lat * PI()/ 180 )* COS( ${lon} * PI()/
            180-a.lon * PI()/ 180 )+ SIN( ${lat} * PI()/ 180 )* SIN( a.lat * PI()/ 180 ))*
            6370996.81 / 1000,
            DECIMAL ( 10, 2 )) AS distance
        </if>
        from qualifi_year a
        where del_flag = 1
          and type = 0
        <if test="status == null or status == ''">
            and status = 2
        </if>
        <if test="county != null and county != ''">
            and a.county = #{county}
        </if>
        <if test="aboutAuth != null and aboutAuth != ''">
            and a.about_auth = #{aboutAuth}
        </if>
        <if test="keyWord != null and keyWord != ''">
            and (a.name like concat('%',#{keyWord},'%') or
            a.address like concat('%',#{keyWord},'%') or
            a.tel like concat('%',#{keyWord},'%') or
            a.person like concat('%',#{keyWord},'%'))
        </if>
        <if test="lat != null and lat != ''">
            ORDER BY distance ASC
        </if>
        <if test="lat == null or lat == ''">
            ORDER BY a.create_date ASC
        </if>
    </select>

    <insert id="copyQualifi" parameterType="java.lang.String">
        INSERT INTO qualifi_year_reduct select * from qualifi_year a  where a.id= #{id}
    </insert>

    <update id="reductQualifi" parameterType="java.lang.String">
        update qualifi_year a, qualifi_year_reduct b
        SET a.name = b.name,
            a.county = b.county,
            a.street = b.street,
            a.area = b.area,
            a.address = b.address,
            a.lon = b.lon,
            a.lat = b.lat,
            a.person = b.person,
            a.tel = b.tel,
            a.type = b.type,
            a.qualifi = b.qualifi,
            a.unified_code = b.unified_code,
            a.account = b.account,
            a.password = b.password,
            a.remarks = b.remarks
        where a.id= #{id} and a.id = b.id and b.del_flag = 1
    </update>

    <update id="deleteReduct" parameterType="com.ruoyi.modules.hospital.entity.QualifiYear">
        update qualifi_year_reduct set  del_flag = 2 where id = #{id} and del_flag=1
    </update>



    <select id="getApply" resultType="com.ruoyi.modules.user.entity.SysUser" parameterType="com.ruoyi.modules.user.entity.SysUser">
       select * from (
           SELECT
               '执法局' AS orgType,
               su.real_name,
               su.mobile
           FROM
               sys_user su
               JOIN sys_user_role sur ON su.id = sur.user_id
               JOIN sys_role sr ON sur.role_id = sr.id
           WHERE
               sr.id = '8c1e3aa5cc2e42d0a9edcb2c86aec7c1'
               and su.dept_id = #{deptId}
               LIMIT 1) a
       UNION ALL
       select  * from (
           SELECT
               '畜牧局' AS orgType,
               su.real_name,
               su.mobile
           FROM
               sys_user su
               JOIN sys_user_role sur ON su.id = sur.user_id
               JOIN sys_role sr ON sur.role_id = sr.id
           WHERE
               sr.id = '81580a763787495da6ccdb3319d5176c'
               and su.dept_id = #{deptId}
               LIMIT 1) b
    </select>

    <select id="getAlarmList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List">
    </include>
        from qualifi_year
        where 1=1
            and create_date <![CDATA[ < ]]> DATE_SUB(CURDATE(), INTERVAL 11 MONTH)
        <if test="name != null and name != ''">
            and name like concat('%',#{name},'%')
        </if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        <if test="area != null and area != ''">
            and area like concat('%',#{area},'%')
        </if>
        <if test="address != null and address != ''">
            and address like concat('%',#{address},'%')
        </if>
        <if test="county != null and county != ''">
            and county = #{county,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="checkExist" resultMap="BaseResultMap">
        select <include refid="Base_Column_List">
    </include>
        from qualifi_year
        where del_flag = 1
        and status not in (3, 4)
        and qualifi_id = #{id,jdbcType=VARCHAR}
        and year = #{year}
    </select>
</mapper>
