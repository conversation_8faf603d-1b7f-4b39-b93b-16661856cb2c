package com.ruoyi.base.entity;


import com.ruoyi.base.CodeConst;

/**
 * <AUTHOR> 返回结果类
 */
public class JsonResult {

    /**
     * 接口返回code码 默认成功
     */
    private int code = CodeConst.SUCCESS;

    /**
     * 接口返回内容
     */
    private Object result = null;

    /**
     * 接口调用成功方法 无返回对象
     * @return
     */
    public static JsonResult ok() {
        return new JsonResult();
    }

    /**
     * 接口调用成功方法 有返回对象
     * @return
     */
    public static JsonResult ok(Object object) {
        JsonResult jsonResult = new JsonResult();
        jsonResult.setResult(object);
        return jsonResult;
    }

    /**
     * 接口调用失败方法 无返回对象
     * @return
     */
    public static JsonResult fail() {
        JsonResult jsonResult = new JsonResult();
        jsonResult.setCode(CodeConst.FAIL);
        return jsonResult;
    }

    /**
     * 接口调用失败方法 有返回对象
     * @return
     */
    public static JsonResult fail(Object object) {
        JsonResult jsonResult = new JsonResult();
        jsonResult.setCode(CodeConst.FAIL);
        jsonResult.setResult(object);
        return jsonResult;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public Object getResult() {
        return result;
    }

    public void setResult(Object result) {
        this.result = result;
    }
}

