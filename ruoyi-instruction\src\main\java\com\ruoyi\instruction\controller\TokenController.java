package com.ruoyi.instruction.controller;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.LoginOtherUnifyAuthorize;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.JwtUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.rsa.RSAv2Util;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.instruction.domain.rspVo.SysUserVo;
import com.ruoyi.system.domain.SysAuthOrganization;
import com.ruoyi.system.mapper.SysAuthOrganizationMapper;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.MalformedJwtException;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;


@RestController
@RequestMapping("/token")
public class TokenController {

    @Autowired
    private SysLoginService loginService;


    @Value("${ssyj.appSecret}")
    private  String appSecret;
    @Autowired
    private SysAuthOrganizationMapper sysAuthOrganizationMapper;

//    /**
//     * 写死账号密码参数的登录方法，用于行政执法指挥中心大屏，当点
//     * 击“舆情中心”板块的时候跳转到后台的指令板块中，需要这个接口走
//     * 直接登录的流程,登录后拥有指令板块的所有权限
//     * <AUTHOR>
//     * @date 2023--5-31
//     * @return 包含账号密码的token
//     */
//    @GetMapping("/InfoAccount")
//    public AjaxResult login() throws JsonProcessingException {
//        String res=loginService.login2("zhilingUser");
//        return AjaxResult.success(res);
//    }

    /**
     * 三色预警系统登录获取信息
     * @return
     * @throws JsonProcessingException
     */
    @GetMapping("/getTokenInfo")
    public AjaxResult getTokenInfo(HttpServletRequest request,
                                   String type, String module)  {
        String token = request.getHeader("Authorization");

        String encode = RSAv2Util.encode(token);

        HashMap hashMap = new HashMap();

        hashMap.put("url","http://10.45.13.116:7001/ssyj_auth_gateway.html#/login?token=" + encode + "&type=" + type + "&module=" + module);
        return AjaxResult.success(hashMap);
    }


//    /**
//     * 县市区单点登录
//     * @return
//     * @throws JsonProcessingException
//     */
//    @GetMapping("/getAreaLogin")
//    public AjaxResult getAreaLogin(LoginOtherUnifyAuthorize loginOtherUnifyAuthorize)  {
//        if (StringUtils.isEmpty(loginOtherUnifyAuthorize.getAppId())) {
//            throw new GlobalException("AppId not null");
//        }
//        if (StringUtils.isEmpty(loginOtherUnifyAuthorize.getAuthorizeToken())){
//            throw new GlobalException("authorizeToken not null");
//        }
//        HashMap token = loginService.loginOtherUnifyAuthorizen(loginOtherUnifyAuthorize);
//        token.put("url","跳转地址");
//        return AjaxResult.success(token);
//    }

    /**
     * 登录鉴权
     * @param loginOtherUnifyAuthorize
     * @return
     */
    @GetMapping("/authentication")
    public  AjaxResult authentication(LoginOtherUnifyAuthorize loginOtherUnifyAuthorize){
        if (StringUtils.isEmpty(loginOtherUnifyAuthorize.getAuthorizeToken())) {
            throw new GlobalException("authorizeToken not null");
        }
        SysUserVo sysUserVo = new SysUserVo();
        if (!StringUtils.isEmpty(loginOtherUnifyAuthorize.getAppId())) {
            SysAuthOrganization authOrganization = sysAuthOrganizationMapper.getObj(loginOtherUnifyAuthorize.getAppId());
            if (authOrganization == null) {
                return AjaxResult.error();
            }
            try {
                if (authOrganization == null) {
                    throw new GlobalException("没有权限，请联系管理员开通权限");
                }
                if ("330784_tsapp_434".equals(loginOtherUnifyAuthorize.getAppId())){
                    JWTVerifier verifier = JWT.require(Algorithm.HMAC256(authOrganization.getAppSecret())).build();
                    DecodedJWT jwt = verifier.verify(loginOtherUnifyAuthorize.getAuthorizeToken());
                    Claim claim = jwt.getClaim("userName");
                    sysUserVo.setUserName(claim!=null?jwt.getClaim("userName").asString():null);
                    String p=claim!=null?jwt.getClaim("password").asString():null;
                    sysUserVo.setPassword(StringUtils.isEmpty(p)?null:Base64.encode(p));
                    return AjaxResult.success(sysUserVo);
                }
                Claims claims = JwtUtil.parseJWT(loginOtherUnifyAuthorize.getAuthorizeToken(), authOrganization.getAppSecret());
                //用户名和密码
                String subject = claims.getSubject().trim();
                sysUserVo = JSONObject.parseObject(subject, SysUserVo.class);
                sysUserVo.setPassword(Base64.encode(sysUserVo.getPassword()));
            } catch (MalformedJwtException e) {
                throw new GlobalException("authorizeToken加密串错误，请确认加密是否正确!");
            } catch (Exception e) {
                e.printStackTrace();
                throw new GlobalException("authorizeToken加密串错误，请确认秘钥是否正确!");
            }

        }else {
            String s1 = Base64.decodeStr( loginOtherUnifyAuthorize.getAuthorizeToken());
            String yyyy_mm_dd = DateUtils.getDate();
            String replace = s1.replace(Md5Utils.hash(yyyy_mm_dd), "");
            if (replace.equals("csdn_xzzfj_hhhh")){
                sysUserVo.setUserName("csdn_xzzfj_hhhh");
                sysUserVo.setPassword(Base64.encode("szjh2022!@#$%"));
            }
        }
        return AjaxResult.success(sysUserVo);
    }
}

