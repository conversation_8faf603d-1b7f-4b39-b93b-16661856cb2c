package com.ruoyi.modules.certificates.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.common.vo.BusinessCountVO;
import com.ruoyi.modules.certificates.entity.PetCertificates;
import com.ruoyi.modules.certificates.entity.ShowMapData;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface DongyangDogDao {

    /**
     * 查询东阳犬主信息
     * @return
     */
    List<Map<String, Object>> selectDongyangOwners();

    /**
     * 查询东阳犬只信息
     * @return
     */
    List<Map<String, Object>> selectDongyangPets();

    /**
     * 查询东阳审批数据
     * @return
     */
    List<Map<String, Object>> selectDongyangApproves();

}
