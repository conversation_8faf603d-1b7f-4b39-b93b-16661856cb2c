package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 分析研判对象 t_judement_info
 * 
 * <AUTHOR>
 * @date 2023-05-04
 */
@Data
public class JudementInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 研判信息id */
    private Long id;

    /**
     * 序号
     */
    private Integer rowNumber;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 关键信息 */
    @Excel(name = "关键信息")
    private String keyInfo;

    /** 处置意见 */
    @Excel(name = "处置意见")
    private String disposalOpinion;

    /** 处置时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "处置时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date disposalTime;

    /** 1:已研判 2：待研判 */
    @Excel(name = "1:已研判 2：待研判")
    private String isJudgment;

    /** 1:已发布 2：未发布 */
    @Excel(name = "1:已发布 2：未发布")
    private String isRelease;

    /** 1:正常 9：删除 */
    @Excel(name = "1:正常 9：删除")
    private String status;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 创建类型 1：自动生成 2：手动创建 */
    @Excel(name = "创建类型 1：自动生成 2：手动创建")
    private String createType;

    /** 驾驶舱标题 */
    private String bigscreenTitle;

}
