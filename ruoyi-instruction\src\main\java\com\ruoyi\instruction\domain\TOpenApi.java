package com.ruoyi.instruction.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 第三方对接配置对象 t_open_api
 * 
 * <AUTHOR>
 * @date 2023-07-20
 */
@Data
public class TOpenApi extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 应用的密钥 */
    @Excel(name = "应用的密钥")
    private String appSecret;

    /** 应用的唯一标识key */
    @Excel(name = "应用的唯一标识key")
    private String appKey;

    /** 应用名称 */
    @Excel(name = "应用名称")
    private String name;

    /** 权限列表 */
    @Excel(name = "权限列表")
    private String permissions;


}
