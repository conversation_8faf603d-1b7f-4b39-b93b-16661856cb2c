package com.ruoyi.modules.takeIn.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.takeIn.entity.ReclaimRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.hssf.record.RecalcIdRecord;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 认领、收养记录(reclaim_record)表数据库访问层
 * <AUTHOR>
 */
@Repository
public interface ReclaimRecordDao extends BaseDao<ReclaimRecord> {
    public void updateByEntity(ReclaimRecord entity);

    List<ReclaimRecord> listByTakeInList(List<String> takeInIdList);

    void updateByTakeInId(ReclaimRecord updateRecord);
}
