package com.ruoyi.modules.hospital.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.hospital.entity.QualifiRecord;
import com.ruoyi.modules.punish.entity.Punish;
import org.springframework.stereotype.Repository;

/**
 * 资质审批记录(qualifi_record)表数据库访问层
 * <AUTHOR>
 */
@Repository
public interface QualifiRecordDao extends BaseDao<QualifiRecord> {

    public void updateByEntity(QualifiRecord qualifiRecord);

}
