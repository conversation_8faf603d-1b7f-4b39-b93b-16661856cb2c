package com.ruoyi.modules.harmTrea.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.modules.harmTrea.entity.HarmTrea;
import com.ruoyi.modules.harmTrea.service.HarmTreaService;
import com.ruoyi.modules.harmTrea.vo.HarmTreaBatchVO;
import com.ruoyi.modules.harmTrea.vo.HarmTreaVO;
import com.ruoyi.modules.takeIn.entity.TakeIn;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;


/**
 * 无害化处理记录表(harm_trea)表控制层
 * <AUTHOR>
 */
@RestController
@RequestMapping("harmTrea")
public class HarmTreaController {
    /**
     * 服务对象
     */
    @Resource
    private HarmTreaService service;

    @RequestMapping("/getById")
    public AjaxResult getById(String id) {
        HarmTrea harmTrea = service.getById(id);
        return AjaxResult.success(harmTrea);
    }

    @RequestMapping("/getByTakeInId")
    public AjaxResult getByTakeInId(String id) {
        HarmTreaVO harmTreaVO = service.getByTakeInId(id);
        return AjaxResult.success(harmTreaVO);
    }

    /**
     * 需要被无害化处理列表
     * @param keywords
     * @return
     */
    @RequestMapping("/getNeedHarmList")
    public AjaxResult getNeedHarmList(String keywords) {
        return AjaxResult.success(service.getNeedHarmList(keywords));
    }

    /**
     * 需要被无害化处理列表V2
     * @param harmTreaVO
     * @return
     */
    @RequestMapping("/getNeedHarmListV2")
    public AjaxResult getNeedHarmListV2(HarmTreaVO harmTreaVO) {
        return AjaxResult.success(service.getNeedHarmListV2(harmTreaVO));
    }

    /**
     * 无害化处理记录列表
     * @param entity
     * @return
     */
    @RequestMapping("/getPageList")
    public AjaxResult getPageList(HarmTrea entity) {
        return AjaxResult.success(service.getPageList(entity));
    }

    /**
     * 无害化处理
     * @param entity
     * @return
     */
    @PostMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(@RequestBody HarmTrea entity) {
        service.saveOrUpdate(entity);
        return AjaxResult.success();
    }

    /**
     * 批量处理
     * @param batchVO
     * @return
     */
    @PostMapping("/batchHandle")
    public AjaxResult batchHandle(@RequestBody HarmTreaBatchVO batchVO) {
        service.batchHandle(batchVO);
        return AjaxResult.success();
    }
//
//    @RequestMapping("/delete")
//    public AjaxResult delete(HarmTrea entity) {
//        entity.setDelFlag(2);
//        service.delete(entity);
//        return AjaxResult.success();
//    }
//
//    @RequestMapping("/updateHarmStatus")
//    public AjaxResult updateHarmStatus(HarmTrea entity) {
//        service.updateHarmStatus(entity);
//        return AjaxResult.success();
//    }
}
