<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.user.dao.SysRoleDao" >

    <sql id="sysRoleSql">
        a.id AS "id",
        a.system_flag AS "systemFlag",
        a.role_name AS "roleName",
        a.description AS "description",
        a.dept_flag AS "deptFlag",
        a.create_date AS "createDate",
        a.create_by AS "createBy",
        a.update_date AS "updateDate",
        a.update_by AS "updateBy",
        a.del_flag AS "delFlag"
    </sql>

    <select id="getById" resultType="com.ruoyi.modules.user.entity.SysRole">
        select
          <include refid="sysRoleSql"/>
        from sys_role a
        where a.id = #{id}
    </select>

    <select id="getList" resultType="com.ruoyi.modules.user.entity.SysRole">
        select
          <include refid="sysRoleSql"/>
        from sys_role a
        where a.del_flag = 1
        <if test='systemFlag != null and systemFlag != ""'>
            AND a.system_flag = #{systemFlag}
        </if>
        <if test='roleName != null and roleName != ""'>
            AND a.role_name like concat('%',#{roleName},'%')
        </if>
        <if test='deptFlag != null and deptFlag != ""'>
            AND a.dept_flag = #{deptFlag}
        </if>
    </select>

    <insert id="insert">
        INSERT INTO sys_role(
            id,
            system_flag,
            role_name,
            description,
            dept_flag,
            create_by,
            create_date,
            update_by,
            update_date,
            del_flag
        ) VALUES (
            #{id},
            #{systemFlag},
            #{roleName},
            #{description},
            #{deptFlag},
            #{createBy},
            #{createDate},
            #{updateBy},
            #{updateDate},
            #{delFlag}
        )
    </insert>

    <update id="update">
        update sys_role a set
          a.system_flag = #{systemFlag} ,
          a.role_name = #{roleName} ,
          a.description = #{description} ,
          a.dept_flag = #{deptFlag},
          a.update_date = #{updateDate} ,
          a.update_by = #{updateBy}
          where a.id = #{id}
    </update>
</mapper>
