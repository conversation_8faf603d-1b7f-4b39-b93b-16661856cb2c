package com.ruoyi.web.controller.common;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.domain.InvokeRequest;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.context.ApplicationContext;
import java.lang.reflect.Method;

@Slf4j
@RestController
@RequestMapping("/invoke")
public class AllInvokerController {

    @Autowired
    private RestTemplate restTemplate;
    @Value("${invoke.url}")
    private String invokeUrl;

    // 你可以自定义参数，比如url、method、body等
    @PostMapping("/call")
    public ResponseEntity<?> callOtherController(@RequestBody InvokeRequest invokeReq, HttpServletRequest request) {
        // invokeReq.url: 目标controller的完整url
        // invokeReq.method: GET/POST/PUT/DELETE
        // invokeReq.body: 请求体
        // invokeReq.headers: 请求头
        log.info("callOtherController, invokeReq:{}", JSON.toJSONString(invokeReq));

        StringBuilder url = new StringBuilder(invokeUrl + invokeReq.getUrl());
        if (MapUtils.isNotEmpty(invokeReq.getParams())) {
            url.append("?");
            for (String key : invokeReq.getParams().keySet()) {
                url.append(key).append("=").append(invokeReq.getParams().get(key)).append("&");
            }

            url = new StringBuilder(url.substring(0, url.length() - 1));
        }

        String authorization = request.getHeader("Authorization");

        HttpHeaders headers = new HttpHeaders();
        if (StringUtils.isNotBlank(authorization)) {
            headers.add("Authorization", authorization);
        }

        HttpEntity<?> entity = new HttpEntity<>(invokeReq.getBody(), headers);

        log.info("callOtherController, url:{}, method:{}, body:{}, headers:{}", url.toString(), invokeReq.getMethod(),
                invokeReq.getBody(), JSON.toJSONString(headers));

        ResponseEntity<?> response = restTemplate.exchange(
                url.toString(),
                HttpMethod.valueOf(invokeReq.getMethod().toUpperCase()),
                entity,
                Object.class
        );

        log.info("callOtherController, response:{}", JSON.toJSONString(response.getBody()));

        return response;
    }

}
