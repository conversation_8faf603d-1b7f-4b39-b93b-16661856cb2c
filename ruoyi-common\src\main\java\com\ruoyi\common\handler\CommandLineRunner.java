package com.ruoyi.common.handler;

import com.ruoyi.common.utils.rsa.AESUtil;
import com.ruoyi.common.utils.rsa.RSAUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.security.NoSuchAlgorithmException;
import java.util.Map;


/**
 * 容器加载就触发
 *
 * <AUTHOR>
 */
@Configuration
public class CommandLineRunner {

    @Bean(name = "rsaKeyPair")
    public Map<String, Object> getMap() throws NoSuchAlgorithmException {
        //生成rsa公私钥对
        return RSAUtil.genKeyPair();
    }


    @Bean(name = "aesKeyPair")
    public String getAesKey() throws NoSuchAlgorithmException {
        //生成aes公私钥对
        return AESUtil.genEncodeRule();
    }
}
