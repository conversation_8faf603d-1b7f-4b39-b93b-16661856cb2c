package com.ruoyi.common.core.domain.entity;


import com.ruoyi.common.core.domain.BaseEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * 部门表 sys_dept
 *
 * <AUTHOR>
 */
public class SysDeptUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 部门或者用户id
     */
    private Long duid;

    /**
     * 父部门ID
     */
    private Long parentId;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 类型 'd' = 部门 'u' = 用户
     */
    private String type;

    /**
     * 编码
     */
    private String code;

    /**
     * 岗位编码
     */
    private String postCode;

    /**
     * 子部门
     */
    private List<SysDeptUser> children = new ArrayList<SysDeptUser>();

    public Long getDuid() {
        return duid;
    }

    public void setDuid(Long duid) {
        this.duid = duid;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<SysDeptUser> getChildren() {
        return children;
    }

    public void setChildren(List<SysDeptUser> children) {
        this.children = children;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }
}
