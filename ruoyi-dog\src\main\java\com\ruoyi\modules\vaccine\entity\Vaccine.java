package com.ruoyi.modules.vaccine.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.base.entity.BaseEntity;
import com.ruoyi.modules.user.entity.SysUploadFile;

import java.util.Date;
import java.util.List;

/**
 * @classname:
 * @description:
 * @author: 李冠英
 * @date: 2021/7/29 9:52
 * @version: 1.0
 **/
public class Vaccine extends BaseEntity {
    private String name;                         //疫苗品牌
    private String manufacturer;                      //生产厂家
    private String remarks;                         //申请描述
    private String applyCompany;                       //申请单位
    private String applyPerson;                       //申请人
    private String auditPerson;                       //审核人
    private String status;                       //审核状态 0:审核中，1:已通过，2:未通过
    private String reason;                       //审核原因
    @JsonFormat(timezone = "GMT+8" ,pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;                       //审核时间
    private Integer batchNumber;                       //疫苗批次数量
    private String batch;                       //疫苗批次
    private Integer batchSurplus;                //剩余疫苗批次数量

    private List<SysUploadFile> uploadFileList;  //附件
    private String uploadFileStr;                //附件字符串

    private String idCard;
    private String applyPersonId;//申请人ID
    private String auditPersonId;//审核人ID

    private String deptId;//权限
    private String keyword;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getApplyCompany() {
        return applyCompany;
    }

    public void setApplyCompany(String applyCompany) {
        this.applyCompany = applyCompany;
    }

    public String getApplyPerson() {
        return applyPerson;
    }

    public void setApplyPerson(String applyPerson) {
        this.applyPerson = applyPerson;
    }

    public String getAuditPerson() {
        return auditPerson;
    }

    public void setAuditPerson(String auditPerson) {
        this.auditPerson = auditPerson;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public List<SysUploadFile> getUploadFileList() {
        return uploadFileList;
    }

    public void setUploadFileList(List<SysUploadFile> uploadFileList) {
        this.uploadFileList = uploadFileList;
    }

    public String getUploadFileStr() {
        return uploadFileStr;
    }

    public void setUploadFileStr(String uploadFileStr) {
        this.uploadFileStr = uploadFileStr;
    }

    public Integer getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(Integer batchNumber) {
        this.batchNumber = batchNumber;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getBatch() {
        return batch;
    }

    public void setBatch(String batch) {
        this.batch = batch;
    }

    public Integer getBatchSurplus() {
        return batchSurplus;
    }

    public void setBatchSurplus(Integer batchSurplus) {
        this.batchSurplus = batchSurplus;
    }

    public String getApplyPersonId() {
        return applyPersonId;
    }

    public void setApplyPersonId(String applyPersonId) {
        this.applyPersonId = applyPersonId;
    }

    public String getAuditPersonId() {
        return auditPersonId;
    }

    public void setAuditPersonId(String auditPersonId) {
        this.auditPersonId = auditPersonId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }
}
