package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.JazzPajhAreaStatistics;
import com.ruoyi.instruction.domain.reqVo.JazzPajhAreaStatisticsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 平安金华区域统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-16
 */
@Mapper
@DataSource(value = DataSourceType.SLAVE)
public interface JazzPajhAreaStatisticsMapper 
{
    /**
     * 查询平安金华区域统计
     * 
     * @param id 平安金华区域统计主键
     * @return 平安金华区域统计
     */
    public JazzPajhAreaStatistics selectJazzPajhAreaStatisticsById(Long id);

    /**
     * 查询平安金华区域统计列表
     * 
     * @param jazzPajhAreaStatistics 平安金华区域统计
     * @return 平安金华区域统计集合
     */
    public List<JazzPajhAreaStatistics> selectJazzPajhAreaStatisticsList(JazzPajhAreaStatistics jazzPajhAreaStatistics);

    /**
     * 新增平安金华区域统计
     * 
     * @param jazzPajhAreaStatistics 平安金华区域统计
     * @return 结果
     */
    public int insertJazzPajhAreaStatistics(JazzPajhAreaStatistics jazzPajhAreaStatistics);

    /**
     * 修改平安金华区域统计
     * 
     * @param jazzPajhAreaStatistics 平安金华区域统计
     * @return 结果
     */
    public int updateJazzPajhAreaStatistics(JazzPajhAreaStatistics jazzPajhAreaStatistics);

    /**
     * 删除平安金华区域统计
     * 
     * @param id 平安金华区域统计主键
     * @return 结果
     */
    public int deleteJazzPajhAreaStatisticsById(Long id);

    /**
     * 批量删除平安金华区域统计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJazzPajhAreaStatisticsByIds(Long[] ids);

    /**
     * 批量新增
     * @param list
     * @return
     */
    int insertBatch(@Param("list") List<JazzPajhAreaStatistics> list);

    /**
     * 批量修改
     * @param list
     * @return
     */
    int updateBatch(@Param("list") List<JazzPajhAreaStatistics> list);

    /**
     * 查询列表
     * @param jazzPajhAreaStatisticsVo
     * @return
     */
    List<JazzPajhAreaStatistics> selectList(JazzPajhAreaStatisticsVo jazzPajhAreaStatisticsVo);

    /**
     * 获取最新的一条数据
     * @return
     */
    JazzPajhAreaStatistics selectNew();

    /**
     * 获取某已区域最新一条数据
     * @param jazzPajhAreaStatistics
     * @return
     */
    JazzPajhAreaStatistics selectAreaNew(JazzPajhAreaStatistics jazzPajhAreaStatistics);

    /**
     * 获取分数最高的数据
     * @param jazzPajhAreaStatistics
     * @return
     */
    JazzPajhAreaStatistics selectMAx(JazzPajhAreaStatistics jazzPajhAreaStatistics);
}
