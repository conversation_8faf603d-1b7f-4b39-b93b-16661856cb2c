package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.IndicatorType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 指标类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-15
 */
@DataSource(value = DataSourceType.SLAVE)
public interface IndicatorTypeMapper 
{
    /**
     * 查询指标类型
     * 
     * @param id 指标类型主键
     * @return 指标类型
     */
    public IndicatorType selectIndicatorTypeById(Long id);

    /**
     * 查询指标类型列表
     * 
     * @param indicatorType 指标类型
     * @return 指标类型集合
     */
    public List<IndicatorType> selectIndicatorTypeList(IndicatorType indicatorType);

    /**
     * 新增指标类型
     * 
     * @param indicatorType 指标类型
     * @return 结果
     */
    public int insertIndicatorType(IndicatorType indicatorType);

    /**
     * 修改指标类型
     * 
     * @param indicatorType 指标类型
     * @return 结果
     */
    public int updateIndicatorType(IndicatorType indicatorType);

    /**
     * 删除指标类型
     * 
     * @param id 指标类型主键
     * @return 结果
     */
    public int deleteIndicatorTypeById(Long id);

    /**
     * 批量删除指标类型
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIndicatorTypeByIds(Long[] ids);

    /**
     * 通过type查询出本级以及下级所有typeIds
     * @param type
     * @return
     */
    List<Long> findChildenType(@Param("type") String type);

    /**
     * 根据类型名称查询类型信息
     * @param typeName
     * @return
     */
    IndicatorType findByTypeName(@Param("typeName") String typeName);
}
