package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.instruction.domain.ItemInfo;
import com.ruoyi.instruction.domain.rspVo.BigScreenItemInfoRspVo;
import com.ruoyi.instruction.domain.rspVo.BigScreenItemRspVo;
import com.ruoyi.instruction.service.IItemInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 金安智治-事项挂牌整治
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/21 11:13
 */
@RestController
@RequestMapping("/bigScreen/item")
public class ItemBigScreenController {


    @Autowired
    private IItemInfoService itemInfoService;

    /**
     * 获取挂牌整治数据
     *
     * @return
     */
    @GetMapping("/getDealList")
    public AjaxResult getDealList(ItemInfo itemInfo) {
        List<BigScreenItemRspVo> itemRspVoList = itemInfoService.getDealList(itemInfo);
        BigScreenItemRspVo itemRspVo = itemInfoService.getItemData(itemInfo);
        return AjaxResult.success(itemRspVoList).put("itemData",itemRspVo);
    }

    /**
     * 根据传入县市区获取相应列表
     * @return
     */
    @GetMapping("/getCountyDealList")
    public AjaxResult getCountyDealList(ItemInfo itemInfo){
        List<ItemInfo> list = itemInfoService.selectItemInfoList(itemInfo);
        return AjaxResult.success(list);
    }

    /**
     * 根据县市区名称获取任务数量、事项类型、整治完成率、超期化解任务
     * @param ItemInfo
     * @return
     */
    @GetMapping("/getCountyInfo")
    public AjaxResult getCountyInfo(ItemInfo itemInfo){
        BigScreenItemInfoRspVo itemInfoRspVo = itemInfoService.getCountyInfo(itemInfo);
        return AjaxResult.success(itemInfoRspVo);
    }

}
