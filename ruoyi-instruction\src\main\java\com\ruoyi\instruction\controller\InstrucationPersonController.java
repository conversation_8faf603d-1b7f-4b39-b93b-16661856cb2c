package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.domain.rspVo.InstructionPersonRspVo;
import com.ruoyi.instruction.service.IInstrucationPersonService;
import com.ruoyi.instruction.service.IInstructionGroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 指令关联人员信息Controller
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@RestController
@RequestMapping("/instruction/person")
public class InstrucationPersonController extends BaseController {

    @Autowired
    private IInstructionGroupService instructionGroupService;

    @Autowired
    private IInstrucationPersonService instrucationPersonService;

    /**
     * 查询指令关联人员信息列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:person:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstrucationPerson instrucationPerson) {
        List<InstrucationPerson> list = new ArrayList<>();
        //根据关联群体查询
        if (instrucationPerson.getGroupName() != null && instrucationPerson.getGroupName() != "") {
            //查询群体关联人员
            String personIds = instructionGroupService.selectPersonIdsByGroupName(instrucationPerson.getGroupName());
            if (personIds != null && personIds.length() > 0) {
                String[] split = personIds.split(",");
                if (split.length > 0) {
                    List<Long> collect = Arrays.stream(split).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                    Map<String, Object> params = instrucationPerson.getParams();
                    params.put("ids", collect);
                    instrucationPerson.setParams(params);
                }
            } else {
                return getDataTable(list);
            }
        }
        startPage();
        list = instrucationPersonService.selectInstrucationPersonList(instrucationPerson);
        return getDataTable(list);
    }

    /**
     * 导出指令关联人员信息列表
     */
    // @PreAuthorize("@ss.hasPermi('instruction:person:export')")
    @Log(title = "指令关联人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstrucationPerson instrucationPerson) {
        List<InstrucationPerson> list = instrucationPersonService.selectInstrucationPersonList(instrucationPerson);
        List<InstructionPersonRspVo> personRspVos = new ArrayList<>();
        for (InstrucationPerson person : list) {
            InstructionPersonRspVo instructionPersonRspVo = new InstructionPersonRspVo();
            BeanUtils.copyProperties(person, instructionPersonRspVo);
            personRspVos.add(instructionPersonRspVo);
        }
        ExcelUtil<InstructionPersonRspVo> util = new ExcelUtil<InstructionPersonRspVo>(InstructionPersonRspVo.class);
        util.exportExcel(response, personRspVos, "重点人员信息数据");
    }

    /**
     * 获取指令关联人员信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:person:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(instrucationPersonService.selectInstrucationPersonById(id));
    }

    /**
     * 新增指令关联人员信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:person:add')")
    // @Log(title = "指令关联人员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstrucationPerson instrucationPerson) {

        return instrucationPersonService.insertInstrucationPerson(instrucationPerson);
    }

    /**
     * 修改指令关联人员信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:person:edit')")
    @Log(title = "指令关联人员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstrucationPerson instrucationPerson) {
        return instrucationPersonService.updateInstrucationPerson(instrucationPerson);
    }

    /**
     * 删除指令关联人员信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:person:remove')")
    @Log(title = "指令关联人员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(instrucationPersonService.deleteInstrucationPersonByIds(ids));
    }


    /**
     * 下载人员导入模板
     *
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<InstrucationPerson> util = new ExcelUtil<InstrucationPerson>(InstrucationPerson.class);
        util.importTemplateExcel(response, "人员导入模板");
    }

    /**
     * 下载重点人员导入模板
     *
     * @param response
     */
    @PostMapping("/importPersonTemplate")
    public void importPersonTemplate(HttpServletResponse response) {
        ExcelUtil<InstructionPersonRspVo> util = new ExcelUtil<>(InstructionPersonRspVo.class);
        util.importTemplateExcel(response, "重点人员导入模板");
    }

    /**
     * 导入重点人员数据
     */
    @Log(title = "重点人员", businessType = BusinessType.IMPORT)
    @PostMapping("/importPersonData")
    public AjaxResult importPersonData(MultipartFile file) throws Exception {
        ExcelUtil<InstructionPersonRspVo> util = new ExcelUtil<InstructionPersonRspVo>(InstructionPersonRspVo.class);
        List<InstructionPersonRspVo> personList = util.importExcel(file.getInputStream());
        //获取操作用户
        String operName = getUsername();
        List<InstrucationPerson> finallyList = new ArrayList<>();
        for (InstructionPersonRspVo rspVo : personList) {
            InstrucationPerson person = new InstrucationPerson();
            BeanUtils.copyProperties(rspVo, person);
            finallyList.add(person);
        }
        //存入数据
        String msg = instrucationPersonService.importPerson(finallyList, operName);
        return AjaxResult.success(msg);
    }


    /**
     * 并返回导入数据集合 通过用户id查询
     */
    @Log(title = "人员信息导入", businessType = BusinessType.IMPORT)
    // @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<InstrucationPerson> util = new ExcelUtil<InstrucationPerson>(InstrucationPerson.class);
        List<InstrucationPerson> personList = util.importExcel(file.getInputStream());
        return AjaxResult.success(personList);
    }

    /**
     * 通过人员id查询人员集合
     */
    @GetMapping("/selectPersonListByIds")
    public TableDataInfo selectPersonListByIds(String ids, String personName, String leadIds, Integer type, Integer pageNum, Integer pageSize) {
        //查询关联人员信息
        return instrucationPersonService.selectInstrucationPersonByIds(ids, personName, leadIds, type, pageNum, pageSize);

    }

    /**
     * 根据人员姓名搜索人员库列表信息
     *
     * @param instrucationPerson
     * @return
     */
    @GetMapping("/selectPersonByName")
    public AjaxResult selectPersonByName(InstrucationPerson instrucationPerson) {
        List<InstrucationPerson> list = instrucationPersonService.selectInstrucationPersonList(instrucationPerson);
        return AjaxResult.success(list);
    }

    /**
     * 查询地址薄接口
     *
     * @param type
     * @param code
     * @return
     */
    @GetMapping("/getPersonPlace")
    public AjaxResult getPersonPlace(Integer type, String code) {
        List<HashMap> maps = instrucationPersonService.getPersonPlace(type, code);
        return AjaxResult.success(maps);
    }

}
