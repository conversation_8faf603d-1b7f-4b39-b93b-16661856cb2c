package com.ruoyi.xzzfj.controller;


import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.xzzfj.domain.TXzzfjWorkNoticePerson;
import com.ruoyi.xzzfj.domain.TXzzfjWorkNoticeTimeline;
import com.ruoyi.xzzfj.mapper.TXzzfjWorkNoticePersonMapper;
import com.ruoyi.xzzfj.mapper.TXzzfjWorkNoticeTimelineMapper;
import com.ruoyi.xzzfj.service.ITXzzfjWorkNoticeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;


/**
 * 工作通知h5功能
 */
@Slf4j
@RestController
@RequestMapping("/pub/provincial/commandXzzfjH5")
@CrossOrigin
public class CommandXzzfjH5Controller {


    @Value("${upload.filePath}")
    private String filePath;
    @Value("${upload.urlPath}")
    private String imagePath;

    @Autowired
    ITXzzfjWorkNoticeService itXzzfjWorkNoticeService;

    @Resource
    TXzzfjWorkNoticeTimelineMapper tXzzfjWorkNoticeTimelineMapper;
    @Autowired
    TXzzfjWorkNoticePersonMapper tXzzfjWorkNoticePersonMapper;


    /**
     * 回复指令
     * @param tWorkNoticeTimeline
     * @return
     */
    @PostMapping("/replyInstruction")
    public AjaxResult replyInstruction(TXzzfjWorkNoticeTimeline tWorkNoticeTimeline){
        if (tWorkNoticeTimeline ==null|| tWorkNoticeTimeline.getWorkNoticeId()==null||StringUtils.isBlank(tWorkNoticeTimeline.getMobile())||StringUtils.isBlank(tWorkNoticeTimeline.getContent())){
            return AjaxResult.error("参数不能为空");
        }
        tWorkNoticeTimeline.setStep("2");
        return  itXzzfjWorkNoticeService.clickUrl(tWorkNoticeTimeline);
    }

    /**
     * 点击url
     * @param tWorkNoticeTimeline
     * @return
     */
    @PostMapping("/clickUrl")
    public AjaxResult clickUrl(TXzzfjWorkNoticeTimeline tWorkNoticeTimeline){
        if (tWorkNoticeTimeline ==null|| tWorkNoticeTimeline.getWorkNoticeId()==null||StringUtils.isBlank(tWorkNoticeTimeline.getMobile())){
            return AjaxResult.error("参数不能为空");
        }
        tWorkNoticeTimeline.setStep("1");
        return itXzzfjWorkNoticeService.clickUrl(tWorkNoticeTimeline);
    }

    /**
     * 上传图片
     * @param tWorkNoticeTimeline
     * @param file
     * @return
     */
    @PostMapping("/uploadPicture")
    public AjaxResult uploadPicture(TXzzfjWorkNoticeTimeline tWorkNoticeTimeline, MultipartFile file){
        if (tWorkNoticeTimeline ==null|| tWorkNoticeTimeline.getWorkNoticeId()==null||StringUtils.isBlank(tWorkNoticeTimeline.getMobile())){
            return AjaxResult.error("用户无权限");
        }
        TXzzfjWorkNoticePerson tXzzfjWorkNoticePerson=new TXzzfjWorkNoticePerson();
        tXzzfjWorkNoticePerson.setNoticeId(tWorkNoticeTimeline.getWorkNoticeId());
        tXzzfjWorkNoticePerson.setCodePhone(tWorkNoticeTimeline.getMobile());
        List<TXzzfjWorkNoticePerson> tXzzfjWorkNoticePeople = tXzzfjWorkNoticePersonMapper.selectTXzzfjWorkNoticePersonList(tXzzfjWorkNoticePerson);
        if (CollectionUtils.isEmpty(tXzzfjWorkNoticePeople)){
            return AjaxResult.error("用户无权限");
        }
        tWorkNoticeTimeline.setType(3);
        List<TXzzfjWorkNoticeTimeline> tXzzfjWorkNoticeTimelines = tXzzfjWorkNoticeTimelineMapper.selectTXzzfjWorkNoticeTimelineList(tWorkNoticeTimeline);
        if (!CollectionUtils.isEmpty(tXzzfjWorkNoticeTimelines)){
            return AjaxResult.error("已回复");
        }
       if (file==null){
           return AjaxResult.error("文件不存在");
       }else {
           if (file.getSize()/1024> 10240 ) { //判断图片大小 单位Kb
               return AjaxResult.error("图片不能大于10M");
           }
       }
        String originalFilename = file.getOriginalFilename();
       if (StringUtils.isBlank(originalFilename)){
            return AjaxResult.error("文件不存在");
        }
        String suffix = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
        if (!FileUtils.IMAGE_EXTENSIONS.contains(suffix)) {
            return AjaxResult.error("图片格式错误！");
        }
        String newFileName = com.ruoyi.common.utils.StringUtils.getUUid()+ suffix;
        String path=filePath+ File.separator+newFileName;
        File targetFile = new File(filePath);
        if (!targetFile.exists()) {
            targetFile.mkdirs();
        }
        FileOutputStream out = null;
        String name=null;
        try {
            out = new FileOutputStream(path);
            out.write(file.getBytes());
            name=newFileName;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                try {
                    out.flush();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return AjaxResult.success(StringUtils.isBlank(name)?null:imagePath+name);
    }



}
