package com.ruoyi.common.component;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
@Order(-1)
public class WxHelper {

    static final String grantType = "authorization_code";
    static final String WX_JK_ACCESS_TOKEN = "wx_jk_access_token";
    private static final Logger log = LoggerFactory.getLogger(WxHelper.class);
    @Value("${wx.appid}")
    private String appid;
    @Value("${wx.appSecret}")
    private String appSecret;
    @Value("${wx.accessTokenFromCodeUrl}")
    private String getAccessTokenFromCodeUrl;

//    @Value("${wx.userInfoFromAccessTokenUrl}")
//    private String getUserInfoFromTokenUrl;
//    @Value("${wx.getAccessTokenUrl}")
//    private String getAccessTokenUrl;
//    @Value("${wx.getPhoneNumberUrl}")
//    private String getPhoneNumberUrl;
//    @Value("${wx.code2SessionUrl}")
//    private String code2SessionUrl;
//    @Value("${spring.profiles.active}")
//    private String active;
//    @Resource
//    private RedisCache redisCache;



    /**
     * 根据wxCode获取access_token
     *
     * @param wxCode 微信-code
     * @return MAP
     */
    public Map<String, String> getAccessTokenByCode(String wxCode) {
        log.info("method getAccessTokenByCode() income with wxCode={}", wxCode);
        Map<String, String> map = new HashMap<>();

        //获取access_token
        String getAccessTokenParams = "appid=" + appid + "&secret=" + appSecret + "&code=" + wxCode + "&grant_type=" + grantType;
        String httpRequestResultForToken = HttpUtils.sendGet(getAccessTokenFromCodeUrl, getAccessTokenParams);
        if (StringUtils.isBlank(httpRequestResultForToken)) {
            throw new GlobalException("get AccessToken Not responding");
        }
        JSONObject jsonObjectForToken = JSONObject.parseObject(httpRequestResultForToken);
        log.info("jsonObjectForToken={}", jsonObjectForToken);
        if (!StringUtils.isBlank(jsonObjectForToken.getString("errcode"))) {
            log.info("getAccessTokenByCode.errcode={}", jsonObjectForToken.getString("errcode"));
            throw new GlobalException(jsonObjectForToken.getString("errcode") + ":" + jsonObjectForToken.getString("errmsg"));
        }
        map.put("openid", jsonObjectForToken.getString("openid"));
        log.info("openid={}", jsonObjectForToken.getString("openid"));
        log.info("unionid={}", jsonObjectForToken.getString("unionid"));
        return map;
    }

//    /**
//     * 获取接口调用凭据
//     *
//     * @return
//     */
//    public String getAccessToken() {
//        if (active.equals("prod")) {
//            Object cacheObject = redisCache.getCacheObject(WX_JK_ACCESS_TOKEN);
//            if (cacheObject != null) {
//                return (String) cacheObject;
//            }
//            String getAccessTokenParams = "appid=" + appid + "&secret=" + appSecret + "&grant_type=client_credential";
//            String httpRequestResultForToken = HttpUtils.sendGet(getAccessTokenUrl, getAccessTokenParams);
//            if (StringUtils.isBlank(httpRequestResultForToken)) {
//                throw new GlobalException("get AccessToken Not responding");
//            }
//            JSONObject jsonObjectForToken = JSONObject.parseObject(httpRequestResultForToken);
//            log.info("jsonObjectForToken={}", jsonObjectForToken);
//            if (!StringUtils.isBlank(jsonObjectForToken.getString("errcode"))) {
//                log.info("getAccessTokenByCode.errcode={}", jsonObjectForToken.getString("errcode"));
//                throw new GlobalException(jsonObjectForToken.getString("errcode") + ":" + jsonObjectForToken.getString("errmsg"));
//            }
//            String token = jsonObjectForToken.getString("access_token");
//            if (StringUtils.isEmpty(token)) {
//                throw new GlobalException("token不存在");
//            }
//            redisCache.setCacheObject(WX_JK_ACCESS_TOKEN, token, 7000, TimeUnit.SECONDS);
//            return token;
//        }
//        String params = "appid=" + appid + "&secret=" + appSecret;
////        String token = HttpUtils.sendGet("https://bwshq.dsjj.jinhua.gov.cn:7443/prod-api/screen/wx/getAccessToken", params);
//        String token = HttpUtils.sendGet("https://www.aijinhua.cn/adm-api/screen/wx/getAccessToken", params);
//        return token;
//    }

}
