package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.InstructionFeedback;
import com.ruoyi.instruction.service.IInstructionFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 指令反馈Controller
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
@RestController
@RequestMapping("/instruction/feedback")
public class InstructionFeedbackController extends BaseController
{
    @Autowired
    private IInstructionFeedbackService instructionFeedbackService;

    /**
     * 查询指令反馈列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:feedback:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionFeedback instructionFeedback)
    {
        startPage();
        List<InstructionFeedback> list = instructionFeedbackService.selectInstructionFeedbackList(instructionFeedback);
        return getDataTable(list);
    }

    /**
     * 导出指令反馈列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:feedback:export')")
    @Log(title = "指令反馈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionFeedback instructionFeedback)
    {
        List<InstructionFeedback> list = instructionFeedbackService.selectInstructionFeedbackList(instructionFeedback);
        ExcelUtil<InstructionFeedback> util = new ExcelUtil<InstructionFeedback>(InstructionFeedback.class);
        util.exportExcel(response, list, "指令反馈数据");
    }

    /**
     * 获取指令反馈详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:feedback:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(instructionFeedbackService.selectInstructionFeedbackById(id));
    }

    /**
     * 新增指令反馈
     */
    @PreAuthorize("@ss.hasPermi('instruction:feedback:add')")
    @Log(title = "指令反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionFeedback instructionFeedback)
    {
        return toAjax(instructionFeedbackService.insertInstructionFeedback(instructionFeedback));
    }

    /**
     * 修改指令反馈
     */
    @PreAuthorize("@ss.hasPermi('instruction:feedback:edit')")
    @Log(title = "指令反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionFeedback instructionFeedback)
    {
        return toAjax(instructionFeedbackService.updateInstructionFeedback(instructionFeedback));
    }

    /**
     * 删除指令反馈
     */
    @PreAuthorize("@ss.hasPermi('instruction:feedback:remove')")
    @Log(title = "指令反馈", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(instructionFeedbackService.deleteInstructionFeedbackByIds(ids));
    }
}
