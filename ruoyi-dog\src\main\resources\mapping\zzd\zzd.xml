<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.modules.zzd.dao.SysUserZzdDao" >

    <sql id="sysUserRoleColumns">
        a.id AS "id",
        a.user_id AS "userId",
        a.employeeCode AS "employeeCode",
        a.account as "account"
    </sql>

    <select id="getList" resultType="com.ruoyi.modules.zzd.entity.SysUserZzd">
        select
        <include refid="sysUserRoleColumns"/>
        from sys_user_zzd a
        where 1=1
        <if test='userId != null and userId != ""'>
            and a.user_id = #{userId}
        </if>
        <if test='employeeCode != null and employeeCode != ""'>
            and a.employeeCode = #{employeeCode}
        </if>
    </select>

    <insert id="insert" parameterType="com.ruoyi.modules.zzd.entity.SysUserZzd">
        INSERT INTO sys_user_zzd(
            id,
            user_id,
            employeeCode,
            account
        )VALUES (
                    #{id},
                    #{userId},
                    #{employeeCode},
                    #{account}
                )
    </insert>

    <update id="update" parameterType="com.ruoyi.modules.zzd.entity.SysUserZzd">
        UPDATE sys_user_zzd SET
        <if test='userId != null and userId != ""'>
            user_id = #{userId},
        </if>
        <if test='employeeCode != null and employeeCode != ""'>
            employeeCode = #{employeeCode},
        </if>
        id = #{id}
        WHERE id = #{id}
    </update>


    <update id="deleteByUserId">
        delete from sys_user_zzd where user_id = #{userId}
    </update>

    <insert id="saveList" parameterType="java.util.List">
        INSERT INTO sys_user_zzd (
        id,
        user_id,
        employeeCode,
        account
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.userId},
            #{employeeCode},
            #{account}
            )
        </foreach>
    </insert>
</mapper>
