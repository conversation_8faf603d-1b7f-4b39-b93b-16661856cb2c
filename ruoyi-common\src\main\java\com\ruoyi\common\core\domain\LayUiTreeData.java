package com.ruoyi.common.core.domain;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * 树状结构显示实体.
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LayUiTreeData implements Serializable {


    private static final long serialVersionUID = 680683206552774292L;
    /**
     * 树节点必须属性
     * 该节点所表示的唯一主键
     * 业务主键id+表名
     */
    private String id;
    /**
     * 树节点必须属性
     * 该节点所表示的唯一主键
     * 业务主键id+表名
     */
    private String pid;

    /**
     * 节点标题
     */
    private String title;

    /**
     * 子节点。支持设定选项同父节点
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Object children;

    /**
     * 数据对象
     */
    private Object object;
    /**
     * layUi树形框架字段
     */
    private String field;


    /**
     * 路径
     */
    private String href;

    /**
     * 节点是否初始展开，默认 false
     */
    private Boolean spread;

    /**
     * 节点是否初始为选中状态（如果开启复选框的话），默认 false
     */
    private Boolean checked;
    /**
     * 节点是否为禁用状态。默认 false
     */
    private Boolean disabled;

//	上面部分为LayUi前端框架所有字段

    /**
     * 数据id
     */
    private Integer tableId;

    /**
     * 数据上级id
     */
    private Integer tableRootId;
    /**
     * 数据rootpath
     */
    private String rootPath;
    /**
     * 默认是否是组元素
     * 默认值：false（不是）
     */
    private Boolean isGroup = false;
    /**
     * 业务数据基础类型
     */
    private Integer baseType;

    /**
     * 组织Id
     */
    private Integer orgId;


    /**
     * 锁定字段，返回树种该字段为1时做为不可删除的条件
     */
    private Integer locked;


    /****************************************************************************
     *    菜单相关字段开始
     *************************************************************************/
    /**
     * 图标
     */
    private String icon;

    /**
     * 菜单编码
     */
    private String keycode;
    /**
     * 是否是外链 1为外链 2为内部地址
     */
    private Integer outside;

    /**
     * 菜单路径
     */
    private String url;

    /**
     * 菜单路由
     */
    private String routerurl;
    /**
     * 类型1为目录，2为菜单
     */
    private Integer typeId;

    /****************************************************************************
     *    菜单相关字段结束
     *************************************************************************/
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Object getChildren() {
        return children;
    }

    public void setChildren(Object children) {
        this.children = children;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getHref() {
        return href;
    }

    public void setHref(String href) {
        this.href = href;
    }

    public Boolean getSpread() {
        return spread;
    }

    public void setSpread(Boolean spread) {
        this.spread = spread;
    }

    public Boolean getChecked() {
        return checked;
    }

    public void setChecked(Boolean checked) {
        this.checked = checked;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    public Integer getTableId() {
        return tableId;
    }

    public void setTableId(Integer tableId) {
        this.tableId = tableId;
    }

    public Integer getTableRootId() {
        return tableRootId;
    }

    public void setTableRootId(Integer tableRootId) {
        this.tableRootId = tableRootId;
    }

    public String getRootPath() {
        return rootPath;
    }

    public void setRootPath(String rootPath) {
        this.rootPath = rootPath;
    }

    public Boolean getIsGroup() {
        return isGroup;
    }

    public void setIsGroup(Boolean group) {
        isGroup = group;
    }

    public Integer getBaseType() {
        return baseType;
    }

    public void setBaseType(Integer baseType) {
        this.baseType = baseType;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getLocked() {
        return locked;
    }

    public void setLocked(Integer locked) {
        this.locked = locked;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getKeycode() {
        return keycode;
    }

    public void setKeycode(String keycode) {
        this.keycode = keycode;
    }

    public Integer getOutside() {
        return outside;
    }

    public void setOutside(Integer outside) {
        this.outside = outside;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getRouterurl() {
        return routerurl;
    }

    public void setRouterurl(String routerurl) {
        this.routerurl = routerurl;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }
}
