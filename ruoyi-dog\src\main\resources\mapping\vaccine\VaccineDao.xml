<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.vaccine.dao.VaccineDao">
    <sql id="qualifiApplySql">
        a.id as "id",
        a.name as "name",
        a.manufacturer as "manufacturer",
        a.remarks as "remarks",
        a.apply_company as "applyCompany",
        a.apply_person as "applyPerson",
        a.audit_person as "auditPerson",
        a.batch_number as "batchNumber",
        a.status as "status",
        a.reason as "reason",
        a.audit_time as "auditTime",
        a.create_date as "createDate",
        a.batch as "batch",
        a.batch_surplus as "batchSurplus",
        a.apply_person_id as "applyPersonId",
        a.audit_person_id as "auditPersonId"
    </sql>

    <select id="getList" parameterType="com.ruoyi.modules.vaccine.entity.Vaccine" resultType="com.ruoyi.modules.vaccine.entity.Vaccine">
        select
        <include refid="qualifiApplySql" />
        from vaccine_record a
        where del_flag = 1
          <if test="keyword!=null and keyword!=''">
              and (
              a.name LIKE concat("%", #{keyword}, "%") or
              a.manufacturer LIKE concat("%", #{keyword}, "%") or
              a.batch LIKE concat("%", #{keyword}, "%")
              )
          </if>
        <if test="name != null and name != ''">
            and a.name LIKE concat("%", #{name}, "%")
        </if>
        <if test="manufacturer != null and manufacturer != ''">
            and a.manufacturer LIKE concat("%", #{manufacturer}, "%")
        </if>
        <if test="batch != null and batch != ''">
            and a.batch LIKE concat("%", #{batch}, "%")
        </if>
        <if test="startTime != null and endTime != null">
            and create_date between #{startTime} and #{endTime}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        <if test="createBy != null and createBy != ''">
            and a.apply_person_id = #{createBy}
        </if>
        <if test="deptId != null and deptId != ''">
            and exists (select 1 from qualifi b where b.account =  a.apply_person_id and b.county=#{deptId}  )
        </if>
        order by  a.create_date desc
    </select>

    <select id="getById" resultType="com.ruoyi.modules.vaccine.entity.Vaccine">
        select
        <include refid="qualifiApplySql" />
        from vaccine_record a
        where id = #{id}
    </select>

    <insert id="insert" parameterType="com.ruoyi.modules.vaccine.entity.Vaccine">
        insert into vaccine_record(
            id,
            name,
            manufacturer,
            remarks,
            apply_company,
            apply_person,
            audit_person,
            status,
            reason,
            audit_time,
            batch_number,
            create_date,
            create_by,
            del_flag,
            batch,
            batch_surplus,
            apply_person_id,
            audit_person_id
        )values (
            #{id},
            #{name},
            #{manufacturer},
            #{remarks},
            #{applyCompany},
            #{applyPerson},
            #{auditPerson},
            #{status},
            #{reason},
            #{auditTime},
            #{batchNumber},
            #{createDate},
            #{createBy},
            #{delFlag},
            #{batch},
            #{batchSurplus},
            #{applyPersonId},
            #{auditPersonId}
        )
    </insert>

    <update id="update" parameterType="com.ruoyi.modules.vaccine.entity.Vaccine">
        update vaccine_record set
            name = #{name},
            manufacturer = #{manufacturer},
            remarks = #{remarks},
            apply_company = #{applyCompany},
            apply_person = #{applyPerson},
            audit_person = #{auditPerson},
            status = #{status},
            reason = #{reason},
            batch_number = #{batchNumber},
            audit_time = #{auditTime},
            update_date = #{updateDate},
            update_by = #{updateBy},
            batch = #{batch}
        where id = #{id}
    </update>

    <update id="delete" parameterType="com.ruoyi.modules.vaccine.entity.Vaccine">
        update vaccine_record set  del_flag = #{delFlag}  where id = #{id}
    </update>

    <update id="updateStatus" parameterType="com.ruoyi.modules.vaccine.entity.Vaccine">
        update vaccine_record set status = #{status},
                                  audit_person = #{auditPerson},
                                  audit_person_id = #{auditPersonId},
                                  audit_time = #{auditTime},
                                  reason = #{reason}
        where id = #{id}
    </update>


    <select id="queryUserVaccine" parameterType="com.ruoyi.modules.vaccine.entity.Vaccine" resultType="com.ruoyi.modules.vaccine.entity.Vaccine">
        select
        <include refid="qualifiApplySql" />
        from vaccine_record a
        where del_flag = 1
         and exists (select 1 from pet_certificates c join immune_register b on c.id = b.pet_id
         where c.pet_id_card = #{idCard} and  a.id=b.vaccine_brand)
        order by  a.create_date desc
    </select>


    <select id="getAllList" parameterType="com.ruoyi.modules.vaccine.entity.Vaccine"
            resultType="com.ruoyi.modules.vaccine.entity.Vaccine">
        select a.id, a.name , a.batch,a.batch_surplus,a.apply_person_id
        from vaccine_record a
        where del_flag = 1
        and a.status = '2'
        <if test="createBy != null and createBy != ''">
            and a.apply_person_id = #{createBy}
        </if>
        order by a.create_date
    </select>

    <delete id="deleteByQualifyId">
        delete from vaccine_record where apply_person_id = #{qualifyId}
    </delete>

    <select id="getDistinctList" resultType="java.lang.String">
        select distinct a.name
        from vaccine_record a
        where del_flag = 1
        and a.status = '2'
        and a.name is not null
        and a.name != ''
        order by a.create_date
    </select>
</mapper>
