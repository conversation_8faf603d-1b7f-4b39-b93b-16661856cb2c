package com.ruoyi.util;

import com.ruoyi.modules.user.dao.SysUploadLocalDao;
import com.ruoyi.modules.user.entity.SysUploadLocal;
import com.ruoyi.common.utils.DateUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Method;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @classname: ExcelUtil
 * @author: liyun
 * @date: 2019/6/1 13:26
 * @version: 1.0
 **/
public class ExcelUtil {
    /**
     * 上传本地附件对象
     */
    private static SysUploadLocalDao sysUploadLocalDao = SpringApplicationContextUtil.getBean(SysUploadLocalDao.class);


    public static void setCellStyle(XSSFCell cell) {
        CellStyle cellStyle = cell.getCellStyle();
        cellStyle.setWrapText(true);
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        cell.setCellStyle(cellStyle);
    }

    public static void setXSSFCellStyle(XSSFCell cell) {
        XSSFCellStyle cellStyle = cell.getCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        cell.setCellStyle(cellStyle);
    }

    public static XSSFWorkbook listToExcel(String sheetName, List<String> headList, List<List<String>> list) {
        XSSFWorkbook wb = new XSSFWorkbook();
        //建立新的sheet对象（excel的表单）
        XSSFSheet sheet = wb.createSheet(sheetName);
        //在sheet里创建表头
        XSSFRow headRow = sheet.createRow(0);
        //创建单元格并设置单元格内容
        int index = 0;
        for (String head : headList) {
            XSSFCell cell = headRow.createCell(index);
            // 确保setCellStyle方法已更新为支持XSSFCell
            setCellStyle(cell);
            cell.setCellValue(head);
            sheet.setColumnWidth(index, 25 * 256);
            index++;
        }

        //在sheet里创建第二行
        index = 1;
        for (List<String> child : list) {
            XSSFRow row = sheet.createRow(index);
            int innerIndex = 0;
            for (String value : child) {
                XSSFCell cell = row.createCell(innerIndex);
                setCellStyle(cell);
                cell.setCellValue(value);
                innerIndex++;
            }
            index++;
        }

        return wb;
    }

    /**
     * 下载execl文件
     *
     * @param fileName
     * @param headList
     * @param list
     * @param response
     */
    public static void downloadExecl(String fileName, List<String> headList, List<List<String>> list, HttpServletResponse response, HttpServletRequest request) {
        Workbook wb = listToExcel("sheet", headList, list); // 使用泛型Workbook，以支持HSSFWorkbook和XSSFWorkbook
        // 输出Excel文件
        OutputStream output = null;
        try {
            output = response.getOutputStream();
            // 浏览器乱码问题处理
            fileName = URLEncoder.encode(fileName, "UTF8"); // 统一使用URLEncoder进行文件名编码
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"); // 修改contentType为.xlsx支持的格式
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName + ".xlsx"); // 修改文件扩展名为.xlsx
            wb.write(output);
            output.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (output != null) {
                try {
                    output.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static Map<String, Object> uploadmt(String account, MultipartFile multipartFile,
                                               String localUploadPrefix, String uploadFilePrefix) throws IOException {
        Map<String, Object> mapResult = new HashMap<>();
        //文件信息
        String fileName = multipartFile.getOriginalFilename();
        String fileType = fileName.substring(fileName.lastIndexOf("."), fileName.length());
        System.out.println(fileType);
        String fileKey = IdGen.uuid();
        String absoluteDirectory = localUploadPrefix + DateUtil.format(new Date(), "yyyy-MM-dd") + "/";
        File pathFile = new File(absoluteDirectory);
        if (!pathFile.exists()) {
            pathFile.mkdirs();
        }
        File targetFile = new File(pathFile, fileKey + fileType);
        multipartFile.transferTo(targetFile);

        mapResult.put("targetFile", targetFile);
        mapResult.put("multipartFile", multipartFile);
        mapResult.put("account", account);
        mapResult.put("filePath", uploadFilePrefix + fileKey);

        // 保存附件
        SysUploadLocal sysUploadLocal = new SysUploadLocal();
        sysUploadLocal.preInsert();
        sysUploadLocal.setFileKey(fileKey);
        sysUploadLocal.setFileUrl(targetFile.getPath());
        sysUploadLocal.setFileName(fileName);
        sysUploadLocalDao.insert(sysUploadLocal);
        return mapResult;
    }

    public static Map<String, Object> uploadCom(String account, MultipartFile multipartFile,
                                                HttpServletRequest request) throws IOException {
        Map<String, Object> mapResult = new HashMap<>();
//        //文件信息
//        String fileType = multipartFile.getOriginalFilename();
//        System.out.println(fileType);
//        //临时地址
//        String newName = IdGen.uuid();
//        String key = fileType;
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
//        String i =fileType.replace("(","");
//        String a= i.replace(" ","");
//        String b = a.replace(")","");
//        key = b;
//        String absoluteDirectory = request.getSession().getServletContext().getRealPath("upload/sysUser/" + newName + "/");
//        File pathFile = new File(absoluteDirectory);
//        if (!pathFile.exists()) pathFile.mkdirs();
//        File targetFile = new File(pathFile,  fileType);
//        multipartFile.transferTo(targetFile);
//        Configuration cfg = new Configuration(Zone.zone1());
//        //...其他参数参考类注释
//        UploadManager uploadManager = new UploadManager(cfg);
//        //生成上传凭证
//        Auth auth = Auth.create(U8Constant.QINIU_ACCESS_KEY, U8Constant.QINIU_SECRET_KEY);
//        //生成token，注意这里加上key参数才可以覆盖上传
//        String upToken;
//        //upToken = auth.uploadToken("u8file-public", key);  //中海
//        upToken = auth.uploadToken("chinau8-pub", key);
//        //上传
//        //Response response = uploadManager.put(absoluteDirectory + "/"+newName + "." + fileType, key, upToken);
//        Response response = uploadManager.put(absoluteDirectory + "/"  +fileType, key, upToken);
//        //解析上传成功的结果
//        DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
//        String filePath = QiniuFileUtils.generatePublicURL(key);
//        System.out.println("filePath"+filePath);
//        System.out.println("数据2==="+putRet);

//        excelUtil.comIndexGovService.saveUploadRecord(multipartFile,account,filePath);
        //删除临时文件
//        FileUtils.deleteDirectory(absoluteDirectory);
        String filePath = UploadLocalUtil.uploadFile(multipartFile);
        mapResult.put("multipartFile", multipartFile);
        mapResult.put("account", account);
        mapResult.put("filePath", filePath);
        return mapResult;
    }

    /**
     * excel工具类，可导出带图片或不带图片的数据
     *
     * @param titles   第一行的标题列
     * @param rows     数据行量
     * @param maps     装载导出数据的封装了map的list数据集合，注意：此中的map尽量用本类中的方法 javaBean2Map直接生成，或自己拼接；但需与参数titles[]的标题相关数据对应上
     * @param fileName 导出到本地的文件路径和文件名
     * @param response response
     * @param path     保存到本地的图片地址(我这里是为了删除该目录下的图片，因为我是把网络图片保存到到本地的，如果图片已经是本地图片的话就不需要删除)
     * @date 2021/01/11
     */
    public static void excelOut(String[] titles, int rows, List<Map<String, Object>> maps, String fileName,
                                HttpServletResponse response, String path) {

        OutputStream out = null;
        BufferedImage bufferImg = null;
        XSSFWorkbook wb = null;

        try {
            //创建工作sheet
            wb = new XSSFWorkbook();
            XSSFSheet sheet = wb.createSheet(fileName);
            //设置单元格内容水平垂直居中
            CellStyle style = wb.createCellStyle();
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setWrapText(true); //设置内容自动换行

            //画图的顶级管理器，一个sheet只能获取一个（一定要注意这点）
            XSSFDrawing patriarch = sheet.createDrawingPatriarch();
            XSSFRow row0 = sheet.createRow(0);
            row0.setHeightInPoints(100);
            if (titles.length == 0) {
                return;
            }
            XSSFCell cell = null;
            //第一行、标题行列
            for (int i = 0; i < titles.length; i++) {
                cell = row0.createCell(i);     //第一个单元格
                cell.setCellValue(titles[i]);         //设定值
                cell.setCellStyle(style);
                sheet.setColumnWidth(i, 6000);
            }

            XSSFRow row = null;
            XSSFCell cellRow = null;
            XSSFClientAnchor anchor = null;
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (int i = 1; i <= rows; i++) {
                int cellColumn = 0;
                //创建行
                row = sheet.createRow(i);
                //设置默认行高
                row.setHeightInPoints(100);
                //行数据处理
                Map<String, Object> stringObjectMap = maps.get(i - 1);
                for (Object value : stringObjectMap.keySet()) {
                    if (!value.equals("pageSize")&&!value.equals("pageNum")&&stringObjectMap.get(value) != null) {
                        //行单元格
                        cellRow = row.createCell(cellColumn);
                        cellRow.setCellStyle(style);
                        //如果行数据中有图片时候的处理
                        if (value.equals("imgFile")) {
                            File file = (File) stringObjectMap.get(value);
                            if (file == null) {
                                cellRow.setCellValue("");
                                continue;
                            } else {
                                row.setHeightInPoints(100);

                                ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();

                                cellRow = row.createCell(cellColumn);
                                cellRow.setCellStyle(style);
                                sheet.setColumnWidth(cellColumn, 5000);
                                System.out.println("图片路径" + file);
                                bufferImg = ImageIO.read(file);
                                ImageIO.write(bufferImg, "png", byteArrayOut);
                                anchor = new XSSFClientAnchor(0, 0, 1023, 255, (short) cellColumn, i, (short) cellColumn, i);
                                anchor.setAnchorType(ClientAnchor.AnchorType.DONT_MOVE_AND_RESIZE);
                                patriarch.createPicture(anchor, wb.addPicture(byteArrayOut.toByteArray(), Workbook.PICTURE_TYPE_PNG));
                                cellColumn++;

                                continue;
                            }
                        }
                        cellRow.setCellValue(stringObjectMap.get(value).toString());
                        if(value.equals("createDate")){
                            cellRow.setCellValue(df.format(stringObjectMap.get(value)).toString());
                        }
                        cellColumn++;
                    }
                }

            }
            if (wb != null) {
                out = response.getOutputStream();
                response.setContentType("application/x-msdownload");
                response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xls");
                // 写入excel文件
                wb.write(out);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                try {
                    out.close();

                    //执行删除生成的图片 TODO
//                    File file = new File("E:\\data\\nginxd\\sportsApplets");//输入要删除文件目录的绝对路径
//                    File file = new File("/data/nginxd/sportsApplets/excelDeleteImage/");//输入要删除文件目录的绝对路径
                    File file = new File(path);//输入要删除文件目录的绝对路径
                    //deleteFile(file);//由于是保存网络图片到本地服务区，所以画完图片到excel就要删除文件
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
    }

    /**
     * 删除文件夹目录下的所有文件 （我是怕到时候本地服务器图片越来越多，占用资源，所以把图片洗完到excel里面就删除）
     *
     * @param file
     * @date 2021/01/11
     */
    public static void deleteFile(File file) {
        //判断文件不为null或文件目录存在
        if (file == null || !file.exists()) {
            System.out.println("文件删除失败,请检查文件路径是否正确");
            return;
        }
        //取得这个目录下的所有子文件对象
        File[] files = file.listFiles();
        //遍历该目录下的文件对象
        for (File f : files) {
            //打印文件名
            String name = file.getName();
            System.out.println("删除的文件名" + name);
            //判断子目录是否存在子目录,如果是文件则删除
            if (f.isDirectory()) {
                deleteFile(f);
            } else {
                f.delete();
            }
        }
        //删除空文件夹  for循环已经把上一层节点的目录清空。
        file.delete();
    }

    /**
     * 将java类对象属性-值转换成map的键值对 去除getClass方法属性，以及自定义的file属性放置最后。
     *
     * @param javaBean
     * @return Map
     * @throws Exception
     * @date 2021/01/11
     */
    public static Map<String, Object> javaBean2Map(Object javaBean) throws Exception {
        Map<String, Object> map = new LinkedHashMap<>();
        //反射的实现方式:第一种
        /*Class<Student> studentClass = Student.class;
        studentClass.getClass();*/
        //第二种实现方式
        Method[] methods = javaBean.getClass().getMethods(); // 获取所有方法
        //第三种实现方式
        /*Class.forName("类路径");*/
        String fileName = null;
        File files = null;
        for (Method method : methods) {
            if (method.getName().startsWith("get")) {
                String field = method.getName(); // 拼接属性名
                if (field.equals("pageNum")||field.equals("pageSize")) {
                    continue;
                }
                if (field.contains("getClass")) {
                    continue;
                }
                field = field.substring(field.indexOf("get") + 3);
                field = field.toLowerCase().charAt(0) + field.substring(1);
                Object value = method.invoke(javaBean, (Object[]) null); // 执行方法
                if (field.equals("imgFile")) {
                    fileName = field;
                    files = (File) value;
                    continue;
                }
                map.put(field, value);
            }
        }
        if (fileName != null) {
            map.put(fileName, files);
        }
        return map;
    }

    /**
     * 保存图片到本地
     *
     * @param imageUrl
     * @param path
     * @return
     * @date 2021/01/11
     */
    public static String saveFile(String imageUrl, String path) {
        String filename = imageUrl.substring(imageUrl.lastIndexOf("/") + 1, imageUrl.length());
        System.out.println("图片====" + filename);
//        Random rand = new Random();
//        int s = rand.nextInt(900)+ 100;
        int s = (int) (Math.random() * 10000);
        System.out.println("随机数==" + s);
        filename = s + filename;  //这里如果有文件名称重复的，就取一个随机数拼接文件名
        File sf = null;
        OutputStream os = null;
        InputStream is = null;
        try {
            String urlpath = "";
            if (path.indexOf("http") < 0) {
                urlpath = "file:///" + path;
            } else {
                urlpath = path;
            }
            // 构造URL
            URL url = new URL(urlpath + imageUrl);
            // 打开连接
            URLConnection con = url.openConnection();
            //设置请求超时为5s
            con.setConnectTimeout(5 * 1000);
            // 输入流
            is = con.getInputStream();

            // 1K的数据缓冲
            byte[] bs = new byte[1024];
            // 读取到的数据长度
            int len;
            // 输出的文件流
//		    String path = "E:\\data\\nginxd\\sportsApplets";
//          String path = "/data/nginxd/sportsApplets/excelDeleteImage/";
            sf = new File(path);
            if (!sf.exists()) {
                sf.mkdirs();
            }
            os = new FileOutputStream(sf.getPath() + "/" + filename);
            // 开始读取
            while ((len = is.read(bs)) != -1) {
                os.write(bs, 0, len);
            }

        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            // 完毕，关闭所有链接
            try {
                if (os != null) {
                    os.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return sf.getPath() + "/" + filename;
    }
}
