package com.ruoyi.modules.takeIn.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.harmTrea.entity.HarmTrea;
import com.ruoyi.modules.harmTrea.vo.HarmTreaVO;
import com.ruoyi.modules.punish.entity.Punish;
import com.ruoyi.modules.takeIn.entity.TakeIn;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 收容处置表(take_in)表数据库访问层
 * <AUTHOR>
 */
@Repository
public interface TakeInDao extends BaseDao<TakeIn> {
    public void updateByEntity(TakeIn takeIn);

    List<TakeIn> getReclaimList(TakeIn entity);

    List<TakeIn> getNeedHarmList(@Param("keywords") String keywords);

    TakeIn getByBizCode(@Param("bizCode") String bizCode);

    List<TakeIn> getPageList(TakeIn entity);

    List<TakeIn> listNonComplete(TakeIn takeIn);

    Long countByPetNum(String petNum);

    void sign(@Param("list") List<String> takeInNeedUpdateList, @Param("userId") Long userId, @Param("nickName") String nickName);

    List<TakeIn> listSendMessage();

    List<HarmTreaVO> getNeedHarmListV2(HarmTreaVO harmTreaVO);

    HarmTreaVO getHarmVOById(String id);

    List<TakeIn> getNeedSignByIdList(@Param("list") String[] takeInIdList);

    List<TakeIn> listUnSign(TakeIn takeIn);
}
