package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 指令反馈对象 t_instruction_feedback
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Data
public class InstructionFeedback extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 反馈表主键
     */
    private Long id;

    /**
     * 处置部门
     */
    @Excel(name = "处置部门")
    private String feedbackDept;

    /**
     * 反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "反馈时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTime;

    /**
     * 反馈人员
     */
    @Excel(name = "反馈人员")
    private String feedbackBy;

    /**
     * 是否办结 1：已办结 2：未办结
     */
    @Excel(name = "是否办结 1：已办结 2：未办结")
    private Integer feedbackIsEnd;

    /**
     * 情况反馈
     */
    @Excel(name = "情况反馈")
    private String feedbackSituation;

    /**
     * 处置表id
     */
    @Excel(name = "处置表id")
    private Long disposeId;

    /**
     * 文件ids
     */
    @Excel(name = "文件ids")
    private String fileIds;

    @Excel(name = "指令id")
    private Long instructionId;

    /**
     * 1:正常 9：删除
     */
    @Excel(name = "1:正常 9：删除")
    private Integer status;


    /**
     * 转交id
     */
    @Excel(name = "转交id")
    private Long transferId;

    private String remark;

    /** 接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    /** 反馈部门id */
    @Excel(name = "反馈部门id")
    private Long feedbackDeptId;



}
