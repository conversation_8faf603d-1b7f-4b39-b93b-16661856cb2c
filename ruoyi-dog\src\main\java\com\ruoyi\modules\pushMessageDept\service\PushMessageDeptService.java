package com.ruoyi.modules.pushMessageDept.service;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.notice.dao.NoticeDao;
import com.ruoyi.modules.notice.entity.Notice;
import com.ruoyi.modules.pushMessageDept.dao.PushMessageDeptDao;
import com.ruoyi.modules.pushMessageDept.entity.PushMessageDept;
import com.ruoyi.modules.user.dao.SysUploadFileDao;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.service.SysUploadFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 工作通知关联机构(push_message_dept)接口
 *
 * <AUTHOR>
 */
@Service
public class PushMessageDeptService extends BaseService<PushMessageDeptDao, PushMessageDept> {

}
