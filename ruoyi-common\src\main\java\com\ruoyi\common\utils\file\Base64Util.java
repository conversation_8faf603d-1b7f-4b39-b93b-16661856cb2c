package com.ruoyi.common.utils.file;


import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Base64;

public class Base64Util {

    public static byte[] ImgStrToByte(String imgByte) {
        if (imgByte.contains("data:image/jpeg;base64,")) {
            imgByte = imgByte.replaceAll("data:image/jpeg;base64,", "");
        }
        if (imgByte.contains("data:image/jpg;base64,")) {
            imgByte = imgByte.replaceAll("data:image/jpg;base64,", "");
        }
        if (imgByte.contains("data:image/png;base64,")) {
            imgByte = imgByte.replaceAll("data:image/png;base64,", "");
        }
        byte[] imageByte = null;
        try {
            imageByte = Base64.getDecoder().decode(imgByte);
        } catch (Exception e) {
            e.printStackTrace();
        }
        for (int i = 0; i < imageByte.length; ++i) {
            if (imageByte[i] < 0) {// 调整异常数据
                imageByte[i] += 256;
            }
        }
        return imageByte;
    }

    public static boolean Base64ToImage(String imgStr, String imgFilePath) {
        if (StringUtils.isEmpty(imgStr)) {
            return false;
        }

        try {
            byte[] b = Base64.getDecoder().decode(imgStr);
            for(int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            File file = new File(imgFilePath);
            if (!file.exists()) {
                if (!file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }
            }
            file.createNewFile();
            OutputStream out = new FileOutputStream(imgFilePath);
            out.write(b);
            out.flush();
            out.close();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

}
