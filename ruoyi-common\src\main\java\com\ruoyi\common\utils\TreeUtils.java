package com.ruoyi.common.utils;

import com.ruoyi.common.core.domain.CameraTreeData;
import com.ruoyi.common.core.domain.LayUiTreeData;
import com.ruoyi.common.core.domain.TreeData;

import java.util.*;

public class TreeUtils {

    /**
     * 构建树形结构
     *
     * @return
     */
    public static List<TreeData> buildTree(List<TreeData> rootNodes, List<TreeData> treeNodes, List<TreeData> checkedList) {
        if (checkedList != null && checkedList.size() > 0 && rootNodes != null && rootNodes.size() > 0) {
            for (TreeData check : checkedList) {
                if (rootNodes.get(0).getNodeKey().equals(check.getNodeKey())) {
                    rootNodes.get(0).setAttributes(check.getAttributes());
                    rootNodes.get(0).setChecked(true);
                }
            }
        }
        for (TreeData rootNode : rootNodes) {
            rootNode.setRootpath("/");
            buildChildNodes(rootNode, treeNodes, checkedList);
        }
        return rootNodes;

    }

    /**
     * 构建LayUi树形结构
     *
     * @return
     */
    public static List<LayUiTreeData> buildLayUiTree(List<LayUiTreeData> rootNodes, List<LayUiTreeData> treeNodes, List<LayUiTreeData> checkedList) {
        if (checkedList != null && checkedList.size() > 0 && rootNodes != null && rootNodes.size() > 0) {
            for (LayUiTreeData check : checkedList) {
                if (rootNodes.get(0).getId().equals(check.getId())) {
                    rootNodes.get(0).setObject(check.getObject());
                    rootNodes.get(0).setChecked(true);
                }
            }
        }
        for (LayUiTreeData rootNode : rootNodes) {
//            rootNode.setRootPath("/");
            buildLayUiChildNodes(rootNode, treeNodes, checkedList);
        }
        return rootNodes;

    }


    /**
     * 递归子节点
     *
     * @param rootNode
     * @param treeNodes
     * @param checkedList
     */
    public static void buildLayUiChildNodes(LayUiTreeData rootNode, List<LayUiTreeData> treeNodes, List<LayUiTreeData> checkedList) {
        List<LayUiTreeData> childNodes = new ArrayList<LayUiTreeData>();
        if (treeNodes != null && treeNodes.size() > 0) {
            for (LayUiTreeData child : treeNodes) {
                if (rootNode.getId().equals(child.getPid())) {
                    if (checkedList != null && checkedList.size() > 0) {
                        int tag = 0;
                        for (LayUiTreeData check : checkedList) {
                            if (child.getId().equals(check.getId())) {
                                child.setChildren(check.getChildren() == null ? null : check.getChildren());
                                child.setChecked(true);
                                rootNode.setChecked(true);
                                tag = 1;
                            }
                        }
                        if (tag == 0) {
                            child.setChildren(null);
                        }
                    }
                    //        child.setRootPath(rootNode.getRootPath() + "/");
                    childNodes.add(child);
                }
            }

            if (childNodes != null && childNodes.size() > 0) {
                for (LayUiTreeData child : childNodes) {
                    buildLayUiChildNodes(child, treeNodes, checkedList);
                }
            }
        }
        if (childNodes.size() == 0) {
            if (rootNode.getIsGroup()) {
                rootNode.setChildren(new ArrayList<>());
            } else {
                rootNode.setChildren(null);
            }

        } else {
            rootNode.setChildren(childNodes);
        }

    }

    /**
     * 递归子节点
     *
     * @param rootNode
     * @param treeNodes
     * @param checkedList
     */
    public static void buildChildNodes(TreeData rootNode, List<TreeData> treeNodes, List<TreeData> checkedList) {
        List<TreeData> childNodes = new ArrayList<TreeData>();
        if (treeNodes != null && treeNodes.size() > 0) {
            for (TreeData child : treeNodes) {
                if (rootNode.getNodeKey().equals(child.getPId())) {
                    if (checkedList != null && checkedList.size() > 0) {
                        int tag = 0;
                        for (TreeData check : checkedList) {
                            if (child.getNodeKey().equals(check.getNodeKey())) {
                                child.setAttributes(check.getAttributes());
                                child.setChecked(true);
                                rootNode.setChecked(true);
                                tag = 1;
                            }
                        }
                        if (tag == 0) {
                            child.setAttributes(new HashMap<>());
                        }
                    }
                    child.setRootpath(rootNode.getRootpath() + "/");
                    childNodes.add(child);
                }
            }

            if (childNodes != null && childNodes.size() > 0) {
                for (TreeData child : childNodes) {
                    buildChildNodes(child, treeNodes, checkedList);
                }
            }
        }
        if (childNodes.size() == 0) {
            rootNode.setChildren(null);
        } else {
            rootNode.setChildren(childNodes);
        }

    }

    /**
     * @param allNodeMap
     * @param serch
     * @return List<TreeData>
     * @Title: findByParam
     * @Description: 获取筛选后的树数据
     * <AUTHOR>
     * @date 2019/10/16 15:45
     */
    public static List<TreeData> findByParam(List<TreeData> serch, Map<String, TreeData> allNodeMap) {
        List<TreeData> result = new ArrayList<TreeData>();
        Map<String, TreeData> map = new HashMap<String, TreeData>();
        for (TreeData tree : serch) {
            if (map.get(tree.getNodeKey()) == null) {
                TreeData node = allNodeMap.get(tree.getNodeKey());
                if (node != null) {
                    map.put(node.getNodeKey(), node);
                    result.add(node);
                    handleData(allNodeMap, map, node, result);
                }
            }
        }
        return result;
    }

    /**
     * @param allNodeMap
     * @param serch
     * @return List<TreeData>
     * @Title: findByParam
     * @Description: 获取筛选后的树数据
     * <AUTHOR>
     * @date 2019/10/16 15:45
     */
    public static List<LayUiTreeData> findByLayUiParam(List<LayUiTreeData> serch, Map<String, LayUiTreeData> allNodeMap) {
        List<LayUiTreeData> result = new ArrayList<LayUiTreeData>();
        Map<String, LayUiTreeData> map = new HashMap<String, LayUiTreeData>();
        for (LayUiTreeData tree : serch) {
            if (map.get(tree.getId()) == null) {
                LayUiTreeData node = allNodeMap.get(tree.getId());
                if (node != null) {
                    map.put(node.getId(), node);
                    result.add(node);
                    handleLayUiData(allNodeMap, map, node, result);
                }
            }
        }
        return result;
    }

    /**
     * @param allNodeMap
     * @param tree
     * @param tree
     * @param result
     * @return void
     * @Title: handleData
     * @Description: 根据参数过滤树数据
     * <AUTHOR>
     * @date 2019/10/16 15:45
     */
    public static void handleData(Map<String, TreeData> allNodeMap, Map<String, TreeData> map, TreeData tree, List<TreeData> result) {
        TreeData rootNode = allNodeMap.get(tree.getPId());
        if (rootNode != null && map.get(rootNode.getNodeKey()) == null) {
            map.put(rootNode.getNodeKey(), rootNode);
            result.add(rootNode);
            if (allNodeMap.get(rootNode.getPId()) != null) {
                handleData(allNodeMap, map, rootNode, result);
            }
        }
    }


    /**
     * @param allNodeMap
     * @param tree
     * @param tree
     * @param result
     * @return void
     * @Title: handleData
     * @Description: 根据参数过滤树数据
     * <AUTHOR>
     * @date 2019/10/16 15:45
     */
    public static void handleLayUiData(Map<String, LayUiTreeData> allNodeMap, Map<String, LayUiTreeData> map, LayUiTreeData tree, List<LayUiTreeData> result) {
        LayUiTreeData rootNode = allNodeMap.get(tree.getPid());
        if (rootNode != null && map.get(rootNode.getId()) == null) {
            map.put(rootNode.getId(), rootNode);
            result.add(rootNode);
            if (allNodeMap.get(rootNode.getPid()) != null) {
                handleLayUiData(allNodeMap, map, rootNode, result);
            }
        }
    }

    /**
     * @param allTrees
     * @param trees
     * @return List<TreeData>
     * @Title: calculationChildCount
     * @Description: 计算节点有多少子集个数
     * <AUTHOR>
     * @date 2019/10/16 15:45
     */
    public static List<TreeData> calculationChildCount(List<TreeData> allTrees, List<TreeData> trees) {
        List<TreeData> result = new ArrayList<TreeData>();
        if (allTrees != null && allTrees.size() > 0) {
            for (TreeData tree : allTrees) {
                int count = 0;
                for (TreeData all : trees) {
                    if (all.getRootId().equals(tree.getId())) {
                        count += 1;
                    }
                }
                tree.setChildCount(count);
            }
            result.addAll(allTrees);
        }
        return result;
    }

    /**
     * @param all
     * @param allNodeMap
     * @return List<TreeData>
     * @Title: findRootNodeList
     * @Description: 获取顶级节点数据
     * <AUTHOR>
     * @date 2019/10/16 15:45
     */
    public static List<TreeData> findRootNodeList(List<TreeData> all, Map<String, TreeData> allNodeMap) throws RuntimeException {
        List<TreeData> rootNodeList = new ArrayList<TreeData>();
        for (TreeData data : all) {
            if (allNodeMap.get(data.getPId()) == null) {
                data.setRootpath("/");
                rootNodeList.add(data);
            }
        }
        return rootNodeList;
    }

    /**
     * @param data
     * @param all
     * @return List<TreeData>
     * @Title: findRootNodeList
     * @Description: 查询当前节点有多少子集
     * <AUTHOR>
     * @date 2019/10/16 15:45
     */
    public static void hasChild(TreeData data, List<TreeData> all) throws RuntimeException {
        for (TreeData tree : all) {
            if (data.getNodeKey().equals(tree.getPId())) {
                data.setChildCount(1);
                break;
            }
        }
    }

    /**
     * 构建树形结构 Map
     *
     * @return List<Map < String, Object>>
     */
    public static List<Map<String, Object>> buildTreeMap(List<TreeData> rootNodes, List<TreeData> treeNodes, List<TreeData> checkedList) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (TreeData rootNode : rootNodes) {
            Map<String, Object> nodeMap = new LinkedHashMap<>();
            //防止组织的id和用户的id重合
            if (rootNode.getType().indexOf("ORGANIZATION") != -1) {
                nodeMap.put("id", rootNode.getNodeKey());
            } else {
                nodeMap.put("id", rootNode.getId());
            }
            nodeMap.put("label", rootNode.getName());
            nodeMap.put("count", 0);
            nodeMap.put("nodeKey", rootNode.getNodeKey());
            nodeMap.put("type", rootNode.getType());
            nodeMap.put("baseType", rootNode.getBaseType());
            buildChildNodesMap(nodeMap, treeNodes);
            result.add(nodeMap);
        }
        return result;
    }

    /**
     * 递归子节点
     *
     * @param rootNode
     * @param treeNodes
     */
    public static void buildChildNodesMap(Map<String, Object> rootNode, List<TreeData> treeNodes) {
        List<TreeData> childNodes = new ArrayList<TreeData>();
        List<Map<String, Object>> chileNodeMapList = new ArrayList<>();
        if (treeNodes != null && treeNodes.size() > 0) {
            for (TreeData child : treeNodes) {
                if (rootNode.get("nodeKey").equals(child.getPId())) {
                    childNodes.add(child);
                    Map<String, Object> childMap = new LinkedHashMap<>();
                    //防止组织的id和用户的id重合
                    if (child.getType().indexOf("ORGANIZATION") != -1) {
                        childMap.put("id", child.getNodeKey());
                    } else {
                        childMap.put("id", child.getId());
                    }
                    childMap.put("label", child.getName());
                    childMap.put("count", 0);
                    childMap.put("nodeKey", child.getNodeKey());
                    childMap.put("type", child.getType());
                    childMap.put("baseType", child.getBaseType());
                    childMap.put("position", child.getPosition());
                    childMap.put("pId", child.getPId());
                    chileNodeMapList.add(childMap);
                }
            }

            if (null != childNodes && childNodes.size() > 0) {
                for (Map<String, Object> map : chileNodeMapList) {
                    buildChildNodesMap(map, treeNodes);
                }
                rootNode.put("children", chileNodeMapList);
                rootNode.put("count", chileNodeMapList.size());
            }
        }
    }

    public static List<Map<String, Object>> buildCameraTreeMap(List<CameraTreeData> rootNodes, List<CameraTreeData> treeNodes) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (CameraTreeData rootNode : rootNodes) {
            Map<String, Object> nodeMap = new LinkedHashMap<>();
            nodeMap.put("name", rootNode.getName());
            nodeMap.put("count", 0);
            nodeMap.put("nodeKey", rootNode.getNodeKey());
            nodeMap.put("type", rootNode.getType());
            nodeMap.put("id", rootNode.getId());
            buildCameraChildNodesMap(nodeMap, treeNodes);
            result.add(nodeMap);
        }
        return result;
    }

    public static void buildCameraChildNodesMap(Map<String, Object> rootNode, List<CameraTreeData> treeNodes) {
        List<CameraTreeData> childNodes = new ArrayList<>();
        List<Map<String, Object>> chileNodeMapList = new ArrayList<>();
        if (treeNodes != null && treeNodes.size() > 0) {
            for (CameraTreeData child : treeNodes) {
                if (rootNode.get("nodeKey").equals(child.getParentNodeKey())) {
                    childNodes.add(child);
                    Map<String, Object> childMap = new LinkedHashMap<>();
                    childMap.put("name", child.getName());
                    childMap.put("count", 0);
                    childMap.put("nodeKey", child.getNodeKey());
                    childMap.put("type", child.getType());
                    childMap.put("id", child.getId());
                    chileNodeMapList.add(childMap);
                }
            }

            if (null != childNodes && childNodes.size() > 0) {
                for (Map<String, Object> map : chileNodeMapList) {
                    buildCameraChildNodesMap(map, treeNodes);
                }
                rootNode.put("children", chileNodeMapList);
                rootNode.put("count", chileNodeMapList.size());
            }
        }
    }

}
