package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 金安驾驶舱通用
 * <AUTHOR> @version 1.0
 * @date 2023/3/6 9:18
 *
 */
@Data
public class BigScreenJazzCommonVo {

    public String name;
    /**
     * 拓展
     */
    public String expand;

    public Integer num;
    /**
     * 拓展int
     */
    public Integer expandInt;
    /**
     * 总数
     */
    private  Integer total;
    /**
     * 完成数
     */
    private  Integer completed;

    /**
     * 完成数
     */
    private  Integer notCompleted;

    /**
     * 预计时间
     */
    private BigDecimal planTime;
    /**
     * 实际时间
     */
    private BigDecimal realityTime;

    private  Long id;

}
