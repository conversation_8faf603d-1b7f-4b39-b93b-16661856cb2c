package com.ruoyi.common.getui;

import com.getui.push.v2.sdk.GtApiConfiguration;
import org.springframework.stereotype.Component;

@Component
public class ApiContext {

    public GtApiConfiguration configuration;
    public String cid;

    private static String appid = "NcmHDks6S78ZF6BwE6Sdt3";

    private static String appkey = "d9L0ULox6K9ha7X16y8RC1";

    private static String mastersecret = "FucMF7aKeT6BZHhAXsZea1";

    public static ApiContext build() {
        ApiContext context = new ApiContext();
        GtApiConfiguration apiConfiguration = new GtApiConfiguration();
        context.configuration = apiConfiguration;

        apiConfiguration.setAppId(appid);
        apiConfiguration.setAppKey(appkey);
        apiConfiguration.setMasterSecret(mastersecret);
        // 接口调用前缀，请查看文档: 接口调用规范 -> 接口前缀, 可不填写appId
        apiConfiguration.setDomain("https://restapi.getui.com/v2/");
        context.cid = "CID";

        return context;
    }
}
