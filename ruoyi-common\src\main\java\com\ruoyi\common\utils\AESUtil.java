package com.ruoyi.common.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/22 14:17
 */
public class AESUtil {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";

    /**
     * AES 加密
     *
     * @param word       要加密的字符串
     * @param encodeRule 加密规则（密钥）
     * @return 加密后的字符串
     */
    public static String AESEncrypt(String word, String encodeRule) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(encodeRule.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedBytes = cipher.doFinal(word.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("AES 加密失败", e);
        }
    }

    /**
     * AES 解密
     *
     * @param word       要解密的字符串
     * @param encodeRule 解密规则（密钥）
     * @return 解密后的字符串
     */
    public static String AESDecrypt(String word, String encodeRule) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(encodeRule.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decodedBytes = Base64.getDecoder().decode(word);
            byte[] decryptedBytes = cipher.doFinal(decodedBytes);
            String res = new String(decryptedBytes, StandardCharsets.UTF_8);

            // 处理解密后的字符串
            if (res.length() == 11) {
                String startNum = res.substring(0, 3);
                String endNum = res.substring(7);
                res = startNum + "****" + endNum;
            }
            return res;
        } catch (Exception e) {
            return word;
        }
    }

    public static void main(String[] args) {
        String word = "uOHTpBsp94dL7w38PXOv4w==";
        String encodeRule = "ivqpsFQwQqxYUr7f"; // 密钥长度必须为16字节（128位）

        // String encrypted = AESEncrypt(word, encodeRule);
        // System.out.println("Encrypted: " + encrypted);

        String decrypted = AESDecrypt(word, encodeRule);
        System.out.println("Decrypted: " + decrypted);
    }
}
