package com.ruoyi.modules.brand.service;


import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.brand.dao.PetAuditRecordsDao;
import com.ruoyi.modules.brand.dao.PetBrandDao;
import com.ruoyi.modules.brand.entity.PetAuditRecords;
import com.ruoyi.modules.brand.entity.PetBrand;
import com.ruoyi.modules.hospital.dao.QualifiDao;
import com.ruoyi.modules.hospital.entity.Qualifi;
import com.ruoyi.modules.user.dao.SysUserDao;
import com.ruoyi.modules.user.entity.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class PetBrandService extends BaseService<PetBrandDao, PetBrand> {
    @Autowired
    private PetAuditRecordsDao petAuditRecordsDao;
    @Autowired
    private QualifiDao qualifiDao;
    @Autowired
    private SysUserDao sysUserDao;

    public void upStatus(PetBrand petBrand) {
        if (petBrand.getIsCancellation() != null && !"".equals(petBrand.getIsCancellation())) {
            petBrand.setOffDate(new Date());
        }
        dao.upStatus(petBrand);
    }

    // 医院申请犬牌发放犬牌
    public void updateList(PetBrand petBrand) {
        List<PetBrand> list = dao.getListByNum(petBrand);
        for (PetBrand p : list) {
            p.setBrandCom(petBrand.getBrandCom());
        }
        dao.updateList(list);
    }

    public int saveList(PetBrand petBrand) {
        List<PetBrand> saveList = new ArrayList<>();
        int sNum = 0;        //开始值
        int eNum = 0;        //结束值
        int count = 0;      //计数
        if (petBrand.getsNum() != null && !"".equals(petBrand.getsNum())) {
            sNum = getStringNum(petBrand.getsNum());
        }
        if (petBrand.geteNum() != null && !"".equals(petBrand.geteNum())) {
            eNum = getStringNum(petBrand.geteNum());
        }
        for (int i = sNum; i <= eNum; i++) {
            PetBrand saveData = new PetBrand();
            saveData.preInsert();
            saveData.setStreet(petBrand.getStreet());
            saveData.setCounty(petBrand.getCounty());
            saveData.setArea(petBrand.getArea());
            saveData.setIsRecovery(1);          //未使用
            saveData.setIsCancellation(1);      //未注销
            saveData.setIsUse(2);               //未使用
            String brandNum = getStringNoNum(petBrand.getsNum()) + i;
            if (brandNum.length() == 8) {
                saveData.setBrandNum(getStringNoNum(petBrand.getsNum()) + i);
            } else if (brandNum.length() == 7) {
                saveData.setBrandNum(getStringNoNum(petBrand.getsNum()) + "0" + i);
            } else if (brandNum.length() == 6) {
                saveData.setBrandNum(getStringNoNum(petBrand.getsNum()) + "00" + i);
            } else if (brandNum.length() == 5) {
                saveData.setBrandNum(getStringNoNum(petBrand.getsNum()) + "000" + i);
            } else if (brandNum.length() == 4) {
                saveData.setBrandNum(getStringNoNum(petBrand.getsNum()) + "0000" + i);
            } else if (brandNum.length() == 3) {
                saveData.setBrandNum(getStringNoNum(petBrand.getsNum()) + "00000" + i);
            } else if (brandNum.length() == 2) {
                saveData.setBrandNum(getStringNoNum(petBrand.getsNum()) + "000000" + i);
            } else if (brandNum.length() == 1) {
                saveData.setBrandNum(getStringNoNum(petBrand.getsNum()) + "0000000" + i);
            }
            saveList.add(saveData);
            count++;
        }
        dao.saveList(saveList);
        return count;
    }

    //截取字符串中的数字
    public int getStringNum(String str) {
        str = str.trim();
        String str2 = "";
        if (str != null && !"".equals(str)) {
            for (int i = 0; i < str.length(); i++) {
                if (str.charAt(i) >= 48 && str.charAt(i) <= 57) {
                    str2 += str.charAt(i);
                }
            }
        }
        return Integer.parseInt(str2);
    }

    //截取字符串中除了数字的值
    public String getStringNoNum(String str) {
        String regEx = "[0-9]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        System.out.println(m.replaceAll("").trim());
        return m.replaceAll("").trim();
    }

    public List<PetBrand> getAllList(PetBrand petBrand) {
        return dao.getAllList(petBrand);
    }

    public HashMap totalEchar() {
        HashMap map = dao.totalEchar();
        map.put("echar", dao.deptEchar());
        return map;
    }

    @Transactional
    public void batchQualifi(String ids, String qualifiId) {
        Qualifi qualifi = qualifiDao.getById(qualifiId);
        String name = "";
        if (qualifi != null) {
            name = qualifi.getName();
        } else {
            SysUser user = sysUserDao.getById(qualifiId);
            if (user != null) name = user.getRealName();
        }
        List<String> list = JSON.parseArray(ids, String.class);
        for (String id : list) {
            PetBrand brand = new PetBrand();
            brand.setId(id);
            brand.setBrandCom(qualifiId);
            dao.updateByEntity(brand);
            //                分配记录
            PetAuditRecords petAuditRecords = new PetAuditRecords();
            petAuditRecords.setApplyId(brand.getId());
            petAuditRecords.setRemarks(name);
            petAuditRecords.preInsert();
            petAuditRecordsDao.insert(petAuditRecords);
        }
    }

    public Integer getNotApplyBrandNum(String deptId) {
        return dao.getNotApplyBrandNum(deptId);
    }

    public List<PetBrand> getBranchList(PetBrand petBrand) {
        return dao.getBranchListNew(petBrand.getArea(),petBrand.getNum());
    }

    @Transactional
    public void handleReset(PetBrand brand) {
//        修改信息
        brand = dao.getById(brand.getId());
        brand.setBrandCom(null);
        dao.update(brand);
//        记录日志
        PetAuditRecords records = new PetAuditRecords();
        records.setApplyId(brand.getId());
        records.setRemarks("重置归属单位");
        records.preInsert();
        petAuditRecordsDao.insert(records);
    }

    /**
     * 分页获取数据对象
     * @param
     * @return
     */
    public PageInfo<PetBrand> getPageList1(PetBrand petBrand){
        Integer count = dao.count(petBrand);
        PageHelper.startPage(petBrand.getPageNum(),petBrand.getPageSize(),false);
        PageInfo<PetBrand> pageInfo = new PageInfo<PetBrand>(getList(petBrand));
        pageInfo.setTotal(count);
        return pageInfo;
    }
}
