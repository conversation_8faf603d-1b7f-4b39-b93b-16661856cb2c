package com.ruoyi.modules.notice.service;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.base.service.BaseService;
import com.ruoyi.modules.notice.dao.NoticeDao;
import com.ruoyi.modules.notice.dao.WorkNoticeDao;
import com.ruoyi.modules.notice.entity.Notice;
import com.ruoyi.modules.notice.entity.WorkNotice;
import com.ruoyi.modules.pushMessageDept.dao.PushMessageDeptDao;
import com.ruoyi.modules.pushMessageDept.entity.PushMessageDept;
import com.ruoyi.modules.user.dao.SysUploadFileDao;
import com.ruoyi.modules.user.entity.SysUploadFile;
import com.ruoyi.modules.user.service.SysUploadFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 违法处罚表(punish)表服务接口
 *
 * <AUTHOR>
 */
@Service
public class WorkNoticeService extends BaseService<WorkNoticeDao, WorkNotice> {

    @Autowired
    SysUploadFileService uploadFileService;
    @Autowired
    PushMessageDeptDao pushMessageDeptDao;
    @Autowired
    SysUploadFileDao uploadFileDao;
    @Override
    public void saveOrUpdate(WorkNotice entity) {
        super.saveOrUpdate(entity);
        if (!ObjectUtils.isEmpty(entity.getMessageDept())) {
            pushMessageDeptDao.deleteMessageId(entity.getId());
            List<PushMessageDept> list = JSON.parseArray(entity.getMessageDept(),PushMessageDept.class);
            List<PushMessageDept> result = new ArrayList<>();
            for (PushMessageDept s : list) {
                s.preInsert();
                s.setMessageId(entity.getId());
                s.setStatus("1");
                result.add(s);
            }
            pushMessageDeptDao.saveAllList(result);
        }
        if (!ObjectUtils.isEmpty(entity.getUploadFileStr())) {
            uploadFileService.delByInstanceAndModel(entity.getId(), "work_id");
            List<SysUploadFile> list = JSON.parseArray(entity.getUploadFileStr(), SysUploadFile.class);
            List<SysUploadFile> result = new ArrayList<>();
            for (SysUploadFile s : list) {
                s.preInsert();
                s.setInstanceId(entity.getId());
                result.add(s);
            }
            uploadFileDao.saveAllList(result);
        }
    }

    public WorkNotice getById(String id){
        WorkNotice workNotice = dao.getById(id);
        SysUploadFile sysUploadFile = new SysUploadFile();
        sysUploadFile.setInstanceId(id);
        sysUploadFile.setModelType("work_id");
        workNotice.setUploadFileList(uploadFileService.getList(sysUploadFile));
        PushMessageDept list = new PushMessageDept();
        list.setMessageId(id);
        workNotice.setMessageDept(JSON.toJSONString(pushMessageDeptDao.getList(list)));
        return workNotice;
    }

    public void updateStatus(WorkNotice entity){
        dao.updateStatus(entity);
    }

}
