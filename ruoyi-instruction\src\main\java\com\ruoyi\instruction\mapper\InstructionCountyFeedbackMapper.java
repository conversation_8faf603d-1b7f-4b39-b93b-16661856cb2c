package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.InstructionCountyFeedback;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 县市区反馈Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-25
 */
@DataSource(value = DataSourceType.SLAVE)
public interface InstructionCountyFeedbackMapper {
    /**
     * 查询县市区反馈
     *
     * @param id 县市区反馈主键
     * @return 县市区反馈
     */
    public InstructionCountyFeedback selectInstructionCountyFeedbackById(Long id);

    /**
     * 查询县市区反馈列表
     *
     * @param instructionCountyFeedback 县市区反馈
     * @return 县市区反馈集合
     */
    public List<InstructionCountyFeedback> selectInstructionCountyFeedbackList(InstructionCountyFeedback instructionCountyFeedback);

    /**
     * 新增县市区反馈
     *
     * @param instructionCountyFeedback 县市区反馈
     * @return 结果
     */
    public int insertInstructionCountyFeedback(InstructionCountyFeedback instructionCountyFeedback);

    /**
     * 修改县市区反馈
     *
     * @param instructionCountyFeedback 县市区反馈
     * @return 结果
     */
    public int updateInstructionCountyFeedback(InstructionCountyFeedback instructionCountyFeedback);

    /**
     * 删除县市区反馈
     *
     * @param id 县市区反馈主键
     * @return 结果
     */
    public int deleteInstructionCountyFeedbackById(Long id);

    /**
     * 批量删除县市区反馈
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionCountyFeedbackByIds(Long[] ids);

    /**
     * 根据指令id、反馈部门 更新县市区反馈状态
     *
     * @param infoId
     * @param dept
     * @param isEnd
     */
    void updateByIstructionIdAndDept(@Param("instructionId") Long infoId, @Param("deptId") Long dept, @Param("isEnd") int isEnd);
}
