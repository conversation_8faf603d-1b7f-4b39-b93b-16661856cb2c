package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.InstructionCase;

import java.util.List;

/**
 * 【经典案例】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-12
 */
@DataSource(value = DataSourceType.SLAVE)
public interface InstructionCaseMapper
{
    /**
     * 查询【经典案例】
     *
     * @param id 【经典案例】主键 id
     * @return
     */
    public InstructionCase selectInstructionCaseById(Long id);

    /**
     * 查询【经典案例】列表
     *
     * @param instructionCase
     * @return 【经典案例】集合
     */
    public List<InstructionCase> selectInstructionCaseList(InstructionCase instructionCase);

    /**
     * 新增【经典案例】
     *
     * @param instructionCase
     * @return 结果
     */
    public int insertInstructionCase(InstructionCase instructionCase);

    /**
     * 修改【经典案例】
     *
     * @param instructionCase
     * @return 结果
     */
    public int updateInstructionCase(InstructionCase instructionCase);

    /**
     * 删除【经典案例】
     *
     * @param id 【经典案例】主键
     * @return 结果
     */
    public int deleteInstructionCaseById(Long id);

    /**
     * 批量删除【经典案例】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionCaseByIds(Long[] ids);
}
