<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.modules.express.dao.ExpressDao">

    <sql id="columns">
        <trim suffixOverrides=",">
            a.id as id,
            a.phone as phone,
            a.petId as petId,
            a.state as state,
            a.provinceCode as provinceCode,
            a.provinceName as provinceName,
            a.cityName as cityName,
            a.cityCode as cityCode,
            a.regionCode as regionCode,
            a.regionName as regionName,
            a.address as address,
            a.name as name,
a.expressCode as expressCode,
a.collectTime as collectTime,
a.sendUserId as sendUserId,
            a.del_flag as delFlag,
            a.create_date as createDate,
            a.create_by as createBy,
            a.update_date as updateDate,
            a.update_by as updateBy,
        </trim>

    </sql>

    <!--查询单个-->
    <select id="getById" parameterType="com.ruoyi.modules.express.entity.Express" resultType="com.ruoyi.modules.express.entity.Express">
        select <include refid="columns"/>
        from express a
        where a.id =#{id}
    </select>



    <!--通过实体作为筛选条件查询-->
    <select id="getList" parameterType="com.ruoyi.modules.express.entity.Express" resultType="com.ruoyi.modules.express.entity.Express">
        select <include refid="columns"/>
        from express a
        where a.del_flag =1 and del_flag = 1
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
        <if test="petId != null and petId != ''">
            and a.petId = #{petId}
        </if>
        <if test="state != null">
            and a.state = #{state}
        </if>
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.ruoyi.modules.express.entity.Express">
        insert into express(id, phone, petId, state, provinceCode, provinceName,
                            cityName,cityCode, regionCode , regionName, address, name,del_flag, create_date,
                              create_by)
        values (#{id}, #{phone}, #{petId}, #{state}, #{provinceCode}, #{provinceName}, #{cityName}, #{cityCode}
                   , #{regionCode}, #{regionName}, #{address}, #{name}, #{delFlag}, #{createDate},
                #{createBy})
    </insert>
    <!--通过主键修改数据-->
    <update id="update" parameterType="com.ruoyi.modules.express.entity.Express">
        update express set
        <trim suffixOverrides=",">
            state = #{state},
            expressCode = #{expressCode},
            collectTime = #{collectTime},
            sendUserId = #{sendUserId},
            del_flag = #{delFlag},
            update_date = #{updateDate},
            update_by = #{updateBy},
        </trim>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.ruoyi.modules.express.entity.Express">
        UPDATE  express
        SET
            del_flag=#{delFlag},
            update_date = #{updateDate},
            update_by = #{updateBy}
        where id = #{id}
    </delete>

</mapper>
