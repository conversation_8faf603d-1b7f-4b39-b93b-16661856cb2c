package com.ruoyi.modules.petLose.dao;

import com.ruoyi.base.dao.BaseDao;
import com.ruoyi.modules.petLose.entity.PetLose;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 宠物丢失表(pet_lose)表数据库访问层
 * <AUTHOR>
 */
@Repository
public interface PetLoseDao extends BaseDao<PetLose> {

    public void updateByEntity(PetLose petLose);

}
