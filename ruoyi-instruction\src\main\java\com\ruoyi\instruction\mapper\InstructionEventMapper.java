package com.ruoyi.instruction.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.InstructionEcologicalEnv;
import com.ruoyi.instruction.domain.InstructionEvent;
import com.ruoyi.instruction.domain.InstructionEventInfos;
import com.ruoyi.instruction.domain.InstructionTrend;
import com.ruoyi.instruction.domain.rspVo.*;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 事件基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@DataSource(value = DataSourceType.SLAVE)
public interface InstructionEventMapper {
    /**
     * 查询事件基本信息
     *
     * @param id 事件基本信息主键
     * @return 事件基本信息
     */
    public InstructionEvent selectInstructionEventById(Long id);

    /**
     * 查询事件基本信息列表
     *
     * @param instructionEvent 事件基本信息
     * @return 事件基本信息集合
     */
    public List<InstructionEvent> selectInstructionEventList(InstructionEvent instructionEvent);

    /**
     * 新增事件基本信息
     *
     * @param instructionEvent 事件基本信息
     * @return 结果
     */
    public int insertInstructionEvent(InstructionEvent instructionEvent);

    /**
     * 修改事件基本信息
     *
     * @param instructionEvent 事件基本信息
     * @return 结果
     */
    public int updateInstructionEvent(InstructionEvent instructionEvent);

    /**
     * 删除事件基本信息
     *
     * @param id 事件基本信息主键
     * @return 结果
     */
    public int deleteInstructionEventById(Long id);

    /**
     * 批量删除事件基本信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionEventByIds(Long[] ids);

    /**
     * 根据人员id查询人员关联事件信息
     *
     * @param id
     * @return
     */
    List<InstructionEvent> findByPersonId(@Param("id") Long id);

    /**
     * 根据群体id查询关联事件
     *
     * @param id
     * @return
     */
    List<InstructionEvent> getEventListByGroupId(@Param("id") Long id);

    /**
     * 根据事件名称查询事件
     *
     * @param eventTitle
     * @return
     */
    InstructionEvent findByEventTile(@Param("eventTitle") String eventTitle);

    /**
     * 根据事件名称、推送时间
     *
     * @param eventTitle
     * @param pushTime
     * @return
     */
    InstructionEvent findByEventTileAndPushTime(@Param("eventTitle") String eventTitle, @Param("pushTime") Date pushTime);

    /**
     * 根据指令id查询指令对应事件
     *
     * @param infoId
     * @return
     */
    InstructionEvent selectInstructionEventByInstrucationId(@Param("id") Long infoId);

    /**
     * 获取事件总数、重大事件数、区县事件数、市级事件数
     *
     * @return
     */
    EventDataRspVo getEventData(Map<String,Object> map);

    /**
     * 获取事件交办数
     * @return
     */
    List<EventAssignRspVo> getAssignData();

    /**
     * 获取信息类别
     * @return
     */
    List<Map<String, Integer>> getInfoCategory();

    /**
     * 获取事件类型
     * @return
     */
    List<Map<String, Integer>> getType();

    /**
     * 获取事件-基本情况
     * @return
     */
    String getEventBaseSituation();

    /**
     * 获取事件关联超过三次的群体名称
     * @return
     */
    List<String> getGroupName(@Param("groupCount")Integer groupCount);

    /**
     * 获取信息来源
     * @return
     */
    List<Map<String, Integer>> getInfoSource();

    /**
     * 获取事件多发区域排名
     * @return
     */
    List<Map<String, Integer>> getAreaRank();

    /**
     * 获取事件子类型数据
     * @param id
     * @return
     */
    List<Map<String, Integer>> getChildType(@Param("id") Integer id);

    /**
     * 获取大屏事件列表
     * @param date
     * @param nextDate
     * @param dutyUnit
     * @return
     */
    List<BigScreenListEventRspVo> getBigScreenEventList(@Param("date") String date,@Param("nextDate")String nextDate,@Param("dutyUnit")String dutyUnit);

    /**
     * 获取事件统计
     * @param date
     * @param nextDate
     * @return
     */
    BigScreenEventStatistics getStatistics(@Param("date") String date,@Param("nextDate")String nextDate,@Param("dutyUnit")String dutyUnit);

    /**
     * 获取事件统计-县市区关联事件数
     * @param date
     * @param nextDate
     * @return
     */
    List<Map<String, Integer>> getCountyEventCount(@Param("date") String date, @Param("nextDate") String nextDate);

    /**
     * 根据id获取事件详情
     * @param id
     * @return
     */
    BigScreenEventInfo getInfoById(@Param("id") Long id);

    /**
     * 根据群组id指令id
     * @param groupId
     * @return
     */
    List<Long> selectInstructionIdByGroupId(@Param("groupId") Long groupId);

    /**
     * 根据群组id获取事件详情
     * @param groupId
     * @return
     */
    List<DisposalProcessEventVo> selectEventByGroupId(@Param("groupId") Long groupId);

    /**
     * 根据群组id获取事件详情,步骤为5时的列表
     * @param groupId
     * @return
     */
    List<DisposalProcessEventVo> selectEventByGroupIdForEnd(@Param("groupId") Long groupId);

    /**
     * 未关联群体事件 更新相关群体id
     * @return
     */
    int synchronizationGroupId();

    /**
     * 查询事件数量在时间范围内
     * @param date
     * @return
     */
    Integer getCountByTime(@Param("date") LocalDate date);
    /**
     * 查询所有事件的责任单位
     * @return
     */
    List<String> selectDutyUnit();

    String getPersonIdsByTime(@Param("date") LocalDate date);

    List<Map<String, Object>> getAreaEventCount(@Param("date") LocalDate date, @Param("thresholdValue") Integer thresholdValue, @Param("thresholdRule") Integer value);

    /**
     * 事件在事件范围内增速
     * @param date
     * @return
     */
    Integer eventGrowthRate(@Param("date") LocalDate date);
    /**
     * 查询重大事件板块左下角的重大事件清单
     * <AUTHOR>
     * @return
     */
    List<InstructionEventInfos> getEventInfo(@Param("year") String year,@Param("distinct") String distinct);

    /**
     * 金安智治驾驶舱根据事件类型统计维稳事件
     * @return
     */
    List<BigScreenJazzCommonVo> getJazzType();

    /**
     * 首页动态监测（维稳事件）
     * @return
     */
    List<InstructionEvent> getJazzEventTypeList();

    /**
     * 查询事件类型增速
     * @param date
     * @param valueOf
     * @param thresholdRule
     * @return
     */
    List<Map<String, Object>> eventTypeGrowthRate(@Param("date") LocalDate date,@Param("value") Integer valueOf, @Param("rule") String thresholdRule,@Param("county") String county);


    /**
     * 按月根据事件统计结果
     * @return
     */
    List<BigScreenJazzPageYearCountVo> getJazzEventTypeByYearList(InstructionEvent instructionEvent);

    /**
     * 获取生态环境安全
     * @param year
     * <AUTHOR>
     */
    InstructionEcologicalEnv selectEcologicalEnv(int year);
    /**
     * 金安大数据生产安全板块  事件详情
     * <AUTHOR>
     */
    List<InstructionEvent> getProduceSafeEventInfo();

    InstructionTrend getTrend(int year);

    /**
     * 根据groupId获取单位
     * @param id
     * @return
     */
    String selectDutyUnitByGroupId(@Param("id") Long id);
}
