package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

import java.util.Date;

/**
 * 指令信息 --重点风险治理
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/21 19:39
 */
@Data
public class JazzInstructionInfoStatisticsRspVo {
    /**
     * 指令id
     */
    private Long id;

    /**
     * 指令标题
     */
    private String instructionTitle;
    /**
     * 办理期限
     */
    private Date handleTime;

    /**
     * 反馈时间
     */
    private Date feedbackTime;
    /**
     * 是否销号 1:销号 2：未销号
     */
    private Integer instrucationIsEnd;

    private Date createTime;

    /**
     * 销号时间
     */
    private Date endTime;
    /**
     * 接收单位
     */
    private  String receiveUnit;
}
